# syntax=docker/dockerfile:1

# Build the application from source
FROM golang:1.22 AS build-stage

WORKDIR /src

COPY go.mod .
COPY go.sum .

RUN go mod download

# Code changes must not make the dependencies to be re-downloaded
COPY cmd/greco_events cmd/greco_events
COPY internal internal

RUN go build cmd/greco_events/main.go

# Config changes must not make the code to be re-compiled
COPY config config

# Deploy the application binary into a lean image
FROM quay.io/centos/centos:stream9-minimal AS release-stage

WORKDIR /app

COPY --from=build-stage /src/main .
COPY --from=build-stage /src/config config

EXPOSE 8080

ENTRYPOINT ["/app/main"]
