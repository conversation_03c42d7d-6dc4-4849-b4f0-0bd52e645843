@startuml
skinparam backgroundcolor transparent
skinparam linetype ortho

entity bets {
    * id: uuid
    --
    * game_id: uuid <<FK>>
    * user_id: uuid <<FK>>
    --
    * bet_type: text
    * currency: text
    * external_id: text
    * ghost_mode: bool
    * hidden_boolean: bool
    * round_status: text
    * time: timestamptz
    bet_amount: numeric
    multiplier: decimal
    payout: numeric
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity boosts {
    * id: uuid
    --
    * user_id: uuid <<FK>>
    --
    * bonus_finishes_at: timestamptz
    * bonus_starts_at: timestamptz
    * boost_duration_hours: integer
    * multiplier: double precision
    bonus_started_at: timestamptz
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity games {
    * id: uuid
    --
    * cms_game_id: integer
    * external_id: text
    * game_data: text
    * vendor_game_id: text
    name: text
    slug: text
    thumbnail_id: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity games_versions {
    * id: integer
    --
    * version: integer
    --
    * created_at: timestamptz
    * updated_at: timestamptz
    deleted_at: timestamptz
}

entity registered_emails {
    * id: uuid
    --
    * email: text
    * email_marketing: boolean
    * registered: boolean
    --
    * created_at: timestamptz
    * updated_at: timestamptz
    deleted_at: timestamptz
}

entity transactions {
    * id: uuid
    --
    user_id: uuid
    --
    * amount: numeric
    * bet_external_id: text
    * category: text
    * currency: text
    * external_id: text
    * inserted_at: timestamptz
    * status: text
    * type: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity user_assets {
    * id: uuid
    --
    * user_id: uuid <<FK>>
    * user_config_asset_id: uuid <<FK>>
    --
    * type: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity user_claimed_coins {
    * id: uuid
    --
    * user_id: uuid <<FK>>
    --
    * coins: numeric
    * source: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity user_config_assets {
    * id: uuid
    --
    * key: text
    * sub_type: text
    * type: text
    * value: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity users {
    * id: uuid
    --
    * company_user: bool
    * external_id: text
    * first_name: text
    * ghost_mode: bool
    * hide_all_stats: bool
    * hide_tournament_stats: bool
    * join_date: timestamptz
    * last_name: text
    * profile_status: text
    * user_name: text
    factor_sid: text
    last_updated_on:timestamptz
    profile_picture: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity vip_user_balances {
    * id: uuid
    --
    * user_id: uuid <<FK>>
    --
    * handle: numeric
    * coins: numeric
    * bets_won: bigint
    * total_bets: bigint
    * source: text
    --
    * created_at: timestamptz
    updated_at: timestamptz
    deleted_at: timestamptz
}

entity users_bet_summary {
    * user_id: uuid <<FK>>
    --
    * total_bets: numeric
    * wagered: numeric
    * number_of_wins: numeric
    * number_of_losses: numeric
}

entity users_coins_summary {
    * user_id: uuid <<FK>>
    --
    * total_coins: double precision
    * claimed_coins: numeric
    * bets_coins: double precision
    * vip_coins: numeric
}

games ||--|{ bets
users ||--|{ bets
users ||--|{ boosts
users |o--|{ transactions
users ||--|{ user_assets
users ||--|{ user_claimed_coins
users ||--|{ users_bet_summary
users ||--|{ users_coins_summary
users ||--|{ vip_user_balances
user_assets }|--|{ user_config_assets
@enduml
