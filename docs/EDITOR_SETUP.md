# Editor Setup and Code Formatting

This document explains the code formatting standards and editor configuration for this project.

## EditorConfig

This project uses [EditorConfig](https://editorconfig.org/) to maintain consistent coding styles across different editors and IDEs. The configuration is defined in the `.editorconfig` file at the root of the repository.

### Supported Editors

EditorConfig is supported by most popular editors either natively or through plugins:

- **VS Code**: Built-in support (no plugin needed)
- **IntelliJ IDEA/GoLand**: Built-in support
- **Vim/Neovim**: Install the `editorconfig-vim` plugin
- **Emacs**: Install the `editorconfig-emacs` package
- **Sublime Text**: Install the `EditorConfig` package
- **Atom**: Install the `editorconfig` package

### Formatting Rules

The project enforces the following formatting standards:

#### Go Files (*.go)
- **Indentation**: Tabs (standard Go convention)
- **Tab size**: 4 spaces (for display purposes)
- **Charset**: UTF-8
- **Line endings**: LF (Unix-style)
- **Final newline**: Required
- **Trailing whitespace**: Trimmed

#### YAML Files (*.yml, *.yaml)
- **Indentation**: 2 spaces
- **Charset**: UTF-8
- **Line endings**: LF
- **Final newline**: Required
- **Trailing whitespace**: Trimmed

#### JSON Files (*.json)
- **Indentation**: 2 spaces
- **Charset**: UTF-8
- **Line endings**: LF
- **Final newline**: Required
- **Trailing whitespace**: Trimmed

#### Shell Scripts (*.sh)
- **Indentation**: 2 spaces
- **Charset**: UTF-8
- **Line endings**: LF
- **Final newline**: Required
- **Trailing whitespace**: Trimmed

#### Dockerfiles
- **Indentation**: 2 spaces
- **Charset**: UTF-8
- **Line endings**: LF
- **Final newline**: Required
- **Trailing whitespace**: Trimmed

#### Markdown Files (*.md)
- **Indentation**: 2 spaces
- **Charset**: UTF-8
- **Line endings**: LF
- **Final newline**: Required
- **Trailing whitespace**: Preserved (for proper markdown formatting)

## Git Attributes

The `.gitattributes` file ensures consistent line endings across different operating systems and properly handles binary files.

### Key Features
- Automatic line ending normalization for text files
- LF line endings for shell scripts and Dockerfiles
- Proper binary file handling
- Git LFS configuration for large files

## Setup Instructions

### For New Contributors

1. **Install EditorConfig support** in your editor (if not built-in)
2. **Clone the repository** - EditorConfig will automatically apply the formatting rules
3. **Verify setup** by opening any Go file - your editor should use tabs for indentation

### Verifying Your Setup

To verify that EditorConfig is working correctly:

1. Open a Go file (e.g., `cmd/rest/main.go`)
2. Add a new line and press Tab - it should insert a tab character, not spaces
3. Open a YAML file (e.g., `docker-compose.yaml`)
4. Add a new line and press Tab - it should insert 2 spaces

### Go Formatting

In addition to EditorConfig, Go files should be formatted using the standard Go tools:

```bash
# Format all Go files
go fmt ./...

# Or use goimports for better import management
goimports -w .
```

### Pre-commit Hooks (Recommended)

Consider setting up pre-commit hooks to automatically format code before commits:

```bash
# Add to .git/hooks/pre-commit
#!/bin/sh
go fmt ./...
go vet ./...
```

## Troubleshooting

### Editor Not Respecting EditorConfig

1. Verify EditorConfig plugin is installed and enabled
2. Check that the `.editorconfig` file is in the repository root
3. Restart your editor
4. Check editor-specific EditorConfig documentation

### Line Ending Issues

If you encounter line ending issues:

1. Run `git config core.autocrlf false` (recommended for this project)
2. Re-clone the repository
3. Verify `.gitattributes` is properly configured

### Mixed Indentation

If you see mixed tabs and spaces:

1. Enable "Show whitespace" in your editor
2. Use your editor's "Convert indentation" feature
3. Follow the EditorConfig rules for the specific file type

## Additional Resources

- [EditorConfig Official Site](https://editorconfig.org/)
- [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- [Effective Go](https://golang.org/doc/effective_go.html)
