<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Monkey Tilt Password</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
        
        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }
        
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
            line-height: 1.6; 
            color: #ffffff; 
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
            margin: 0;
            padding: 0;
            width: 100%;
        }
        
        .email-wrapper {
            width: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
            padding: 20px 10px;
        }
        
        .email-container { 
            max-width: 800px; 
            width: 95%;
            margin: 0 auto; 
            background: linear-gradient(145deg, #1a1a1a 0%, #252525 100%);
            border-radius: 20px; 
            overflow: hidden; 
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.7), 0 0 0 1px rgba(255, 215, 0, 0.15);
            position: relative;
        }
        
        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #FFD700 0%, #FFA500 25%, #FFD700 50%, #FFA500 75%, #FFD700 100%);
        }
        
        .header { 
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 30%, #FFD700 70%, #FFA500 100%); 
            padding: 60px 50px; 
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: shimmer 8s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(180deg); }
        }
        
        .logo { 
            max-width: 300px; 
            height: auto; 
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
        }
        
        .content { 
            padding: 60px 50px; 
            background: linear-gradient(145deg, #1a1a1a 0%, #252525 100%);
            position: relative;
        }
        
        .content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at top left, rgba(255, 215, 0, 0.06) 0%, transparent 60%),
                        radial-gradient(ellipse at bottom right, rgba(255, 165, 0, 0.06) 0%, transparent 60%);
            pointer-events: none;
        }
        
        .title { 
            font-size: 42px; 
            font-weight: 800; 
            color: #ffffff; 
            margin-bottom: 16px; 
            text-align: center;
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #cccccc;
            text-align: center;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
            font-weight: 500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
            position: relative;
            z-index: 2;
        }
        
        .message-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1f1f1f 100%);
            border-radius: 16px;
            padding: 40px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            position: relative;
            z-index: 2;
            grid-column: span 2;
        }
        
        .message { 
            font-size: 18px; 
            color: #e0e0e0; 
            margin-bottom: 20px; 
            line-height: 1.7;
        }
        
        .warning-card { 
            background: linear-gradient(145deg, #2d1810 0%, #3d2415 100%);
            border: 1px solid #FFA500; 
            border-radius: 16px;
            padding: 25px; 
            position: relative;
            z-index: 2;
        }
        
        .warning-card::before {
            content: '⚠️';
            position: absolute;
            top: 50%;
            left: 25px;
            font-size: 24px;
            transform: translateY(-50%);
        }
        
        .warning-text { 
            color: #FFD700; 
            font-weight: 600; 
            font-size: 17px;
            margin-left: 45px;
            line-height: 1.4;
            text-align: center;
        }
        
        .security-card { 
            background: linear-gradient(145deg, #1a2332 0%, #243447 100%);
            border: 1px solid #4A9EFF; 
            border-radius: 16px;
            padding: 25px; 
            position: relative;
            z-index: 2;
        }
        
        .security-card::before {
            content: '🔒';
            position: absolute;
            top: 50%;
            left: 25px;
            font-size: 22px;
            transform: translateY(-50%);
        }
        
        .security-text { 
            color: #87CEEB; 
            font-size: 16px; 
            line-height: 1.6;
            margin-left: 45px;
            font-weight: 500;
            text-align: center;
        }
        
        .cta-section {
            text-align: center; 
            margin: 50px 0;
            position: relative;
            z-index: 2;
        }
        
        .cta-button { 
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); 
            color: #000000 !important; 
            padding: 22px 50px; 
            text-decoration: none; 
            border-radius: 16px; 
            font-weight: 700; 
            font-size: 20px; 
            display: inline-block; 
            transition: all 0.3s ease; 
            border: none;
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .cta-button:hover { 
            transform: translateY(-4px); 
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.5);
            background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
        }
        
        .backup-card { 
            background: linear-gradient(145deg, #2a2a2a 0%, #1f1f1f 100%);
            border: 1px solid #444; 
            border-radius: 16px;
            padding: 35px; 
            margin: 40px 0; 
            position: relative;
            z-index: 2;
        }
        
        .backup-card-title { 
            color: #ffffff; 
            font-weight: 600; 
            margin-bottom: 20px;
            font-size: 16px;
            text-align: center;
        }
        
        .backup-url { 
            color: #4A9EFF; 
            word-break: break-all; 
            font-family: 'Monaco', 'Courier New', monospace;
            background: rgba(74, 158, 255, 0.15);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(74, 158, 255, 0.3);
            font-size: 14px;
            text-align: center;
        }
        
        .footer { 
            background: linear-gradient(145deg, #0f0f0f 0%, #1a1a1a 100%);
            padding: 50px; 
            text-align: center; 
            border-top: 1px solid rgba(255, 215, 0, 0.2);
            position: relative;
        }
        
        .footer-brand {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 12px;
        }
        
        .footer-team {
            font-size: 16px;
            color: #888;
            margin-bottom: 35px;
        }
        
        .social-links { 
            margin: 30px 0;
        }
        
        .social-links a { 
            color: #4A9EFF; 
            text-decoration: none; 
            margin: 0 20px; 
            font-size: 16px;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover { 
            color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
        }
        
        .copyright { 
            color: #666; 
            font-size: 14px; 
            margin-top: 30px;
            line-height: 1.6;
        }
        
        /* Responsive Design - Progressive Enhancement */
        
        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .email-container { max-width: 850px; width: 90%; }
            .header { padding: 60px 50px; }
            .content { padding: 60px 50px; }
            .footer { padding: 50px; }
            .title { font-size: 44px; }
            .subtitle { font-size: 21px; }
            .logo { max-width: 320px; }
        }
        
        /* Desktop (992px+) */
        @media (min-width: 992px) {
            .email-wrapper { padding: 30px 15px; }
            .content-grid { gap: 35px; }
            .message-card { padding: 45px; }
        }
        
        /* Tablet (768px-991px) */
        @media (max-width: 991px) and (min-width: 768px) {
            .email-container { max-width: 700px; width: 92%; }
            .email-wrapper { padding: 25px 12px; }
            .header { padding: 50px 35px; }
            .content { padding: 50px 35px; }
            .footer { padding: 40px 35px; }
            .title { font-size: 36px; }
            .subtitle { font-size: 18px; }
            .cta-button { padding: 20px 45px; font-size: 18px; }
        }
        
        /* Mobile (768px and below) */
        @media (max-width: 767px) {
            .email-wrapper { padding: 15px 8px; }
            .email-container { margin: 0; border-radius: 16px; width: 98%; }
            .header { padding: 40px 25px; }
            .content { padding: 40px 25px; }
            .footer { padding: 30px 25px; }
            .title { font-size: 28px; }
            .subtitle { font-size: 16px; }
            .cta-button { padding: 18px 35px; font-size: 16px; }
            .message-card { padding: 25px; }
            .backup-card { padding: 20px; }
            .security-card { padding: 18px; }
            .warning-card { padding: 18px; }
            .logo { max-width: 200px; }
            .social-links a { margin: 0 10px; font-size: 14px; }
            .content-grid { 
                grid-template-columns: 1fr; 
                gap: 15px; 
            }
            .message-card { grid-column: span 1; }
        }
        
        /* Small Mobile (480px and below) */
        @media (max-width: 480px) {
            .email-wrapper { padding: 10px 5px; }
            .email-container { width: 100%; }
            .header { padding: 30px 15px; }
            .content { padding: 30px 15px; }
            .footer { padding: 25px 15px; }
            .title { font-size: 24px; }
            .subtitle { font-size: 15px; }
            .cta-button { padding: 16px 28px; font-size: 15px; }
            .message-card { padding: 20px; }
            .backup-card { padding: 18px; }
            .logo { max-width: 170px; }
            .social-links a { margin: 0 6px; font-size: 13px; }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <div class="header">
                <img src="https://assets.monkeytilt.com/images/games/<EMAIL>?w=1920&q=50" alt="Monkey Tilt" class="logo">
            </div>
            
            <div class="content">
                <h1 class="title">Password Reset Request</h1>
                <p class="subtitle">Secure access to your gaming account</p>
                
                <div class="message-card">
                    <p class="message">Hello,</p>
                    <p class="message">You recently requested to reset your password for your Monkey Tilt account. Click the button below to create a new password and get back to the action!</p>
                </div>
                
                <div style="margin: 40px 0;"></div>
                
                <div class="security-card">
                    <p class="security-text">If you didn't request this reset, simply ignore this email.</p>
                </div>
                
                <div class="cta-section">
                    <a href="{{.ResetLink}}" class="cta-button">Reset My Password</a>
                </div>
                
                <div class="warning-card">
                    <p class="warning-text">This reset link expires in 1 hour for your security.</p>
                </div>
                
                <div class="backup-card">
                    <p class="backup-card-title">If the button above doesn't work, copy and paste this link:</p>
                    <div class="backup-url">{{.ResetLink}}</div>
                </div>
            </div>
            
            <div class="footer">
                <p class="footer-brand">Best regards,</p>
                <p class="footer-team">The Monkey Tilt Team</p>
                
                <div class="social-links">
                    <a href="https://twitter.com/MonkeyTiltPlay">Twitter</a>
                    <a href="https://discord.gg/monkeytilt">Discord</a>
                    <a href="https://monkeytilthelp.zendesk.com/hc/en-us/requests/new">Support</a>
                </div>
                
                <p class="copyright">© 2025 Monkey Tilt. All rights reserved.<br>This email was sent to {{.Email}}</p>
            </div>
        </div>
    </div>
</body>
</html> 