FROM golang:1.22 AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY ./cmd/events_load_test ./cmd/events_load_test

WORKDIR /app/cmd/events_load_test
RUN go build -o kafka-load-test main.go

FROM quay.io/centos/centos:stream9-minimal

COPY --from=builder /app/cmd/events_load_test/kafka-load-test /usr/local/bin/kafka-load-test

COPY --from=builder /app/cmd/events_load_test/data /app/data

ENV TOPICS_TO_TEST=""
ENV INTERVAL_TPS=""
ENV INTERVAL_DURATIONS=""
ENV BOOTSTRAP_SERVERS=""

ENTRYPOINT ["/usr/local/bin/kafka-load-test"]

CMD []