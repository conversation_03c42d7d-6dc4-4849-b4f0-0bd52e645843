with-expecter: True

dir: "internal/mocks/{{.PackageName}}mock"
filename: "{{.InterfaceNameSnake}}.go"
mockname: "{{.InterfaceName}}"
outpkg: "{{.PackageName}}mock"
inpackage: False
keeptree: False

packages:
  github.com/Monkey-Tilt/monketilt/Golang/community/internal/api:
    interfaces:
      AuthMiddleware:
  github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain:
    interfaces:
      AuthenticationService:
      BetRepository:
      BetService:
      BoostRepository:
      GameProvider:
      GameRepository:
      GameService:
      TransactionRepository:
      UserConfigAssetRepository:
      UserConfigAssetsService:
      UserRepository:
      UserService:
      VIPTiersService:
      VIPUserBalanceRepository:
      UserBonusRepository:
      UserBonusService:
      BonusQueueService:
      UserBonusQueue:
      SlackAlertClient:
      UserWageringRepository:
      BonusRepository:
      ElantilWageringClient:
      DirectusCMSClient:
      BonusTemplateRepository:
      BonusTemplateService:
      SettingsRepository:
      SettingsService:
      BonusDropRepository:
      BonusDropService:
  github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client:
    interfaces:
      HTTPClient:
  github.com/Monkey-Tilt/monketilt/Golang/community/internal/greco:
    interfaces:
      Producer:
      GameVendorProvider:
  github.com/Monkey-Tilt/monketilt/Golang/community/internal/websocket:
    interfaces:
      WebSocketConn: