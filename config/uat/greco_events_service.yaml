enabled: false
log_level: info

postgres:
  user: community
  host: community-v2-rds.uat.monkeytilt.codes
  port: 5432
  database: communityv2

kafka_consumer:
  brokers: b-1.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092,b-2.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092,b-3.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092
  group_id: monkeytilt-uat-greco
  work_queue_size: 100

greco_kafka_producer:
  brokers: b-1-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196,b-2-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196
  username: monkeytilt-production-external
