address: :6969
log_level: info

postgres:
  user: community
  host: community-v2-rds.uat.monkeytilt.codes
  port: 5432
  database: communityv2

# keycloak secret client is same as wagering, if needed, can be replaced with it
keycloak:
  url: https://apps.p-prod.monkeytilt.biz/keycloak
  realm: monkeytilt
  client_id: web-monkeytilt
  tenant_id: monkeytilt

directus:
  url: https://cms.m-dev.monkeytilt.biz/items

wagering: 
  base_url: https://semlyr.d-prod.monkeytilt.biz/cubejs-api/v1/load
  authentication_url: https://apps.p-prod.monkeytilt.biz/keycloak-bo/realms/d/protocol/openid-connect/token
  client_id: monkeytilt

cms_url: https://cms.monkeytilt.codes

wallet:
  authentication_url: https://apps.p-prod.monkeytilt.biz/keycloak-bo/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-prod.monkeytilt.biz/manual-adjustments/v1/profiles
  client_id: middlelayer
  elantil_url: https://api.p-prod.monkeytilt.biz 

email: 
  authentication_url: https://apps.p-prod.monkeytilt.biz/keycloak/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-prod.monkeytilt.biz
  realm: monkeytilt
  client_id: middleware-monkeytilt

sendgrid:

rails_client: 
  base_url: https://core.c-dev.monkeytilt.biz

swagger: