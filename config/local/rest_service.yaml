address: :8080
log_level: debug
enable_auth_bypass: true

postgres:
  user: postgres
  host: postgres
  port: 5432
  database: postgres

keycloak:
  url: https://apps.p-dev.monkeytilt.codes/keycloak
  realm: monkeytilt
  client_id: web-monkeytilt
  tenant_id: monkeytilt
  client_secret: t8CDPejjCQx6RWKMrMlFMl4DH3P17E0e

directus:
  url: https://cms.m-dev.monkeytilt.biz/items
  api_key: sirvIpytlHTwjslP_vNjFLr61pG5xlIQ

wagering:
  base_url: https://semlyr.d-dev-mt.monkeytilt.codes/cubejs-api/v1/load
  authentication_url: https://apps.p-dev.monkeytilt.codes/keycloak-bo/realms/d/protocol/openid-connect/token
  client_id: monkeytilt
  client_secret: t8CDPejjCQx6RWKMrMlFMl4DH3P17E0e

cms_url: https://cms.m-dev.monkeytilt.biz

# wallet is sent using keycloak-bo
# this is used to retrieve tags
wallet:
  authentication_url: https://apps.p-dev.monkeytilt.codes/keycloak-bo/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-dev.monkeytilt.codes/manual-adjustments/v1/profiles
  client_secret: Xj2R68zXO1sSWtKucVj4CL0VVfmmBfXT
  client_id: middlelayer
  elantil_url: https://api.p-dev.monkeytilt.codes

# email is sent using keycloak
email:
  authentication_url: https://apps.p-dev.monkeytilt.codes/keycloak/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-dev.monkeytilt.codes
  realm: monkeytilt
  client_id: middleware-monkeytilt
  client_secret: 7yiWPt9DH9vpM0WwYE6JP8GzDfmmsDOG

sendgrid:
  sendgrid_api_key: *********************************************************************

rails_client:
  base_url: https://core.c-dev.monkeytilt.biz

swagger:
  username: admin
  password: password

slack:
  token: *******************************************
  channel_id: C010A0A0A0

AWS:
  bucketName: alphabasket
  Region: us-east-1

optimove:
  key: 952ac24de504c5b0ed9a30c4fa2c8a35c2f00c36046205113fc3 