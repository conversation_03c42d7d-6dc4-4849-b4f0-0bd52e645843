enabled: false
log_level: debug

postgres:
  user: postgres
  host: localhost
  port: 5432
  database: mt-community

kafka_consumer:
  # This could be kafka:9092 (when running app from compose) or localhost:29092 (when running app with docker run)
  brokers: kafka:9092
  group_id: monkeytilt-local
  work_queue_size: 50

greco_kafka_producer:
  brokers: b-1-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196,b-2-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196
  username: monkeytilt-production-external
