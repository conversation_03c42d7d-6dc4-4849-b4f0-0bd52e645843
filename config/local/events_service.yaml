address: :8080
log_level: debug

postgres:
  user: postgres
  # This could be postgres (when running app from compose) or localhost (when running app with docker run)
  host: postgres
  port: 5432
  database: postgres

kafka_consumer:
  # This could be kafka:9092 (when running app from compose) or localhost:29092 (when running app with docker run)
  brokers: kafka:9092
  group_id: monkeytilt-local
  work_queue_size: 50

bet_simulation:
  enabled: true
  new_bet_interval_range: [1000, 5000]  # milliseconds (max 5s)
  high_payout_threshold: 500.0
  high_payout_probability: 0.1
  vip_tiers_file: ../vip_tiers.csv
  usernames_file: ../slated_usernames.csv
  games_file: ../games_local_staging.csv
  active_user_queue_size: 15