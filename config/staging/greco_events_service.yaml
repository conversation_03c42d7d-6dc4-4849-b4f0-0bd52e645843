enabled: false
log_level: debug

postgres:
  user: community
  host: community-rds.staging.monkeytilt.codes
  port: 5432
  database: community-v2

kafka_consumer:
  brokers: b-1.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092,b-2.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092,b-3.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092
  group_id: monkeytilt-staging-greco
  work_queue_size: 50

greco_kafka_producer:
  brokers: b-1-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196,b-2-public.monkeytiltproduction.wkzvax.c3.kafka.eu-west-1.amazonaws.com:9196
  username: monkeytilt-production-external
