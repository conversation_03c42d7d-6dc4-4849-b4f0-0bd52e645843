address: :6969
log_level: debug
enable_auth_bypass: true

postgres:
  user: community
  host: community-rds.staging.monkeytilt.codes
  port: 5432
  database: community-v2

keycloak:
  url: https://apps.p-dev.monkeytilt.codes/keycloak
  realm: monkeytilt
  client_id: web-monkeytilt
  tenant_id: monkeytilt

directus:
  url: https://cms.m-dev.monkeytilt.biz/items

wagering:
  base_url: https://semlyr.d-dev-mt.monkeytilt.codes/cubejs-api/v1/load
  authentication_url: https://keycloak.central-dev.monkeytilt.codes/realms/d/protocol/openid-connect/token
  client_id: monkeytilt

cms_url: https://cms.m-dev.monkeytilt.biz
cms_games_url: https://cms.m-dev.monkeytilt.biz/items/

# wallet is sent using keycloak-bo
# this is used to retrieve tags
wallet:
  authentication_url: https://apps.p-dev.monkeytilt.codes/keycloak-bo/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-dev.monkeytilt.codes/manual-adjustments/v1/profiles
  client_id: middlelayer
  elantil_url: https://api.p-dev.monkeytilt.codes

# email is sent using keycloak
email:
  authentication_url: https://apps.p-dev.monkeytilt.codes/keycloak/realms/monkeytilt/protocol/openid-connect/token
  base_url: https://api.p-dev.monkeytilt.codes
  realm: monkeytilt
  client_id: middleware-monkeytilt

sendgrid:

rails_client:
  base_url: http://crypto-payments-core.mt-crypto:8080
  # base_url: https://core.c-dev.monkeytilt.biz

optimove:
  key: 952ac24de504c5b0ed9a30c4fa2c8a35c2f00c36046205113fc3 

swagger:
