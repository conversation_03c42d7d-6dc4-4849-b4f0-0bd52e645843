address: :6969
log_level: debug

postgres:
  user: community
  host: community-rds.staging.monkeytilt.codes
  port: 5432
  database: community-v2

kafka_consumer:
  brokers: b-1.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092,b-2.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092,b-3.pdev.pw05yt.c5.kafka.eu-central-1.amazonaws.com:9092
  group_id: monkeytilt-staging
  work_queue_size: 50

bet_simulation:
  enabled: true
  new_bet_interval_range: [1000, 5000]  # milliseconds (max 5s)
  high_payout_threshold: 500.0
  high_payout_probability: 0.1
  vip_tiers_file: ../vip_tiers.csv
  usernames_file: ../slated_usernames.csv
  games_file: ../games_local_staging.csv
  active_user_queue_size: 15
