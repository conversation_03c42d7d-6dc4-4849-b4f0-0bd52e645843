address: :6969
log_level: debug

postgres:
  user: community
  host: community-v2-rds.prod.monkeytilt.codes
  port: 5432
  database: communityv2

kafka_consumer:
  brokers: b-1.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092,b-2.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092,b-3.pprod.xqgto0.c5.kafka.eu-central-1.amazonaws.com:9092
  group_id: monkeytilt-prod
  work_queue_size: 100

bet_simulation:
  enabled: true
  new_bet_interval_range: [1000, 5000]  # milliseconds (max 5s)
  high_payout_threshold: 500.0
  high_payout_probability: 0.1
  vip_tiers_file: ../vip_tiers.csv
  usernames_file: ../slated_usernames.csv
  games_file: ../games_production.csv
  active_user_queue_size: 15