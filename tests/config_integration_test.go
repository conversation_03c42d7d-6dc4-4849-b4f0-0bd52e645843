package tests

import (
	"encoding/csv"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func TestLoadGamesFromCSV_RealConfigFiles(t *testing.T) {
	// Define environments to test
	environments := []string{"local", "production", "staging", "uat"}

	// Get the project root directory
	projectRoot := utils.FindProjectRoot("config")

	// Test each environment's games file
	for _, env := range environments {
		t.Run(env, func(t *testing.T) {
			// Set up config directory for this environment
			configDir := filepath.Join(projectRoot, "config", env)

			// Read the actual YAML config file to get games_file value
			v := viper.New()
			v.AddConfigPath(configDir)
			v.SetConfigName("events_service")
			v.SetConfigType("yaml")

			err := v.ReadInConfig()
			require.NoError(t, err, "Failed to read events_service.yaml for environment %s", env)

			// Extract the games_file value from the YAML
			gamesFile := v.GetString("bet_simulation.games_file")
			require.NotEmpty(t, gamesFile, "games_file not found in events_service.yaml for environment %s", env)

			// Create the full file path for validation
			fullPath := gamesFile
			if !filepath.IsAbs(fullPath) {
				fullPath = filepath.Join(configDir, gamesFile)
			}

			// Verify the file exists
			require.FileExists(t, fullPath, "Games CSV file %s does not exist for environment %s", gamesFile, env)

			// Since loadGamesFromCSV is not exported, we'll validate the file can be opened and parsed
			// by reading it directly to ensure it has valid content
			csvFile, err := os.Open(fullPath)
			require.NoError(t, err, "Failed to open games file %s for environment %s", gamesFile, env)
			defer csvFile.Close()

			reader := csv.NewReader(csvFile)
			records, err := reader.ReadAll()
			require.NoError(t, err, "Failed to parse CSV file %s for environment %s", gamesFile, env)
			require.NotEmpty(t, records, "CSV file %s is empty for environment %s", gamesFile, env)

			// Verify CSV has proper structure (header + at least one data row)
			require.GreaterOrEqual(t, len(records), 2, "CSV file %s should have header + data rows for environment %s", gamesFile, env)

			// Verify header has expected columns (either 6 or 9 columns)
			header := records[0]
			require.True(t, len(header) == 6 || len(header) == 9,
				"CSV header should have 6 or 9 columns, got %d for %s in environment %s", len(header), gamesFile, env)

			// Verify each data row has consistent column count
			dataRowCount := 0
			for i, record := range records[1:] {
				if len(strings.TrimSpace(strings.Join(record, ""))) == 0 {
					continue // Skip empty lines
				}
				dataRowCount++
				require.True(t, len(record) == 6 || len(record) == 9,
					"CSV row %d should have 6 or 9 columns, got %d for %s in environment %s", i+2, len(record), gamesFile, env)

				// Verify required fields are not empty (columns 0-5 are required)
				require.NotEmpty(t, strings.TrimSpace(record[0]), "Game ID (column 1) is empty at row %d in %s", i+2, gamesFile)
				require.NotEmpty(t, strings.TrimSpace(record[1]), "CMS Game ID (column 2) is empty at row %d in %s", i+2, gamesFile)
				require.NotEmpty(t, strings.TrimSpace(record[3]), "Game Name (column 4) is empty at row %d in %s", i+2, gamesFile)

				// Verify CMS Game ID is a valid integer
				_, err := strconv.ParseInt(strings.TrimSpace(record[1]), 10, 64)
				require.NoError(t, err, "CMS Game ID '%s' is not a valid integer at row %d in %s", record[1], i+2, gamesFile)
			}

			require.Greater(t, dataRowCount, 0, "No valid data rows found in %s for environment %s", gamesFile, env)

			t.Logf("Successfully validated %d games in %s (from %s/events_service.yaml) for environment %s",
				dataRowCount, gamesFile, env, env)
		})
	}
}
