package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// TestBetFeedOptimizationIntegration demonstrates the key optimization changes
func TestBetFeedOptimizationIntegration(t *testing.T) {
	t.Run("PaginationOptimization", func(t *testing.T) {
		// Before: Bets used PagedItems[Bet] with TotalCount
		// After: Bets uses PagedItemsWithoutTotalCount[Bet] without TotalCount

		bets := domain.Bets{
			Items: []domain.Bet{
				{ExternalID: "bet1", RoundStatus: "completed"},
				{ExternalID: "bet2", RoundStatus: "completed"},
			},
			Paging: domain.PagingWithoutTotalCount{
				CurrentPage: 1,
				PageSize:    10,
			},
		}

		// Verify the optimization: no TotalCount field
		assert.Len(t, bets.Items, 2)
		assert.Equal(t, 1, bets.Paging.CurrentPage)
		assert.Equal(t, 10, bets.Paging.PageSize)

		// Verify TotalCount field doesn't exist
		_, hasTotalCount := interface{}(bets.Paging).(struct{ TotalCount int64 })
		assert.False(t, hasTotalCount, "Optimization successful: no TotalCount field")
	})

	t.Run("TypeCompatibility", func(t *testing.T) {
		// Verify that Bets is compatible with PagedItemsWithoutTotalCount
		bets := domain.Bets{
			Items:  []domain.Bet{{ExternalID: "test"}},
			Paging: domain.PagingWithoutTotalCount{CurrentPage: 1, PageSize: 5},
		}

		// Should be convertible
		pagedItems := domain.PagedItemsWithoutTotalCount[domain.Bet](bets)
		assert.Equal(t, bets.Items, pagedItems.Items)
		assert.Equal(t, bets.Paging, pagedItems.Paging)
	})

	t.Run("BackwardCompatibility", func(t *testing.T) {
		// Old Paging type should still exist for gradual migration
		oldPaging := domain.Paging{
			TotalCount:  100,
			CurrentPage: 1,
			PageSize:    10,
		}

		newPaging := domain.PagingWithoutTotalCount{
			CurrentPage: 1,
			PageSize:    10,
		}

		// Both should work
		assert.Equal(t, int64(100), oldPaging.TotalCount)
		assert.Equal(t, 1, oldPaging.CurrentPage)
		assert.Equal(t, 1, newPaging.CurrentPage)

		// But they should be different types
		assert.IsType(t, domain.Paging{}, oldPaging)
		assert.IsType(t, domain.PagingWithoutTotalCount{}, newPaging)
	})
}

// TestOptimizationBenefits documents the performance benefits
func TestOptimizationBenefits(t *testing.T) {
	t.Run("PerformanceBenefit", func(t *testing.T) {
		// This test documents the performance benefit conceptually

		// Before optimization:
		// 1. SELECT COUNT(*) FROM bets WHERE conditions... (expensive)
		// 2. SELECT * FROM bets WHERE conditions... LIMIT 10 OFFSET 0

		// After optimization:
		// 1. SELECT * FROM bets WHERE conditions... ORDER BY time DESC LIMIT 10 OFFSET 0
		//    (with optimized indexes: idx_bets_optimal_casino_query, etc.)

		// The new approach eliminates the expensive COUNT(*) operation
		// and relies on targeted indexes for performance

		bets := domain.Bets{
			Items:  []domain.Bet{{ExternalID: "optimized"}},
			Paging: domain.PagingWithoutTotalCount{CurrentPage: 1, PageSize: 10},
		}

		// Verify no total count is needed or available
		assert.NotEmpty(t, bets.Items)
		assert.Equal(t, 1, bets.Paging.CurrentPage)

		// The absence of TotalCount represents the optimization
		_, hasTotalCount := interface{}(bets.Paging).(struct{ TotalCount int64 })
		assert.False(t, hasTotalCount, "Performance optimization: no expensive COUNT(*) needed")
	})
}
