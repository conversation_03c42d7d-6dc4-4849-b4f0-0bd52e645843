{"schema": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "gameId"}, {"type": "boolean", "optional": true, "field": "isEnabled"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "country"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item_countries_array_item"}, "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item_countries_array", "field": "countries"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item", "field": "item"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item"}, "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array", "field": "conditions"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "boolean", "optional": true, "field": "isDemoAllowed"}, {"type": "string", "optional": true, "field": "gameExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}, {"type": "string", "optional": true, "field": "reportingGameCategory"}, {"type": "string", "optional": true, "field": "jurisdictionGameCategory"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game", "field": "game"}], "optional": true, "name": "singularity.previous_data_casinoData_config", "field": "config"}], "optional": true, "name": "singularity.previous_data_casinoData", "field": "casinoData"}], "optional": true, "name": "singularity.previous_data", "field": "data"}, {"type": "string", "optional": true, "field": "type"}, {"type": "string", "optional": true, "field": "amount"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}, {"type": "string", "optional": true, "field": "walletId"}], "optional": true, "name": "singularity.previous_ratios_array_item"}, "optional": true, "name": "singularity.previous_ratios_array", "field": "ratios"}, {"type": "string", "optional": true, "field": "status"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "tenantId"}, {"type": "string", "optional": true, "field": "ipToCountry"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}], "optional": true, "name": "singularity.previous_baggage", "field": "baggage"}, {"type": "string", "optional": true, "field": "ownerId"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "balanceAmount"}, {"type": "string", "optional": true, "field": "balanceBeforeAmount"}, {"type": "string", "optional": true, "field": "balanceDifferenceAmount"}, {"type": "string", "optional": true, "field": "walletId"}, {"type": "string", "optional": true, "field": "walletType"}], "optional": true, "name": "singularity.previous_balances_array_item"}, "optional": true, "name": "singularity.previous_balances_array", "field": "balances"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_metadata", "field": "metadata"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_statuses_array_item_metadata", "field": "metadata"}, {"type": "string", "optional": true, "field": "status"}], "optional": true, "name": "singularity.previous_statuses_array_item"}, "optional": true, "name": "singularity.previous_statuses_array", "field": "statuses"}, {"type": "string", "optional": true, "field": "createdOn"}, {"type": "string", "optional": true, "field": "ownerType"}, {"type": "string", "optional": true, "field": "productId"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array_item"}, "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array", "field": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}], "optional": true, "name": "singularity.previous_aggregates_relativeAggregates", "field": "relativeAggregates"}], "optional": true, "name": "singularity.previous_aggregates", "field": "aggregates"}, {"type": "string", "optional": true, "field": "externalId"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "string", "optional": true, "field": "description"}, {"type": "struct", "fields": [{"type": "boolean", "optional": true, "field": "canBeAuthorized"}], "optional": true, "name": "singularity.previous_capabilities", "field": "capabilities"}, {"type": "string", "optional": true, "field": "currencyCode"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_riskProfiles", "field": "riskProfiles"}, {"type": "string", "optional": true, "field": "lastUpdatedOn"}, {"type": "string", "optional": true, "field": "batchExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}], "optional": true, "name": "singularity.previous", "field": "previous"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "gameId"}, {"type": "boolean", "optional": true, "field": "isEnabled"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "country"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item_countries_array_item"}, "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item_countries_array", "field": "countries"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item", "field": "item"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item"}, "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array", "field": "conditions"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "boolean", "optional": true, "field": "isDemoAllowed"}, {"type": "string", "optional": true, "field": "gameExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}, {"type": "string", "optional": true, "field": "reportingGameCategory"}, {"type": "string", "optional": true, "field": "jurisdictionGameCategory"}], "optional": true, "name": "singularity.current_data_casinoData_config_game", "field": "game"}], "optional": true, "name": "singularity.current_data_casinoData_config", "field": "config"}], "optional": true, "name": "singularity.current_data_casinoData", "field": "casinoData"}], "optional": true, "name": "singularity.current_data", "field": "data"}, {"type": "string", "optional": true, "field": "type"}, {"type": "string", "optional": true, "field": "amount"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}, {"type": "string", "optional": true, "field": "walletId"}], "optional": true, "name": "singularity.current_ratios_array_item"}, "optional": true, "name": "singularity.current_ratios_array", "field": "ratios"}, {"type": "string", "optional": true, "field": "status"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "tenantId"}, {"type": "string", "optional": true, "field": "ipToCountry"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}], "optional": true, "name": "singularity.current_baggage", "field": "baggage"}, {"type": "string", "optional": true, "field": "ownerId"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "balanceAmount"}, {"type": "string", "optional": true, "field": "balanceBeforeAmount"}, {"type": "string", "optional": true, "field": "balanceDifferenceAmount"}, {"type": "string", "optional": true, "field": "walletId"}, {"type": "string", "optional": true, "field": "walletType"}], "optional": true, "name": "singularity.current_balances_array_item"}, "optional": true, "name": "singularity.current_balances_array", "field": "balances"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.current_metadata", "field": "metadata"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [], "optional": true, "name": "singularity.current_statuses_array_item_metadata", "field": "metadata"}, {"type": "string", "optional": true, "field": "status"}], "optional": true, "name": "singularity.current_statuses_array_item"}, "optional": true, "name": "singularity.current_statuses_array", "field": "statuses"}, {"type": "string", "optional": true, "field": "createdOn"}, {"type": "string", "optional": true, "field": "ownerType"}, {"type": "string", "optional": true, "field": "productId"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.current_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array_item"}, "optional": true, "name": "singularity.current_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array", "field": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}], "optional": true, "name": "singularity.current_aggregates_relativeAggregates", "field": "relativeAggregates"}], "optional": true, "name": "singularity.current_aggregates", "field": "aggregates"}, {"type": "string", "optional": true, "field": "externalId"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "string", "optional": true, "field": "description"}, {"type": "struct", "fields": [{"type": "boolean", "optional": true, "field": "canBeAuthorized"}], "optional": true, "name": "singularity.current_capabilities", "field": "capabilities"}, {"type": "string", "optional": true, "field": "currencyCode"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "name"}, {"type": "int32", "optional": true, "field": "level"}], "optional": true, "name": "singularity.current_riskProfiles_transactionAmountRiskLevel", "field": "transactionAmountRiskLevel"}], "optional": true, "name": "singularity.current_riskProfiles", "field": "riskProfiles"}, {"type": "string", "optional": true, "field": "lastUpdatedOn"}, {"type": "string", "optional": true, "field": "batchExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}], "optional": true, "name": "singularity.current", "field": "current"}], "optional": false, "name": "wallets.public.orleans.Envelope"}, "payload": {"previous": {"id": "45cdd4e6-1c83-5b3a-a889-138cdd55f72a", "data": {"casinoData": {"config": {"id": "3f9353db-79df-49d4-83a9-77bc2012dbaf", "game": {"gameId": "1024", "isEnabled": true, "conditions": [{"item": {"countries": [{"country": "MT"}, {"country": "ES"}, {"country": "DE"}]}}], "providerId": "swintt", "isDemoAllowed": true, "gameExternalId": "TipsyCharms", "providerNamespace": "integrations", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc", "reportingGameCategory": "", "jurisdictionGameCategory": ""}}}}, "type": "credit", "amount": "0.2", "ratios": [{"denominator": "1", "numerator": "1", "walletId": "00000000-0000-0000-0000-000000000000"}], "status": "completed", "baggage": {"tenantId": "ka<PERSON><PERSON>s", "ipToCountry": "MT", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc"}, "ownerId": "2fe32bb7-ca8a-4847-9238-9adbbe648b34", "balances": [{"balanceAmount": "455.4", "balanceBeforeAmount": "455.2", "balanceDifferenceAmount": "0.2", "walletId": "00000000-0000-0000-0000-000000000000", "walletType": "main"}], "metadata": {}, "statuses": [{"metadata": {}, "status": "validated"}, {"metadata": {}, "status": "authorizationAccepted"}, {"metadata": {}, "status": "submitted"}, {"metadata": {}, "status": "completed"}], "createdOn": "2024-08-14T13:59:30.6989102Z", "ownerType": "profiles", "productId": "casino", "aggregates": {"relativeAggregates": {"e8b9ee3f-1cd1-524e-9842-19068ac36160": [{"aggregateId": "bonus-closed-rounds(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}]}}, "externalId": "9125a9b0ab944acd6a6336869fe9912e", "providerId": "swintt", "description": "TipsyCharms", "capabilities": {"canBeAuthorized": false}, "currencyCode": "EUR", "riskProfiles": {}, "lastUpdatedOn": "2024-08-14T13:59:30.7535863Z", "batchExternalId": "580c0caff6264eaeadc2761288a170ff", "providerNamespace": "integrations"}, "current": {"id": "45cdd4e6-1c83-5b3a-a889-138cdd55f72a", "data": {"casinoData": {"config": {"id": "3f9353db-79df-49d4-83a9-77bc2012dbaf", "game": {"gameId": "1024", "isEnabled": true, "conditions": [{"item": {"countries": [{"country": "MT"}, {"country": "ES"}, {"country": "DE"}]}}], "providerId": "swintt", "isDemoAllowed": true, "gameExternalId": "TipsyCharms", "providerNamespace": "integrations", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc", "reportingGameCategory": "", "jurisdictionGameCategory": ""}}}}, "type": "credit", "amount": "0.2", "ratios": [{"denominator": "1", "numerator": "1", "walletId": "00000000-0000-0000-0000-000000000000"}], "status": "completed", "baggage": {"tenantId": "ka<PERSON><PERSON>s", "ipToCountry": "MT", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc"}, "ownerId": "2fe32bb7-ca8a-4847-9238-9adbbe648b34", "balances": [{"balanceAmount": "455.4", "balanceBeforeAmount": "455.2", "balanceDifferenceAmount": "0.2", "walletId": "00000000-0000-0000-0000-000000000000", "walletType": "main"}], "metadata": {}, "statuses": [{"metadata": {}, "status": "validated"}, {"metadata": {}, "status": "authorizationAccepted"}, {"metadata": {}, "status": "submitted"}, {"metadata": {}, "status": "completed"}], "createdOn": "2024-08-14T13:59:30.6989102Z", "ownerType": "profiles", "productId": "casino", "aggregates": {"relativeAggregates": {"e8b9ee3f-1cd1-524e-9842-19068ac36160": [{"aggregateId": "bonus-closed-rounds(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}]}}, "externalId": "9125a9b0ab944acd6a6336869fe9912e", "providerId": "swintt", "description": "TipsyCharms", "capabilities": {"canBeAuthorized": false}, "currencyCode": "EUR", "riskProfiles": {"transactionAmountRiskLevel": {"name": "Transaction Amount Risk Level", "level": 1}}, "lastUpdatedOn": "2024-08-14T13:59:31.1897424Z", "batchExternalId": "580c0caff6264eaeadc2761288a170ff", "providerNamespace": "integrations"}}}