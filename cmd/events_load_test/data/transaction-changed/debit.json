{"schema": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "gameId"}, {"type": "boolean", "optional": true, "field": "isEnabled"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "country"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item_countries_array_item"}, "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item_countries_array", "field": "countries"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item_item", "field": "item"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array_item"}, "optional": true, "name": "singularity.previous_data_casinoData_config_game_conditions_array", "field": "conditions"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "boolean", "optional": true, "field": "isDemoAllowed"}, {"type": "string", "optional": true, "field": "gameExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}, {"type": "string", "optional": true, "field": "reportingGameCategory"}, {"type": "string", "optional": true, "field": "jurisdictionGameCategory"}], "optional": true, "name": "singularity.previous_data_casinoData_config_game", "field": "game"}], "optional": true, "name": "singularity.previous_data_casinoData_config", "field": "config"}], "optional": true, "name": "singularity.previous_data_casinoData", "field": "casinoData"}], "optional": true, "name": "singularity.previous_data", "field": "data"}, {"type": "string", "optional": true, "field": "type"}, {"type": "string", "optional": true, "field": "amount"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}, {"type": "string", "optional": true, "field": "walletId"}], "optional": true, "name": "singularity.previous_ratios_array_item"}, "optional": true, "name": "singularity.previous_ratios_array", "field": "ratios"}, {"type": "string", "optional": true, "field": "status"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "tenantId"}, {"type": "string", "optional": true, "field": "ipToCountry"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}], "optional": true, "name": "singularity.previous_baggage", "field": "baggage"}, {"type": "string", "optional": true, "field": "ownerId"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "balanceAmount"}, {"type": "string", "optional": true, "field": "balanceBeforeAmount"}, {"type": "string", "optional": true, "field": "balanceDifferenceAmount"}, {"type": "string", "optional": true, "field": "walletId"}, {"type": "string", "optional": true, "field": "walletType"}], "optional": true, "name": "singularity.previous_balances_array_item"}, "optional": true, "name": "singularity.previous_balances_array", "field": "balances"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_metadata", "field": "metadata"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_statuses_array_item_metadata", "field": "metadata"}, {"type": "string", "optional": true, "field": "status"}], "optional": true, "name": "singularity.previous_statuses_array_item"}, "optional": true, "name": "singularity.previous_statuses_array", "field": "statuses"}, {"type": "string", "optional": true, "field": "createdOn"}, {"type": "string", "optional": true, "field": "ownerType"}, {"type": "string", "optional": true, "field": "productId"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_00000000-0000-0000-0000-000000000000_array_item"}, "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_00000000-0000-0000-0000-000000000000_array", "field": "00000000-0000-0000-0000-000000000000"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array_item"}, "optional": true, "name": "singularity.previous_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array", "field": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}], "optional": true, "name": "singularity.previous_aggregates_relativeAggregates", "field": "relativeAggregates"}], "optional": true, "name": "singularity.previous_aggregates", "field": "aggregates"}, {"type": "string", "optional": true, "field": "externalId"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "string", "optional": true, "field": "description"}, {"type": "struct", "fields": [{"type": "boolean", "optional": true, "field": "canBeAuthorized"}], "optional": true, "name": "singularity.previous_capabilities", "field": "capabilities"}, {"type": "string", "optional": true, "field": "currencyCode"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.previous_riskProfiles", "field": "riskProfiles"}, {"type": "string", "optional": true, "field": "lastUpdatedOn"}, {"type": "string", "optional": true, "field": "batchExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}], "optional": true, "name": "singularity.previous", "field": "previous"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "string", "optional": true, "field": "id"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "gameId"}, {"type": "boolean", "optional": true, "field": "isEnabled"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "country"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item_countries_array_item"}, "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item_countries_array", "field": "countries"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item_item", "field": "item"}], "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array_item"}, "optional": true, "name": "singularity.current_data_casinoData_config_game_conditions_array", "field": "conditions"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "boolean", "optional": true, "field": "isDemoAllowed"}, {"type": "string", "optional": true, "field": "gameExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}, {"type": "string", "optional": true, "field": "reportingGameCategory"}, {"type": "string", "optional": true, "field": "jurisdictionGameCategory"}], "optional": true, "name": "singularity.current_data_casinoData_config_game", "field": "game"}], "optional": true, "name": "singularity.current_data_casinoData_config", "field": "config"}], "optional": true, "name": "singularity.current_data_casinoData", "field": "casinoData"}], "optional": true, "name": "singularity.current_data", "field": "data"}, {"type": "string", "optional": true, "field": "type"}, {"type": "string", "optional": true, "field": "amount"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}, {"type": "string", "optional": true, "field": "walletId"}], "optional": true, "name": "singularity.current_ratios_array_item"}, "optional": true, "name": "singularity.current_ratios_array", "field": "ratios"}, {"type": "string", "optional": true, "field": "status"}, {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "tenantId"}, {"type": "string", "optional": true, "field": "ipToCountry"}, {"type": "string", "optional": true, "field": "tenantIntegrationId"}], "optional": true, "name": "singularity.current_baggage", "field": "baggage"}, {"type": "string", "optional": true, "field": "ownerId"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "balanceAmount"}, {"type": "string", "optional": true, "field": "balanceBeforeAmount"}, {"type": "string", "optional": true, "field": "balanceDifferenceAmount"}, {"type": "string", "optional": true, "field": "walletId"}, {"type": "string", "optional": true, "field": "walletType"}], "optional": true, "name": "singularity.current_balances_array_item"}, "optional": true, "name": "singularity.current_balances_array", "field": "balances"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.current_metadata", "field": "metadata"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "struct", "fields": [], "optional": true, "name": "singularity.current_statuses_array_item_metadata", "field": "metadata"}, {"type": "string", "optional": true, "field": "status"}], "optional": true, "name": "singularity.current_statuses_array_item"}, "optional": true, "name": "singularity.current_statuses_array", "field": "statuses"}, {"type": "string", "optional": true, "field": "createdOn"}, {"type": "string", "optional": true, "field": "ownerType"}, {"type": "string", "optional": true, "field": "productId"}, {"type": "struct", "fields": [{"type": "struct", "fields": [{"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.current_aggregates_relativeAggregates_00000000-0000-0000-0000-000000000000_array_item"}, "optional": true, "name": "singularity.current_aggregates_relativeAggregates_00000000-0000-0000-0000-000000000000_array", "field": "00000000-0000-0000-0000-000000000000"}, {"type": "array", "items": {"type": "struct", "fields": [{"type": "string", "optional": true, "field": "aggregateId"}, {"type": "boolean", "optional": true, "field": "canCreate"}, {"type": "boolean", "optional": true, "field": "canTriggerMarkerLimit"}, {"type": "string", "optional": true, "field": "denominator"}, {"type": "string", "optional": true, "field": "numerator"}], "optional": true, "name": "singularity.current_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array_item"}, "optional": true, "name": "singularity.current_aggregates_relativeAggregates_e8b9ee3f-1cd1-524e-9842-19068ac36160_array", "field": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}], "optional": true, "name": "singularity.current_aggregates_relativeAggregates", "field": "relativeAggregates"}], "optional": true, "name": "singularity.current_aggregates", "field": "aggregates"}, {"type": "string", "optional": true, "field": "externalId"}, {"type": "string", "optional": true, "field": "providerId"}, {"type": "string", "optional": true, "field": "description"}, {"type": "struct", "fields": [{"type": "boolean", "optional": true, "field": "canBeAuthorized"}], "optional": true, "name": "singularity.current_capabilities", "field": "capabilities"}, {"type": "string", "optional": true, "field": "currencyCode"}, {"type": "struct", "fields": [], "optional": true, "name": "singularity.current_riskProfiles", "field": "riskProfiles"}, {"type": "string", "optional": true, "field": "lastUpdatedOn"}, {"type": "string", "optional": true, "field": "batchExternalId"}, {"type": "string", "optional": true, "field": "providerNamespace"}], "optional": true, "name": "singularity.current", "field": "current"}], "optional": false, "name": "wallets.public.orleans.Envelope"}, "payload": {"previous": {"id": "{{UUID}}", "data": {"casinoData": {"config": {"id": "3f9353db-79df-49d4-83a9-77bc2012dbaf", "game": {"gameId": "1024", "isEnabled": true, "conditions": [{"item": {"countries": [{"country": "MT"}, {"country": "ES"}, {"country": "DE"}]}}], "providerId": "swintt", "isDemoAllowed": true, "gameExternalId": "TipsyCharms", "providerNamespace": "integrations", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc", "reportingGameCategory": "", "jurisdictionGameCategory": ""}}}}, "type": "debit", "amount": "1", "ratios": [{"denominator": "1", "numerator": "1", "walletId": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}, {"denominator": "1", "numerator": "1", "walletId": "00000000-0000-0000-0000-000000000000"}], "status": "validated", "baggage": {"tenantId": "ka<PERSON><PERSON>s", "ipToCountry": "MT", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc"}, "ownerId": "2fe32bb7-ca8a-4847-9238-9adbbe648b34", "balances": [{"balanceAmount": "455.2", "balanceBeforeAmount": "456.2", "balanceDifferenceAmount": "1", "walletId": "00000000-0000-0000-0000-000000000000", "walletType": "main"}], "metadata": {}, "statuses": [{"metadata": {}, "status": "validated"}], "createdOn": "2024-08-14T13:59:30.5313221Z", "ownerType": "profiles", "productId": "casino", "aggregates": {"relativeAggregates": {"00000000-0000-0000-0000-000000000000": [{"aggregateId": "casino-wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-wagering-contributions(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": false, "denominator": "1", "numerator": "1"}], "e8b9ee3f-1cd1-524e-9842-19068ac36160": [{"aggregateId": "casino-wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-wagering-contributions(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-open-rounds(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}]}}, "externalId": "0e067582f7dec523f39b2bbdf8f8635f", "providerId": "swintt", "description": "TipsyCharms", "capabilities": {"canBeAuthorized": false}, "currencyCode": "EUR", "riskProfiles": {}, "lastUpdatedOn": "2024-08-14T13:59:30.5313221Z", "batchExternalId": "580c0caff6264eaeadc2761288a170ff", "providerNamespace": "integrations"}, "current": {"id": "{{UUID}}", "data": {"casinoData": {"config": {"id": "3f9353db-79df-49d4-83a9-77bc2012dbaf", "game": {"gameId": "1024", "isEnabled": true, "conditions": [{"item": {"countries": [{"country": "MT"}, {"country": "ES"}, {"country": "DE"}]}}], "providerId": "swintt", "isDemoAllowed": true, "gameExternalId": "TipsyCharms", "providerNamespace": "integrations", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc", "reportingGameCategory": "", "jurisdictionGameCategory": ""}}}}, "type": "debit", "amount": "1", "ratios": [{"denominator": "1", "numerator": "1", "walletId": "e8b9ee3f-1cd1-524e-9842-19068ac36160"}, {"denominator": "1", "numerator": "1", "walletId": "00000000-0000-0000-0000-000000000000"}], "status": "completed", "baggage": {"tenantId": "ka<PERSON><PERSON>s", "ipToCountry": "MT", "tenantIntegrationId": "ccaad499-79d1-4cf3-9834-5163df06e1bc"}, "ownerId": "2fe32bb7-ca8a-4847-9238-9adbbe648b34", "balances": [{"balanceAmount": "455.2", "balanceBeforeAmount": "456.2", "balanceDifferenceAmount": "1", "walletId": "00000000-0000-0000-0000-000000000000", "walletType": "main"}], "metadata": {}, "statuses": [{"metadata": {}, "status": "validated"}, {"metadata": {}, "status": "authorizationAccepted"}, {"metadata": {}, "status": "submitted"}, {"metadata": {}, "status": "completed"}], "createdOn": "2024-08-14T13:59:30.5313221Z", "ownerType": "profiles", "productId": "casino", "aggregates": {"relativeAggregates": {"00000000-0000-0000-0000-000000000000": [{"aggregateId": "casino-wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-wagering-contributions(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": false, "denominator": "1", "numerator": "1"}], "e8b9ee3f-1cd1-524e-9842-19068ac36160": [{"aggregateId": "casino-wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "wagers", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-wagering-contributions(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}, {"aggregateId": "bonus-open-rounds(ownerBonusId=673bf5ec-3036-465a-8ed4-01d221c2d901)", "canCreate": false, "canTriggerMarkerLimit": true, "denominator": "1", "numerator": "1"}]}}, "externalId": "0e067582f7dec523f39b2bbdf8f8635f", "providerId": "swintt", "description": "TipsyCharms", "capabilities": {"canBeAuthorized": false}, "currencyCode": "EUR", "riskProfiles": {}, "lastUpdatedOn": "2024-08-14T13:59:30.5592742Z", "batchExternalId": "580c0caff6264eaeadc2761288a170ff", "providerNamespace": "integrations"}}}