# Kafka Load Testing Tool

This tool is designed to perform load testing on a Kafka cluster by sending a series of messages to specified topics. It allows to configure the rate of messages per second (TPS) and the duration for which these rates should be maintained, per topic. The payloads are read from JSON files, with placeholders replaced by unique IDs generated at runtime.

## Features

- Configurable topics, TPS rates, and durations through environment variables.
- Sequential processing of JSON files for each topic.
- Dynamic replacement of placeholders in JSON payloads with unique IDs.
- Controlled message sending rate (TPS) and duration for load testing.

## Setup

### Environment Variables

Ensure the following environment variables are set before running the tool:

- `BOOTSTRAP_SERVER`: A comma-separated list of bootstrap servers to produce the kafka messages there.
- `TOPICS_TO_TEST`: A comma-separated list of Kafka topics to test. Allowed topic names: transaction-changed, owner-sessions-changed. If more topics are needed to be added you might need to modify the events_load_test/main.go file adding those topics in the mapping from topics to files and also add new folder under events_load_test/data with the same topic name and the file(s) with the event payload to be sent to kafka.
- `INTERVAL_TPS`: A space-separated list of TPS values for each interval, with commas separating different topics.
- `INTERVAL_DURATION`: A space-separated list of durations for each interval, with commas separating different topics.

Example:
```bash
export BOOTSTRAP_SERVER="localhost:29092"
export TOPICS_TO_TEST="topic1,topic2"
export TPS="1000 2000 10000,3000 5000000"
export DURATION="5m 10m 20m,1m 7m"
```

### JSON Payload Files

Create JSON files for each topic with the payloads you want to send. Define placeholders in the JSON content that will be replaced by unique IDs. Example payload file (`payload1.json`):

```json
{
  "message": "Hello, {{UUID}}!"
}
```

#### File Order

If a topic has multiple JSON files, and these files need to be processed in a specific order, ensure that the order of files is maintained as specified in the `topicJSONFiles` map in the code. The tool will read and send messages from these files sequentially.

### Running the Tool

To run the load testing tool, execute the Go program:

```bash
go run cmd/events_load_test/main.go
```

### Code Description

1. **Configuration Parsing**: The tool reads and parses the `TOPICS_TO_TEST`, `TPS`, and `DURATION` environment variables to determine the topics, TPS rates, and durations for each interval.
   
2. **Payload Handling**: JSON payloads are read from specified files, with placeholders replaced by unique UUIDs. If a topic has multiple files, they are processed in the specified order.

3. **Load Testing Execution**: For each topic, messages are sent according to the specified TPS and duration intervals. The tool maintains the rate of sending messages and ensures that messages are sent in the order specified by the JSON files.

4. **Error Handling**: Logs are provided for any errors encountered during file reading, JSON processing, or Kafka message production.

## Example Configuration

For `topic1`, the tool will send messages at:
- 1000 TPS for 5 minutes
- 2000 TPS for 10 minutes
- 10000 TPS for 20 minutes

For `topic2`, the tool will send messages at:
- 3000 TPS for 1 minute
- 5000000 TPS for 7 minutes

