package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/google/uuid"
)

type LoadTestInterval struct {
	TPS      int
	Duration time.Duration
}

type MessagePayload struct {
	Content map[string]any
}

func readJSONFile(filePath string) (MessagePayload, error) {
	var payload MessagePayload
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return payload, fmt.Errorf("error reading JSON file: %s", err)
	}
	if err := json.Unmarshal(fileContent, &payload.Content); err != nil {
		return payload, fmt.Errorf("error unmarshaling JSON: %s", err)
	}
	return payload, nil
}

func replacePlaceholders(payload *MessagePayload, uniqueID string) {
	replaceUUIDInMap(payload.Content, uniqueID)
}

func replaceUUIDInMap(data map[string]interface{}, uniqueID string) {
	for key, value := range data {
		switch v := value.(type) {
		case string:
			if strings.Contains(v, "{{UUID}}") {
				data[key] = strings.Replace(v, "{{UUID}}", uniqueID, -1)
			}
		case map[string]any:
			replaceUUIDInMap(v, uniqueID)
		case []any:
			replaceUUIDInSlice(v, uniqueID)
		}
	}
}

func replaceUUIDInSlice(data []interface{}, uniqueID string) {
	for i, value := range data {
		switch v := value.(type) {
		case string:
			if strings.Contains(v, "{{UUID}}") {
				data[i] = strings.Replace(v, "{{UUID}}", uniqueID, -1)
			}
		case map[string]any:
			replaceUUIDInMap(v, uniqueID)
		case []any:
			replaceUUIDInSlice(v, uniqueID)
		}
	}
}

func parseLoadTestConfig() (map[string][]LoadTestInterval, error) {
	topics := os.Getenv("TOPICS_TO_TEST")
	tps := os.Getenv("INTERVAL_TPS")
	durations := os.Getenv("INTERVAL_DURATION")

	if topics == "" || tps == "" || durations == "" {
		return nil, errors.New("missing environment variables: ensure TOPICS_TO_TEST, INTERVAL_TPS, and INTERVAL_DURATION are set")
	}

	topicsList := strings.Split(topics, ",")
	tpsList := strings.Split(tps, ",")
	durationsList := strings.Split(durations, ",")

	if len(topicsList) != len(tpsList) || len(topicsList) != len(durationsList) {
		return nil, errors.New("mismatch in the number of topics, TPS configurations, or duration configurations")
	}

	loadTestConfig := make(map[string][]LoadTestInterval)

	for i, topic := range topicsList {
		tpsValues := strings.Split(tpsList[i], " ")
		durationValues := strings.Split(durationsList[i], " ")

		if len(tpsValues) != len(durationValues) {
			return nil, fmt.Errorf("mismatch in the number of TPS and durations for topic: %s", topic)
		}

		intervals := make([]LoadTestInterval, 0, len(tpsValues))
		for j, tpsStr := range tpsValues {
			tpsValue, err := strconv.Atoi(tpsStr)
			if err != nil {
				return nil, fmt.Errorf("invalid TPS value for topic %s: %s", topic, tpsStr)
			}

			durationValue, err := time.ParseDuration(durationValues[j])
			if err != nil {
				return nil, fmt.Errorf("invalid duration value for topic %s: %s", topic, durationValues[j])
			}

			intervals = append(intervals, LoadTestInterval{
				TPS:      tpsValue,
				Duration: durationValue,
			})
		}

		loadTestConfig[topic] = intervals
	}

	return loadTestConfig, nil
}

func runLoadTestForTopic(p *kafka.Producer, topic string, intervals []LoadTestInterval, jsonFiles []string) {
	for _, interval := range intervals {
		log.Printf("Running interval for topic %s: %d TPS for %v", topic, interval.TPS, interval.Duration)

		messagesToSend := interval.TPS * int(interval.Duration.Seconds())
		sleepTime := time.Second / time.Duration(interval.TPS)

		fileIndex := 0
		messageCount := 0

		var wg sync.WaitGroup
		// Semaphore to limit TPS
		sem := make(chan struct{}, interval.TPS)

		for i := 0; i < messagesToSend; i++ {
			// Get semaphore
			sem <- struct{}{}
			wg.Add(1)

			go func(fileIndex int) {
				defer wg.Done()
				// Free semaphore
				defer func() { <-sem }()

				jsonFile := jsonFiles[fileIndex]
				payload, err := readJSONFile(filepath.Join("data", topic, jsonFile))
				if err != nil {
					log.Printf("Error reading JSON file: %s", err)
					return
				}

				uniqueID := uuid.New().String()
				replacePlaceholders(&payload, uniqueID)

				payloadBytes, err := json.Marshal(payload.Content)
				if err != nil {
					log.Printf("Error marshaling payload: %s", err)
					return
				}

				msg := &kafka.Message{
					TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
					Value:          payloadBytes,
				}
				err = p.Produce(msg, nil)
				if err != nil {
					log.Printf("Error sending message: %s", err)
					return
				}

				messageCount++
			}(fileIndex)

			fileIndex = (fileIndex + 1) % len(jsonFiles)

			time.Sleep(sleepTime)
		}

		wg.Wait()

		log.Printf("Total messages sent to topic %s in interval: %d", topic, messageCount)
	}
}

func main() {
	bootstrapServers := os.Getenv("BOOTSTRAP_SERVERS")
	p, err := kafka.NewProducer(&kafka.ConfigMap{"bootstrap.servers": bootstrapServers})
	if err != nil {
		log.Printf("Error creating Kafka producer: %s", err)
		return
	}
	defer p.Close()

	loadTestConfig, err := parseLoadTestConfig()
	if err != nil {
		log.Printf("Error parsing load test configuration: %s", err)
		return
	}

	topicJSONFiles := map[string][]string{
		"transaction-changed":    {"debit.json", "credit.json"},
		"owner-sessions-changed": {"user.json"},	
	}

	for topic, intervals := range loadTestConfig {
		jsonFiles, exists := topicJSONFiles[topic]
		if !exists {
			log.Printf("No JSON files configured for topic: %s", topic)
			return
		}
		runLoadTestForTopic(p, topic, intervals, jsonFiles)
	}

	p.Flush(15 * 1000)
}
