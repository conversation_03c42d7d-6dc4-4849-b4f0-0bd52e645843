package main

import (
	"log/slog"
	"net"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/api"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/service"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

func main() {
	validator := validator.New()

	config, err := utils.LoadRESTServiceConfig(validator)
	if err != nil {
		utils.LogFatal("Failed to load configuration", err)
	}

	utils.InitializeLogger(config.LogLevel)

	db, err := postgres.ConnectToDB(&config.Postgres)
	if err != nil {
		utils.LogFatal("Failed to connect to the database", err)
	}

	// TODO Create a stand alone go app/process to create migrations
	if os.Getenv("DATABASE_MIGRATION") == "true" {
		slog.Info("Performing database migration")
		if err := postgres.AutoMigrate(db); err != nil {
			utils.LogFatal("Failed to perform database migration", err)
		}
		slog.Info("Database migration completed")
	}

	httpClient := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
		Timeout: time.Second * 30,
	}

	elantilGameClient := rest_client.NewElantilGamesClient(httpClient, config.CMSURL)
	directusClient := rest_client.NewDirectusCmsClient(httpClient, config)
	elantilMiddleware := rest_client.NewRestClientMiddleware(config)

	betRepository := postgres.NewBetRepository(db, service.VIPTiers)
	boostRepository := postgres.NewBoostRepository(db)
	gameRepository := postgres.NewGameRepository(db)
	userConfigAssetRepository := postgres.NewUserConfigAssetRepository(db)
	dropoutUsersRepo := postgres.NewDropoutUsersRepository(db, &config.SendGridConfig)
	dropoutUsersRepo.ScheduleDailyReports()
	transRepository := postgres.NewTransactionRepository(db)
	vipUserBalanceRepository := postgres.NewVIPUserBalanceRepository(db)
	bonusRepository := postgres.NewBonusRepository(db, service.VIPTiers, directusClient)
	maintenanceToastRepository := postgres.NewToastRepository(db)
	settingsRepository := postgres.NewSettingsRepository(db)
	bonusTemplateRepository := postgres.NewBonusTemplateRepository(db)
	termsAndconditionsRepository := postgres.NewCMSTermsAndConditionsRepository(db)
	emailCampaignsRepository := postgres.NewEmailCampaignsRepository(db)

	wageringClient := rest_client.NewElantilWageringClient(httpClient, config, *elantilMiddleware, transRepository)
	userRepository := postgres.NewUserRepository(db, service.VIPTiers, wageringClient)
	railsClient := rest_client.NewRailsClient(httpClient, config, wageringClient)
	optimoveClient := rest_client.NewOptimoveClient(httpClient, config)
	slackAlertClient := rest_client.NewSlackAlertClient(httpClient, config)

	userBonusQueue := postgres.NewUserBonusQueue(db, directusClient, wageringClient)
	bonusQueue := service.NewBonusQueueService(userBonusQueue)
	userBonusRepository := postgres.NewUserBonusRepository(db, service.VIPTiers, directusClient, userRepository, transRepository, bonusRepository, wageringClient, bonusQueue, slackAlertClient)
	userReferralRepository := postgres.NewReferralRepository(db, wageringClient, directusClient, userBonusRepository)
	bonusDropRepository := postgres.NewBonusDropRepository(db, wageringClient, directusClient, betRepository)
	userConfigAssetsService := service.NewUserConfigAssetsService(userConfigAssetRepository)
	gameService, err := service.NewGameService(gameRepository, elantilGameClient)
	if err != nil {
		utils.LogFatal("Failed to create game service", err)
	}
	userService := service.NewUserService(userRepository)
	transService := service.NewTransactionService(transRepository, railsClient)
	vipTierService := service.NewVIPTierService()
	vipUserBalanceService := service.NewVIPUserBalanceService(vipUserBalanceRepository, vipTierService, userRepository)
	boostService := service.NewBoostService(boostRepository, userService)
	keycloakService := service.NewKeyCloakClient(config, httpClient)
	bonusService := service.NewBonusService(directusClient, bonusRepository, wageringClient)
	userBonusService := service.NewUserBonusService(directusClient, userBonusRepository)
	refferalService := service.NewReferralService(userReferralRepository)
	railsClientService := service.NewRailsClientService(railsClient)
	settingsService := service.NewSettingsService(settingsRepository)
	bonusTemplateService := service.NewBonusTemplateService(bonusTemplateRepository)
	termsAndconditionsService := service.NewCMSTermsAndConditionsService(termsAndconditionsRepository)
	emailCampaignsService := service.NewEmailCampaignsService(emailCampaignsRepository, *optimoveClient)

	healhtService, err := service.NewHealthService(config)
	bonusDropService := service.NewBonusDropService(bonusDropRepository, betRepository, wageringClient, directusClient)
	cacheService := service.NewCacheService(config)
	cacheService.CacheGamesResponse()
	if err != nil {
		slog.Error("Error creating health service", "error", err)
	}

	slatedUsernames := utils.NewSlatedUsernames(filepath.Join("config", "slated_usernames.csv"))

	app := api.NewApp()
	app.Use(cors.New())
	app.Use(api.NewCSPMiddleware(validator).CSPReportOnlyMiddleware)

	var authenticationMiddleware fiber.Handler
	var authorizationMiddleware fiber.Handler
	var directusMiddleware fiber.Handler
	var swaggerMiddleware fiber.Handler
	if config.EnableAuthBypass {
		slog.Warn("Auth bypass enabled!")
		authenticationMiddleware = api.NewAuthenticationMiddleware(validator, keycloakService).WithBypass
		authorizationMiddleware = api.NewAuthorizationMiddleware(validator).WithBypass
	} else {
		authenticationMiddleware = api.NewAuthenticationMiddleware(validator, keycloakService).WithAccessToken
		authorizationMiddleware = api.NewAuthorizationMiddleware(validator).WithAccessToken
	}

	betMessageHandler := service.NewBetMessageHandler(
		betRepository,
		gameRepository,
		userRepository,
		boostRepository,
		make(chan domain.Bet),
		wageringClient,
		nil,
	)

	transactionMessageHandler := service.NewTransactionMessageHandler(transRepository, userRepository, bonusTemplateRepository, wageringClient, nil)
	recoveryService := service.NewRecoveryService(db, wageringClient, *betMessageHandler, *transactionMessageHandler, userRepository)
	recoveryHandler := api.NewRecoveryHandler(recoveryService)
	swaggerMiddleware = api.NewSwaggerMiddleware(&config.SwaggerConfig).WithPassword
	directusMiddleware = api.NewDirectusAuthentication(validator, &config.Directus).WithAccessToken
	recoverXpHandler := api.NewRecoverXpHandler(userRepository, wageringClient, betMessageHandler)
	healthHandler := api.NewHealthHandler(healhtService)
	bonusDropHandler := api.NewBonusDropHandler(bonusDropService)

	redisClient := utils.ConnectToRedis()

	betService := service.NewBetService(betRepository, gameRepository, userRepository, boostRepository, redisClient)
	passwordResetService := service.NewPasswordResetService(httpClient, config, redisClient, elantilMiddleware)

	api.SetUpRoutes(app, &api.Dependencies{
		BetHandler:               api.NewBetHandler(betService, validator, keycloakService, redisClient),
		BoostHandler:             api.NewBoostHandler(boostService, userService, validator),
		GameHandler:              api.NewGameHandler(gameService, validator),
		LeaderboardHandler:       api.NewLeaderboardHandler(userService, validator, keycloakService),
		TransactionHandler:       api.NewTransactionHandler(transService, validator),
		UserConfigAsssetsHandler: api.NewUserConfigAssetsHandler(userConfigAssetsService),
		UserHandler:              api.NewUserHandler(userService, userConfigAssetsService, validator, keycloakService, redisClient),
		VIPUserBalanceHandler:    api.NewVIPUserBalanceHandler(vipUserBalanceService, validator),
		AuthenticationMiddleware: authenticationMiddleware,
		AuthorizationMiddleware:  authorizationMiddleware,
		BonusHandler:             api.NewBonusHandler(bonusService, validator),
		UserBonusHandler:         api.NewUserBonusHandler(userBonusService, validator),
		ElantilClientHandler:     api.NewElantilClientHandler(wageringClient, userRepository, userReferralRepository),
		RefferalHandler:          api.NewReferralHandler(refferalService),
		RailsClientHandler:       api.NewRestClientHandler(railsClientService),
		MaintenanceToastHandler:  api.NewToastHandler(maintenanceToastRepository),
		DirectusMiddleware:       directusMiddleware,
		SwaggerMiddleware:        swaggerMiddleware,
		RecoverXpHandler:         recoverXpHandler,
		SettingsHandler:          api.NewSettingsHandler(settingsService),
		BonusTemplateHandler:     api.NewBonusTemplateHandler(bonusTemplateService),
		CMSTemplateHandler:       api.NewCMSTermsAndConditionsHandler(termsAndconditionsService),
		EmailCampaignsHandler:    api.NewEmailCampaignsHandler(emailCampaignsService),
		HealthHandler:            healthHandler,
		BonusDropHandler:         bonusDropHandler,
		SlatedUsernameHandler:    api.NewSlatedUsernameHandler(slatedUsernames),
		PasswordResetHandler:     api.NewPasswordResetHandler(passwordResetService, validator),
	})

	api.SetupRecoveryRoutes(app, recoveryHandler)

	go func() {
		if err := app.Listen(config.Address); err != nil {
			utils.LogFatal("Failed to start HTTP server", err)
		}
	}()

	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	<-signals

	slog.Info("Shutdown signal received, starting graceful shutdown")

	if betMessageHandler != nil {
		betMessageHandler.Shutdown()
		slog.Info("Bet message handler shut down")
	}

	if err := app.Shutdown(); err != nil {
		utils.LogFatal("Failed to stop HTTP server", err)
	}

	userService.StopCache()
	keycloakService.StopCache()
}
