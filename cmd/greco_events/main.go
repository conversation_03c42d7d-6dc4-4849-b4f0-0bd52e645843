package main

import (
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/greco"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func main() {
	validator := validator.New()

	config, err := utils.LoadEventsGrecoServiceConfig(validator)
	if err != nil {
		utils.LogFatal("Failed to load configuration", err)
	}

	if !config.Enabled {
		utils.LogFatal("Service not enabled", nil)
	}

	utils.InitializeLogger(config.LogLevel)

	db, err := postgres.ConnectToDB(&config.Postgres)
	if err != nil {
		utils.LogFatal("Failed to connect to the database", err)
	}

	kafkaProducer, err := greco.NewKafkaProducer(&config.GrecoKafkaProducer)
	if err != nil {
		utils.LogFatal("Failed to create Greco Kafka producer", err)
	}

	kafkaConsumer, err := newKafkaConsumer(&config.KafkaConsumer, db, kafkaProducer)
	if err != nil {
		utils.LogFatal("Failed to create Kafka consumer", err)
	}

	go kafkaConsumer.Start()
	slog.Info("Kafka consumer started")

	// Wait for signal
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	<-signals

	if err := kafkaConsumer.Stop(); err != nil {
		utils.LogFatal("Failed to stop the Kafka consumer", err)
	}
}

func newKafkaConsumer(
	config *utils.KafkaConsumerConfig,
	_ *gorm.DB,
	_ *greco.KafkaProducer,
) (*kafka.Consumer, error) {
	const topicGreco = "greco"
	// TODO: Add builders when Greco gets implemented for Elantil
	handlersMap := kafka.HandlersMap{}
	return kafka.NewConsumer(config, handlersMap)
}
