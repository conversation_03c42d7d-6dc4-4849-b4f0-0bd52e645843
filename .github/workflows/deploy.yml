# This workflow is designed to be triggered manually. To trigger the workflow, click the "Run workflow" dropdown, select the desired branch, and click the "Run workflow" button.
name: Deploy workflow

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - 'staging'
          - 'uat'
          - 'production'
      image_tag:
        description: 'The tag to apply to the image'
        required: true
        default: 'latest'

env:
  AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
  ECS_AWS_REGION: eu-central-1
  ECR_AWS_REGION: eu-central-1
  ECR_REGISTRY: 190685083100
  ECS_SERVICE: 'community-v2'
  CONTAINER_NAME: 'community-v2'

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: AWS & ECR authentication
      uses: ./.github/actions/aws-auth
      id: aws-auth
      with:
        AWS_REGION: ${{ env.ECS_AWS_REGION }}
        AWS_ROLE_ARN: ${{ env.AWS_ROLE_ARN }}
        ECR_LOGIN: 'false'
        ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

    - name: Deploy rest service
      uses: ./.github/actions/deploy-service
      with:
        ENVIRONMENT: ${{ inputs.environment }}
        MODULE: 'rest'
        ECS_CLUSTER: '${{ inputs.environment }}-community'
        ECS_SERVICE: '${{ env.ECS_SERVICE }}-rest'
        CONTAINER_NAME: '${{ env.CONTAINER_NAME }}-rest'
        IMAGE: "${{ env.ECR_REGISTRY }}.dkr.ecr.${{ env.ECR_AWS_REGION }}.amazonaws.com/${{ env.CONTAINER_NAME }}-rest:${{ inputs.image_tag }}"

    - name: Deploy events service
      uses: ./.github/actions/deploy-service
      with:
        ENVIRONMENT: ${{ inputs.environment }}
        MODULE: 'events'
        ECS_CLUSTER: '${{ inputs.environment }}-community'
        ECS_SERVICE: '${{ env.ECS_SERVICE }}-events'
        CONTAINER_NAME: '${{ env.CONTAINER_NAME }}-events'
        IMAGE: "${{ env.ECR_REGISTRY }}.dkr.ecr.${{ env.ECR_AWS_REGION }}.amazonaws.com/${{ env.CONTAINER_NAME }}-events:${{ inputs.image_tag }}"

    - name: Deploy greco events service
      if: inputs.environment == 'production'
      uses: ./.github/actions/deploy-service
      with:
        ENVIRONMENT: ${{ inputs.environment }}
        MODULE: 'greco_events'
        ECS_CLUSTER: '${{ inputs.environment }}-community'
        ECS_SERVICE: '${{ env.ECS_SERVICE }}-greco_events'
        CONTAINER_NAME: '${{ env.CONTAINER_NAME }}-greco_events'
        IMAGE: "${{ env.ECR_REGISTRY }}.dkr.ecr.${{ env.ECR_AWS_REGION }}.amazonaws.com/${{ env.CONTAINER_NAME }}-greco_events:${{ inputs.image_tag }}"
          
  # Will implement Slack notification to inform the team about the deployment
  on-success:
    runs-on: ubuntu-latest
    steps:
      - run: echo 'The triggering workflow passed'
  on-failure:
    runs-on: ubuntu-latest
    steps:
      - run: echo 'The triggering workflow failed'
