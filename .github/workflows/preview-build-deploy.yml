name: Branch CI & Auto-Deploy

on:
  push:
    branches-ignore:
      - main
      - dev

env:
  AWS_REGION: eu-central-1
  HELM_NAMESPACE: mt-preview
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write
  contents: read
  checks: read

jobs:
  precheck:
    runs-on: p-dev-runners
    outputs:
      deploy: ${{ steps.detect.outputs.deploy }}
      sha: ${{ steps.vars.outputs.sha }}
      branch: ${{ steps.vars.outputs.branch }}
      release: ${{ steps.vars.outputs.release }}
      staging_exists: ${{ steps.detect.outputs.staging_exists }}
      uat_exists: ${{ steps.detect.outputs.uat_exists }}
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - name: Vars
        id: vars
        run: |
          BRANCH="${GITHUB_REF_NAME//\//-}"
          BRANCH_SANIT="${BRANCH//[^a-zA-Z0-9-]/}"
          echo "branch=$BRANCH_SANIT" >> $GITHUB_OUTPUT
          echo "raw=$GITHUB_REF_NAME" >> $GITHUB_OUTPUT
          echo "sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "release=community-$BRANCH_SANIT" >> $GITHUB_OUTPUT

      - name: AWS auth (read only)
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - uses: azure/setup-helm@v4.3.0

      - name: Detect existing preview releases
        id: detect
        run: |
          RELEASE=${{ steps.vars.outputs.release }}
          echo "Looking for release: $RELEASE"
          echo "deploy=false" >> $GITHUB_OUTPUT
          echo "staging_exists=false" >> $GITHUB_OUTPUT
          echo "uat_exists=false" >> $GITHUB_OUTPUT
          
          FOUND_ANY=false
          
          for pair in "staging mt-dev" "uat mt-prod"; do
            env=${pair%% *}; cluster=${pair##* }
            echo "Checking $cluster for release $RELEASE in namespace $HELM_NAMESPACE"
            
            # Authenticate to cluster
            echo "::group::eks-auth-$cluster"
            aws eks update-kubeconfig --name $cluster --region $AWS_REGION --alias $cluster
            echo "::endgroup::"
            
            # Check if release exists
            echo "Running: helm status $RELEASE -n $HELM_NAMESPACE --kube-context $cluster"
            if helm status "$RELEASE" -n $HELM_NAMESPACE --kube-context $cluster &>/dev/null; then
              echo "✓ Found release $RELEASE in $cluster ($env)";
              echo "${env}_exists=true" >> $GITHUB_OUTPUT
              FOUND_ANY=true
            else
              echo "✗ Release $RELEASE not found in $cluster ($env)"
            fi
          done
          
          if [ "$FOUND_ANY" = true ]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "Found preview releases, will proceed with build and deploy"
          else
            echo "No preview releases found for $RELEASE"
          fi

  build:
    needs: precheck
    if: needs.precheck.outputs.deploy == 'true'
    runs-on: p-dev-runners
    strategy:
      matrix:
        module: [rest, events]
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - name: Configure kubectl for both clusters
        run: |
          # Configure both clusters
          aws eks update-kubeconfig --name mt-dev --region $AWS_REGION --alias mt-dev
          aws eks update-kubeconfig --name mt-prod --region $AWS_REGION --alias mt-prod
          EKS_CREDS=$(cat ~/.kube/config | base64 -w 0)
          echo "::add-mask::$EKS_CREDS"
          echo "EKS_CREDS=$EKS_CREDS" >> $GITHUB_ENV

      - uses: tale/kubectl-action@v1
        with:
          base64-kube-config: ${{ env.EKS_CREDS }}

      - name: AWS auth
        uses: ./.github/actions/aws-auth
        id: aws
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "true"
          ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

      - name: Build & push ${{ matrix.module }} with Docker
        uses: ./.github/actions/build-tag-push
        with:
          ECR_REGISTRY: ${{ steps.aws.outputs.registry }}
          IMAGE_TAG: ${{ needs.precheck.outputs.sha }}
          MODULE: ${{ matrix.module }}
          PLATFORM: linux/arm64

  deploy:
    needs: [precheck, build]
    if: needs.precheck.outputs.deploy == 'true'
    runs-on: p-dev-runners
    strategy:
      matrix:
        include:
          - environment: staging
            cluster: mt-dev
            domain: monkeytilt.pro
            ingress_class: nginx
          - environment: uat
            cluster: mt-prod
            domain: monkeytilt.pro
            ingress_class: nginx
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - uses: azure/setup-helm@v4.3.0

      - name: AWS auth
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - name: Configure kubectl
        run: |
          echo "Authenticating to ${{ matrix.cluster }}"
          aws eks update-kubeconfig --name ${{ matrix.cluster }} --region $AWS_REGION
          
      - name: Check if preview exists in this environment
        id: check
        run: |
          case "${{ matrix.environment }}" in
            staging) ENV_EXISTS="${{ needs.precheck.outputs.staging_exists }}" ;;
            uat) ENV_EXISTS="${{ needs.precheck.outputs.uat_exists }}" ;;
          esac
          echo "exists=$ENV_EXISTS" >> $GITHUB_OUTPUT
          echo "Preview exists in ${{ matrix.environment }}: $ENV_EXISTS"

      - name: Helm upgrade
        if: steps.check.outputs.exists == 'true'
        run: |
          echo "Deploying to ${{ matrix.environment }} environment on ${{ matrix.cluster }} cluster"
          
          helm upgrade --install ${{ needs.precheck.outputs.release }} ./helm/community \
            --namespace $HELM_NAMESPACE --create-namespace \
            --set services.rest.image.tag=${{ needs.precheck.outputs.sha }} \
            --set services.events.image.tag=${{ needs.precheck.outputs.sha }} \
            --set ingress.host=community.${{ matrix.cluster }}.${{ matrix.domain }} \
            --set ingress.className=${{ matrix.ingress_class }} \
            --set env.ENV=${{ matrix.environment }} \
            -f helm/community/values.yaml 
