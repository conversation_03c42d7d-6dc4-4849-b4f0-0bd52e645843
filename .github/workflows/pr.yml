name: PR workflow

on: pull_request

env:
  AWS_REGION: eu-central-1
  AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  build:
    name: Build & push [${{ matrix.module }}]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [events, greco_events, rest]
    steps:
      - uses: actions/checkout@v3

      - name: AWS & ECR authentication
        uses: ./.github/actions/aws-auth
        id: aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: ${{ env.AWS_ROLE_ARN }}
          ECR_LOGIN: "true"
          ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

      - name: Build, tag & push to ECR
        uses: ./.github/actions/build-tag-push
        with:
          ECR_REGISTRY: ${{ steps.aws-auth.outputs.registry }}
          IMAGE_TAG: ${{ github.event.pull_request.head.sha }}
          MODULE: ${{ matrix.module }}
