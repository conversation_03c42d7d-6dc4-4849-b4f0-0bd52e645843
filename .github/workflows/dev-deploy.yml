name: Dev Branch Auto-Deploy

on:
  push:
    branches:
      - dev

env:
  AWS_REGION: eu-central-1
  HELM_NAMESPACE: mt-backend
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write
  contents: read

jobs:
  vars:
    runs-on: p-dev-runners
    outputs:
      sha: ${{ steps.set.outputs.sha }}
      release: ${{ steps.set.outputs.release }}
    steps:
      - uses: actions/checkout@v3

      - id: set
        name: Set variables
        run: |
          echo "sha=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "release=community" >> $GITHUB_OUTPUT

  build:
    needs: vars
    runs-on: p-dev-runners
    strategy:
      matrix:
        module: [rest, events]
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --name mt-dev --region $AWS_REGION
          EKS_CREDS=$(cat ~/.kube/config | base64 -w 0)
          echo "::add-mask::$EKS_CREDS"
          echo "EKS_CREDS=$EKS_CREDS" >> $GITHUB_ENV

      - uses: tale/kubectl-action@v1
        with:
          base64-kube-config: ${{ env.EKS_CREDS }}

      - name: AWS auth
        uses: ./.github/actions/aws-auth
        id: aws
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "true"
          ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

      - name: Build & push ${{ matrix.module }} with Docker
        uses: ./.github/actions/build-tag-push
        with:
          ECR_REGISTRY: ${{ steps.aws.outputs.registry }}
          IMAGE_TAG: ${{ needs.vars.outputs.sha }}
          MODULE: ${{ matrix.module }}
          PLATFORM: linux/arm64

  deploy:
    needs: [vars, build]
    runs-on: p-dev-runners
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - uses: azure/setup-helm@v4.3.0

      - name: AWS auth
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - name: Configure kubectl
        run: |
          echo "Authenticating to mt-dev cluster for dev deployment"
          aws eks update-kubeconfig --name mt-dev --region $AWS_REGION

      - name: Deploy to dev environment
        run: |
          echo "Deploying dev branch to mt-dev cluster"
          
          helm upgrade --install ${{ needs.vars.outputs.release }} ./helm/community \
            --namespace $HELM_NAMESPACE --create-namespace \
            --set services.rest.image.tag=${{ needs.vars.outputs.sha }} \
            --set services.events.image.tag=${{ needs.vars.outputs.sha }} \
            --set ingress.host=community.mt-dev.monkeytilt.pro \
            --set ingress.className=nginx \
            --set env.ENV=staging \
            -f helm/community/values.yaml
            
          echo "✅ Dev deployment completed successfully"
          echo "🌐 Available at: https://community.mt-dev.monkeytilt.pro" 