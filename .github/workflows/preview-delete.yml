name: Delete Preview Environment

on:
  pull_request:
    types: [closed]
  delete:
  workflow_dispatch:

env:
  AWS_REGION: eu-central-1
  HELM_NAMESPACE: mt-preview
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write
  contents: read

jobs:
  cleanup:
    runs-on: p-dev-runners
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - name: A<PERSON> auth
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - uses: azure/setup-helm@v4.3.0

      - name: Determine release name
        id: rel
        run: |
          if [[ "${{ github.event_name }}" == "delete" ]]; then
            BRANCH="${GITHUB_REF_NAME//\//-}"
          elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
            BRANCH="${GITHUB_HEAD_REF//\//-}"
          else
            # Manual workflow_dispatch - we'll handle all releases
            BRANCH=""
          fi
          
          if [[ -n "$BRANCH" ]]; then
            # Sanitize branch name
            BRANCH_SANIT="${BRANCH//[^a-zA-Z0-9-]/}"
            RELEASE="community-$BRANCH_SANIT"
            echo "release=$RELEASE" >> $GITHUB_OUTPUT
            echo "mode=single" >> $GITHUB_OUTPUT
            echo "Deleting single release: $RELEASE"
          else
            echo "mode=all" >> $GITHUB_OUTPUT
            echo "Deleting all preview releases"
          fi

      - name: Delete from both clusters
        run: |
          MODE="${{ steps.rel.outputs.mode }}"
          RELEASE="${{ steps.rel.outputs.release }}"
          
          for pair in "staging mt-dev" "uat mt-prod"; do
            env=${pair%% *}; cluster=${pair##* }
            echo "Checking $cluster ($env) for preview releases..."
            
            # Authenticate to cluster
            echo "::group::eks-auth-$cluster"
            aws eks update-kubeconfig --name $cluster --region $AWS_REGION --alias $cluster
            echo "::endgroup::"
            
            if [[ "$MODE" == "single" ]]; then
              # Delete specific release
              echo "Attempting to delete release: $RELEASE from $cluster"
              if helm status "$RELEASE" -n $HELM_NAMESPACE --kube-context $cluster &>/dev/null; then
                echo "✓ Found $RELEASE in $cluster, deleting..."
                helm uninstall "$RELEASE" -n $HELM_NAMESPACE --kube-context $cluster
                echo "✓ Deleted $RELEASE from $cluster"
              else
                echo "✗ Release $RELEASE not found in $cluster"
              fi
            else
              # Delete all preview releases (manual mode)
              echo "Finding all community-* releases in $cluster..."
              RELEASES=$(helm list -n $HELM_NAMESPACE --kube-context $cluster -q | grep "^community-" || true)
              if [[ -n "$RELEASES" ]]; then
                echo "Found releases in $cluster: $RELEASES"
                for release in $RELEASES; do
                  echo "Deleting $release from $cluster..."
                  helm uninstall "$release" -n $HELM_NAMESPACE --kube-context $cluster || echo "Failed to delete $release"
                done
              else
                echo "No community-* releases found in $cluster"
              fi
            fi
          done
          
          echo "Cleanup completed" 