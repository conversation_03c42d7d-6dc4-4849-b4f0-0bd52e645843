name: Create Preview Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Target environment"
        type: choice
        options:
          - staging
          - uat
        default: staging
      ttlDays:
        description: "TTL for preview (days)"
        default: "30"
        type: string

env:
  AWS_REGION: eu-central-1
  HELM_NAMESPACE: mt-preview
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write
  contents: read
  checks: write

jobs:
  vars:
    runs-on: p-dev-runners
    outputs:
      branch: ${{ steps.set.outputs.branch }}
      raw: ${{ steps.set.outputs.raw }}
      sha: ${{ steps.set.outputs.sha }}
      release: ${{ steps.set.outputs.release }}
      ttl: ${{ steps.set.outputs.ttl }}
      cluster: ${{ steps.set.outputs.cluster }}
    steps:
      - uses: actions/checkout@v3

      - id: set
        name: Set variables
        run: |
          BRANCH="${GITHUB_REF_NAME}"
          BRANCH_SANIT=${BRANCH//\//-}
          echo "branch=$BRANCH_SANIT"   >> "$GITHUB_OUTPUT"
          echo "raw=$BRANCH"            >> "$GITHUB_OUTPUT"
          echo "sha=$(git rev-parse --short HEAD)" >> "$GITHUB_OUTPUT"
          echo "release=community-$BRANCH_SANIT"   >> "$GITHUB_OUTPUT"
          echo "ttl=${{ inputs.ttlDays }}"         >> "$GITHUB_OUTPUT"

          ENV="${{ inputs.environment }}"
          echo "env=$ENV"               >> "$GITHUB_OUTPUT"

          case "$ENV" in
            staging)   CLUSTER="mt-dev" ;;
            uat)       CLUSTER="mt-prod";;
            *)         echo "::error::unknown environment $ENV"; exit 1;;
          esac
          echo "cluster=$CLUSTER"       >> "$GITHUB_OUTPUT"

      - name: Mark preview enabled check
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.checks.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              name: `preview-enabled-${{ github.event.inputs.environment }}`,
              head_sha: process.env.GITHUB_SHA,
              status: 'completed',
              conclusion: 'success',
              output: {title:`Preview enabled for ${{ github.event.inputs.environment }}` , summary:'Branch will auto-deploy'}
            })

  build:
    needs: vars
    runs-on: p-dev-runners
    strategy:
      matrix:
        module: [rest, events]
    steps:
      - uses: actions/checkout@v3

      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --name ${{ needs.vars.outputs.cluster }} --region $AWS_REGION
          EKS_CREDS=$(cat ~/.kube/config | base64 -w 0)
          echo "::add-mask::$EKS_CREDS"
          echo "EKS_CREDS=$EKS_CREDS" >> $GITHUB_ENV

      - uses: tale/kubectl-action@v1
        with:
          base64-kube-config: ${{ env.EKS_CREDS }}

      - name: AWS auth
        uses: ./.github/actions/aws-auth
        id: aws
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "true"
          ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

      - name: Build & push ${{ matrix.module }} with Docker
        uses: ./.github/actions/build-tag-push
        with:
          ECR_REGISTRY: ${{ steps.aws.outputs.registry }}
          IMAGE_TAG: ${{ needs.vars.outputs.sha }}
          MODULE: ${{ matrix.module }}
          PLATFORM: linux/arm64

  deploy:
    needs: [vars, build]
    runs-on: p-dev-runners
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ needs.vars.outputs.raw }}
      
      - name: install-aws-cli-action
        uses: unfor19/install-aws-cli-action@v1
        with:
          version: 2
          verbose: false
          arch: arm64
          
      - name: AWS auth
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - name: Configure kubectl
        run: aws eks update-kubeconfig --name ${{ needs.vars.outputs.cluster }} --region $AWS_REGION

      - uses: azure/setup-helm@v4.3.0

      - name: Helm upgrade
        run: |
          # Set ingress class, domain, and environment based on cluster
          case "${{ needs.vars.outputs.cluster }}" in
            mt-dev) 
              INGRESS_CLASS="nginx"
              DOMAIN="monkeytilt.pro"
              ;;
                        mt-prod) 
                INGRESS_CLASS="nginx"
                DOMAIN="monkeytilt.pro"
              ;;
          esac
          
          helm upgrade --install ${{ needs.vars.outputs.release }} ./helm/community \
            --namespace $HELM_NAMESPACE --create-namespace \
            --set services.rest.image.tag=${{ needs.vars.outputs.sha }} \
            --set services.events.image.tag=${{ needs.vars.outputs.sha }} \
            --set ingress.host=community.${{ needs.vars.outputs.cluster }}.$DOMAIN \
            --set ingress.className=$INGRESS_CLASS \
            --set env.ENV=${{ inputs.environment }} \
            --set ttlDays=${{ needs.vars.outputs.ttl }} \
            -f helm/community/values.yaml 
