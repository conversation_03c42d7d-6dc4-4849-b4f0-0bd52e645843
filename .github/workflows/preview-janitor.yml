name: Preview Janitor

on:
  schedule:
    - cron: "0 2 * * *"

env:
  AWS_REGION: eu-central-1
  EKS_CLUSTER: p-dev
  HELM_NAMESPACE: mt-preview

permissions:
  id-token: write
  contents: read

jobs:
  cleanup:
    runs-on: p-dev-runners
    steps:
      - name: <PERSON><PERSON> auth
        uses: ./.github/actions/aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
          ECR_LOGIN: "false"

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --name $EKS_CLUSTER --region $AWS_REGION

      - name: Delete preview releases older than 10 days
        run: |
          helm ls -n $HELM_NAMESPACE -o json | jq -r '.[] | select(.name|test("^community-")) | "\(.name) \(.updated)"' | while read name ts; do
            AGE=$(date -d "$ts" +%s)
            LIMIT=$(date -d "-10 days" +%s)
            if [ $AGE -lt $LIMIT ]; then
              echo "Removing stale preview $name"
              helm uninstall $name -n $HELM_NAMESPACE || true
            fi
          done 