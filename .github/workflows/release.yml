name: Release workflow

on:
  push:
    tags: "v[1-9]+.[0-9]+.[0-9]+"
    branches:
      - release/[1-9]+.[0-9]+.[0-9]+
      - hotfix/[1-9]+.[0-9]+.[0-9]+

env:
  AWS_REGION: eu-central-1
  AWS_ROLE_ARN: arn:aws:iam::190685083100:role/GithubActionsRole
  ECR_REGISTRY: 190685083100

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [events, greco_events, rest]
    steps:
      - uses: actions/checkout@v3

      - name: Parse version
        id: parse_version
        run: |
          # Parse the version from the branch name or tag (in case it's just tag with x.y.z format the awk won't do anything)
          echo "version=$(echo ${{ github.ref_name }} | awk -F'/' '{print $NF}')" >> $GITHUB_OUTPUT

          # If the branch name is release/x.y.z, then the image tag should be release-x.y.z-latest, if it's tag x.y.z, then the image tag should be x.y.z
          echo "image-tag=$(echo ${{ github.ref_name }} | awk -F'/' '{if (NF==1) print $1; else print $1"-"$NF"-latest"}')" >> $GITHUB_OUTPUT
        shell: bash

      - name: AWS & ECR authentication
        uses: ./.github/actions/aws-auth
        id: aws-auth
        with:
          AWS_REGION: ${{ env.AWS_REGION }}
          AWS_ROLE_ARN: ${{ env.AWS_ROLE_ARN }}
          ECR_LOGIN: "true"
          ECR_REGISTRY: ${{ env.ECR_REGISTRY }}

      - name: Build, tag & push to ECR
        uses: ./.github/actions/build-tag-push
        with:
          ECR_REGISTRY: ${{ steps.aws-auth.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
          ADDITIONAL_TAGS: ${{ steps.parse_version.outputs.image-tag }}
          MODULE: ${{ matrix.module }}
