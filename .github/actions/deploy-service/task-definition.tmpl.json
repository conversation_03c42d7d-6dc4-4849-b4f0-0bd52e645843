{"family": "core-v2-{{ .module }}-{{ .environment }}", "containerDefinitions": [{"name": "community-v2-{{ .module }}", "image": "{{ .image }}", "cpu": 0, "portMappings": [{"name": "http", "containerPort": 6969, "hostPort": 6969, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "DATABASE_MIGRATION", "value": "true"}, {"name": "AWS_REGION", "value": "eu-central-1"}, {"name": "ENV", "value": "{{ .environment }}"}, {"name": "POPULATE_GAMES", "value": "true"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/{{ .environment }}/core-v2/{{ .module }}", "awslogs-create-group": "true", "awslogs-region": "eu-central-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::190685083100:role/ecs-community-{{ .environment }}-secrets-role", "executionRoleArn": "arn:aws:iam::190685083100:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "{{ .cpu }}", "memory": "{{ .ram }}", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "provisioner", "value": "github-actions"}]}