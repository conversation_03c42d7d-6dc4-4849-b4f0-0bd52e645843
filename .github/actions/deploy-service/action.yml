name: Deploy service
description: Deploy a new version of a service to Amazon ECS

inputs:
  ENVIRONMENT:
    description: "The environment to deploy to"
    required: true
    default: "staging"
  ECS_CLUSTER:
    description: "The Amazon ECS cluster"
    required: true
  ECS_SERVICE:
    description: "The Amazon ECS service"
    required: true
  CONTAINER_NAME:
    description: "The container name in the task definition"
    required: true
  IMAGE:
    description: "The image to use in the task definition"
    required: true
  MODULE:
    description: "The module to deploy"
    required: true

runs:
  using: "composite"
  steps:
    - name: Render template
      id: render_template
      uses: chuhlomin/render-template@v1
      with:
        template: ./.github/actions/deploy-service/task-definition.tmpl.json
        result_path: ./.github/actions/deploy-service/task-definition.json
        vars: |
          image: ${{ inputs.IMAGE }}
          environment: ${{ inputs.ENVIRONMENT }}
          cpu: ${{ inputs.ENVIRONMENT == 'production' && '4096' || '256' }}
          ram: ${{ inputs.ENVIRONMENT == 'production' && '8192' || '1024' }}
          module: ${{ inputs.MODULE }}

    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@c804dfbdd57f713b6c079302a4c01db7017a36fc
      with:
        task-definition: ./.github/actions/deploy-service/task-definition.json
        container-name: ${{ inputs.CONTAINER_NAME }}
        image: ${{ inputs.IMAGE }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: ${{ inputs.ECS_SERVICE }}
        cluster: ${{ inputs.ECS_CLUSTER }}
        wait-for-service-stability: true
