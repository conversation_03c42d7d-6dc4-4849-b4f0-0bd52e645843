name: Build, tag, and push image to Amazon ECR
description: Build, tag, and push image to Amazon ECR

inputs:
  ECR_REGISTRY:
    description: 'The ECR registry'
    required: true
  IMAGE_TAG:
    description: 'The tag to apply to the image'
    required: true
  ADDITIONAL_TAGS:
    description: 'Additional tags to apply to the image'
    required: false
    default: ''
  MODULE:
    description: 'The module to build'
    required: true
  PLATFORM:
    description: 'Target platform architecture (e.g., linux/amd64, linux/arm64)'
    required: false
    default: 'linux/amd64'

runs:
  using: 'composite'
  steps:
    - name: Build, tag, and push image to Amazon ECR
      working-directory: .
      id: build-publish
      shell: bash
      env:
        ECR_REGISTRY: ${{ inputs.ECR_REGISTRY }}
        ECR_REPOSITORY: community-v2-${{ inputs.MODULE }}
        DOCKERFILE: ${{ inputs.MODULE }}.Dockerfile
        PLATFORM: ${{ inputs.PLATFORM }}
      run: |
        docker buildx create --use --name multiarch || true
        docker buildx inspect --bootstrap

        # Parse additional tags into `--tag` options
        TAG_ARGS="-t $ECR_REGISTRY/$ECR_REPOSITORY:${{ inputs.IMAGE_TAG }}"
        if [ -n "${{ inputs.ADDITIONAL_TAGS }}" ]; then
          for tag in $(echo "${{ inputs.ADDITIONAL_TAGS }}" | tr ',' '\n'); do
            TAG_ARGS="$TAG_ARGS -t $ECR_REGISTRY/$ECR_REPOSITORY:$tag"
          done
        fi

        docker buildx build . --push $TAG_ARGS \
          -f "$DOCKERFILE" \
          --platform="$PLATFORM" \
          --label "github.ref=${GITHUB_REF}" \
          --label "github.sha=${GITHUB_SHA}" \
          --label "github.actor=${GITHUB_ACTOR}"

        echo "Images pushed:"
        echo "$ECR_REGISTRY/$ECR_REPOSITORY:${{ inputs.IMAGE_TAG }}"
        if [ -n "${{ inputs.ADDITIONAL_TAGS }}" ]; then
          for tag in $(echo "${{ inputs.ADDITIONAL_TAGS }}" | tr ',' '\n'); do
            echo "$ECR_REGISTRY/$ECR_REPOSITORY:$tag"
          done
        fi