name: <PERSON><PERSON> Auth
description: Configure AWS credentials and login to Amazon ECR

inputs:
  AWS_REGION:
    description: 'The AWS region to use'
    required: true
  AWS_ROLE_ARN:
    description: 'The ARN of the role to assume'
    required: true
  ECR_LOGIN:
    description: 'Controls whether to login to ECR'
    required: false
  ECR_REGISTRY:
    description: 'The ECR registry'
    required: false

outputs:
  registry:
    description: 'The ECR registry'
    value: ${{ steps.login-ecr.outputs.registry }}

runs:
  using: 'composite'
  steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-region: ${{ inputs.AWS_REGION }}
        role-to-assume: ${{ inputs.AWS_ROLE_ARN }}
        role-session-name: GithubActions

    - name: Login to Amazon ECR
      id: login-ecr
      if: ${{ inputs.ECR_LOGIN == 'true' }}
      uses: aws-actions/amazon-ecr-login@v1
      with:
        registries: ${{ inputs.ECR_REGISTRY }}
        mask-password: "true" # see: https://github.com/aws-actions/amazon-ecr-login#docker-credentials
