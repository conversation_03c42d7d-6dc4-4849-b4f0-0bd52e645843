name: Kaniko build in-cluster
description: Build and push docker image in cluster using a Kubernetes Job with kaniko executor
inputs:
  image:
    description: Full image destination (registry/repo:tag)
    required: true
  module:
    description: Module / path for Dockerfile (e.g. rest)
    required: true
  git_ref:
    description: Git ref (commit SHA)
    required: true
runs:
  using: composite
  steps:
    - shell: bash
      run: |
        set -e
        JOB=kaniko-${{ inputs.module }}-${{ inputs.git_ref }}
        CONTEXT="https://github.com/${GITHUB_REPOSITORY}.git#$GITHUB_SHA"
        cat <<EOF > job.yaml
        apiVersion: batch/v1
        kind: Job
        metadata:
          name: $JOB
        spec:
          backoffLimit: 0
          ttlSecondsAfterFinished: 600
          template:
            spec:
              restartPolicy: Never
              containers:
                - name: kaniko
                  image: gcr.io/kaniko-project/executor:latest
                  args:
                    - "--dockerfile=${{ inputs.module }}.Dockerfile"
                    - "--context=$CONTEXT"
                    - "--destination=${{ inputs.image }}"
                    - "--platform=linux/arm64,linux/amd64"
                  env:
                    - name: AWS_REGION
                      value: $AWS_REGION
                    - name: AWS_ACCESS_KEY_ID
                      value: $AWS_ACCESS_KEY_ID
                    - name: AWS_SECRET_ACCESS_KEY
                      value: $AWS_SECRET_ACCESS_KEY
                    - name: AWS_SESSION_TOKEN
                      value: $AWS_SESSION_TOKEN
        EOF
        kubectl apply -f job.yaml
        kubectl wait --for=condition=complete --timeout=1h job/$JOB
        kubectl logs job/$JOB -c kaniko
