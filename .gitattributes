# Set default behavior to automatically normalize line endings
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.go text
*.md text
*.txt text
*.yaml text
*.yml text
*.json text
*.sh text eol=lf
*.sql text

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf
Dockerfile* text eol=lf

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary
*.ez binary
*.bz2 binary
*.swp binary
*.jar binary
*.class binary
*.so binary
*.dll binary
*.exe binary

# Git LFS configuration
docs/*.png filter=lfs diff=lfs merge=lfs -text
