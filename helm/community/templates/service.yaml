apiVersion: v1
kind: List
items:
{{- range $name, $svc := .Values.services }}
  {{- if $svc.enabled }}
  - apiVersion: v1
    kind: Service
    metadata:
      name: {{ include "community.fullname" $ }}-{{ $name }}
      labels:
        {{- include "community.labels" $ | nindent 8 }}
        app.kubernetes.io/component: {{ $name }}
    spec:
      type: {{ $svc.service.type | default "ClusterIP" }}
      ports:
        - port: {{ $svc.service.port | default 80 }}
          targetPort: http
          protocol: TCP
          name: http
      selector:
        app.kubernetes.io/name: {{ include "community.name" $ }}
        app.kubernetes.io/instance: {{ $.Release.Name }}
        app.kubernetes.io/component: {{ $name }}
  {{- end }}
{{- end }}
