{{- $suffix := trimPrefix "community-" .Release.Name }}
apiVersion: v1
kind: List
items:
{{- range $name, $svc := .Values.services }}
  {{- if $svc.enabled }}
  - apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: {{ include "community.fullname" $ }}-{{ $name }}
      labels:
        {{- include "community.labels" $ | nindent 8 }}
        app.kubernetes.io/component: {{ $name }}
    spec:
      replicas: {{ $svc.replicaCount | default 1 }}
      selector:
        matchLabels:
          app.kubernetes.io/name: {{ include "community.name" $ }}
          app.kubernetes.io/instance: {{ $.Release.Name }}
          app.kubernetes.io/component: {{ $name }}
      template:
        metadata:
          labels:
            {{- include "community.labels" $ | nindent 12 }}
            app.kubernetes.io/component: {{ $name }}
        spec:
          serviceAccountName: {{ include "community.serviceAccountName" $ }}
          containers:
            - name: {{ $name }}
              image: "{{ $svc.image.repository }}:{{ $svc.image.tag }}"
              imagePullPolicy: {{ $svc.image.pullPolicy | default "IfNotPresent" }}
              ports:
                - name: http
                  containerPort: {{ $svc.service.port | default 8080 }}
                  protocol: TCP
              env:
                {{- /* Shared environment variables first */ -}}
                {{- range $key, $val := $.Values.env }}
                - name: {{ $key }}
                  value: {{ $val | quote }}
                {{- end }}
                {{- /* Service-specific environment variables (can override shared ones) */ -}}
                {{- range $key, $val := $svc.env }}
                - name: {{ $key }}
                  value: {{ $val | quote }}
                {{- end }}
                - name: START_KAFKA_CONSUMERS
                  value: {{ $.Values.startKafkaConsumers | default true | quote }}
                - name: INTERNAL_BASE_URL_REST
                  value: "http://{{ include "community.fullname" $ }}-rest:{{ (index $.Values.services "rest").service.port | default 6969 }}"
                - name: INTERNAL_BASE_URL_EVENTS
                  value: "http://{{ include "community.fullname" $ }}-events:{{ (index $.Values.services "events").service.port | default 6969 }}"
                - name: REDIS_ADDR
                  value: "redis-master.mt-data:6379"
                - name: REDIS_PREFIX
                  value: {{ if ne $suffix $.Release.Name }}{{ $suffix | quote }}{{ else }}{{ "community" | quote }}{{ end }}
              {{- with $svc.resources }}
              resources:
                {{- toYaml . | nindent 16 }}
              {{- end }}
  {{- end }}
{{- end }}
