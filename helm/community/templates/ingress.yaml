{{/*
Single host ingress template.
- Host taken from .Values.ingress.host.
- If release name is "community-<suffix>" the suffix is appended to the subdomain so
  host becomes "community-<suffix>.domain".
- Two routes/paths:
    /      -> rest service
    /ws    -> events service
- Kind is IngressRoute when className == "traefik", otherwise standard Ingress.
*/}}
{{- if .Values.ingress.enabled }}
{{- $isTraefik := eq (.Values.ingress.className | default "") "traefik" }}
{{- $isNginx := eq (.Values.ingress.className | default "") "nginx" }}
{{- $enableTLS := false }}
{{- if hasKey .Values.ingress.tls "enabled" }}
  {{- $enableTLS = .Values.ingress.tls.enabled }}
{{- else }}
  {{- $enableTLS = $isNginx }}
{{- end }}
{{- $baseChart := include "community.name" . }}
{{- $suffix := "" }}
{{- if hasPrefix .Release.Name (printf "%s-" $baseChart) }}
  {{- $suffix = trimPrefix (printf "%s-" $baseChart) .Release.Name }}
{{- end }}
{{- $hostBase := .Values.ingress.host }}
{{- $hostParts := splitList "." $hostBase }}
{{- $domain := join "." (slice $hostParts 1) }}
{{- $host := printf "%s.%s" .Release.Name $domain }}
{{- $restSvc := printf "%s-rest" (include "community.fullname" .) }}
{{- $eventsSvc := printf "%s-events" (include "community.fullname" .) }}
{{- $restPort := (index .Values.services "rest").service.port | default 80 }}
{{- $eventsPort := (index .Values.services "events").service.port | default 80 }}
---
{{- if $isTraefik }}
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: {{ include "community.fullname" . }}
  labels:
    {{- include "community.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  entryPoints:
    - {{ .Values.ingress.entryPoint | default "websecure" | quote }}
  routes:
    - kind: Rule
      match: "Host(`{{ $host }}`) && PathPrefix(`/ws`)"
      services:
        - name: {{ $eventsSvc }}
          port: {{ $eventsPort }}
    - kind: Rule
      match: "Host(`{{ $host }}`) && PathPrefix(`/`)"
      services:
        - name: {{ $restSvc }}
          port: {{ $restPort }}
  {{- if $enableTLS }}
  tls:
    secretName: {{ .Values.ingress.tls.secretName | default (printf "%s-tls" .Release.Name) | quote }}
  {{- end }}
{{- else }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "community.fullname" . }}
  labels:
    {{- include "community.labels" . | nindent 4 }}
  {{- if or ($enableTLS) (.Values.ingress.annotations) }}
  annotations:
    {{- if $enableTLS }}
    cert-manager.io/cluster-issuer: {{ .Values.ingress.tls.issuer | quote }}
    {{- end }}
    {{- with .Values.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  {{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className | quote }}
  {{- end }}
  {{- if $enableTLS }}
  tls:
    - hosts:
        - {{ $host | quote }}
      secretName: {{ .Values.ingress.tls.secretName | default (printf "%s-tls" .Release.Name) | quote }}
  {{- end }}
  rules:
    - host: {{ $host | quote }}
      http:
        paths:
          - path: /ws
            pathType: Prefix
            backend:
              service:
                name: {{ $eventsSvc }}
                port:
                  number: {{ $eventsPort }}
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ $restSvc }}
                port:
                  number: {{ $restPort }}
{{- end }}
{{- end }}
