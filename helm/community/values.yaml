# Default values for community.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# ---- Community multi-service configuration ----
# The chart now supports multiple workloads (REST API, events consumer, Greco events consumer).
# Each workload is configured under the `services` map. Top-level image/service keys are kept
# for backwards compatibility but are no longer used by the templates.

# Shared environment variables for all services
env:
  DATABASE_MIGRATION: "true"
  AWS_REGION: "eu-central-1"
  ENV: "staging"
  POPULATE_GAMES: "true"

services:
  rest:
    enabled: true
    replicaCount: 2
    image:
      repository: "190685083100.dkr.ecr.eu-central-1.amazonaws.com/community-v2-rest"
      tag: "arm64-test"
      pullPolicy: IfNotPresent
    service:
      type: ClusterIP
      port: 6969
    # Service-specific environment variables (merged with shared env)
    env: {}
    resources: {}

  events:
    enabled: true
    replicaCount: 1
    image:
      repository: "190685083100.dkr.ecr.eu-central-1.amazonaws.com/community-v2-events"
      tag: "arm64-test"
      pullPolicy: IfNotPresent
    service:
      type: ClusterIP
      port: 6969
    # Service-specific environment variables (merged with shared env)
    env: {}
    resources: {}

  greco_events:
    enabled: false # Only enabled in production by overriding values
    replicaCount: 1
    image:
      repository: "190685083100.dkr.ecr.eu-central-1.amazonaws.com/community-v2-greco_events"
      tag: "arm64-test"
      pullPolicy: IfNotPresent
    service:
      type: ClusterIP
      port: 6969
    # Service-specific environment variables (merged with shared env)
    # Note: This service will use shared env.ENV which can be overridden per deployment
    env: {}
    resources: {}


# Ingress definition is kept but will only target the `rest` workload by default
ingress:
  enabled: true
  className: nginx  # Use "nginx" for cert-manager TLS, "traefik" for Traefik TLS
  entryPoint: websecure  # Only used for Traefik
  host: community.mt-dev.monkeytilt.pro
  # Additional annotations (merged with automatic cert-manager annotations for nginx)
  annotations: {}
    # Example nginx annotations:
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # nginx.ingress.kubernetes.io/rate-limit: "100"
  # TLS configuration - automatically enabled for nginx ingress class
  tls:
    # enabled: true/false to force enable/disable, omit for automatic (nginx=enabled, traefik=disabled)
    issuer: letsencrypt-prod  # cert-manager ClusterIssuer name (used only for nginx)
    secretName: ""  # If empty, will auto-generate: <release-name>-tls

# ---- legacy keys below (deprecated) ----
replicaCount: 1
image:
  repository: nginx
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}
