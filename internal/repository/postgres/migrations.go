package postgres

import (
	"database/sql"
	"fmt"
	"log/slog"

	"gorm.io/gorm"
)

const (
	AnalyzeBetsTableQuery = `ANALYZE bets;`
)

func AutoMigrate(gormDB *gorm.DB) error {
	// We only use GORM to create the tables and to use our repositories.
	// The idea is to make the database setup as much ORM-agnostic as possible.
	db, err := gormDB.DB()
	if err != nil {
		return err
	}

	return autoMigrate(db, gormDB)
}

func autoMigrate(db *sql.DB, gormDB *gorm.DB) error {
	if _, err := db.Exec(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`); err != nil {
		return err
	}

	if err := createEnums(db); err != nil {
		return fmt.Errorf("create enums: %w", err)
	}

	if err := createTables(gormDB); err != nil {
		return fmt.Errorf("create tables: %w", err)
	}

	if err := createViews(db); err != nil {
		return fmt.Errorf("create views: %w", err)
	}

	if _, err := db.Exec(`CREATE UNIQUE INDEX IF NOT EXISTS users_asset_type_index ON user_assets (user_id, type);`); err != nil {
		return err
	}

	if _, err := db.Exec(`CREATE UNIQUE INDEX IF NOT EXISTS idx_bonus_drop_user ON bonus_drop_redemptions (bonus_drop_code, user_id);`); err != nil {
		return err
	}

	return nil
}

func createTables(gormDB *gorm.DB) error {
	return gormDB.AutoMigrate(
		&game{},
		&gamesVersion{},
		&user{},
		&maintainanceToast{},
		&userConfigAsset{},
		&bet{},
		&boost{},
		&userAsset{},
		&vipUserBalance{},
		&userClaimedCoins{},
		&registeredEmails{},
		&transaction{},
		&bonusConfig{},
		&userBonus{},
		&referralCampaign{},
		&commissionTracking{},
		&commissionHistory{},
		&userBonusTracking{},
		&adminCampaign{},
		&Settings{},
		&UserBonusTemplates{},
		&BonusDrop{},
		&BonusDropRedemption{},
		&TermsAndConditionTemplate{},
		&EmailCampaigns{},
	)
}

func createViews(db *sql.DB) error {
	query := `
	CREATE MATERIALIZED VIEW IF NOT EXISTS users_bets_summary AS
	WITH bets_summary AS (
		SELECT
			user_id,
			COUNT(*) AS total_bets,
			COALESCE(SUM(bet_amount), 0) AS wagered,
			COUNT(CASE WHEN multiplier > 1 THEN 1 END) AS number_of_wins,
			COUNT(CASE WHEN multiplier <= 1 THEN 1 END) AS number_of_losses
		FROM
			bets
			where bets.round_status = 'completed'
		GROUP BY
			user_id
	),
	vip_summary AS (
		SELECT
			user_id,
			COALESCE(SUM(handle), 0) AS wagered,
			COALESCE(SUM(bets_won), 0) AS number_of_wins,
			COALESCE(SUM(total_bets), 0) AS total_bets
		FROM
			vip_user_balances
		GROUP BY
			user_id
	)
	SELECT
		COALESCE(bs.user_id, vs.user_id) AS user_id,
		COALESCE(bs.total_bets, 0) + COALESCE(vs.total_bets, 0) AS total_bets,
		COALESCE(bs.wagered, 0) + COALESCE(vs.wagered, 0) AS wagered,
		COALESCE(bs.number_of_wins, 0) + COALESCE(vs.number_of_wins, 0) AS number_of_wins,
		COALESCE(bs.number_of_losses, 0) + COALESCE(vs.total_bets - vs.number_of_wins, 0) AS number_of_losses
	FROM
		bets_summary bs
	FULL OUTER JOIN
		vip_summary vs
	ON
		bs.user_id = vs.user_id;
	`
	if _, err := db.Exec(query); err != nil {
		return err
	}

	query = `CREATE UNIQUE INDEX IF NOT EXISTS idx_users_bets_summary_user_id ON users_bets_summary(user_id);`
	if _, err := db.Exec(query); err != nil {
		return err
	}

	query = `
	CREATE MATERIALIZED VIEW IF NOT EXISTS users_coins_summary AS
	with bet_coins_summary as (
		SELECT
			b.user_id as user_id,
			COALESCE(SUM(b.bet_amount * COALESCE(bo.multiplier, 1) * 0.1), 0) as coins
		FROM
			bets b
		LEFT JOIN
			boosts bo ON b.time BETWEEN bo.boost_started_at AND (bo.boost_started_at + INTERVAL '1 hour' * bo.boost_duration_hours)
			AND bo.user_id = b.user_id
		WHERE
			b.round_status = 'completed'
		group by b.user_id
	),
	vip_coins_summary AS (
		SELECT
			user_id,
			COALESCE(SUM(vip_user_balances.coins), 0) as coins
		FROM
			vip_user_balances
		GROUP BY
			user_id
	),
	user_claimed_coins_summary AS (
		SELECT
			user_id,
			COALESCE(SUM(user_claimed_coins.coins), 0) as claimed_coins
		FROM
			user_claimed_coins
		GROUP BY
			user_id
	),
	all_users AS (
		SELECT user_id FROM bet_coins_summary
		UNION
		SELECT user_id FROM vip_coins_summary
		UNION
		SELECT user_id FROM user_claimed_coins_summary
	)
	SELECT
		au.user_id,
		COALESCE(b.coins, 0) + COALESCE(vub.coins, 0) as total_coins,
		COALESCE(uccs.claimed_coins, 0) as claimed_coins,
		COALESCE(b.coins, 0) as bets_coins,
		COALESCE(vub.coins, 0) as vip_coins
	FROM
		all_users au
	LEFT JOIN
		bet_coins_summary b ON au.user_id = b.user_id
	LEFT JOIN
		vip_coins_summary vub ON au.user_id = vub.user_id
	LEFT JOIN
		user_claimed_coins_summary uccs ON au.user_id = uccs.user_id;
	`
	if _, err := db.Exec(query); err != nil {
		return err
	}

	query = `CREATE UNIQUE INDEX IF NOT EXISTS idx_users_coins_summary_user_id ON users_coins_summary(user_id);`
	if _, err := db.Exec(query); err != nil {
		return err
	}

	query = `
    CREATE MATERIALIZED VIEW IF NOT EXISTS user_wagering_summary AS
WITH user_deposits AS (
    SELECT
        user_id,
        COALESCE(SUM(amount), 0) as total_deposits
    FROM transactions
    WHERE type = 'credit'
    AND status = 'completed'
    AND category = 'payments'
    AND currency = 'USD'
    GROUP BY user_id
),
regular_bets_summary AS (
    SELECT
        user_id,
        COALESCE(SUM(CASE
            WHEN bet_type = 'CASINO' THEN bet_amount
            ELSE 0
        END), 0) as casino_wagered,
        COALESCE(SUM(CASE
            WHEN bet_type = 'SPORTS' THEN bet_amount
            ELSE 0
        END), 0) as sports_wagered,
        COALESCE(SUM(bet_amount), 0) as total_wagered
    FROM bets
    WHERE round_status = 'completed'
    GROUP BY user_id
)
SELECT
    u.external_id,
    u.id as internal_user_id,
    COALESCE(rb.casino_wagered, 0) as casino_wagered,
    COALESCE(rb.sports_wagered, 0) as sports_wagered,
    COALESCE(rb.total_wagered, 0) as total_wagered,
    COALESCE(d.total_deposits, 0) as total_deposits,
    u.created_at
FROM users u
LEFT JOIN regular_bets_summary rb ON u.id = rb.user_id
LEFT JOIN user_deposits d ON u.id = d.user_id;

-- Indexes without commission-related ones
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_wagering_summary_external_id
ON user_wagering_summary(external_id);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_internal_id
ON user_wagering_summary(internal_user_id);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_casino
ON user_wagering_summary(casino_wagered DESC);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_sports
ON user_wagering_summary(sports_wagered DESC);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_total
ON user_wagering_summary(total_wagered DESC);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_deposits
ON user_wagering_summary(total_deposits DESC);

CREATE INDEX IF NOT EXISTS idx_user_wagering_summary_created_at
ON user_wagering_summary(created_at DESC);
    `
	if _, err := db.Exec(query); err != nil {
		return fmt.Errorf("failed to create wagering summary view: %w", err)
	}

	createBetsCompletedCasinoIndexQuery := `CREATE INDEX IF NOT EXISTS idx_bets_completed_casino ON bets (time DESC) WHERE round_status = 'completed' AND bet_type = 'CASINO';`
	if _, err := db.Exec(createBetsCompletedCasinoIndexQuery); err != nil {
		return err
	}

	createBetsPayoutIndexQuery := `CREATE INDEX IF NOT EXISTS idx_bets_payout_time_type ON bets (payout, time DESC, bet_type) WHERE payout > 0;`
	if _, err := db.Exec(createBetsPayoutIndexQuery); err != nil {
		return err
	}

	if _, err := db.Exec(AnalyzeBetsTableQuery); err != nil {
		return err
	}

	if err := verifyViews(db); err != nil {
		return fmt.Errorf("view verification failed: %w", err)
	}

	return nil
}

func createEnums(db *sql.DB) error {
	query := `
		DO $$
		BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'asset_type') THEN
				CREATE TYPE asset_type AS ENUM ('avatar', 'background', 'wallet');
			END IF;
		END$$;
	`
	if _, err := db.Exec(query); err != nil {
		return err
	}

	query = `
		DO $$
		BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'asset_sub_type') THEN
				CREATE TYPE asset_sub_type AS ENUM (
					'monkey',
					'dog',
					'cat',
					'lion',
					'crocodile',
					'panther',
					'rhino',
					'snake',
					'gradient',
					'solid',
					'texture',
					'geometric',
					'minimal',
					'glass',
					'default'
				);
			END IF;
		END$$;
	`
	_, err := db.Exec(query)
	return err
}

func verifyViews(db *sql.DB) error {
	viewsToVerify := []string{
		"users_bets_summary",
		"users_coins_summary",
		"user_wagering_summary",
	}

	for _, viewName := range viewsToVerify {
		if err := verifyView(db, viewName); err != nil {
			slog.Error("View verification failed",
				"view", viewName,
				"error", err)
			return fmt.Errorf("failed to verify view %s: %w", viewName, err)
		}
	}

	return nil
}

func verifyView(db *sql.DB, viewName string) error {
	query := `
        SELECT EXISTS (
            SELECT 1
            FROM pg_matviews
            WHERE matviewname = $1
        );`

	var exists bool
	err := db.QueryRow(query, viewName).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if view exists: %w", err)
	}

	if !exists {
		return fmt.Errorf("view %s was not created successfully", viewName)
	}
	testQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s LIMIT 1;", viewName)
	var count int
	err = db.QueryRow(testQuery).Scan(&count)
	if err != nil {
		return fmt.Errorf("view %s exists but cannot be queried: %w", viewName, err)
	}
	return nil
}
