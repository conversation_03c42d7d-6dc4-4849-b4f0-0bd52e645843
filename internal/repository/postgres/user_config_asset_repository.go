package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Assert interface implementation.
var _ domain.UserConfigAssetRepository = (*UserConfigAssetRepository)(nil)

// UserConfigAssetRepository is a struct that will implement the UserConfigAssetRepository interface.
type UserConfigAssetRepository struct {
	db *gorm.DB
}

// NewUserConfigAssetRepository creates a new instance of UserConfigAssetRepository.
func NewUserConfigAssetRepository(db *gorm.DB) *UserConfigAssetRepository {
	return &UserConfigAssetRepository{db: db}
}

func (r *UserConfigAssetRepository) GetAssets(ctx context.Context) ([]domain.UserConfigAsset, error) {
	var assets []userConfigAsset
	if err := r.db.WithContext(ctx).Find(&assets).Error; err != nil {
		return nil, fmt.Errorf("find assets: %w", err)
	}

	return rowsToAssets(assets), nil
}

func (r *UserConfigAssetRepository) GetAsset(ctx context.Context, id uuid.UUID) (*domain.UserConfigAsset, error) {
	var asset userConfigAsset
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&asset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}
		return nil, err
	}

	return rowToAssetPtr(asset), nil
}

func (r *UserConfigAssetRepository) GetAssetsTypes(ctx context.Context) ([]string, error) {
	var rows []string
	tx := r.db.
		WithContext(ctx).
		Raw("SELECT unnest(enum_range(NULL::asset_type)) AS enum_value;").
		Scan(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("getting asset_types : %w", err)
	}

	return rows, nil
}

func (r *UserConfigAssetRepository) GetAssetsSubTypes(ctx context.Context) ([]string, error) {
	var rows []string
	tx := r.db.
		WithContext(ctx).
		Raw("SELECT unnest(enum_range(NULL::asset_sub_type)) AS enum_value;").
		Scan(&rows)

	if err := tx.Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("getting asset_types : %w", err)
	}

	return rows, nil
}
