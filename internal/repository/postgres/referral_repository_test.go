package postgres

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func (s *databaseSuite) TestReferralRepository_CreateUserReferralCampaign() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_create_campaign"
	username := "testuser"
	campaignName := "test_campaign"

	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	defaultRecord := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                "default",
		Username:                    username,
		RefferalCode:                "defaultcode",
		ReferredUsers:               referredUsersJSON,
		OverallCommissionPercentage: 5.0,
		CurrentCommissionPercentage: 5.0,
		LastCommissionPercentage:    5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&defaultRecord).Error
	require.NoError(s.T(), err)
	err = repository.CreateUserReferralCampaign(context.Background(), userID, username, campaignName)
	if err == nil {
		var created commissionTracking
		err = s.gormDB.Where("user_id = ? AND campaign_name = ?", userID, campaignName).First(&created).Error
		require.NoError(s.T(), err)

		require.Equal(s.T(), userID, created.UserID)
		require.Equal(s.T(), campaignName, created.CampaignName)
		require.Equal(s.T(), username, created.Username)
		require.NotEmpty(s.T(), created.RefferalCode)
		require.Equal(s.T(), defaultRecord.OverallCommissionPercentage, created.CurrentCommissionPercentage)
	}
}

func (s *databaseSuite) TestReferralRepository_CreateUserReferralCampaign_AlreadyExists() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_duplicate"
	username := "testuser"
	campaignName := "duplicate_campaign"
	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	existingRecord := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                campaignName,
		Username:                    username,
		RefferalCode:                "existingcode",
		ReferredUsers:               referredUsersJSON,
		OverallCommissionPercentage: 5.0,
		CurrentCommissionPercentage: 5.0,
		LastCommissionPercentage:    5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&existingRecord).Error
	require.NoError(s.T(), err)
	err = repository.CreateUserReferralCampaign(context.Background(), userID, username, campaignName)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrUserAlreadyHasReferralCampaign, err)
}

func (s *databaseSuite) TestReferralRepository_GetUserReferralCampaign() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_get_campaigns"
	username := "testuser"
	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	campaigns := []commissionTracking{
		{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			UserID:                      userID,
			CampaignName:                "default",
			Username:                    username,
			RefferalCode:                "defaultcode123",
			ReferredUsers:               referredUsersJSON,
			CurrentCommissionPercentage: 5.0,
			LastCalculationTime:         time.Now(),
		},
		{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: time.Now().Add(time.Hour),
				UpdatedAt: time.Now().Add(time.Hour),
			},
			UserID:                      userID,
			CampaignName:                "special",
			Username:                    username,
			RefferalCode:                "specialcode123",
			ReferredUsers:               referredUsersJSON,
			CurrentCommissionPercentage: 10.0,
			LastCalculationTime:         time.Now(),
		},
	}

	for _, campaign := range campaigns {
		err = s.gormDB.Create(&campaign).Error
		require.NoError(s.T(), err)
	}
	response, count, err := repository.GetUserReferralCampaign(context.Background(), userID, "")
	require.NoError(s.T(), err)
	require.Len(s.T(), response, 2)
	require.Equal(s.T(), 2, count)
	require.Equal(s.T(), "default", response[0].CampaignsName)
	require.Equal(s.T(), "defaultcode123", response[0].ReferralCode)
	require.Equal(s.T(), 5.0, response[0].ComissionPercentage)

	response, count, err = repository.GetUserReferralCampaign(context.Background(), userID, "special")
	require.NoError(s.T(), err)
	require.Len(s.T(), response, 1)
	require.Equal(s.T(), 1, count)
	require.Equal(s.T(), "special", response[0].CampaignsName)
	require.Equal(s.T(), "specialcode123", response[0].ReferralCode)
	require.Equal(s.T(), 10.0, response[0].ComissionPercentage)
}

func (s *databaseSuite) TestReferralRepository_DetectIfUserIsNotReferringItself() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_self_refer"
	referralCode := "selfrefcode"
	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	ownCampaign := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                "own_campaign",
		Username:                    "testuser",
		RefferalCode:                referralCode,
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&ownCampaign).Error
	require.NoError(s.T(), err)
	isNotSelfReferring, err := repository.DetectIfUserIsNotReferringItself(context.Background(), referralCode, userID)
	require.NoError(s.T(), err)
	require.False(s.T(), isNotSelfReferring) 

	differentUserID := "different_user"
	isNotSelfReferring, err = repository.DetectIfUserIsNotReferringItself(context.Background(), referralCode, differentUserID)
	require.NoError(s.T(), err)
	require.True(s.T(), isNotSelfReferring) 
}

func (s *databaseSuite) TestReferralRepository_GetParentUserIDByReferralCode() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	parentUserID := "parent_user_123"
	referralCode := "parentcode123"
	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	parentCampaign := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      parentUserID,
		CampaignName:                "parent_campaign",
		Username:                    "parentuser",
		RefferalCode:                referralCode,
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&parentCampaign).Error
	require.NoError(s.T(), err)
	retrievedParentID, err := repository.GetParentUserIDByReferralCode(context.Background(), referralCode)
	require.NoError(s.T(), err)
	require.Equal(s.T(), parentUserID, retrievedParentID)
	_, err = repository.GetParentUserIDByReferralCode(context.Background(), "nonexistent")
	require.Error(s.T(), err)
}

func (s *databaseSuite) TestReferralRepository_CheckIfUserAlreadyUsedTheCode() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	parentUserID := "parent_user_check"
	referralCode := "checkcode123"
	referredUserID := "referred_user_123"
	referredUsers := []domain.ReferredUser{
		{
			UserID:                   referredUserID,
			Username:                 "referreduser",
			CreatedAt:                time.Now(),
			LastCasinoWageringAmount: 0,
			LastSportsWageringAmount: 0,
			LastCommission:           0,
			TotalWagered:             0,
			TotalCommission:          0,
		},
	}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	campaign := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      parentUserID,
		CampaignName:                "check_campaign",
		Username:                    "parentuser",
		RefferalCode:                referralCode,
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&campaign).Error
	require.NoError(s.T(), err)

	hasUsed, err := repository.CheckIfUserAlreadyUsedTheCode(context.Background(), referralCode, referredUserID)
	require.NoError(s.T(), err)
	require.True(s.T(), hasUsed)

	newUserID := "new_user_123"
	hasUsed, err = repository.CheckIfUserAlreadyUsedTheCode(context.Background(), referralCode, newUserID)
	require.NoError(s.T(), err)
	require.False(s.T(), hasUsed)
}

func (s *databaseSuite) TestReferralRepository_DeleteCampaigns() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_delete"
	campaignName := "deletable_campaign"

	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	deletableCampaign := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                campaignName,
		Username:                    "testuser",
		RefferalCode:                "deletecode",
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&deletableCampaign).Error
	require.NoError(s.T(), err)

	err = repository.DeleteCampaigns(context.Background(), userID, campaignName)
	require.NoError(s.T(), err)

	var count int64
	s.gormDB.Model(&commissionTracking{}).Where("user_id = ? AND campaign_name = ?", userID, campaignName).Count(&count)
	require.Equal(s.T(), int64(0), count)
}

func (s *databaseSuite) TestReferralRepository_DeleteCampaigns_WithReferredUsers() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_delete_with_users"
	campaignName := "campaign_with_users"

	referredUsers := []domain.ReferredUser{
		{
			UserID:   "referred_user_123",
			Username: "referreduser",
		},
	}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	campaignWithUsers := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                campaignName,
		Username:                    "testuser",
		RefferalCode:                "nodelete",
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&campaignWithUsers).Error
	require.NoError(s.T(), err)

	err = repository.DeleteCampaigns(context.Background(), userID, campaignName)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrDeleleCampaignWithReferredUsers, err)

	var count int64
	s.gormDB.Model(&commissionTracking{}).Where("user_id = ? AND campaign_name = ?", userID, campaignName).Count(&count)
	require.Equal(s.T(), int64(1), count)
}

func (s *databaseSuite) TestReferralRepository_CreateAdminCampaign() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	referralCode := "ADMIN123"
	rewardAmount := 100.0
	codeUsageLimit := 50

	err := repository.CreateAdminCampaign(context.Background(), referralCode, rewardAmount, codeUsageLimit)
	require.NoError(s.T(), err)

	var created adminCampaign
	err = s.gormDB.Where("refferal_code = ?", referralCode).First(&created).Error
	require.NoError(s.T(), err)

	require.Equal(s.T(), referralCode, created.RefferalCode)
	require.Equal(s.T(), rewardAmount, created.RewardAmount)
	require.Equal(s.T(), codeUsageLimit, created.UsageLimit)
	require.Equal(s.T(), 0, created.NumberOfUsage)
	require.NotEmpty(s.T(), created.ID)

	err = repository.CreateAdminCampaign(context.Background(), referralCode, rewardAmount, codeUsageLimit)
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "admin campaign already exists")
}

func (s *databaseSuite) TestReferralRepository_CheckIfCodeIsfromAdminCampaign() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	referralCode := "ADMINCHECK"
	rewardAmount := 50.0
	usageLimit := 2
	userID := "test_user_admin"

	userIds := []string{}
	userIdsJSON, err := json.Marshal(userIds)
	require.NoError(s.T(), err)

	adminCamp := adminCampaign{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		RefferalCode:  referralCode,
		RewardAmount:  rewardAmount,
		UsageLimit:    usageLimit,
		NumberOfUsage: 0,
		UserIds:       userIdsJSON,
	}
	err = s.gormDB.Create(&adminCamp).Error
	require.NoError(s.T(), err)

	isAdmin, reward, err := repository.CheckIfCodeIsfromAdminCampaign(context.Background(), referralCode, userID)
	require.NoError(s.T(), err)
	require.True(s.T(), isAdmin)
	require.Equal(s.T(), rewardAmount, reward)

	isAdmin, reward, err = repository.CheckIfCodeIsfromAdminCampaign(context.Background(), referralCode, userID)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrCodeUsed, err)
	require.False(s.T(), isAdmin)
	require.Equal(s.T(), 0.0, reward)

	userID2 := "test_user_admin_2"
	isAdmin, reward, err = repository.CheckIfCodeIsfromAdminCampaign(context.Background(), referralCode, userID2)
	require.NoError(s.T(), err)
	require.True(s.T(), isAdmin)
	require.Equal(s.T(), rewardAmount, reward)

	userID3 := "test_user_admin_3"
	isAdmin, reward, err = repository.CheckIfCodeIsfromAdminCampaign(context.Background(), referralCode, userID3)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrCodeLimitReached, err)
	require.False(s.T(), isAdmin)
	require.Equal(s.T(), 0.0, reward)

	isAdmin, reward, err = repository.CheckIfCodeIsfromAdminCampaign(context.Background(), "NONEXISTENT", userID)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrRecordNotFound, err)
	require.False(s.T(), isAdmin)
	require.Equal(s.T(), 0.0, reward)
}

func (s *databaseSuite) TestReferralRepository_UpdateUserDefaultCommissionPercentage() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_commission_update"
	newCommissionPercentage := "7.5"
	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	trackingRecord := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                "default",
		Username:                    "testuser",
		RefferalCode:                "testcode",
		ReferredUsers:               referredUsersJSON,
		OverallCommissionPercentage: 5.0,
		CurrentCommissionPercentage: 5.0,
		LastCalculationTime:         time.Now(),
	}
	err = s.gormDB.Create(&trackingRecord).Error
	require.NoError(s.T(), err)

	err = repository.UpdateUserDefaultCommissionPercentage(userID, newCommissionPercentage)
	require.NoError(s.T(), err)

	var updated commissionTracking
	err = s.gormDB.Where("user_id = ?", userID).First(&updated).Error
	require.NoError(s.T(), err)
	require.Equal(s.T(), 7.5, updated.OverallCommissionPercentage)

	err = repository.UpdateUserDefaultCommissionPercentage(userID, "invalid")
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "failed to parse commission percentage")

	err = repository.UpdateUserDefaultCommissionPercentage("nonexistent", "10.0")
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "no records updated")
}

func (s *databaseSuite) TestReferralRepository_IsUserInAnyReferralCampaign() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	parentUserID := "parent_user_campaign_check"
	referredUserID := "referred_user_campaign_check"
	referredUsers := []domain.ReferredUser{
		{
			UserID:   referredUserID,
			Username: "referreduser",
		},
	}
	referredUsersJSON, err := json.Marshal(referredUsers)
	require.NoError(s.T(), err)

	campaign := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:        parentUserID,
		CampaignName:  "test_campaign",
		Username:      "parentuser",
		RefferalCode:  "campaigncode",
		ReferredUsers: referredUsersJSON,
	}
	err = s.gormDB.Create(&campaign).Error
	require.NoError(s.T(), err)
	isInCampaign, parentID, err := repository.IsUserInAnyReferralCampaign(context.Background(), referredUserID)
	require.NoError(s.T(), err)
	require.True(s.T(), isInCampaign)
	require.Equal(s.T(), parentUserID, parentID)
	newUserID := "new_user_not_in_campaign"
	isInCampaign, parentID, err = repository.IsUserInAnyReferralCampaign(context.Background(), newUserID)
	require.NoError(s.T(), err)
	require.False(s.T(), isInCampaign)
	require.Empty(s.T(), parentID)
}

func (s *databaseSuite) TestReferralRepository_GetCommissionHistory() {
	repository := NewReferralRepository(s.gormDB, nil, nil, nil)

	userID := "test_user_commission_history"
	histories := []commissionHistory{
		{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: time.Now().Add(-2 * time.Hour),
				UpdatedAt: time.Now().Add(-2 * time.Hour),
			},
			UserID:   userID,
			Currency: "USD",
			Claimed:  100.0,
		},
		{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: time.Now().Add(-1 * time.Hour),
				UpdatedAt: time.Now().Add(-1 * time.Hour),
			},
			UserID:   userID,
			Currency: "USD",
			Claimed:  50.0,
		},
		{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			UserID:   userID,
			Currency: "USD",
			Claimed:  75.0,
		},
	}

	for _, history := range histories {
		err := s.gormDB.Create(&history).Error
		require.NoError(s.T(), err)
	}

	response, err := repository.GetCommissionHistory(context.Background(), userID, 0, 2)
	require.NoError(s.T(), err)

	require.Equal(s.T(), int64(3), response.Total)
	require.Equal(s.T(), 0, response.Offset)
	require.Equal(s.T(), 2, response.Limit)
	require.Len(s.T(), response.CommissionHistory, 2)
	require.Equal(s.T(), 75.0, response.CommissionHistory[0].ClaimedAmount)
	require.Equal(s.T(), 50.0, response.CommissionHistory[1].ClaimedAmount)
	require.Equal(s.T(), "USD", response.CommissionHistory[0].Currency)
	response, err = repository.GetCommissionHistory(context.Background(), userID, 1, 2)
	require.NoError(s.T(), err)
	require.Len(s.T(), response.CommissionHistory, 2)
	require.Equal(s.T(), 50.0, response.CommissionHistory[0].ClaimedAmount)
	require.Equal(s.T(), 100.0, response.CommissionHistory[1].ClaimedAmount)

	response, err = repository.GetCommissionHistory(context.Background(), userID, 0, 0)
	require.NoError(s.T(), err)
	require.Len(s.T(), response.CommissionHistory, 3)
	response, err = repository.GetCommissionHistory(context.Background(), "nonexistent", 0, 10)
	require.NoError(s.T(), err)
	require.Equal(s.T(), int64(0), response.Total)
	require.Len(s.T(), response.CommissionHistory, 0)
}