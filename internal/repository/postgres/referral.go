package postgres

import (
	"encoding/json"
	"time"
)

type referralCampaign struct {
	UUIDKeyModel
	UserID                      string          `gorm:"type:text;index;not null"`
	Username                    string          `gorm:"type:text;index;not null"`
	ParentID                    string          `gorm:"type:text;index"`
	Campaigns                   json.RawMessage `gorm:"type:jsonb"`
	ReferredUsers               json.RawMessage `gorm:"type:jsonb"`
	ClaimedCommission           float64         `gorm:"type:numeric"`
	LastCalculationTime         time.Time       `gorm:"type:timestamp with time zone"`
	LastCommissionPercentage    float64         `gorm:"type:numeric"`
	DefaultCommissionPercentage float64         `gorm:"type:numeric"`
	LastWageringAmount          float64         `gorm:"type:numeric"`
}

func (r *referralCampaign) TableName() string {
	return "referral_campaigns"
}

type commissionTracking struct {
	UUIDKeyModel
	UserID                      string          `gorm:"type:text;index;not null"`
	Username                    string          `gorm:"type:text;index"`
	ParentID                    string          `gorm:"type:text;index"`
	CampaignName                string          `gorm:"type:text;index;not null"`
	RefferalCode                string          `gorm:"type:text;index;not null"`
	ReferredUsers               json.RawMessage `gorm:"type:jsonb"`
	CurrentCommissionPercentage float64         `gorm:"type:numeric"`
	LastCommissionPercentage    float64         `gorm:"type:numeric"`
	OverallCommissionPercentage float64         `gorm:"type:numeric"`
	ClaimedCommission           float64         `gorm:"type:numeric"`
	EligibleCommission          float64         `gorm:"type:numeric"`
	EligibleToClaim             bool            `gorm:"type:boolean;default:false"`
	TotalCommission             float64         `gorm:"type:numeric"`
	LastCalculationTime         time.Time       `gorm:"type:timestamp with time zone"`
	RateChangeTime              time.Time       `gorm:"type:timestamp with time zone"`
	LastCasinoWageringAmount    float64         `gorm:"type:numeric;default:0"`
	LastSportsWageringAmount    float64         `gorm:"type:numeric;default:0"`
}

func (c *commissionTracking) TableName() string {
	return "commission_tracking"
}

type adminCampaign struct {
	UUIDKeyModel
	CampaignName  string          `gorm:"type:text;index;not null"`
	RefferalCode  string          `gorm:"type:text;index;not null"`
	RewardAmount  float64         `gorm:"type:numeric"`
	UsageLimit    int             `gorm:"type:integer"`
	NumberOfUsage int             `gorm:"type:integer"`
	UserIds       json.RawMessage `gorm:"type:jsonb"`
}

func (a *adminCampaign) TableName() string {
	return "admin_campaigns"
}

type commissionHistory struct {
	UUIDKeyModel
	UserID   string  `gorm:"type:text;index;not null"`
	Currency string  `gorm:"type:text;index;not null"`
	Claimed  float64 `gorm:"type:numeric"`
}

func (c *commissionHistory) TableName() string {
	return "commission_history"
}