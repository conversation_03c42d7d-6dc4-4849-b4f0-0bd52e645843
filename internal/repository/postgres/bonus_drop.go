package postgres

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
)

type BonusDrop struct {
	UUIDKeyModel
	Title           string     `gorm:"type:varchar(255);not null"`
	StartDate       time.Time  `gorm:"not null"`
	EndDate         *time.Time `gorm:"default:null"`
	Code            string     `gorm:"type:varchar(50);uniqueIndex;not null"`
	TotalDropLimit  int        `gorm:"not null"`
	MaxParticipants int        `gorm:"not null"`
	CodeValue       int        `gorm:"not null"`
	WagerRequired   float64    `gorm:"type:decimal(10,2);not null"`
	WagerDays       int        `gorm:"not null"`
	Status          string     `gorm:"type:varchar(20);default:'draft'"`
	CurrentRedeems  int        `gorm:"default:0"`
}

type BonusDropRedemption struct {
	UUIDKeyModel
	BonusDropCode string `gorm:"type:varchar(50);index;not null"`
	UserID        string `gorm:"type:text;index;not null"`
	Username      string `gorm:"type:text;not null"`
	Amount        int    `gorm:"not null"`
	Status        string `gorm:"type:varchar(20);default:'completed'"`
}

func (BonusDrop) TableName() string {
	return "bonus_drops"
}

func (BonusDropRedemption) TableName() string {
	return "bonus_drop_redemptions"
}

func (b *BonusDrop) IsExpired() bool {
	if b.EndDate == nil {
		return false
	}
	return time.Now().UTC().After(*b.EndDate)
}

func (b *BonusDrop) IsActive() bool {
	return b.Status == "active"
}

func (b *BonusDrop) BeforeSave(*gorm.DB) error {

	if b.CurrentRedeems >= b.MaxParticipants {
		b.Status = "completed"
	}

	return nil
}

func bonusDropToRow(bonusDrop domain.BonusDrop) BonusDrop {
	return BonusDrop{
		Title:           bonusDrop.Title,
		StartDate:       bonusDrop.StartDate,
		EndDate:         bonusDrop.EndDate,
		Code:            bonusDrop.Code,
		TotalDropLimit:  bonusDrop.TotalDropLimit,
		MaxParticipants: bonusDrop.MaxParticipants,
		CodeValue:       bonusDrop.CodeValue,
		WagerRequired:   bonusDrop.WagerRequired,
		WagerDays:       bonusDrop.WagerDays,
		Status:          bonusDrop.Status,
	}
}

func bonusRedemptionToDomain(bonusDropRedemption BonusDropRedemption) domain.RedemptionUser {
	return domain.RedemptionUser{
		BonusDropCode: bonusDropRedemption.BonusDropCode,
		UserID:        bonusDropRedemption.UserID,
		Username:      bonusDropRedemption.Username,
		Amount:        bonusDropRedemption.Amount,
		Status:        bonusDropRedemption.Status,
	}
}
