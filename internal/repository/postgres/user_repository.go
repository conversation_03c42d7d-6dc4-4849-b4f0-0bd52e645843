package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.UserRepository = (*UserRepository)(nil)

// UserRepository is a struct that will implement the UserRepository interface.
type UserRepository struct {
	db       *gorm.DB
	vipTiers domain.VIPTiers
	wagering domain.ElantilWageringClient
}

// NewUserRepository creates a new instance of UserRepository.
func NewUserRepository(db *gorm.DB, vipTiers domain.VIPTiers, wagering domain.ElantilWageringClient) *UserRepository {
	return &UserRepository{db: db, vipTiers: vipTiers, wagering: wagering}
}

// UpsertUser updates a user or creates it if it doesn't exist.
func (r *UserRepository) UpsertUser(ctx context.Context, u *domain.User) (*domain.User, error) {
	var existingUser user
	result := r.db.WithContext(ctx).Where("external_id = ?", u.ExternalID).First(&existingUser)
	isNewUser := result.Error == gorm.ErrRecordNotFound

	fmt.Printf("UpsertUser: Processing new user. IsNewUser: %v, UserID: %s\n", isNewUser, u.ExternalID)

	row := userToRowForUpsert(*u)
	if isNewUser {
		row.ElantilVipStatus = "Baboon"
	}

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&user{}).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "external_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"company_user",
					"first_name",
					"join_date",
					"last_name",
					"last_updated_on",
					"profile_status",
					"updated_at",
					"user_name",
					"email",
				}),
			}).
			Create(&row).Error; err != nil {
			fmt.Printf("UpsertUser: Failed to upsert user: %v\n", err)
			return fmt.Errorf("upsert user: %w", err)
		}

		registeredEmailRow := registeredEmails{
			Email:      u.Email,
			Registered: true,
		}

		if err := tx.Model(&registeredEmails{}).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "email"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"registered",
				}),
			}).
			Create(&registeredEmailRow).Error; err != nil {
			return fmt.Errorf("upsert registeredEmail: %w", err)
		}
		if isNewUser {
			fmt.Printf("UpsertUser: About to create default campaign for user: %s\n", row.ExternalID)
			referralCode := uuid.New().String()[:8]
			if err := r.createCommissionTracking(ctx, tx, row.ExternalID, u.UserName, "default", referralCode); err != nil {
				fmt.Printf("UpsertUser: Failed to create commission tracking: %v\n", err)
			}
			fmt.Printf("UpsertUser: Successfully created default campaign for user %s\n", row.ExternalID)
		}
		return nil
	})

	if err != nil {
		fmt.Printf("UpsertUser: Transaction failed: %v\n", err)
		if unwrapped := errors.Unwrap(err); unwrapped != nil {
			fmt.Printf("UpsertUser: Root cause: %v\n", unwrapped)
		}
		return nil, fmt.Errorf("upsert user transaction: %w", err)
	}

	fmt.Printf("UpsertUser: Successfully completed for user: %s\n", u.ExternalID)
	return rowToUserPtr(row), nil
}

func (r *UserRepository) UpdateUserElantilVIPStatus(externalID string, vipStatus string) error {
	err := r.db.WithContext(context.Background()).Model(&user{}).
		Where("external_id = ?", externalID).
		Update("elantil_vip_status", vipStatus).Error
	if err != nil {
		return fmt.Errorf("update user Elantil VIP status: %w", err)
	}
	return nil
}

func (r *UserRepository) GetExternalIDsByVIPStatus(ctx context.Context, vipStatus string) ([]string, error) {
	var externalIDs []string

	err := r.db.WithContext(ctx).
		Model(&user{}).
		Where("elantil_vip_status = ?", vipStatus).
		Pluck("external_id", &externalIDs).
		Error

	if err != nil {
		return nil, fmt.Errorf("fetch external IDs by VIP status: %w", err)
	}

	return externalIDs, nil
}

// GetUserByID retrieves a user by their ID from the database.
func (r *UserRepository) GetUserByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	var row userWithStats

	tx := r.db.
		WithContext(ctx).
		Preload("UserAsset").
		Preload("UserAsset.UserConfigAsset").
		Model(&user{}).
		Select(`users.* ,
		bs.number_of_wins AS number_of_wins,
		bs.number_of_losses AS number_of_losses,
		bs.wagered AS wagered,
		bs.total_bets AS total_bets,
		cs.total_coins AS total_coins,
		cs.claimed_coins AS claimed_coins`).
		Joins("LEFT JOIN users_bets_summary bs ON users.id = bs.user_id").
		Joins("LEFT JOIN users_coins_summary cs ON users.id = cs.user_id ").
		Where("id = ?", id).First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find user by ID '%v': %w", id, err)
	}

	return r.rowToUserWithStatsPtr(row), nil
}

// GetUserByID retrieves a user by their userName from the database.
func (r *UserRepository) GetUserByUserName(ctx context.Context, userName string) (*domain.User, error) {
	var row userWithStats

	tx := r.db.
		WithContext(ctx).
		Preload("UserAsset").
		Preload("UserAsset.UserConfigAsset").
		Model(&user{}).
		Select(`users.* ,
		bs.number_of_wins AS number_of_wins,
		bs.number_of_losses AS number_of_losses,
		bs.wagered AS wagered,
		bs.total_bets AS total_bets,
		cs.total_coins AS total_coins,
		cs.claimed_coins AS claimed_coins`).
		Joins("LEFT JOIN users_bets_summary bs ON users.id = bs.user_id").
		Joins("LEFT JOIN users_coins_summary cs ON users.id = cs.user_id ").
		Where("user_name = ?", userName).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find user by name '%v': %w", userName, err)
	}

	return r.rowToUserWithStatsPtr(row), nil
}

func (r *UserRepository) GetUserAndGameByExternalId(ctx context.Context, userId, gameId string) (domain.UserGame, error) {

	query := `
	SELECT
		u.id, u.user_name, u.elantil_vip_status, u.ghost_mode,
		g.id, g.cms_game_id, g.external_id, g.name, g.slug, g.thumbnail_id
	FROM users u
	LEFT JOIN games g ON g.external_id = ?
	WHERE u.external_id = ?`

	var result domain.UserGame
	if err := r.db.WithContext(ctx).Raw(query, gameId, userId).Scan(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.UserGame{}, domain.ErrResourceNotFound
		}
		return domain.UserGame{}, fmt.Errorf("find user and game by external ID '%v' and game ID '%v': %w", userId, gameId, err)
	}

	return result, nil
}

func (r *UserRepository) GetRankedUserByExternalID(ctx context.Context, id string) (*domain.RankedUser, error) {
	var row rankedUser

	userRow, err := r.getUserByExternalID(ctx, id)
	if err != nil {
		return nil, err
	}

	query := r.db.
		WithContext(ctx).
		Model(&user{}).
		Select(`users.*,
		bs.number_of_wins AS number_of_wins,
		bs.number_of_losses AS number_of_losses,
		bs.wagered AS wagered,
		bs.total_bets AS total_bets,
		cs.total_coins AS total_coins,
		cs.claimed_coins AS claimed_coins,
		RANK() OVER (ORDER BY COALESCE( cs.total_coins, 0) DESC, users.join_date ASC) AS rank `).
		Joins("LEFT JOIN users_bets_summary bs ON users.id = bs.user_id").
		Joins("LEFT JOIN users_coins_summary cs ON users.id = cs.user_id ")

	if !userRow.CompanyUser {
		query = query.Where("company_user = false")
	}

	tx := r.db.
		WithContext(ctx).
		Table("(?) as u", query).
		Preload("UserAsset").
		Preload("UserAsset.UserConfigAsset").
		Where("external_id = ?", id).
		Order("rank").
		Find(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find ranked user by external ID '%v': %w", id, err)
	}

	return r.rowToRankedUserInternal(row), nil
}

func (r *UserRepository) GetUserByExternalID(ctx context.Context, id string) (*domain.User, error) {
	row, err := r.getUserByExternalID(ctx, id)
	if err != nil {
		return nil, err
	}

	return rowToUserPtr(*row), nil
}

func (r *UserRepository) getUserByExternalID(ctx context.Context, id string) (*user, error) {
	var row user

	tx := r.db.
		WithContext(ctx).
		Where("external_id = ?", id).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find user by external ID '%v': %w", id, err)
	}

	return &row, nil
}

func (r *UserRepository) UpdateUserByID(ctx context.Context, u *domain.User) error {
	row := userToRowForUpdate(*u)

	tx := r.db.
		WithContext(ctx).
		Model(&user{}).
		Where("id = ?", row.ID).
		Select(
			"FactorSID",
			"GhostMode",
			"HideAllStats",
			"HideTournamentStats",
		).
		Updates(row)

	if err := tx.Error; err != nil {
		return fmt.Errorf("update user by ID '%v': %w", row.ID, err)
	}

	for _, asset := range u.UserAssets {
		rowUserAssetConfig := userAsset{
			UserID:            row.ID,
			Type:              asset.Type,
			UserConfigAssetID: asset.UserConfigAsset.ID,
		}
		tx := r.db.
			WithContext(ctx).
			Model(&userAsset{}).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "user_id"}, {Name: "type"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"user_config_asset_id",
				}),
			}).
			Create(&rowUserAssetConfig)

		if err := tx.Error; err != nil {
			return fmt.Errorf("update user asset by ID '%v': %w", row.ID, err)
		}
	}

	return nil
}

func (r *UserRepository) GetRankedUsers(
	ctx context.Context,
	params *domain.GetUserParams,
) (*domain.RankedUsers, error) {
	var rows []rankedUser
	var count int64

	var rankedParam string
	switch params.OrderBy {
	case "total_bets":
		rankedParam = "bs.total_bets"
	case "total_coins":
		rankedParam = "cs.total_coins"
	case "wagered":
		rankedParam = "bs.wagered"
	default:
		return nil, fmt.Errorf("invalid order by param: %v", params.OrderBy)
	}

	tx := r.db.
		WithContext(ctx).
		Model(&user{}).
		Preload("UserAsset").
		Preload("UserAsset.UserConfigAsset").
		Select(`users.*,
		bs.number_of_wins AS number_of_wins,
		bs.number_of_losses AS number_of_losses,
		bs.wagered AS wagered,
		bs.total_bets AS total_bets,
		cs.total_coins AS total_coins,
		cs.claimed_coins AS claimed_coins,
		RANK() OVER (ORDER BY COALESCE( ` + rankedParam + ` , 0) DESC, users.join_date ASC) AS rank `).
		Joins("LEFT JOIN users_bets_summary bs ON users.id = bs.user_id").
		Joins("LEFT JOIN users_coins_summary cs ON users.id = cs.user_id ").
		Where("users.company_user = false").
		Order("rank").
		Count(&count).
		Limit(params.PageSize).
		Offset(calculateOffset(params.PagingParams)).
		Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find ranked users: %w", err)
	}

	return r.rowsToRankedUsers(rows, count, params.PageNumber, params.PageSize), nil
}

// Retrieve ranked users Doing a temporary table by the orderBy key sended.
func (r *UserRepository) GetRankedUsersByExternalID(
	ctx context.Context,
	id string,
	retrieveAmount int,
	orderBy string,
) ([]domain.RankedUser, error) {
	var rows []rankedUser

	var rankedParam string
	switch orderBy {
	case "total_bets":
		rankedParam = "bs.total_bets"
	case "total_coins":
		rankedParam = "cs.total_coins"
	case "wagered":
		rankedParam = "bs.wagered"
	default:
		return nil, fmt.Errorf("invalid order by param: %v", orderBy)
	}

	query := `
    WITH ranked_users AS (
        SELECT
            id,
            user_name,
            bs.number_of_wins AS number_of_wins,
            bs.number_of_losses AS number_of_losses,
            bs.wagered AS wagered,
            bs.total_bets AS total_bets,
            cs.total_coins AS total_coins,
            cs.claimed_coins AS claimed_coins,
            external_id,
            profile_status,
            hide_all_stats,
            hide_tournament_stats,
            RANK() OVER (ORDER BY COALESCE( ` + rankedParam + ` , 0) DESC, join_date ASC) AS rank
        FROM
            users
        LEFT JOIN users_bets_summary bs ON users.id = bs.user_id
        LEFT JOIN users_coins_summary cs ON users.id = cs.user_id
        WHERE
            company_user = false
    )
    SELECT
        ru.id,
        ru.user_name,
        ru.wagered,
        ru.external_id,
        ru.rank,
        ru.number_of_wins,
        ru.number_of_losses,
        ru.profile_status,
        ru.total_bets,
        ru.claimed_coins,
        ru.total_coins,
        ru.hide_all_stats,
        ru.hide_tournament_stats
    FROM
        ranked_users ru
    WHERE
        ru.rank >= (
            SELECT COALESCE(rank - ?, 1)
            FROM ranked_users
            WHERE external_id = ?
        )
        AND ru.rank <= (
            SELECT rank + ?
            FROM ranked_users
            WHERE external_id = ?
        )
    ORDER BY ru.rank;`

	if err := r.db.
		WithContext(ctx).
		Raw(query, retrieveAmount, id, retrieveAmount, id).
		Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("find ranked user by external ID '%v': %w", id, err)
	}

	return rowsToRankedUsersUnpaged(rows, r.vipTiers), nil
}

func (r *UserRepository) GetUsersByExternalIDs(ctx context.Context, externalIDs []string) ([]domain.GetAllUsersResponse, error) {
	var users []struct {
		ID               string
		ExternalID       string
		UserName         string
		ElantilVipStatus string `gorm:"column:elantil_vip_status"`
	}

	err := r.db.WithContext(ctx).
		Model(&user{}).
		Select("id, external_id, user_name, elantil_vip_status").
		Where("external_id IN ?", externalIDs).
		Find(&users).Error

	if err != nil {
		return nil, fmt.Errorf("error fetching users: %w", err)
	}

	result := make([]domain.GetAllUsersResponse, len(users))
	for i, u := range users {
		result[i] = domain.GetAllUsersResponse{
			ID:               uuid.MustParse(u.ID),
			ExternalID:       u.ExternalID,
			UserName:         u.UserName,
			ElantilVIpStatus: u.ElantilVipStatus,
		}
	}

	return result, nil
}

// SaveRegisteredEmail save a registered email in the database using GORM.
func (r *UserRepository) SaveRegisteredEmail(
	ctx context.Context,
	email string,
	emailMarketing bool,
) error {
	registeredEmail := registeredEmails{
		Email:          email,
		EmailMarketing: emailMarketing,
		Registered:     false,
	}

	tx := r.db.WithContext(ctx).Model(&registeredEmails{}).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "email"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"email_marketing",
			}),
		}).
		Create(&registeredEmail)

	if err := tx.Error; err != nil {
		return err
	}

	return nil
}

func (r *UserRepository) GetRegisteredEmail(
	ctx context.Context,
	email string,
) (*domain.RegisteredEmail, error) {
	var row registeredEmails
	tx := r.db.
		WithContext(ctx).
		Where("email = ?", email).
		First(&row)
	if err := tx.Error; err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("finding registered email '%s': %w", email, err)
	}

	return rowToRegisterEmail(&row), nil
}

// RefreshMaterializedViews refreshes the materialized views users_coins_summary, users_bets_summary, user_wagering_summary.
func (r *UserRepository) RefreshMaterializedViews(ctx context.Context) error {
	err := r.db.WithContext(ctx).Exec("REFRESH MATERIALIZED VIEW users_coins_summary").Error
	if err != nil {
		return fmt.Errorf("failed to refresh users_coins_summary: %w", err)
	}

	err = r.db.WithContext(ctx).Exec("REFRESH MATERIALIZED VIEW users_bets_summary").Error
	if err != nil {
		return fmt.Errorf("failed to refresh users_bets_summary: %w", err)
	}

	err = r.db.WithContext(ctx).Exec("REFRESH MATERIALIZED VIEW user_wagering_summary").Error
	if err != nil {
		return fmt.Errorf("failed to refresh user_wagering_summary: %w", err)
	}

	return nil
}

func (r *UserRepository) rowToUserWithStatsPtr(row userWithStats) *domain.User {
	return rowToUserWithStatsPtr(row, r.vipTiers)
}

func (r *UserRepository) rowToRankedUserPtr(row rankedUser) *domain.RankedUser {
	return rowToRankedUserPtr(row, r.vipTiers)
}

func (r *UserRepository) rowToRankedUserInternal(row rankedUser) *domain.RankedUser {
	return rowToRankedUserPtrInternal(row, r.vipTiers)
}

func (r *UserRepository) rowsToRankedUsers(
	rows []rankedUser,
	totalCount int64,
	pageNum, pageSize int,
) *domain.RankedUsers {
	return rowsToRankedUsers(rows, totalCount, pageNum, pageSize, r.vipTiers)
}

func (r *UserRepository) GetWinnerOfTheMonth(ctx context.Context) ([]domain.WinnerDetails, error) {
	var rows []domain.WinnerDetails
	query := `WITH monthly_wins AS (
    SELECT
        CASE
            WHEN u.ghost_mode = true THEN 'Hidden'
            ELSE u.user_name
        END as user_name,
        u.id as user_id,
        u.elantil_vip_status as vip_status,
        (b.payout - b.bet_amount) as win_amount,
        g.name as game_name,
        g.cms_game_id,
        b.payout,
        b.bet_amount,
        g.thumbnail_id,
        TO_CHAR(DATE_TRUNC('month', b.created_at), 'YYYY-MM') as month,
        TO_CHAR(b.created_at, 'YYYY-MM-DD HH24:MI:SS') as win_timestamp,
        ROW_NUMBER() OVER (ORDER BY (b.payout - b.bet_amount) DESC) as rank
    FROM
        bets b
    JOIN
        users u ON b.user_id = u.id
    LEFT JOIN
        games g ON b.game_id = g.id
    WHERE
        b.round_status = 'completed'
        AND b.created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
        AND b.created_at < DATE_TRUNC('month', CURRENT_DATE)
        AND b.payout > b.bet_amount  -- ensure we're only looking at wins
)
SELECT
    user_id,
    user_name,
    vip_status as elantil_vip_status,
    win_amount,
    game_name,
    cms_game_id,
    bet_amount,
    payout,
    thumbnail_id,
    month,
    win_timestamp
FROM
    monthly_wins
WHERE
    rank = 1;`

	if err := r.db.
		WithContext(ctx).
		Raw(query).
		Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("find winner of the month: %w", err)
	}

	if len(rows) == 0 {
		return nil, domain.ErrResourceNotFound
	}

	return rows, nil
}

func (r *UserRepository) StreamAllUsers(ctx context.Context, userChan chan<- domain.GetAllUsersResponse, needsVipStatus bool) error {
	defer slog.Info("StreamAllUsers completed")

	const batchSize = 100
	var offset int64 = 0
	var totalProcessed int64 = 0

	for {
		slog.Info("Fetching batch of users",
			slog.Int64("offset", offset),
			slog.Int("batchSize", batchSize))

		query := r.db.WithContext(ctx).
			Model(&user{}).
			Select("id, external_id, user_name")

		if needsVipStatus {
			query = query.Where("elantil_vip_status != ?", "Baboon")
		}

		var users []struct {
			ID         string
			ExternalID string `gorm:"column:external_id"`
			UserName   string `gorm:"column:user_name"`
		}

		if err := query.Limit(batchSize).
			Offset(int(offset)).
			Find(&users).Error; err != nil {
			slog.Error("Error fetching users",
				slog.Any("error", err),
				slog.Int64("offset", offset))
			return fmt.Errorf("find users: %w", err)
		}

		if len(users) == 0 {
			slog.Info("No more users to fetch",
				slog.Int64("totalProcessed", totalProcessed))
			break
		}

		processedUsers := make(map[string]bool)

		for _, u := range users {
			if processedUsers[u.ExternalID] {
				slog.Error("Duplicate user detected",
					"externalID", u.ExternalID,
					"username", u.UserName)
				continue
			}

			processedUsers[u.ExternalID] = true

			user := domain.GetAllUsersResponse{
				ID:         uuid.MustParse(u.ID),
				ExternalID: u.ExternalID,
				UserName:   u.UserName,
			}

			select {
			case userChan <- user:
				totalProcessed++
				slog.Debug("User sent to channel",
					"userName", user.UserName,
					"externalID", user.ExternalID)
			case <-ctx.Done():
				slog.Info("Context cancelled, stopping stream",
					"totalProcessed", totalProcessed)
				return ctx.Err()
			}
		}

		offset += int64(len(users))
		slog.Info("Batch processed",
			"offset", offset,
			"totalProcessed", totalProcessed)
	}

	slog.Info("StreamAllUsers finished successfully",
		"totalProcessed", totalProcessed)
	return nil
}

func (r *UserRepository) createCommissionTracking(ctx context.Context, tx *gorm.DB, userID, username, campaignName, referralCode string) error {
	trackingRecord := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		Username:                    username,
		CampaignName:                campaignName,
		RefferalCode:                referralCode,
		ReferredUsers:               json.RawMessage("[]"),
		CurrentCommissionPercentage: 10, // Default value
		LastCommissionPercentage:    10,
		OverallCommissionPercentage: 10,
		ClaimedCommission:           0,
		EligibleCommission:          0,
		TotalCommission:             0,
	}

	if err := tx.Create(&trackingRecord).Error; err != nil {
		fmt.Printf("createCommissionTracking: Failed to create record: %v\n", err)
		return fmt.Errorf("failed to create commission tracking record: %w", err)
	}

	fmt.Printf("createCommissionTracking: Successfully created tracking record\n")
	return nil
}

func (r *UserRepository) AssignSpecialBonusToUserOnTierUpgrade(ctx context.Context, userExternalID string, bonusAmount float64, newVipStatus string) error {
	user, err := r.GetUserByExternalID(ctx, userExternalID)
	if err != nil {
		slog.Error("Failed to get user for bonus",
			slog.String("user_id", userExternalID),
			slog.String("error", err.Error()))
		return fmt.Errorf("get user: %w", err)
	}

	expiryDate := time.Now().AddDate(0, 0, 30)

	userBonus := userBonus{
		ExternalID:    userExternalID,
		BonusConfigID: 6,
		Username:      user.UserName,
		Category:      "special",
		ExpiresOn:     expiryDate,
		Eligible:      true,
		Availed:       false,
		BonusStatus:   "active",
		UserVipStatus: newVipStatus,
		RewardAmount:  bonusAmount,
		ClaimAttempt:  false,
		ClaimSuccess:  false,
	}

	result := r.db.Create(&userBonus)
	if result.Error != nil {
		slog.Error("Failed to create bonus in database",
			slog.String("error", result.Error.Error()))
		return fmt.Errorf("create bonus: %w", result.Error)
	}

	slog.Info("Successfully created special bonus",
		slog.String("user_id", userExternalID),
		slog.String("username", user.UserName),
		slog.Float64("amount", bonusAmount))

	return nil
}

func (r *UserRepository) BeginTransaction(ctx context.Context) (*gorm.DB, error) {
	return r.db.WithContext(ctx).Begin(), nil
}

func (r *UserRepository) GetCurrentVipStatus(ctx context.Context, userExternalID string) (string, error) {
	var vipStatus string
	if err := r.db.WithContext(ctx).Table("users").
		Where("external_id = ?", userExternalID).
		Select("elantil_vip_status").
		Scan(&vipStatus).Error; err != nil {
		return "", err
	}
	return vipStatus, nil
}

func (r *UserRepository) GetUserWageringAndVIPStatus(ctx context.Context, userExternalID string) (float64, string, error) {
	var vipStatus string
	var user struct {
		ID string  `gorm:"column:id"`
		XP float64 `gorm:"column:xp"`
	}
	if err := r.db.WithContext(ctx).Table("users").
		Where("external_id = ?", userExternalID).
		Select("id, xp").
		Scan(&user).Error; err != nil {
		return 0, "", err
	}

	xp := user.XP
	vipStatus = r.vipTiers.MatchVIPTier(xp)
	if err := r.UpdateUserElantilVIPStatus(userExternalID, vipStatus); err != nil {
		return 0, "", err
	}
	return xp, vipStatus, nil
}

func (r *UserRepository) DB() *gorm.DB {
	return r.db
}

func (r *UserRepository) UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	var totalXP float64

	err := r.db.WithContext(ctx).
		Raw("UPDATE users SET xp = COALESCE(xp, 0) + ? WHERE external_id = ? RETURNING xp", xpToAdd, userID).
		Scan(&totalXP).Error

	if err != nil {
		return 0, fmt.Errorf("update user xp: %w", err)
	}

	// update xp in elantil
	err = r.wagering.UpsertPlayerActivityTagsByUserExternalID(userID, "player_activity", "xp", fmt.Sprintf("%f", totalXP))
	if err != nil {
		return 0, fmt.Errorf("update user xp in elantil: %w", err)
	}

	return totalXP, nil
}

func (r *UserRepository) GetAllUsersWithPagination(ctx context.Context, limit, offset int) ([]domain.GetUsersResponseForXP, error) {
	var users []domain.GetUsersResponseForXP
	tx := r.db.
		WithContext(ctx).
		Model(&user{}).
		Select(`users.external_id, COALESCE(users.xp, 0) as xp`). // Added COALESCE to handle NULL values
		Where("users.external_id IS NOT NULL").
		Limit(limit).
		Offset(offset).
		Find(&users)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find users with pagination: %w", err)
	}

	return users, nil
}

func (r *UserRepository) GetUsersXPAndUpdateInElantil(ctx context.Context) error {
	const batchSize = 100
	var offset int

	for {
		users, err := r.GetAllUsersWithPagination(ctx, batchSize, offset)
		if err != nil {
			return fmt.Errorf("failed to fetch users batch: %w", err)
		}

		if len(users) == 0 {
			break
		}

		errChan := make(chan error, len(users))
		sem := make(chan struct{}, 10)

		for _, user := range users {
			sem <- struct{}{}
			go func(u domain.GetUsersResponseForXP) {
				defer func() { <-sem }()
				xpStr := fmt.Sprintf("%.2f", u.XP)
				if err := r.wagering.UpsertPlayerActivityTagsByUserExternalID(
					u.ExternalID,
					"player_activity",
					"xp",
					xpStr,
				); err != nil {
					errChan <- fmt.Errorf("failed to store XP in Elantil for user %s: %w", u.ExternalID, err)
					return
				}

				slog.Info("Stored XPs in Elantil",
					"user_id", u.ExternalID,
					"xp", xpStr)
				errChan <- nil
			}(user)
		}

		var hasErrors bool
		for i := 0; i < len(users); i++ {
			if err := <-errChan; err != nil {
				hasErrors = true
				slog.Error("Error processing user", "error", err)
			}
		}

		if hasErrors {
			slog.Error("Some users failed to process in batch",
				"offset", offset,
				"batch_size", batchSize)
		}

		offset += len(users)
	}

	return nil
}
func (r *UserRepository) UpdateUpsertUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	var totalXP float64

	err := r.db.WithContext(ctx).
		Raw("UPDATE users SET xp = ? WHERE external_id = ? RETURNING xp", xpToAdd, userID).
		Scan(&totalXP).Error

	if err != nil {
		return 0, fmt.Errorf("update user xp: %w", err)
	}

	// update xp in elantil
	err = r.wagering.UpsertPlayerActivityTagsByUserExternalID(userID, "player_activity", "xp", fmt.Sprintf("%f", totalXP))
	if err != nil {
		return 0, fmt.Errorf("update user xp in elantil: %w", err)
	}

	return totalXP, nil
}

func (r *UserRepository) UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode, hideStats *bool) error {
	updates := make(map[string]interface{})
	if ghostMode != nil {
		updates["ghost_mode"] = *ghostMode
	}
	if hideStats != nil {
		updates["hide_all_stats"] = *hideStats
	}
	if len(updates) == 0 {
		return nil
	}
	result := r.db.WithContext(ctx).Table("users").
		Where("external_id = ?", userExternalID).
		Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("update user preferences: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no user found with external ID: %s", userExternalID)
	}

	if ghostMode != nil {
		if err := r.wagering.UpsertPlayerActivityTagsByUserExternalID(userExternalID, "player_activity", "username_hidden", fmt.Sprintf("%t", *ghostMode)); err != nil {
			return fmt.Errorf("update user hidden username in elantil: %w", err)
		}
	}

	if hideStats != nil {
		if err := r.wagering.UpsertPlayerActivityTagsByUserExternalID(userExternalID, "player_activity", "stats_hidden", fmt.Sprintf("%t", *hideStats)); err != nil {
			return fmt.Errorf("update user hidden stats in elantil: %w", err)
		}
	}

	return nil
}

func (r *UserRepository) GetUserAndGameInfo(ctx context.Context, userID, gameID string) (domain.UserGameDTO, error) {
	var dto domain.UserGameDTO
	query := `
    SELECT
        u.id as user_id,
        u.external_id as user_external_id,
        CASE
            WHEN u.ghost_mode = true THEN 'Hidden'
            ELSE u.user_name
        END as user_name,
        u.elantil_vip_status,
        u.ghost_mode,
        g.id as game_id,
        g.cms_game_id,
        g.external_id as game_external_id,
        g.name as game_name,
        g.slug as game_slug,
        g.thumbnail_id
    FROM users u
    INNER JOIN games g ON g.external_id = ?
    WHERE u.external_id = ?`

	if err := r.db.WithContext(ctx).Raw(query, gameID, userID).Scan(&dto).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.UserGameDTO{}, domain.ErrResourceNotFound
		}
		return domain.UserGameDTO{}, fmt.Errorf("find user and game by external ID '%v' and game ID '%v': %w", userID, gameID, err)
	}
	// Validate that we got valid IDs
	if dto.UserID == uuid.Nil || dto.GameID == uuid.Nil {
		return domain.UserGameDTO{}, fmt.Errorf("invalid user or game ID returned from database")
	}

	return dto, nil
}

func (r *UserRepository) UpdateUserEmailVerificationStatus(ctx context.Context, username string, verified bool) error {
	err := r.db.WithContext(ctx).Table("users").
		Where("user_name = ?", username).
		Update("verified", verified).Error
	if err != nil {
		return fmt.Errorf("update user email verification status: %w", err)
	}

	return nil
}

func (r *UserRepository) UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error {
	var updatedMultiCurrency bool
	result := r.db.WithContext(ctx).Table("users").
		Where("external_id = ?", userID).
		Update("multi_currency", multiCurrency).
		Select("multi_currency").
		Scan(&updatedMultiCurrency)

	if result.Error != nil {
		return fmt.Errorf("update user multi currency: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no user found with external ID: %s", userID)
	}

	if err := r.wagering.UpsertPlayerActivityTagsByUserExternalID(userID, "player_activity", "multi_currency", fmt.Sprintf("%t", updatedMultiCurrency)); err != nil {
		return fmt.Errorf("update user multi currency in elantil: %w", err)
	}

	return nil
}

func (r *UserRepository) IsUserNameUnique(ctx context.Context, userName string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&user{}).
		Where("user_name = ?", userName).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("check unique user name: %w", err)
	}
	return count == 0, nil
}