package postgres

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/stretchr/testify/require"
)

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_CreateandUpdateCMSTermsAndConditions_Create() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)

	payload := &domain.AddCMSTermsAndConditions{
		Category: "Test Category",
		Content:  "This is test content for terms and conditions",
		Status:   "published",
		Version:  "1.0",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), payload)
	require.NoError(s.T(), err)

	var created TermsAndConditionTemplate
	err = s.gormDB.Where("category = ?", "test category").First(&created).Error
	require.NoError(s.T(), err)

	require.Equal(s.T(), "test category", created.Category) 
	require.Equal(s.T(), "This is test content for terms and conditions", created.Content)
	require.Equal(s.T(), "published", created.Status)
	require.Equal(s.T(), "1.0", created.Version)
	require.Equal(s.T(), "general", created.Type)
	require.NotEmpty(s.T(), created.ID)
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_CreateandUpdateCMSTermsAndConditions_Update() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	initialPayload := &domain.AddCMSTermsAndConditions{
		Category: "Update Category",
		Content:  "Initial content",
		Status:   "draft",
		Version:  "1.0",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), initialPayload)
	require.NoError(s.T(), err)
	updatePayload := &domain.AddCMSTermsAndConditions{
		Category: "Update Category",
		Content:  "Updated content",
		Status:   "published",
		Version:  "2.0",
		Type:     "specific",
	}

	err = repository.CreateandUpdateCMSTermsAndConditions(context.Background(), updatePayload)
	require.NoError(s.T(), err)

	var updated TermsAndConditionTemplate
	err = s.gormDB.Where("category = ?", "update category").First(&updated).Error
	require.NoError(s.T(), err)

	require.Equal(s.T(), "update category", updated.Category)
	require.Equal(s.T(), "Updated content", updated.Content)
	require.Equal(s.T(), "published", updated.Status)
	require.Equal(s.T(), "2.0", updated.Version)
	require.Equal(s.T(), "specific", updated.Type)
	var count int64
	s.gormDB.Model(&TermsAndConditionTemplate{}).Where("category = ?", "update category").Count(&count)
	require.Equal(s.T(), int64(1), count)
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_GetCMSTermsAndConditions() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	payload := &domain.AddCMSTermsAndConditions{
		Category: "Get Test Category",
		Content:  "Content for get test",
		Status:   "published",
		Version:  "1.5",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), payload)
	require.NoError(s.T(), err)
	result, err := repository.GetCMSTermsAndConditions(context.Background(), "Get Test Category")
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)

	require.Equal(s.T(), "get test category", result.Category) 
	require.Equal(s.T(), "Content for get test", result.Content)
	require.Equal(s.T(), "published", result.Status)
	require.Equal(s.T(), "1.5", result.Version)
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_GetCMSTermsAndConditions_NotFound() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	result, err := repository.GetCMSTermsAndConditions(context.Background(), "NonExistent Category")
	require.Error(s.T(), err)
	require.Nil(s.T(), result)
	require.Contains(s.T(), err.Error(), "terms and conditions not found for category")
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_GetCMSTermsAndConditions_DraftStatus() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	payload := &domain.AddCMSTermsAndConditions{
		Category: "Draft Category",
		Content:  "Draft content",
		Status:   "draft", 
		Version:  "1.0",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), payload)
	require.NoError(s.T(), err)
	result, err := repository.GetCMSTermsAndConditions(context.Background(), "Draft Category")
	require.Error(s.T(), err)
	require.Nil(s.T(), result)
	require.Contains(s.T(), err.Error(), "terms and conditions not found for category")
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_GetSportsBookCMSTermsAndConditions() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	s.gormDB.Where("type = ?", "sportsbook_individual_sports").Delete(&TermsAndConditionTemplate{})

	sportsbook1 := &domain.AddCMSTermsAndConditions{
		Category: "Basketball",
		Content:  "Basketball terms and conditions",
		Status:   "published",
		Version:  "1.0",
		Type:     "sportsbook_individual_sports",
	}

	sportsbook2 := &domain.AddCMSTermsAndConditions{
		Category: "Football",
		Content:  "Football terms and conditions",
		Status:   "published",
		Version:  "1.1",
		Type:     "sportsbook_individual_sports",
	}

	general := &domain.AddCMSTermsAndConditions{
		Category: "General",
		Content:  "General terms",
		Status:   "published",
		Version:  "1.0",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), sportsbook1)
	require.NoError(s.T(), err)

	err = repository.CreateandUpdateCMSTermsAndConditions(context.Background(), sportsbook2)
	require.NoError(s.T(), err)

	err = repository.CreateandUpdateCMSTermsAndConditions(context.Background(), general)
	require.NoError(s.T(), err)

	results, err := repository.GetSportsBookCMSTermsAndConditions(context.Background())
	require.NoError(s.T(), err)
	require.NotNil(s.T(), results)
	require.Len(s.T(), results, 2) 

	categories := make(map[string]*domain.AddCMSTermsAndConditions)
	for _, result := range results {
		categories[result.Category] = result
	}

	require.Contains(s.T(), categories, "basketball")
	require.Contains(s.T(), categories, "football")
	require.NotContains(s.T(), categories, "general")
	require.Equal(s.T(), "", categories["basketball"].Content) 
	require.Equal(s.T(), "", categories["football"].Content)   
}
func (s *databaseSuite) TestCMSTermsAndConditionsRepository_DeleteCMSTermsAndConditions() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	payload := &domain.AddCMSTermsAndConditions{
		Category: "Delete Test Category",
		Content:  "Content to be deleted",
		Status:   "published",
		Version:  "1.0",
		Type:     "general",
	}

	err := repository.CreateandUpdateCMSTermsAndConditions(context.Background(), payload)
	require.NoError(s.T(), err)
	var count int64
	s.gormDB.Model(&TermsAndConditionTemplate{}).Where("category = ?", "delete test category").Count(&count)
	require.Equal(s.T(), int64(1), count)
	err = repository.DeleteCMSTermsAndConditions(context.Background(), "Delete Test Category")
	require.NoError(s.T(), err)
	s.gormDB.Model(&TermsAndConditionTemplate{}).Where("category = ?", "delete test category").Count(&count)
	require.Equal(s.T(), int64(0), count)
}

func (s *databaseSuite) TestCMSTermsAndConditionsRepository_DeleteCMSTermsAndConditions_NotFound() {
	repository := NewCMSTermsAndConditionsRepository(s.gormDB)
	err := repository.DeleteCMSTermsAndConditions(context.Background(), "NonExistent Category")
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "no terms and conditions found for category")
}