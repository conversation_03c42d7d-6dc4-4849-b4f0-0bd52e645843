package postgres

import (
	"io"
	"log"
	"time"

	"github.com/google/uuid"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func init() {
	log.SetOutput(io.Discard)
}

var (
	gameID1         = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	gameID2         = uuid.MustParse("00000000-0000-0000-0000-000000000002")
	gameID3         = uuid.MustParse("00000000-0000-0000-0000-000000000003")
	gameNonExistant = uuid.MustParse("00000000-0000-0000-0000-000000000666")

	userID1           = uuid.MustParse("00000000-0000-0000-0000-000000000010")
	userID2           = uuid.MustParse("00000000-0000-0000-0000-000000000020")
	userID3           = uuid.MustParse("00000000-0000-0000-0000-000000000030")
	userID4           = uuid.MustParse("00000000-0000-0000-0000-000000000040")
	userID5           = uuid.MustParse("00000000-0000-0000-0000-000000000050")
	userIDNonExistant = uuid.MustParse("00000000-0000-0000-0000-000000000666")

	betID1           = uuid.MustParse("00000000-0000-0000-0000-000000000100")
	betID2           = uuid.MustParse("00000000-0000-0000-0000-000000000200")
	betID3           = uuid.MustParse("00000000-0000-0000-0000-000000000300")
	betID4           = uuid.MustParse("00000000-0000-0000-0000-000000000400")
	betID5           = uuid.MustParse("00000000-0000-0000-0000-000000000500")
	betID6           = uuid.MustParse("00000000-0000-0000-0000-000000000600")
	betIDNonExistant = uuid.MustParse("00000000-0000-0000-0000-000000000666")

	boostID1 = uuid.MustParse("00000000-0000-0000-0000-000000010000")
	boostID2 = uuid.MustParse("00000000-0000-0000-0000-000000020000")

	transactionID1           = uuid.MustParse("00000000-0000-0000-0000-000001000000")
	transactionID2           = uuid.MustParse("00000000-0000-0000-0000-000002000000")
	transactionID3           = uuid.MustParse("00000000-0000-0000-0000-000003000000")
	transactionID4           = uuid.MustParse("00000000-0000-0000-0000-000004000000")
	transactionIDNonExistant = uuid.MustParse("00000000-0000-0000-0000-000000000666")

	game1 = domain.Game{
		CMSGameID:    1,
		ID:           gameID1,
		ExternalID:   "id1",
		Name:         utils.PointerOf("game1"),
		VendorGameID: "id1",
	}

	game2 = domain.Game{
		CMSGameID:    2,
		ID:           gameID2,
		ExternalID:   "id2",
		Name:         utils.PointerOf("game2"),
		VendorGameID: "id2",
	}

	game3 = domain.Game{
		CMSGameID:    3,
		ID:           gameID3,
		ExternalID:   "id3",
		VendorGameID: "id3",
	}

	user1 = domain.User{
		ID:                  userID1,
		ExternalID:          "id1",
		HideAllStats:        false, // Changed from true
		HideTournamentStats: false, // Changed from true
		UserName:            "user1",
		VIPStatus:           "tier1", // Changed from "tier5"
	}

	user2 = domain.User{
		ID:                  userID2,
		ExternalID:          "id2",
		HideAllStats:        false, // Changed from true
		HideTournamentStats: false, // Changed from true
		UserName:            "user2",
		VIPStatus:           "tier1", // Changed from "tier5"
	}

	user3 = domain.User{
		ID:                  userID3,
		ExternalID:          "id3",
		HideAllStats:        false, // Changed from true
		HideTournamentStats: false, // Changed from true
		UserName:            "user3",
		VIPStatus:           "tier1", // No change needed here
	}

	user4 = domain.User{
		ID:                  userID4,
		ExternalID:          "id4",
		HideAllStats:        false, // Changed from true
		HideTournamentStats: false, // Changed from true
		UserName:            "user4",
		VIPStatus:           "tier1", // Changed from ""
	}

	user5 = domain.User{
		ID:                  userID5,
		ExternalID:          "id5",
		HideAllStats:        false, // Changed from true
		HideTournamentStats: false, // Changed from true
		UserName:            "user5",
		VIPStatus:           "tier1", // Changed from ""
	}

	user1WithStats = func() domain.User {
		user := user1
		user.NumberOfLosses = 102
		user.NumberOfWins = 100
		user.TotalBets = 202
		user.TotalCoins = 5003
		user.VIPStatus = "tier1" // Changed from "tier5"
		user.Wagered = 10030
		return user
	}()

	user2WithStats = func() domain.User {
		user := user2
		user.NumberOfLosses = 250
		user.NumberOfWins = 51
		user.TotalBets = 301
		user.TotalCoins = 10000.6
		user.VIPStatus = "tier1" // Changed from "tier5"
		user.Wagered = 20006
		return user
	}()

	user3WithStats = func() domain.User {
		user := user3
		user.NumberOfLosses = 1
		user.NumberOfWins = 0
		user.TotalBets = 1
		user.TotalCoins = 6
		user.VIPStatus = "tier1"
		user.Wagered = 60
		return user
	}()

	user4WithStats = func() domain.User {
		user := user4
		user.NumberOfLosses = 1
		user.NumberOfWins = 1
		user.TotalBets = 2
		user.TotalCoins = 2
		user.VIPStatus = "tier1" // Changed from ""
		user.Wagered = 20
		return user
	}()

	user5WithStats = func() domain.User {
		user := user5
		user.NumberOfLosses = 0
		user.NumberOfWins = 0
		user.TotalBets = 0
		user.TotalCoins = 0
		user.VIPStatus = "tier1" // Changed from ""
		user.Wagered = 0
		return user
	}()

	user1WithStatsWithAssets = func() domain.User {
		user := user1WithStats
		user.UserAssets = []domain.UserAsset{}
		return user
	}()

	user2WithStatsWithAssets = func() domain.User {
		user := user2WithStats
		user.UserAssets = []domain.UserAsset{}
		return user
	}()

	user3WithStatsWithAssets = func() domain.User {
		user := user3WithStats
		user.UserAssets = []domain.UserAsset{}
		return user
	}()

	user4WithStatsWithAssets = func() domain.User {
		user := user4WithStats
		user.UserAssets = []domain.UserAsset{}
		return user
	}()

	user5WithStatsWithAssets = func() domain.User {
		user := user5WithStats
		user.UserAssets = []domain.UserAsset{}
		return user
	}()

	bet1 = domain.Bet{
		CoinsMultiplier: 1,
		BetAmount:       utils.PointerOf(10.0),
		ID:              betID1,
		Multiplier:      0.0,
		ExternalID:      "id1",
		Payout:          utils.PointerOf(0.0),
		Game:            game1,
		User:            user1WithStats,
		RoundStatus:     "completed",
	}

	bet2 = domain.Bet{
		ID:              betID2,
		BetAmount:       utils.PointerOf(20.0),
		Multiplier:      0.25,
		CoinsMultiplier: 1,
		ExternalID:      "id2",
		Payout:          utils.PointerOf(5.0),
		Game:            game1,
		User:            user1WithStats,
		RoundStatus:     "completed",
	}

	bet3 = domain.Bet{
		ID:              betID3,
		BetAmount:       utils.PointerOf(6.0),
		Multiplier:      1.6666666666666667,
		CoinsMultiplier: 1,
		ExternalID:      "id3",
		Payout:          utils.PointerOf(10.0),
		Game:            game1,
		User:            user2WithStats,
		RoundStatus:     "completed",
	}

	bet4 = domain.Bet{
		ID:              betID4,
		BetAmount:       utils.PointerOf(60.0),
		Multiplier:      0.16666666666666666,
		CoinsMultiplier: 1,
		ExternalID:      "id4",
		Payout:          utils.PointerOf(10.0),
		Game:            game2,
		User:            user3WithStats,
		RoundStatus:     "completed",
	}

	bet5 = domain.Bet{
		ID:              betID5,
		BetAmount:       utils.PointerOf(15.0),
		Multiplier:      0.7333333333333333,
		CoinsMultiplier: 1,
		ExternalID:      "id5",
		Payout:          utils.PointerOf(11.0),
		Game:            game2,
		User:            user4WithStats,
		RoundStatus:     "completed",
	}

	bet6 = domain.Bet{
		ID:              betID6,
		BetAmount:       utils.PointerOf(5.0),
		Multiplier:      2.2,
		CoinsMultiplier: 1,
		ExternalID:      "id6",
		Payout:          utils.PointerOf(11.0),
		Game:            game3,
		User:            user4WithStats,
		RoundStatus:     "completed",
	}

	boost1 = domain.Boost{
		ID:                 boostID1,
		BonusStartsAt:      parseTime("2006-01-02T15:04:05Z"),
		BonusFinishesAt:    parseTime("2006-01-02T20:04:05Z"),
		BoostDurationHours: 20,
		Multiplier:         1.5,
		User:               user1,
	}

	boost2 = domain.Boost{
		ID:                 boostID2,
		BonusStartsAt:      parseTime("2006-01-02T15:04:05Z"),
		BonusFinishesAt:    parseTime("2006-01-02T20:04:05Z"),
		BoostDurationHours: 20,
		BoostStartedAt:     utils.PointerOf(parseTime("2006-01-02T19:04:05Z")),
		Multiplier:         2,
		User:               user2,
	}

	transaction1 = domain.Transaction{
		ID:            transactionID1,
		Amount:        1,
		BetExternalID: bet1.ExternalID,
		Category:      "casino",
		ExternalID:    "id1",
		InsertedAt:    parseTime("2006-01-02T15:04:05Z"),
		Type:          "debit",
	}

	transaction2 = domain.Transaction{
		ID:         transactionID2,
		Amount:     2,
		Category:   "payments",
		ExternalID: "id2",
		InsertedAt: parseTime("2006-01-02T15:04:06Z"),
		Type:       "credit",
	}

	transaction3 = domain.Transaction{
		ID:         transactionID3,
		Amount:     3,
		Category:   "payments",
		ExternalID: "id3",
		InsertedAt: parseTime("2006-01-02T15:04:07Z"),
		Type:       "debit",
	}

	transaction4 = domain.Transaction{
		ID:         transactionID4,
		Amount:     4,
		Category:   "payments",
		ExternalID: "id4",
		InsertedAt: parseTime("2006-01-02T15:04:08Z"),
		Type:       "credit",
	}
)

func parseTime(val string) time.Time {
	res, err := time.Parse(time.RFC3339, val)
	if err != nil {
		panic(err)
	}

	return res
}
