package postgres

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

type user struct {
	UUIDKeyModel
	CompanyUser         bool        `gorm:"type:bool;default:false;index;not null"`
	ExternalID          string      `gorm:"type:text;unique;index;not null"`
	FactorSID           *string     `gorm:"type:text;column:factor_sid"`
	FirstName           string      `gorm:"type:text;not null"`
	GhostMode           bool        `gorm:"type:bool;default:false;not null"`
	HideAllStats        bool        `gorm:"type:bool;default:false;not null"`
	HideTournamentStats bool        `gorm:"type:bool;default:false;not null"`
	JoinDate            time.Time   `gorm:"type:timestamptz"`
	LastName            string      `gorm:"type:text;not null"`
	LastUpdatedOn       *time.Time  `gorm:"type:timestamptz"`
	ProfileStatus       string      `gorm:"type:text;not null"`
	UserName            string      `gorm:"type:text;unique;index;not null"`
	ElantilVipStatus    string      `gorm:"type:text"`
	LifeTimeLossSummary float64     `gorm:"type:float"`
	LifeTimeBetSummary  float64     `gorm:"type:float"`
	UserAsset           []userAsset `gorm:"foreignKey:UserID"`
	XP                  float64     `gorm:"type:float"`
	Email               string      `gorm:"type:text;"`
	Verified            bool        `gorm:"type:bool;default:false;column:verified"`
	MultiCurrency       bool        `gorm:"type:bool;default:false"`
}

type userActivityTags struct {
	UUIDKeyModel
	ExternalID  uuid.UUID `gorm:"type:uuid;index;not null"`
	CategoryKey string    `gorm:"type:text;not null"`
	Key         string    `gorm:"type:text;not null"`
	Value       string    `gorm:"type:text;not null"`
}

type userWithStats struct {
	User         user             `gorm:"embedded"`
	BetsSummary  userBetsSummary  `gorm:"embedded"`
	CoinsSummary userCoinsSummary `gorm:"embedded"`
}

type userBetsSummary struct {
	NumberOfWins   int
	NumberOfLosses int
	Wagered        float64
	TotalBets      int
}

type userCoinsSummary struct {
	ClaimedCoins float64
	TotalCoins   float64
}

type rankedUser struct {
	UserWithStats userWithStats `gorm:"embedded"`
	Rank          int
}

type userWageredStats struct {
	UserWithStats userWithStats `gorm:"embedded"`
}

type userAsset struct {
	UUIDKeyModel
	UserID            uuid.UUID `gorm:"type:uuid;unique_index:users_asset_type_index;index;not null"`
	Type              string    `gorm:"type:text;unique_index:users_asset_type_index;not null"`
	UserConfigAssetID uuid.UUID `gorm:"type:uuid;index;not null"`
	UserConfigAsset   userConfigAsset
	User              user `gorm:"-"`
}

type userClaimedCoins struct {
	UUIDKeyModel
	UserID uuid.UUID `gorm:"type:uuid;not null"`
	Coins  float64   `gorm:"type:float;not null"`
	Source string    `gorm:"type:text;not null"`
}

type registeredEmails struct {
	UUIDKeyModel
	Email          string `gorm:"type:text;not null;unique;index"`
	EmailMarketing bool   `gorm:"type:bool;default:false;not null;index"`
	Registered     bool   `gorm:"type:bool;default:false;not null"`
}

func userToRowForUpdate(u domain.User) user {
	return user{
		UUIDKeyModel: UUIDKeyModel{ID: u.ID},
		FactorSID:    u.FactorSID,
		GhostMode:    u.GhostMode,
		HideAllStats: u.HideAllStats,
	}
}

func userToRowForUpsert(u domain.User) user {
	return user{
		CompanyUser:         u.CompanyUser,
		ExternalID:          u.ExternalID,
		FirstName:           u.FirstName,
		GhostMode:           false,
		HideAllStats:        false,
		JoinDate:            u.JoinDate,
		LastName:            u.LastName,
		LastUpdatedOn:       u.LastUpdatedOn,
		ProfileStatus:       u.ProfileStatus,
		UserName:            u.UserName,
		ElantilVipStatus:    u.ElantilVIpStatus,
		LifeTimeBetSummary:  u.LifeTimeBetSummary,
		LifeTimeLossSummary: u.LifeTimeLossSummary,
		XP:                  u.XP,
		Email:               u.Email,
		Verified:            u.Verified,
	}
}

func rowToUser(row user) domain.User {
	vipTiers := domain.VIPTiers{}
	return domain.User{
		ID:                  row.ID,
		CompanyUser:         row.CompanyUser,
		ExternalID:          row.ExternalID,
		FactorSID:           row.FactorSID,
		FirstName:           row.FirstName,
		GhostMode:           row.GhostMode,
		HideAllStats:        row.HideAllStats,
		HideTournamentStats: row.HideTournamentStats,
		JoinDate:            row.JoinDate,
		LastName:            row.LastName,
		LastUpdatedOn:       row.LastUpdatedOn,
		ProfileStatus:       row.ProfileStatus,
		UserName:            row.UserName,
		UserAssets:          rowsToUserAsset(row.UserAsset),
		ElantilVIpStatus:    vipTiers.MatchVIPTier(row.XP),
		LifeTimeBetSummary:  row.LifeTimeBetSummary,
		LifeTimeLossSummary: row.LifeTimeLossSummary,
		XP:                  row.XP,
		Email:               row.Email,
		Verified:            row.Verified,
		MultiCurrency:       row.MultiCurrency,
	}
}

func rowToUserPtr(row user) *domain.User {
	return utils.PointerOf(rowToUser(row))
}

func rowToUserForLeaderBoardWithStats(row userWithStats, vipTiers domain.VIPTiers) domain.User {
	user := rowToUser(row.User)
	user.ClaimedCoins = row.CoinsSummary.ClaimedCoins
	user.NumberOfLosses = row.BetsSummary.NumberOfLosses
	user.NumberOfWins = row.BetsSummary.NumberOfWins
	user.TotalBets = row.BetsSummary.TotalBets
	user.TotalCoins = row.CoinsSummary.TotalCoins
	user.XP = row.User.XP
	user.VIPStatus = vipTiers.MatchVIPTier(row.User.XP)
	user.Wagered = row.BetsSummary.Wagered
	return user
}

// TODO: hide user functionality endpoint
func rowToUserWithStats(row userWithStats, vipTiers domain.VIPTiers) domain.User {
	user := rowToUser(row.User)
	user.ClaimedCoins = row.CoinsSummary.ClaimedCoins
	user.NumberOfLosses = row.BetsSummary.NumberOfLosses
	user.NumberOfWins = row.BetsSummary.NumberOfWins
	user.TotalBets = row.BetsSummary.TotalBets
	user.TotalCoins = row.CoinsSummary.TotalCoins
	user.Wagered = row.BetsSummary.Wagered

	user.XP = row.User.XP
	user.VIPStatus = vipTiers.MatchVIPTier(row.User.XP)
	return user
}

func rowToUserWithStatsInternal(row userWithStats, vipTiers domain.VIPTiers) domain.User {
	user := rowToUser(row.User)
	user.ClaimedCoins = row.CoinsSummary.ClaimedCoins
	user.NumberOfLosses = row.BetsSummary.NumberOfLosses
	user.NumberOfWins = row.BetsSummary.NumberOfWins
	user.TotalBets = row.BetsSummary.TotalBets
	user.TotalCoins = row.CoinsSummary.TotalCoins
	user.XP = row.User.XP
	user.VIPStatus = vipTiers.MatchVIPTier(row.User.XP)
	user.Wagered = row.BetsSummary.Wagered
	return user
}

func rowToUserWithStatsPtr(row userWithStats, vipTiers domain.VIPTiers) *domain.User {
	return utils.PointerOf(rowToUserWithStats(row, vipTiers))
}

func rowToRankedUser(row rankedUser, vipTiers domain.VIPTiers) domain.RankedUser {
	return domain.RankedUser{
		User:                rowToUserForLeaderBoardWithStats(row.UserWithStats, vipTiers),
		Rank:                row.Rank,
		RemainingToNextRank: vipTiers.GetAmountsForNextRank(row.UserWithStats.User.XP),
	}
}

func rowToRankedUserForLeaderboard(row rankedUser, vipTiers domain.VIPTiers) domain.RankedUser {
	return domain.RankedUser{
		User:                rowToUserWithStats(row.UserWithStats, vipTiers),
		Rank:                row.Rank,
		RemainingToNextRank: vipTiers.GetAmountsForNextRank(row.UserWithStats.User.XP),
	}
}

func rowToRankedUserInternal(row rankedUser, vipTiers domain.VIPTiers) domain.RankedUser {
	return domain.RankedUser{
		User:                rowToUserWithStatsInternal(row.UserWithStats, vipTiers),
		Rank:                row.Rank,
		RemainingToNextRank: vipTiers.GetAmountsForNextRank(row.UserWithStats.User.XP),
	}
}

func rowToRankedUserPtr(row rankedUser, vipTiers domain.VIPTiers) *domain.RankedUser {
	return utils.PointerOf(rowToRankedUser(row, vipTiers))
}

func rowToRankedUserPtrInternal(row rankedUser, vipTiers domain.VIPTiers) *domain.RankedUser {
	return utils.PointerOf(rowToRankedUserInternal(row, vipTiers))
}

func rowsToUserAsset(rows []userAsset) []domain.UserAsset {
	return utils.MapSlice(rows, rowToUserAsset)
}

func rowToUserAsset(row userAsset) domain.UserAsset {
	return domain.UserAsset{
		ID:              row.ID,
		UserConfigAsset: rowToAsset(row.UserConfigAsset),
		Type:            row.Type,
	}
}

func rowsToRankedUsers(
	rows []rankedUser,
	totalCount int64,
	pageNum, pageSize int,
	vipTiers domain.VIPTiers,
) *domain.RankedUsers {
	return &domain.RankedUsers{
		Items: utils.MapSlice(rows, func(row rankedUser) domain.RankedUser {
			return rowToRankedUser(row, vipTiers)
		}),
		Paging: domain.Paging{
			TotalCount:  totalCount,
			CurrentPage: pageNum,
			PageSize:    pageSize,
		},
	}
}

func rowsToRankedUsersUnpaged(rows []rankedUser, vipTiers domain.VIPTiers) []domain.RankedUser {
	return utils.MapSlice(rows, func(row rankedUser) domain.RankedUser {
		return rowToRankedUser(row, vipTiers)
	})
}

func claimedCoinsToRow(c *domain.UserClaimedCoins) *userClaimedCoins {
	return &userClaimedCoins{
		UserID: c.RankedUser.ID,
		Coins:  c.Coins,
		Source: c.Source,
	}
}

func rowToClaimedCoins(row *userClaimedCoins, rankedUser *domain.RankedUser) *domain.UserClaimedCoins {
	return &domain.UserClaimedCoins{
		ID:         row.ID,
		RankedUser: *rankedUser,
		Coins:      row.Coins,
		Source:     row.Source,
	}
}

func rowToRegisterEmail(row *registeredEmails) *domain.RegisteredEmail {
	return &domain.RegisteredEmail{
		Email:          row.Email,
		EmailMarketing: row.EmailMarketing,
		Registered:     row.Registered,
	}
}

func anonymizeUserStats(user *domain.User) {
	user.NumberOfLosses = 0
	user.NumberOfWins = 0
	user.TotalBets = 0
	user.TotalCoins = 0
	user.ClaimedCoins = 0
	user.Wagered = 0
}

func anonymizePersonalInfo(user *domain.User) {
	user.Email = "*****"
	user.JoinDate = time.Time{}
	user.LastName = "*****"
	user.FirstName = "*****"
}

func rowToDomainUser(row user) domain.User {
	return domain.User{
		ID:                  row.ID,
		CompanyUser:         row.CompanyUser,
		ExternalID:          row.ExternalID,
		FactorSID:           row.FactorSID,
		FirstName:           row.FirstName,
		GhostMode:           row.GhostMode,
		HideAllStats:        row.HideAllStats,
		HideTournamentStats: row.HideTournamentStats,
		JoinDate:            row.JoinDate,
		LastName:            row.LastName,
		LastUpdatedOn:       row.LastUpdatedOn,
		ProfileStatus:       row.ProfileStatus,
		UserName:            row.UserName,
		ElantilVIpStatus:    row.ElantilVipStatus,
		LifeTimeBetSummary:  row.LifeTimeBetSummary,
		LifeTimeLossSummary: row.LifeTimeLossSummary,
		XP:                  row.XP,
	}
}
