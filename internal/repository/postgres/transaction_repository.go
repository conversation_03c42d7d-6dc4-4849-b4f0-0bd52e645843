package postgres

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
)

// Assert interface implementation.
var _ domain.TransactionRepository = (*TransactionRepository)(nil)

type TransactionRepository struct {
	db *gorm.DB
}

func NewTransactionRepository(db *gorm.DB) *TransactionRepository {
	return &TransactionRepository{db: db}
}

func (r *TransactionRepository) UpsertTransaction(ctx context.Context, trans *domain.Transaction) (*domain.Transaction, error) {
	row := transactionToRow(*trans)

	tx := r.db.
		WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "external_id"}},
			UpdateAll: true,
		}).
		Create(&row)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("upsert transaction: %w", err)
	}

	return rowToTransactionPtr(row), nil
}

func (r *TransactionRepository) GetTransactions(
	ctx context.Context,
	params *domain.GetTransactionParams,
) (*domain.Transactions, error) {
	var count int64
	rows := []transaction{}

	tx := r.db.WithContext(ctx).Model(&transaction{})
	tx = tx.
		Joins("INNER JOIN users ON transactions.user_id = users.id").
		Where("users.external_id = ?", params.UserExternalID)

	if len(params.Categories) > 0 {
		tx = tx.Where("category IN (?)", params.Categories)
	}

	if params.From != nil {
		tx = tx.Where("inserted_at >= ?", *params.From)
	}

	if params.To != nil {
		tx = tx.Where("inserted_at <= ?", *params.To)
	}

	if len(params.Currency) > 0 {
		tx = tx.Where("currency IN (?)", params.Currency)
	}

	var totalWithdrawal, totalDeposit float64

	txDebit := tx.Session(&gorm.Session{}).Select("COALESCE(SUM(amount), 0)").
		Where("type = 'debit' AND status = 'completed'").
		Scan(&totalWithdrawal)

	if err := txDebit.Error; err != nil {
		return nil, fmt.Errorf("calculate total debit: %w", err)
	}

	txCredit := tx.Session(&gorm.Session{}).Select("COALESCE(SUM(amount), 0)").
		Where("type = 'credit' AND status = 'completed'").
		Scan(&totalDeposit)

	if err := txCredit.Error; err != nil {
		return nil, fmt.Errorf("calculate total credit: %w", err)
	}

	if len(params.Types) > 0 {
		tx = tx.Where("type IN (?)", params.Types)
	}

	if len(params.Status) > 0 {
		tx = tx.Where("status IN (?)", params.Status)
	}

	tx = tx.
		Count(&count).
		Order(getOrderBy(params.OrderParams)).
		Limit(params.PageSize).
		Offset(calculateOffset(params.PagingParams)).
		Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find transactions: %w", err)
	}

	transactions := rowsToTransactions(rows, count, params.PageNumber, params.PageSize, totalDeposit, totalWithdrawal)

	return transactions, nil
}

func (r *TransactionRepository) ExportTransactions(
	ctx context.Context,
	params *domain.ExportTransactionsParams,
) ([]domain.Transaction, error) {
	rows := []transaction{}

	tx := r.db.
		WithContext(ctx).
		Model(&transaction{}).
		Joins("INNER JOIN users ON transactions.user_id = users.id").
		Where("users.external_id = ?", params.UserExternalID).
		Where("transactions.inserted_at BETWEEN ? AND ?", params.StartDate, params.EndDate)

	if len(params.Types) > 0 {
		tx.Where("type IN (?)", params.Types)
	}

	if len(params.Categories) > 0 {
		tx.Where("category IN (?)", params.Categories)
	}

	if err := tx.Find(&rows).Error; err != nil {
		return nil, fmt.Errorf("find transactions for export: %w", err)
	}

	return rowsToUnpagedTransactions(rows), nil
}

func (r *TransactionRepository) GetTransactionByType(ctx context.Context, transType string, category string, currency string, userId string, offset int, limit int) ([]domain.Transaction, error) {
	rows := []transaction{}

	query := r.db.Table("transactions").
		WithContext(ctx).
		Joins("INNER JOIN users ON transactions.user_id = users.id").
		Where("users.external_id = ? AND status = 'completed'", userId).
		Offset(offset).
		Limit(limit)

	if transType != "" {
		query = query.Where("type = ?", transType)
	}
	if category != "" {
		query = query.Where("category = ?", category)
	}
	if currency != "" {
		query = query.Where("currency = ?", currency)
	}

	tx := query.Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find transactions by type: %w", err)
	}

	return rowsToUnpagedTransactions(rows), nil
}

func (r *TransactionRepository) GetFirstCreditTransaction(ctx context.Context, userId string) (float64, error) {
	var amount float64
	maxRetries := 3
	backoffDuration := 500 * time.Millisecond
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		tx := r.db.Table("transactions").WithContext(ctx).
			Joins("INNER JOIN users ON transactions.user_id = users.id").
			Where("users.external_id = ?", userId).
			Where("type = 'credit' AND status = 'completed' AND category = 'payments'").
			Order("inserted_at ASC").
			Limit(1).
			Select("amount").
			Find(&amount)

		if err := tx.Error; err != nil {
			lastErr = fmt.Errorf("find first credit transaction: %w", err)
			// If it's a database error, retry after a delay
			time.Sleep(backoffDuration)
			backoffDuration *= 2 // Exponential backoff
			continue
		}

		if tx.RowsAffected == 0 {
			if attempt < maxRetries-1 {
				// No transaction found yet, retry after a delay
				time.Sleep(backoffDuration)
				backoffDuration *= 2 // Exponential backoff
				continue
			}
			return 0, fmt.Errorf("no credit transaction found after %d attempts", maxRetries)
		}

		// Success
		return amount, nil
	}

	// If we get here, all retries failed with a database error
	return 0, lastErr
}

func (r *TransactionRepository) HasOnlyOneCreditTransaction(ctx context.Context, userId uuid.UUID) (bool, error) {
	var count int64

	tx := r.db.Table("transactions").WithContext(ctx).
		Where("type = 'credit' AND status = 'completed' AND category = 'payments' AND user_id = ?", userId).
		Count(&count)

	if err := tx.Error; err != nil {
		return false, fmt.Errorf("check transaction count: %w", err)
	}

	return count == 1, nil
}

func (r *TransactionRepository) GetWageringForInstantBonuses(ctx context.Context, category string) (map[string]domain.BatchWageringData, string, error) {
	if category == "casino_vt" {
		category = "casino"
	} else if category == "sport_vt" {
		category = "sports"
	} else {
		return nil, "", fmt.Errorf("invalid category: %s", category)
	}

	now := time.Now().UTC()
	endTime := now.Truncate(time.Hour)
	startTime := endTime.Add(-2 * time.Hour)
	timeRange := fmt.Sprintf("%s - %s", startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	type tempResult struct {
		Amount     float64
		UserID     uuid.UUID
		ExternalID string
	}
	var results []tempResult

	err := r.db.Table("transactions").
		WithContext(ctx).
		Select("SUM(transactions.amount) as amount, transactions.user_id, users.external_id").
		Joins("INNER JOIN users ON transactions.user_id = users.id").
		Where("transactions.type = ? AND transactions.status = ? AND transactions.category = ? AND transactions.wallet_type IN ? AND transactions.inserted_at BETWEEN ? AND ?",
			"debit", "completed", category, []string{"main", "bonus-locked"}, startTime, endTime).
		Group("transactions.user_id, users.external_id").
		Scan(&results).Error

	result := make(map[string]domain.BatchWageringData)
	for _, data := range results {
		result[data.ExternalID] = domain.BatchWageringData{
			BetSum: fmt.Sprintf("%.2f", data.Amount),
		}
	}

	if err != nil {
		return nil, "", fmt.Errorf("get wagering data: %w", err)
	}

	return result, timeRange, nil
}
