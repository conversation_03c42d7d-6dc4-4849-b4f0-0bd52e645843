package postgres

import (
	"bytes"
	"encoding/base64"
	"encoding/csv"
	"fmt"
	"log/slog"
	"time"
	_ "time/tzdata"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/robfig/cron/v3"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
	"gorm.io/gorm"
)

type DropoutUsersRepository struct {
	db     *gorm.DB
	config *utils.SendGridConfig
}

func NewDropoutUsersRepository(db *gorm.DB, config *utils.SendGridConfig) *DropoutUsersRepository {
	return &DropoutUsersRepository{
		db:     db,
		config: config,
	}
}

type UserReport struct {
	DaysAgo int
	Date    string
	Users   []struct {
		Email     string
		CreatedAt time.Time
	}
}

func (r *DropoutUsersRepository) GenerateDropoutReport(daysAgo int) (*UserReport, error) {
	targetDate := time.Now().AddDate(0, 0, -daysAgo)
	formattedDate := targetDate.Format("2006-01-02")

	var users []struct {
		Email     string
		CreatedAt time.Time
	}

	if err := r.db.Table("registered_emails").
		Select("email, created_at").
		Where("DATE(created_at) = ? AND registered = false", formattedDate).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to query users: %w", err)
	}

	return &UserReport{
		DaysAgo: daysAgo,
		Date:    formattedDate,
		Users:   users,
	}, nil
}

func (r *DropoutUsersRepository) GenerateAggregateReport() error {
	var reports []*UserReport

	for daysAgo := 1; daysAgo <= 14; daysAgo++ {
		report, err := r.GenerateDropoutReport(daysAgo)
		if err != nil {
			slog.Error("Failed to generate report", "days_ago", daysAgo, "error", err)
			continue
		}
		reports = append(reports, report)
	}

	if len(reports) == 0 {
		return fmt.Errorf("no reports were generated")
	}

	return r.SendDropoutReports(EmailConfig{}, reports)
}

type EmailConfig struct {
	FromEmail string
	ToEmails  []string
}

func (r *DropoutUsersRepository) SendDropoutReports(config EmailConfig, reports []*UserReport) error {
	config.FromEmail = "<EMAIL>"
	config.ToEmails = []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
	}

	personalization := mail.NewPersonalization()
	for _, email := range config.ToEmails {
		personalization.AddTos(mail.NewEmail("", email))
	}

	message := mail.NewV3Mail()
	message.SetFrom(mail.NewEmail("Report Sender", config.FromEmail))
	message.Subject = fmt.Sprintf("Sign-Up Dropout Reports - %s", time.Now().Format("02-01-2006"))
	message.AddPersonalizations(personalization)

	message.AddContent(mail.NewContent("text/plain",
		fmt.Sprintf("Please find the attached Sign-Up Dropout reports for %s.", time.Now().Format("02-01-2006"))))

	for _, report := range reports {
		var buf bytes.Buffer
		writer := csv.NewWriter(&buf)

		if err := writer.Write([]string{"Email Address", "Date of Sign-Up Start"}); err != nil {
			return fmt.Errorf("failed to write headers: %w", err)
		}

		for _, user := range report.Users {
			if err := writer.Write([]string{user.Email, user.CreatedAt.Format("2006-01-02")}); err != nil {
				return fmt.Errorf("failed to write record: %w", err)
			}
		}
		writer.Flush()

		attachment := mail.NewAttachment()
		attachment.SetContent(base64.StdEncoding.EncodeToString(buf.Bytes()))
		attachment.SetType("text/csv")
		attachment.SetFilename(fmt.Sprintf("Sign-Up Dropout - Day %d - [%s].csv", report.DaysAgo, report.Date))
		message.AddAttachment(attachment)
	}

	client := sendgrid.NewSendClient(r.config.SendGridAPIKey)
	_, err := client.Send(message)
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	return nil
}

func (r *DropoutUsersRepository) ScheduleDailyReports() {
	loc, err := time.LoadLocation("Europe/Paris")
	if err != nil {
		slog.Error("Failed to load timezone", "error", err)
		return
	}

	scheduler := cron.New(cron.WithLocation(loc))

	_, err = scheduler.AddFunc("0 10 * * *", func() {
		if err := r.GenerateAggregateReport(); err != nil {
			slog.Error("Failed to generate and send scheduled reports", "error", err)
		}
	})

	if err != nil {
		slog.Error("Failed to schedule reports", "error", err)
		return
	}

	scheduler.Start()
}
