package postgres

import (
	"github.com/google/uuid"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type vipUserBalance struct {
	UUIDKeyModel
	UserID    uuid.UUID `gorm:"type:uuid;index;not null"`
	Handle    float64   `gorm:"type:numeric;not null"`
	Coins     float64   `gorm:"type:numeric;not null"`
	BetsWon   int       `gorm:"type:bigint;not null"`
	TotalBets int       `gorm:"type:bigint;not null"`
	Source    string    `gorm:"type:text;not null"`
	User      user
}

func vipUserBalanceToRow(balance *domain.VIPUserBalance) *vipUserBalance {
	return &vipUserBalance{
		UserID:    balance.User.ID,
		Handle:    balance.Handle,
		Coins:     balance.Coins,
		BetsWon:   balance.BetsWon,
		TotalBets: balance.TotalBets,
		Source:    balance.Source,
	}
}

func rowToVIPUserBalance(balance *vipUserBalance) *domain.VIPUserBalance {
	return &domain.VIPUserBalance{
		ID:        balance.ID,
		Handle:    balance.Handle,
		Coins:     balance.Coins,
		BetsWon:   balance.BetsWon,
		TotalBets: balance.TotalBets,
		Source:    balance.Source,
		User:      rowToUser(balance.User),
	}
}
