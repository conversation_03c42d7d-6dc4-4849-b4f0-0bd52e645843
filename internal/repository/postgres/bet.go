package postgres

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

type betCommon struct {
	UUIDKeyModel
	GameID          uuid.UUID `gorm:"type:uuid;not null"`
	UserID          uuid.UUID `gorm:"type:uuid;index;not null"`
	BetAmount       *float64  `gorm:"type:numeric"`
	ActualBetAmount *float64  `gorm:"type:numeric"`
	BetType         string    `gorm:"type:text;not null"`
	Currency        string    `gorm:"type:text;not null"`
	ConvertedTo     string    `gorm:"type:text"`
	ExternalID      string    `gorm:"type:text;unique;index;not null"`
	GhostMode       bool      `gorm:"type:bool;default:false;not null"`
	HiddenBoolean   bool      `gorm:"type:bool;default:false;not null"`
	Multiplier      float64   `gorm:"->;type:decimal generated always as (payout / coalesce(nullif(bet_amount, 0), 1)) stored"`
	Payout          *float64  `gorm:"type:numeric"`
	ActualWinAmount *float64  `gorm:"type:numeric"`
	RoundStatus     string    `gorm:"type:text;index;not null"`
	Time            time.Time `gorm:"type:timestamptz;not null"`
	Event           *string   `gorm:"type:text"`
	Odds            *float64  `gorm:"type:numeric"`
}

type bet struct {
	Bet  betCommon `gorm:"embedded"`
	User user
	Game game
}

type betWithStats struct {
	Bet           betCommon     `gorm:"embedded"`
	Game          game          `gorm:"embedded"`
	UserWithStats userWithStats `gorm:"embedded"`
}

func rowsToBets(
	rows []betWithStats,
	pageNum, pageSize int,
	vipTiers domain.VIPTiers,
) *domain.Bets {
	return &domain.Bets{
		Items: utils.MapSlice(rows, func(row betWithStats) domain.Bet {
			return rowToBet(row, vipTiers)
		}),
		Paging: domain.PagingWithoutTotalCount{
			CurrentPage: pageNum,
			PageSize:    pageSize,
		},
	}
}

func rowsToUserBets(
	rows []betWithStats,
	count int64,
	pageNum, pageSize int,
	vipTiers domain.VIPTiers,
	details domain.Details,
) *domain.UserBets {
	return &domain.UserBets{
		Items: utils.MapSlice(rows, func(row betWithStats) domain.Bet {
			return rowToUserBet(row, vipTiers)
		}),
		Paging: domain.Paging{
			TotalCount:  count,
			CurrentPage: pageNum,
			PageSize:    pageSize,
		},
		Details: details,
	}
}

func rowsToBetsForExport(rows []bet) []domain.Bet {
	return utils.MapSlice(rows, rowToBetForExport)
}

func rowToBaseBet(row betCommon) domain.Bet {
	actualBetAmount := row.ActualBetAmount
	if actualBetAmount == nil {
		actualBetAmount = row.BetAmount
	}

	actualWinAmount := row.ActualWinAmount
	if actualWinAmount == nil {
		actualWinAmount = row.Payout
	}

	return domain.Bet{
		ID:              row.ID,
		BetAmount:       row.BetAmount,
		ActualBetAmount: actualBetAmount,
		BetType:         domain.BetType(row.BetType),
		CoinsMultiplier: 1,
		Currency:        row.Currency,
		ConvertedTo:     row.ConvertedTo,
		ExternalID:      row.ExternalID,
		GhostMode:       row.GhostMode,
		HiddenBoolean:   row.HiddenBoolean,
		Multiplier:      row.Multiplier,
		Payout:          row.Payout,
		ActualWinAmount: actualWinAmount,
		RoundStatus:     row.RoundStatus,
		Time:            row.Time,
	}
}

func rowToBaseUserBet(row betCommon) domain.Bet {
	actualBetAmount := row.ActualBetAmount
	if actualBetAmount == nil {
		actualBetAmount = row.BetAmount
	}

	actualWinAmount := row.ActualWinAmount
	if actualWinAmount == nil {
		actualWinAmount = row.Payout
	}

	if row.BetType == "SPORTS" && row.Payout == nil {
		row.RoundStatus = "open"
	} else if row.BetType == "SPORTS" && row.Payout != nil {
		if *row.Payout > *row.BetAmount {
			row.RoundStatus = "won"
		} else {
			row.RoundStatus = "lost"
		}
	}

	return domain.Bet{
		ID:              row.ID,
		BetAmount:       row.BetAmount,
		ActualBetAmount: actualBetAmount,
		BetType:         domain.BetType(row.BetType),
		CoinsMultiplier: 1,
		Currency:        row.Currency,
		ConvertedTo:     row.ConvertedTo,
		ExternalID:      row.ExternalID,
		GhostMode:       row.GhostMode,
		HiddenBoolean:   row.HiddenBoolean,
		Multiplier:      row.Multiplier,
		Payout:          row.Payout,
		ActualWinAmount: actualWinAmount,
		RoundStatus:     row.RoundStatus,
		Time:            row.Time,
		Event:           row.Event,
		Odds:            row.Odds,
	}
}

func rowToBet(row betWithStats, vipTiers domain.VIPTiers) domain.Bet {
	bet := rowToBaseBet(row.Bet)
	bet.Game = rowToGame(row.Game)
	bet.User = rowToUserWithStats(
		userWithStats{
			User:         row.UserWithStats.User,
			CoinsSummary: row.UserWithStats.CoinsSummary,
			BetsSummary:  row.UserWithStats.BetsSummary,
		},
		vipTiers,
	)
	return bet
}

func rowToUserBet(row betWithStats, vipTiers domain.VIPTiers) domain.Bet {
	bet := rowToBaseUserBet(row.Bet)
	bet.Game = rowToGame(row.Game)
	bet.User = rowToUserWithStats(
		userWithStats{
			User:         row.UserWithStats.User,
			CoinsSummary: row.UserWithStats.CoinsSummary,
			BetsSummary:  row.UserWithStats.BetsSummary,
		},
		vipTiers,
	)
	return bet
}

func rowToBetPtr(row betWithStats, vipTiers domain.VIPTiers) *domain.Bet {
	return utils.PointerOf(rowToBet(row, vipTiers))
}

func rowToBetForUpsert(row bet) domain.Bet {
	bet := rowToBaseBet(row.Bet)
	bet.User.ID = row.Bet.UserID
	bet.Game = rowToGame(row.Game)
	return bet
}

func rowToBetForUpsertPtr(row bet) *domain.Bet {
	return utils.PointerOf(rowToBetForUpsert(row))
}

func rowToBetForExport(row bet) domain.Bet {
	bet := rowToBaseBet(row.Bet)
	bet.Game = rowToGame(row.Game)
	return bet
}

func betToRow(b *domain.Bet) bet {
	return bet{
		Bet: betCommon{
			GameID:          b.Game.ID,
			UserID:          b.User.ID,
			BetAmount:       b.BetAmount,
			ActualBetAmount: b.ActualBetAmount,
			BetType:         string(b.BetType),
			Currency:        b.Currency,
			ConvertedTo:     b.ConvertedTo,
			ExternalID:      b.ExternalID,
			GhostMode:       b.User.GhostMode,
			HiddenBoolean:   b.User.HideAllStats,
			Multiplier:      b.Multiplier,
			Payout:          b.Payout,
			ActualWinAmount: b.ActualWinAmount,
			RoundStatus:     b.RoundStatus,
			Time:            b.Time,
			Event:           b.Event,
			Odds:            b.Odds,
		},
		Game: gameToRow(b.Game),
	}
}

func rowToDomainBet(b bet) domain.Bet {
	bets := domain.Bet{
		ID:              b.Bet.ID,
		BetAmount:       b.Bet.BetAmount,
		BetType:         domain.BetType(b.Bet.BetType),
		CoinsMultiplier: 1,
		Currency:        b.Bet.Currency,
		ExternalID:      b.Bet.ExternalID,
		GhostMode:       b.Bet.GhostMode,
		HiddenBoolean:   b.Bet.HiddenBoolean,
		Multiplier:      b.Bet.Multiplier,
		Payout:          b.Bet.Payout,
		RoundStatus:     b.Bet.RoundStatus,
		Time:            b.Bet.Time,
		Game:            rowToGame(b.Game),
	}
	return bets
}
