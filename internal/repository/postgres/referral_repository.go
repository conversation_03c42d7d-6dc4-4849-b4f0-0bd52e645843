package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"math"
	"strconv"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ReferralRepository struct {
	db             *gorm.DB
	wageringClient domain.ElantilWageringClient
	directusClient domain.DirectusCMSClient
	bonusRepo      domain.UserBonusRepository
}

func NewReferralRepository(db *gorm.DB, wageringClient domain.ElantilWageringClient, directusClient domain.DirectusCMSClient, bonusRepo domain.UserBonusRepository) *ReferralRepository {
	return &ReferralRepository{db: db, wageringClient: wageringClient, directusClient: directusClient, bonusRepo: bonusRepo}
}

func (r *ReferralRepository) CreateUserReferralCampaign(ctx context.Context, userID string, username string, campaignName string) error {
	var existingCampaign commissionTracking
	err := r.db.Where("user_id = ? AND campaign_name = ?", userID, campaignName).
		First(&existingCampaign).Error
	if err == nil {
		return domain.ErrUserAlreadyHasReferralCampaign
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing campaign: %w", err)
	}

	var existingTracking commissionTracking
	err = r.db.Where("user_id = ?", userID).
		First(&existingTracking).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.ErrRecordNotFound
		}
		return fmt.Errorf("failed to fetch existing tracking record: %w", err)
	}

	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	referredUsers := []domain.ReferredUser{}
	referredUsersJSON, err := json.Marshal(referredUsers)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to marshal referred users: %w", err)
	}

	referralCode := uuid.New().String()[:8]
	trackingRecord := commissionTracking{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:                      userID,
		CampaignName:                campaignName,
		Username:                    username,
		RefferalCode:                referralCode,
		ReferredUsers:               referredUsersJSON,
		CurrentCommissionPercentage: existingTracking.OverallCommissionPercentage,
		LastCommissionPercentage:    existingTracking.OverallCommissionPercentage,
		OverallCommissionPercentage: existingTracking.OverallCommissionPercentage,
		LastCalculationTime:         time.Now(),
	}

	if err = tx.Create(&trackingRecord).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create tracking record: %w", err)
	}

	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	newCampaign := domain.Campaigns{
		CampaignName:         campaignName,
		ReferralCode:         referralCode,
		CommissionPercentage: existingTracking.OverallCommissionPercentage,
		ReferredUsers:        []domain.ReferredUser{},
	}

	return r.directusClient.CreateCampaignInDirectus(domain.ReferralCampaign{
		UserID:                      userID,
		Username:                    username,
		ParentID:                    existingTracking.ParentID,
		DefaultCommissionPercentage: existingTracking.OverallCommissionPercentage,
		Campaigns:                   []domain.Campaigns{newCampaign},
	})
}

// 	return r.db.WithContext(ctx).Exec("REFRESH MATERIALIZED VIEW CONCURRENTLY user_wagering_summary").Error
// }

func (r *ReferralRepository) GetUserReferralCampaign(ctx context.Context, userID string, campaignName string) ([]domain.UserCampaignResponse, int, error) {
	var trackingRecords []commissionTracking
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)
	if campaignName != "" {
		query = query.Where("campaign_name = ?", campaignName)
	}
	query = query.Order("CASE WHEN campaign_name = 'default' THEN 0 ELSE 1 END, created_at DESC")
	err := query.Find(&trackingRecords).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []domain.UserCampaignResponse{}, 0, nil
		}
		slog.Error("Error fetching user referral campaigns", "error", err, "userID", userID)
		return nil, 0, fmt.Errorf("failed to fetch user referral campaigns: %w", err)
	}

	response := make([]domain.UserCampaignResponse, 0, len(trackingRecords))
	for _, record := range trackingRecords {
		campaign := domain.UserCampaignResponse{
			CampaignsName:       record.CampaignName,
			ReferralCode:        record.RefferalCode,
			ComissionPercentage: record.CurrentCommissionPercentage,
		}
		response = append(response, campaign)
	}

	return response, len(response), nil
}

func (r *ReferralRepository) DetectIfUserIsNotReferringItself(ctx context.Context, referralCode string, userID string) (bool, error) {
	var existingTracking commissionTracking
	err := r.db.WithContext(ctx).Where("user_id = ? AND refferal_code = ?", userID, referralCode).First(&existingTracking).Error
	if err == nil {
		slog.Error("User trying to use their own referral code",
			"userID", userID,
			"referralCode", referralCode)
		return false, nil
	}

	if err != gorm.ErrRecordNotFound {
		slog.Error("Error checking for self-referral",
			"error", err,
			"userID", userID,
			"referralCode", referralCode)
		return false, fmt.Errorf("failed to check for self-referral: %w", err)
	}

	return true, nil
}

func (r *ReferralRepository) AddUserToTheListOfRefferedUsers(ctx context.Context, referralCode string, userID string, username string) error {
	var count int64
	result := r.db.WithContext(ctx).Table("users").Where("external_id = ?", userID).Count(&count)
	if result.Error != nil {
		slog.Error("Error checking if user exists", "error", result.Error, "userID", userID)
		return domain.ErrRecordNotFound
	}

	if count == 0 {
		slog.Error("User does not exist", "userID", userID)
		return domain.ErrRecordNotFound
	}

	// fraud detection in case if user is trying to refer itself
	isUserNotReferringItself, err := r.DetectIfUserIsNotReferringItself(ctx, referralCode, userID)
	if err != nil {
		slog.Error("Error detecting if user is not referring itself", "error", err, "userID", userID)
		return fmt.Errorf("failed to detect if user is not referring itself: %w", err)
	}

	if !isUserNotReferringItself {
		slog.Error("User is trying to refer itself", "userID", userID)
		return domain.ErrUserReferringItself
	}

	isUserInCampaign, _, err := r.IsUserInAnyReferralCampaign(ctx, userID)
	if err != nil {
		slog.Error("Error checking if user is in any referral campaign", "error", err, "userID", userID)
		return domain.ErrResourceNotFound
	}

	if isUserInCampaign {
		slog.Error("User is already in a referral campaign", "userID", userID)
		return domain.ErrUserAlreadyInReferredList
	}

	ifAdminCampaign, rewardAmount, err := r.CheckIfCodeIsfromAdminCampaign(ctx, referralCode, userID)
	if err != nil {
		switch {
		case err == domain.ErrCodeUsed:
			return domain.ErrCodeUsed
		case err == domain.ErrCodeLimitReached:
			return domain.ErrCodeLimitReached
		case errors.Is(err, domain.ErrRecordNotFound):
			slog.Info("Code not found in admin campaigns, checking regular campaigns")
		default:
			return fmt.Errorf("failed to check if code is from admin campaign: %w", err)
		}
	}
	if ifAdminCampaign {
		if err := r.handleAdminCampaignAndAssignBonus(ctx, userID, rewardAmount); err != nil {
			return fmt.Errorf("failed to handle admin campaign and assign bonus: %w", err)
		}
		return nil
	}
	var commissionTracking commissionTracking
	err = r.db.WithContext(ctx).Where("refferal_code = ?", referralCode).First(&commissionTracking).Error
	if err != nil {
		return domain.ErrRecordNotFound
	}

	newRefferedUser := domain.ReferredUser{
		UserID:                   userID,
		Username:                 username,
		CreatedAt:                time.Now(),
		LastCasinoWageringAmount: 0,
		LastSportsWageringAmount: 0,
		LastCommission:           0,
		TotalWagered:             0,
		TotalCommission:          0,
	}

	var referredUsers []domain.ReferredUser
	if err := json.Unmarshal(commissionTracking.ReferredUsers, &referredUsers); err != nil {
		return fmt.Errorf("failed to unmarshal referred users: %w", err)
	}

	referredUsers = append(referredUsers, newRefferedUser)
	referredUsersJSON, err := json.Marshal(referredUsers)
	if err != nil {
		return fmt.Errorf("failed to marshal referred users: %w", err)
	}

	if err := r.db.WithContext(ctx).Model(&commissionTracking).
		Update("referred_users", referredUsersJSON).Error; err != nil {
		return fmt.Errorf("failed to update commission tracking record: %w", err)
	}

	parentId, err := r.GetParentUserIDByReferralCode(ctx, referralCode)
	if err != nil {
		return fmt.Errorf("failed to get parent ID by referral code: %w", err)
	}

	updateResult := r.db.WithContext(ctx).
		Table("commission_tracking").
		Where("user_id = ?", userID).
		Update("parent_id", parentId)

	if updateResult.Error != nil {
		return fmt.Errorf("failed to update parent_id: %w", updateResult.Error)
	}

	go r.directusClient.UpdateParentIdInDirectus(ctx, userID, parentId)
	err = r.directusClient.UpsertReferredUsersInDirectus(
		commissionTracking.UserID,
		referralCode,
		[]domain.ReferredUser{newRefferedUser})

	if err != nil {
		return fmt.Errorf("failed to upsert referred user in Directus: %w", err)
	}

	go r.wageringClient.UpsertPlayerRefferals(commissionTracking.UserID, userID, username)
	return nil
}

func (r *ReferralRepository) UpdateUserCommisionPercentage(ctx context.Context, userId string, campaignName string, commissionPercentage string, campaignReferredUsers []domain.CampaignReferredUsers) error {
	commissionFloat, err := strconv.ParseFloat(commissionPercentage, 64)
	if err != nil {
		return fmt.Errorf("failed to convert commission percentage: %w", err)
	}

	var trackingRecord commissionTracking
	err = r.db.WithContext(ctx).Where("user_id = ? AND campaign_name = ?", userId, campaignName).
		First(&trackingRecord).Error
	if err != nil {
		return fmt.Errorf("failed to fetch tracking record: %w", err)
	}

	var referredUsers []domain.ReferredUser
	if err := json.Unmarshal(trackingRecord.ReferredUsers, &referredUsers); err != nil {
		return fmt.Errorf("failed to unmarshal referred users: %w", err)
	}

	referredUserIDs := make(map[string]struct{}, len(referredUsers))
	for _, user := range referredUsers {
		referredUserIDs[user.UserID] = struct{}{}
	}

	if trackingRecord.CurrentCommissionPercentage == commissionFloat && len(referredUsers) != len(campaignReferredUsers) {
		for _, campaignUser := range campaignReferredUsers {
			if _, exists := referredUserIDs[campaignUser.UserID]; !exists {
				err := r.UpdateReferredUsersFromCMS(ctx, trackingRecord, referredUsers, campaignUser)
				if err != nil {
					return fmt.Errorf("failed to update referred users from CMS: %w", err)
				}
			}
		}
		return nil
	}

	if len(referredUsers) == 0 {
		for _, campaignUser := range campaignReferredUsers {
			if _, exists := referredUserIDs[campaignUser.UserID]; !exists {
				err := r.UpdateReferredUsersFromCMS(ctx, trackingRecord, referredUsers, campaignUser)
				if err != nil {
					slog.Error("Error updating referred users from CMS", "error", err)
					return fmt.Errorf("failed to update referred users from CMS: %w", err)
				}
			}
		}

		updates := map[string]interface{}{
			"last_commission_percentage":    trackingRecord.CurrentCommissionPercentage,
			"current_commission_percentage": commissionFloat,
			"rate_change_time":              time.Now(),
		}
		result := r.db.WithContext(ctx).Model(&trackingRecord).Updates(updates)
		return result.Error
	}

	userIDs := make([]string, len(referredUsers))
	for i, user := range referredUsers {
		userIDs[i] = user.UserID
	}

	casinoVt := "casino_vt"
	casinoWagering, _, err := r.wageringClient.GetBatchWageringSummary(context.Background(), userIDs, &casinoVt, "lifetime", "", "")
	if err != nil {
		return fmt.Errorf("failed to get casino wagering: %w", err)
	}

	sportVt := "sport_vt"
	sportsWagering, _, err := r.wageringClient.GetBatchWageringSummary(context.Background(), userIDs, &sportVt, "lifetime", "", "")
	if err != nil {
		return fmt.Errorf("failed to get sports wagering: %w", err)
	}

	var newCommission, LastCasinoWageringAmount, LastSportsWageringAmount float64
	var updatedReferredUsers []domain.ReferredUser
	commissionRate := trackingRecord.CurrentCommissionPercentage / 100

	for _, user := range referredUsers {
		currentCasinoBet, _ := strconv.ParseFloat(casinoWagering[user.UserID].BetSum, 64)
		currentSportsBet, _ := strconv.ParseFloat(sportsWagering[user.UserID].BetSum, 64)

		newCasinoWagering := math.Max(0, currentCasinoBet-user.LastCasinoWageringAmount)
		newSportsWagering := math.Max(0, currentSportsBet-user.LastSportsWageringAmount)

		if newCasinoWagering > 0 {
			casinoCommission := ((newCasinoWagering * 0.01) / 2) * commissionRate
			user.LastCommission = casinoCommission
			user.TotalCommission += casinoCommission
			newCommission += casinoCommission
		}
		if newSportsWagering > 0 {
			sportsCommission := ((newSportsWagering * 0.03) / 2) * commissionRate
			user.LastCommission += sportsCommission
			user.TotalCommission += sportsCommission
			newCommission += sportsCommission
		}

		user.LastCasinoWageringAmount = currentCasinoBet
		user.LastSportsWageringAmount = currentSportsBet
		user.TotalWagered += newCasinoWagering + newSportsWagering
		LastCasinoWageringAmount += currentCasinoBet
		LastSportsWageringAmount += currentSportsBet
		updatedReferredUsers = append(updatedReferredUsers, user)
	}

	referredUsersJSON, err := json.Marshal(updatedReferredUsers)
	if err != nil {
		return fmt.Errorf("failed to marshal updated referred users: %w", err)
	}

	updates := map[string]interface{}{
		"referred_users":                referredUsersJSON,
		"last_commission_percentage":    trackingRecord.CurrentCommissionPercentage,
		"current_commission_percentage": commissionFloat,
		"rate_change_time":              time.Now(),
		"last_casino_wagering_amount":   LastCasinoWageringAmount,
		"last_sports_wagering_amount":   LastSportsWageringAmount,
		"total_commission":              trackingRecord.TotalCommission + newCommission,
		"eligible_commission":           trackingRecord.EligibleCommission + newCommission,
	}

	result := r.db.WithContext(ctx).Model(&trackingRecord).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update record: %w", result.Error)
	}

	for _, campaignUser := range campaignReferredUsers {
		if _, exists := referredUserIDs[campaignUser.UserID]; !exists {
			err := r.UpdateReferredUsersFromCMS(ctx, trackingRecord, updatedReferredUsers, campaignUser)
			if err != nil {
				return fmt.Errorf("failed to update referred users from CMS: %w", err)
			}
		}
	}

	return nil
}

func (r *ReferralRepository) CheckIfUserAlreadyUsedTheCode(ctx context.Context, referralCode string, userId string) (bool, error) {
	query := `
	SELECT
    EXISTS (
        SELECT 1
        FROM commission_tracking,
        jsonb_array_elements(referred_users) as referred_user
        WHERE referred_user->>'userId' = ?
              AND refferal_code = ?
    ) AS user_and_code_exists;`

	type Result struct {
		Exists bool `gorm:"column:user_and_code_exists"`
	}

	var result Result
	err := r.db.WithContext(ctx).Raw(query, userId, referralCode).Scan(&result).Error
	if err != nil {
		return false, fmt.Errorf("failed to check if user already used the code: %w", err)
	}

	return result.Exists, nil
}

func (r *ReferralRepository) CalculateUserCommissions(ctx context.Context, userID string) error {
	var trackingRecords []commissionTracking
	err := r.db.Where("user_id = ?", userID).Find(&trackingRecords).Error
	if err != nil {
		return fmt.Errorf("failed to fetch commission tracking records: %w", err)
	}

	for _, trackingRecord := range trackingRecords {
		var referredUsers []domain.ReferredUser
		err = json.Unmarshal(trackingRecord.ReferredUsers, &referredUsers)
		if err != nil {
			slog.Error("Error unmarshaling referred users", "error", err)
			continue
		}

		if len(referredUsers) == 0 {
			continue
		}

		userIDs := make([]string, len(referredUsers))
		for i, user := range referredUsers {
			userIDs[i] = user.UserID
		}

		casinoVt := "casino_vt"
		casinoWagering, _, err := r.wageringClient.GetBatchWageringSummary(ctx, userIDs, &casinoVt, "lifetime", "", "")
		if err != nil {
			slog.Error("Error fetching casino wagering data", "error", err)
			continue
		}

		sportVt := "sport_vt"
		sportsWagering, _, err := r.wageringClient.GetBatchWageringSummary(ctx, userIDs, &sportVt, "lifetime", "", "")
		if err != nil {
			slog.Error("Error fetching sports wagering data", "error", err)
			continue
		}

		var newCommission, LastCasinoWageringAmount, LastSportsWageringAmount float64
		var updatedReferredUsers []domain.ReferredUser
		commissionRate := trackingRecord.CurrentCommissionPercentage / 100

		for _, user := range referredUsers {
			currentCasinoBet, _ := strconv.ParseFloat(casinoWagering[user.UserID].BetSum, 64)
			currentSportsBet, _ := strconv.ParseFloat(sportsWagering[user.UserID].BetSum, 64)

			newCasinoWagering := math.Max(0, currentCasinoBet-user.LastCasinoWageringAmount)
			newSportsWagering := math.Max(0, currentSportsBet-user.LastSportsWageringAmount)

			if newCasinoWagering > 0 {
				casinoCommission := ((newCasinoWagering * 0.01) / 2) * commissionRate
				user.LastCommission = casinoCommission
				user.TotalCommission += casinoCommission
				newCommission += casinoCommission
			}
			if newSportsWagering > 0 {
				sportsCommission := ((newSportsWagering * 0.03) / 2) * commissionRate
				user.LastCommission += sportsCommission
				user.TotalCommission += sportsCommission
				newCommission += sportsCommission
			}

			user.LastCasinoWageringAmount = currentCasinoBet
			user.LastSportsWageringAmount = currentSportsBet
			user.TotalWagered += newCasinoWagering + newSportsWagering
			LastCasinoWageringAmount += currentCasinoBet
			LastSportsWageringAmount += currentSportsBet
			updatedReferredUsers = append(updatedReferredUsers, user)
		}

		referredUsersJSON, err := json.Marshal(updatedReferredUsers)
		if err != nil {
			slog.Error("Error marshaling updated referred users", "error", err)
			continue
		}

		updates := map[string]interface{}{
			"referred_users":              referredUsersJSON,
			"eligible_commission":         trackingRecord.EligibleCommission + newCommission,
			"total_commission":            trackingRecord.TotalCommission + newCommission,
			"last_calculation_time":       time.Now(),
			"last_casino_wagering_amount": LastCasinoWageringAmount,
			"last_sports_wagering_amount": LastSportsWageringAmount,
			"EligibleToClaim":             "false",
		}

		if err := r.db.Model(&trackingRecord).Updates(updates).Error; err != nil {
			slog.Error("Error updating tracking record", "error", err)
			continue
		}
	}

	return nil
}

func (r *ReferralRepository) ClaimReward(ctx context.Context, token string, userExternalID string) (float64, error) {
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0.0, fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	type CommissionTotal struct {
		TotalCommission    float64 `gorm:"column:total_commission"`
		ClaimedCommission  float64 `gorm:"column:claimed_commission"`
		CampaignName       string  `gorm:"column:campaign_name"`
		EligibleCommission float64 `gorm:"column:eligible_commission"`
	}

	var commissionTotals []CommissionTotal
	if err := tx.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).
		Model(&commissionTracking{}).
		Select("total_commission, claimed_commission, campaign_name, eligible_commission").
		Where("user_id = ?", userExternalID).
		Find(&commissionTotals).Error; err != nil {
		tx.Rollback()
		return 0.0, fmt.Errorf("failed to get commission totals: %w", err)
	}

	var claimableAmount float64
	for _, ct := range commissionTotals {
		claimableAmount += ct.EligibleCommission
	}

	if claimableAmount <= 0 {
		tx.Rollback()
		return 0.0, fmt.Errorf("no claimable commission available (eligible: %f)", claimableAmount)
	}

	walletUpdateRequest := domain.UserWalletUpdateRequest{
		CurrencyCode: "USD",
		Amount:       strconv.FormatFloat(claimableAmount, 'f', 2, 64),
		Reason:       "Referral Commission",
		ProductId:    "commission",
		Category:     "referral_commission",
	}

	_, err := r.wageringClient.UpdateUserWallet(ctx, token, walletUpdateRequest)
	if err != nil {
		slog.Error("Error updating user wallet", "error", err)
		return 0.0, fmt.Errorf("failed to update user wallet: %w", err)
	}

	for _, ct := range commissionTotals {
		if ct.EligibleCommission <= 0 {
			continue
		}
		result := tx.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate}).
			Model(&commissionTracking{}).
			Where("user_id = ? AND campaign_name = ?",
				userExternalID, ct.CampaignName).
			Updates(map[string]interface{}{
				"claimed_commission":  gorm.Expr("claimed_commission + ?", ct.EligibleCommission),
				"eligible_commission": gorm.Expr("eligible_commission - ?", ct.EligibleCommission),
			})

		if result.Error != nil {
			tx.Rollback()
			return 0.0, fmt.Errorf("failed to update campaign claim: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			tx.Rollback()
			return 0.0, fmt.Errorf("concurrent claim detected for campaign %s", ct.CampaignName)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return 0.0, fmt.Errorf("failed to commit transaction: %w", err)
	}

	commissionHistory := commissionHistory{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		UserID:   userExternalID,
		Currency: "USD",
		Claimed:  claimableAmount,
	}

	if err := r.db.WithContext(ctx).Create(&commissionHistory).Error; err != nil {
		return 0.0, fmt.Errorf("failed to create commission history: %w", err)
	}

	return claimableAmount, nil
}

func (r *ReferralRepository) GetCommissionHistory(ctx context.Context, userID string, offset, limit int) (domain.CommissionHistoryResponse, error) {
	var commissionHistories []commissionHistory
	var totalRecords int64

	query := r.db.WithContext(ctx).Model(&commissionHistory{}).
		Where("user_id = ?", userID).
		Order("created_at DESC").Count(&totalRecords).
		Offset(offset)

	if limit > 0 {
		query = query.Limit(limit)
	}

	// Execute query
	err := query.Find(&commissionHistories).Error
	if err != nil {
		return domain.CommissionHistoryResponse{}, fmt.Errorf("failed to get commission histories: %w", err)
	}

	histories := make([]domain.CommissionHistory, len(commissionHistories))
	for i, history := range commissionHistories {
		histories[i] = domain.CommissionHistory{
			ID:            history.ID,
			Currency:      history.Currency,
			ClaimedAmount: history.Claimed,
			CreatedAt:     history.CreatedAt,
		}
	}

	response := domain.CommissionHistoryResponse{
		CommissionHistory: histories,
		Offset:            offset,
		Limit:             limit,
		Total:             totalRecords,
	}

	return response, nil
}

func (r *ReferralRepository) GetDetailedCommissionInfo(ctx context.Context, parentUserId string) (domain.UserCommissionInfo, error) {
	var trackingRecords []commissionTracking

	err := r.db.WithContext(ctx).
		Table("commission_tracking").
		Where("user_id = ? AND deleted_at IS NULL", parentUserId).
		Order("CASE WHEN campaign_name = 'default' THEN 0 ELSE 1 END, created_at DESC").
		Find(&trackingRecords).Error
	if err != nil {
		return domain.UserCommissionInfo{}, fmt.Errorf("failed to get tracking records: %w", err)
	}

	result := domain.UserCommissionInfo{
		UserID:    parentUserId,
		Campaigns: make([]domain.CampaignCommission, 0),
	}

	var totalClaimed float64
	var totalReferredUsers int

	for _, record := range trackingRecords {
		var referredUsers []domain.ReferredUser
		if len(record.ReferredUsers) == 0 || string(record.ReferredUsers) == "null" {
			referredUsers = []domain.ReferredUser{}
		} else if err := json.Unmarshal(record.ReferredUsers, &referredUsers); err != nil {
			return domain.UserCommissionInfo{}, fmt.Errorf("failed to unmarshal referred users: %w", err)
		}

		// Skip zero time
		if record.LastCalculationTime.IsZero() {
			record.LastCalculationTime = time.Now()
		}

		campaign := domain.CampaignCommission{
			CampaignName:         record.CampaignName,
			RefferalCode:         record.RefferalCode,
			TotalCommission:      record.TotalCommission,
			ReferredUsers:        make([]domain.ReferredUserCommission, 0),
			CommissionPercentage: record.CurrentCommissionPercentage,
			ReferralCount:        len(referredUsers),
			CampaignCreatedAt:    record.CreatedAt,
			LastCalculationTime:  record.LastCalculationTime,
		}

		totalReferredUsers += len(referredUsers)

		for _, user := range referredUsers {
			var userCreatedAt time.Time
			err = r.db.WithContext(ctx).
				Table("users").
				Select("created_at").
				Where("external_id = ?", user.UserID).
				Scan(&userCreatedAt).Error
			if err != nil {
				return domain.UserCommissionInfo{}, fmt.Errorf("failed to get user creation date: %w", err)
			}

			var depositAmount float64
			err = r.db.WithContext(ctx).
				Table("user_wagering_summary").
				Select("total_deposits").
				Where("external_id = ?", user.UserID).
				Scan(&depositAmount).Error
			if err != nil {
				return domain.UserCommissionInfo{}, fmt.Errorf("failed to get deposit amount: %w", err)
			}

			userCommission := domain.ReferredUserCommission{
				// UserID:          user.UserID,
				Username:        user.Username,
				CampaignName:    record.CampaignName,
				CreatedAt:       userCreatedAt,
				CasinoWagered:   strconv.FormatFloat(user.LastCasinoWageringAmount, 'f', -1, 64),
				SportsWagered:   strconv.FormatFloat(user.LastSportsWageringAmount, 'f', -1, 64),
				TotalWagered:    user.TotalWagered,
				Deposits:        strconv.FormatFloat(depositAmount, 'f', -1, 64),
				NetEarnings:     strconv.FormatFloat(depositAmount-user.TotalWagered, 'f', -1, 64),
				TotalCommission: user.TotalCommission,
			}

			campaign.ReferredUsers = append(campaign.ReferredUsers, userCommission)
			campaign.TotalWagered += user.TotalWagered
		}

		if len(campaign.ReferredUsers) > 0 {
			username := campaign.ReferredUsers[0].Username
			if len(username) > 2 {
				username = username[len(username)-2:]
			}
			campaign.TopReferredUser = username
		}

		result.Campaigns = append(result.Campaigns, campaign)
		result.TotalWageredAllCampaigns += campaign.TotalWagered
		result.TotalCommission += record.TotalCommission
		totalClaimed += record.ClaimedCommission
		result.ClaimableCommission += record.EligibleCommission
	}

	if result.ClaimableCommission < 0 {
		result.ClaimableCommission = 0
	}

	result.TotalReferrals = totalReferredUsers    // Changed to total number of users
	result.TotalCampaigns = len(result.Campaigns) // Added total campaigns count
	result.ClaimedCommission = totalClaimed

	if len(result.Campaigns) > 0 {
		result.AverageReferrals = float64(totalReferredUsers) / float64(len(result.Campaigns))

		var maxReferrals int
		for _, campaign := range result.Campaigns {
			if campaign.ReferralCount > maxReferrals {
				maxReferrals = campaign.ReferralCount
				result.TopCampaign = campaign.CampaignName
			}
		}
	}

	return result, nil
}

func (r *ReferralRepository) GetUserWageringSummaryByExternalIDs(ctx context.Context, externalIDs []string) ([]domain.WageringSummary, error) {
	var wageringSummaries []domain.WageringSummary
	err := r.db.WithContext(ctx).
		Table("user_wagering_summary").
		Where("external_id IN ?", externalIDs).
		Find(&wageringSummaries).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get wagering summaries: %w", err)
	}
	return wageringSummaries, nil
}

func (r *ReferralRepository) GetParentUserIDByReferralCode(ctx context.Context, referralCode string) (string, error) {
	var existingTracking commissionTracking
	err := r.db.WithContext(ctx).Where("refferal_code = ?", referralCode).First(&existingTracking).Error
	if err != nil {
		return "", fmt.Errorf("failed to fetch referral campaign: %w", err)
	}
	return existingTracking.UserID, nil
}

func (r *ReferralRepository) IsUserInAnyReferralCampaign(ctx context.Context, userID string) (bool, string, error) {
	query := `
    SELECT
    ct.user_id AS userId,
    ct.campaign_name AS campaign_name
	FROM
    commission_tracking ct,
    jsonb_array_elements(ct.referred_users) as referred_user
	WHERE
    referred_user->>'userId' = ?
	LIMIT 1;`

	type Result struct {
		UserID       string `gorm:"column:userid"`
		CampaignName string `gorm:"column:campaign_name"`
	}

	var result Result
	err := r.db.WithContext(ctx).Raw(query, userID).Scan(&result).Error

	// Handle different cases
	if err == gorm.ErrRecordNotFound {
		return true, "", nil
	}

	if err != nil {
		return false, "", fmt.Errorf("database error checking referral status: %w", err)
	}

	return result.UserID != "", result.UserID, nil
}

func (r *ReferralRepository) CreateDefaultCampaignInDirectus(ctx context.Context, userId, username string) error {
	var existingTracking commissionTracking
	err := r.db.Where("user_id = ? AND campaign_name = ?", userId, "default").
		First(&existingTracking).Error
	if err != nil {
		return fmt.Errorf("failed to fetch referral campaign: %w", err)
	}

	// create default campaign in directus
	err = r.directusClient.CreateFirstCampaignOfUser(domain.ReferralCampaign{
		UserID:                      userId,
		Username:                    username,
		ParentID:                    existingTracking.ParentID,
		DefaultCommissionPercentage: existingTracking.CurrentCommissionPercentage,
		Campaigns: []domain.Campaigns{
			{
				CampaignName:         "default",
				ReferralCode:         existingTracking.RefferalCode,
				CommissionPercentage: existingTracking.CurrentCommissionPercentage,
				ReferredUsers:        []domain.ReferredUser{},
			},
		},
	})

	if err != nil {
		return fmt.Errorf("failed to create campaign in Directus: %w", err)
	}

	return nil
}

func (r *ReferralRepository) DeleteCampaigns(ctx context.Context, userID string, campaignName string) error {
	// First check if campaign exists and has referred users
	var trackingRecord commissionTracking
	err := r.db.Where("user_id = ? AND campaign_name = ?", userID, campaignName).
		First(&trackingRecord).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("campaign not found")
		}
		return fmt.Errorf("failed to fetch campaign: %w", err)
	}

	var referredUsers []struct {
		UserID string `json:"userId"`
	}
	if err := json.Unmarshal(trackingRecord.ReferredUsers, &referredUsers); err != nil {
		return fmt.Errorf("failed to unmarshal referred users: %w", err)
	}

	if len(referredUsers) > 0 {
		return domain.ErrDeleleCampaignWithReferredUsers
	}

	result := r.db.Where("user_id = ? AND campaign_name = ?", userID, campaignName).
		Delete(&commissionTracking{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete campaign: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no campaign found to delete")
	}
	return nil
}

func (r *ReferralRepository) GetCampaignStats(ctx context.Context, userID string, campaignName string) (domain.CampaignStats, error) {
	var trackingRecord commissionTracking
	err := r.db.Where("user_id = ? AND campaign_name = ?", userID, campaignName).
		First(&trackingRecord).Error
	if err != nil {
		return domain.CampaignStats{}, fmt.Errorf("failed to get tracking record: %w", err)
	}

	var referredUsers []struct {
		UserID string `json:"userId"`
	}
	if err := json.Unmarshal(trackingRecord.ReferredUsers, &referredUsers); err != nil {
		return domain.CampaignStats{}, fmt.Errorf("failed to unmarshal referred users: %w", err)
	}

	userIDs := make([]string, len(referredUsers))
	for i, user := range referredUsers {
		userIDs[i] = user.UserID
	}

	var totalWagered, totalDeposits float64
	if len(userIDs) > 0 {
		var wageringSummary struct {
			TotalWagered  float64 `gorm:"column:total_wagered"`
			TotalDeposits float64 `gorm:"column:total_deposits"`
		}
		err = r.db.Table("user_wagering_summary").
			Select("SUM(total_wagered) as total_wagered, SUM(total_deposits) as total_deposits").
			Where("external_id IN ?", userIDs).
			Scan(&wageringSummary).Error
		if err != nil {
			return domain.CampaignStats{}, fmt.Errorf("failed to get wagering summary: %w", err)
		}
		totalWagered = wageringSummary.TotalWagered
		totalDeposits = wageringSummary.TotalDeposits
	}

	return domain.CampaignStats{
		TotalReferrals:      len(referredUsers),
		TotalWagered:        totalWagered,
		TotalDeposits:       totalDeposits,
		TotalCommission:     trackingRecord.TotalCommission,
		ClaimedCommission:   trackingRecord.ClaimedCommission,
		ClaimableCommission: trackingRecord.EligibleCommission,
		CampaignCreatedAt:   trackingRecord.CreatedAt,
	}, nil
}

func (r *ReferralRepository) CalculateAllUsersCommissions(ctx context.Context) error {
	var userIDs []string
	err := r.db.Model(&commissionTracking{}).
		Distinct("user_id").
		Pluck("user_id", &userIDs).Error
	if err != nil {
		return fmt.Errorf("failed to get user IDs: %w", err)
	}

	numWorkers := 5
	jobs := make(chan string, len(userIDs))
	errs := make(chan error, len(userIDs))
	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for userID := range jobs {
				if err := r.CalculateUserCommissions(ctx, userID); err != nil {
					slog.Error("Failed to calculate commissions",
						"error", err,
						"userID", userID)
					errs <- fmt.Errorf("user %s: %w", userID, err)
					continue
				}
			}
		}()
	}

	// Send jobs
	for _, userID := range userIDs {
		jobs <- userID
	}
	close(jobs)

	// Wait for workers
	wg.Wait()
	close(errs)

	// Collect errors
	var errList []error
	for err := range errs {
		errList = append(errList, err)
	}

	if len(errList) > 0 {
		return fmt.Errorf("failed to process %d users", len(errList))
	}

	return nil
}

func (r *ReferralRepository) UpdateUserDefaultCommissionPercentage(userId string, defaultCommissionPercentage string) error {
	commission, err := strconv.ParseFloat(defaultCommissionPercentage, 64)
	if err != nil {
		return fmt.Errorf("failed to parse commission percentage: %w", err)
	}

	result := r.db.Table("commission_tracking").
		Where("user_id = ?", userId).
		Update("overall_commission_percentage", commission)
	if result.Error != nil {
		return fmt.Errorf("failed to update commission percentage: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no records updated")
	}

	return nil
}

func (r *ReferralRepository) ManuallyUpdateReferralOfUser(userIdToUpdate string, userNameToUpdate string, newParentId string) error {
	var parentUserCampaign commissionTracking
	err := r.db.Where("user_id = ? AND campaignName = 'default'", newParentId).First(&parentUserCampaign).Error
	if err != nil {
		return fmt.Errorf("failed to get parent user campaign: %w", err)
	}
	var referredUsers []domain.ReferredUser
	err = json.Unmarshal(parentUserCampaign.ReferredUsers, &referredUsers)
	if err != nil {
		return fmt.Errorf("failed to unmarshal referred users: %w", err)
	}

	referredUsers = append(referredUsers, domain.ReferredUser{
		UserID:                   userIdToUpdate,
		Username:                 userNameToUpdate,
		CreatedAt:                time.Now(),
		LastCasinoWageringAmount: 0,
		LastSportsWageringAmount: 0,
		LastCommission:           0,
		TotalWagered:             0,
		TotalCommission:          0,
	})

	result := r.db.Table("commission_tracking").
		Where("user_id = ?", userIdToUpdate).
		Update("parent_id", newParentId)
	if result.Error != nil {
		return fmt.Errorf("failed to update parent_id in referral_campaigns: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no records updated in referral_campaigns")
	}

	err = r.directusClient.UpdateParentIdInDirectus(context.Background(), userIdToUpdate, newParentId)
	if err != nil {
		return fmt.Errorf("failed to update parent_id in Directus: %w", err)
	}
	err = r.directusClient.UpsertReferredUsersInDirectus(newParentId, parentUserCampaign.RefferalCode, referredUsers)
	if err != nil {
		return fmt.Errorf("failed to update referred users in Directus: %w", err)
	}

	return nil
}

func (r *ReferralRepository) CreateAdminCampaign(ctx context.Context, referralCode string, rewardAmount float64, codeUsageLimit int) error {
	var data adminCampaign
	err := r.db.WithContext(ctx).Where("refferal_code = ?", referralCode).First(&data).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to fetch admin campaign: %w", err)
		}
	} else {
		return fmt.Errorf("admin campaign already exists")
	}

	// create admin campaign
	adminCampaign := adminCampaign{
		UUIDKeyModel: UUIDKeyModel{
			ID:        uuid.New(),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		RefferalCode:  referralCode,
		RewardAmount:  rewardAmount,
		UsageLimit:    codeUsageLimit,
		NumberOfUsage: 0,
	}

	if err := r.db.WithContext(ctx).Create(&adminCampaign).Error; err != nil {
		return fmt.Errorf("failed to create admin campaign: %w", err)
	}
	return nil
}

func (r *ReferralRepository) CheckIfCodeIsfromAdminCampaign(ctx context.Context, referralCode string, userId string) (bool, float64, error) {
	var data adminCampaign
	err := r.db.WithContext(ctx).Where("refferal_code = ?", referralCode).First(&data).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, 0, domain.ErrRecordNotFound
		}
		return false, 0, fmt.Errorf("failed to fetch admin campaign: %w", err)
	}
	var existingUserIds []string
	if len(data.UserIds) > 0 {
		if err := json.Unmarshal(data.UserIds, &existingUserIds); err != nil {
			return false, 0, fmt.Errorf("failed to parse user ids: %w", err)
		}
	}
	for _, id := range existingUserIds {
		if id == userId {
			return false, 0, domain.ErrCodeUsed
		}
	}
	if data.UsageLimit > 0 && data.NumberOfUsage >= data.UsageLimit {
		return false, 0, domain.ErrCodeLimitReached
	}
	existingUserIds = append(existingUserIds, userId)
	newUserIds, err := json.Marshal(existingUserIds)
	if err != nil {
		return false, 0, fmt.Errorf("failed to marshal user ids: %w", err)
	}
	result := r.db.WithContext(ctx).Model(&adminCampaign{}).
		Where("refferal_code = ?", referralCode).
		Updates(map[string]interface{}{
			"number_of_usage": gorm.Expr("number_of_usage + 1"),
			"user_ids":        newUserIds,
		})
	if result.Error != nil {
		return false, 0, fmt.Errorf("failed to update campaign: %w", result.Error)
	}
	return true, data.RewardAmount, nil
}

func (r *ReferralRepository) handleAdminCampaignAndAssignBonus(ctx context.Context, userID string, rewardAmount float64) error {
	err := r.bonusRepo.CreateCampaignBonusForUser(ctx, userID, rewardAmount, "admin campaign bonus", "this is an admin campaign bonus")
	if err != nil {
		return fmt.Errorf("failed to assign bonus to user: %w", err)
	}
	// update tags in Elantil
	err = r.wageringClient.UpsertPlayerActivityTagsByUserExternalID(userID, "player_activity", "qr_campaign_bonus", "true")
	if err != nil {
		return fmt.Errorf("failed to update tags in Elantil: %w", err)
	}

	return nil
}

func (r *ReferralRepository) UpdateReferredUsersFromCMS(ctx context.Context, trackingRecord commissionTracking, referredUsers []domain.ReferredUser, campaignUser domain.CampaignReferredUsers) error {
	var count int64
	result := r.db.WithContext(ctx).Table("users").Where("external_id = ?", campaignUser.UserID).Count(&count)
	if result.Error != nil {
		return domain.ErrRecordNotFound
	}

	if count == 0 {
		return domain.ErrRecordNotFound
	}

	isUserNotReferringItself, err := r.DetectIfUserIsNotReferringItself(ctx, trackingRecord.RefferalCode, campaignUser.UserID)
	if err != nil {
		return fmt.Errorf("failed to detect if user is not referring itself: %w", err)
	}

	if !isUserNotReferringItself {
		return domain.ErrUserReferringItself
	}
	// check if user is part of any campaign
	isUserInCampaign, _, err := r.IsUserInAnyReferralCampaign(ctx, campaignUser.UserID)
	if err != nil {
		return domain.ErrResourceNotFound
	}

	if isUserInCampaign {
		return domain.ErrUserAlreadyInReferredList
	}

	ifAdminCampaign, rewardAmount, err := r.CheckIfCodeIsfromAdminCampaign(ctx, trackingRecord.RefferalCode, campaignUser.UserID)
	if err != nil {
		switch {
		case err == domain.ErrCodeUsed:
			return domain.ErrCodeUsed
		case err == domain.ErrCodeLimitReached:
			return domain.ErrCodeLimitReached
		case errors.Is(err, domain.ErrRecordNotFound):
		default:
			return fmt.Errorf("failed to check if code is from admin campaign: %w", err)
		}
	}
	if ifAdminCampaign {
		if err := r.handleAdminCampaignAndAssignBonus(ctx, campaignUser.UserID, rewardAmount); err != nil {
			return fmt.Errorf("failed to handle admin campaign and assign bonus: %w", err)
		}
		return nil
	}
	referredUsers = append(referredUsers, domain.ReferredUser{
		UserID:                   campaignUser.UserID,
		Username:                 campaignUser.Username,
		CreatedAt:                time.Now(),
		LastCasinoWageringAmount: 0,
		LastSportsWageringAmount: 0,
		LastCommission:           0,
		TotalWagered:             0,
		TotalCommission:          0,
	})

	referredUsersJSON, err := json.Marshal(referredUsers)
	if err != nil {
		return fmt.Errorf("failed to marshal referred users: %w", err)
	}

	updates := map[string]interface{}{
		"referred_users": referredUsersJSON,
	}

	res := r.db.WithContext(ctx).Model(&trackingRecord).Updates(updates)
	if res.Error != nil {
		return fmt.Errorf("failed to update record: %w", res.Error)
	}

	updateResult := r.db.WithContext(ctx).
		Table("commission_tracking").
		Where("user_id = ?", campaignUser.UserID).
		Update("parent_id", trackingRecord.UserID)

	if updateResult.Error != nil {
		return fmt.Errorf("failed to update parent_id: %w", updateResult.Error)
	}
	go r.directusClient.UpdateParentIdInDirectus(ctx, campaignUser.UserID, trackingRecord.UserID)
	go r.wageringClient.UpsertPlayerRefferals(trackingRecord.UserID, campaignUser.UserID, campaignUser.Username)

	return nil

}

func (r *ReferralRepository) IsReferralCodeValid(ctx context.Context, referralCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&commissionTracking{}).
		Where("refferal_code = ?", referralCode).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check referral code validity: %w", err)
	}
	return count > 0, nil
}
