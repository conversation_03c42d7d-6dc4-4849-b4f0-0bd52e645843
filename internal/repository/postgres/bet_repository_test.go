package postgres

import (
	"context"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func (s *databaseSuite) TestBetRepository_UpsertBet() {
	testTime := parseTime("2006-01-02T15:04:06Z")

	betRepository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		bet             *domain.Bet
		expectedBet     *domain.Bet
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "UpdateNoPayout",
			bet: &domain.Bet{
				BetAmount:       utils.PointerOf(100.0),
				BetType:         domain.BetTypeCasino,
				CoinsMultiplier: 1,
				Currency:        "USD",
				ExternalID:      "id2",
				RoundStatus:     "other",
				Time:            testTime.Add(1),
				Game:            domain.Game{ID: gameID1},
				User:            domain.User{ID: userID1},
			},
			expectedBet: &domain.Bet{
				BetAmount:       utils.PointerOf(120.0), // 100 + existing 20
				BetType:         domain.BetTypeCasino,
				ActualBetAmount: utils.PointerOf(120.0),
				CoinsMultiplier: 1,
				Currency:        "USD",
				ExternalID:      "id2",
				Payout:          utils.PointerOf(5.0),
				ActualWinAmount: utils.PointerOf(5.0), 
				RoundStatus:     "other",
				Game:            domain.Game{ID: gameID1},
				User:            domain.User{ID: userID1},
			},
			expectedUser: &domain.User{
				ID:             userID1,
				ExternalID:     "id1",
				NumberOfLosses: 2,
				NumberOfWins:   1,
				TotalBets:      21,
				UserName:       "user1",
				Wagered:        110,
				TotalCoins:     0,
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "UpdateNoBetAmount",
			bet: &domain.Bet{
				BetType:         domain.BetTypeCasino,
				CoinsMultiplier: 1,
				Currency:        "USD",
				ExternalID:      "id2",
				Payout:          utils.PointerOf(100.0),
				RoundStatus:     "other",
				Time:            testTime.Add(1),
				Game:            domain.Game{ID: gameID1},
				User:            domain.User{ID: userID1},
			},
			expectedBet: &domain.Bet{
				BetAmount:       utils.PointerOf(20.0),
				BetType:         domain.BetTypeCasino,
				CoinsMultiplier: 1,
				Currency:        "USD",
				ExternalID:      "id2",
				ActualBetAmount: utils.PointerOf(100.0),
				Payout:          utils.PointerOf(105.0), // 100 + existing 5
				RoundStatus:     "other",
				ActualWinAmount: utils.PointerOf(105.0),
				Game:            domain.Game{ID: gameID1},
				User:            domain.User{ID: userID1},
			},
			expectedUser: &domain.User{
				ID:             userID1,
				ExternalID:     "id1",
				NumberOfLosses: 2,
				NumberOfWins:   1,
				TotalBets:      21,
				UserName:       "user1",
				Wagered:        110,
				TotalCoins:     0,
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "Create",
			bet: &domain.Bet{
				BetAmount:   utils.PointerOf(100.0),
				BetType:     domain.BetTypeCasino,
				Currency:    "USD",
				ExternalID:  "id10",
				RoundStatus: "completed",
				Time:        testTime.Add(1),
				Game:        domain.Game{ID: gameID1},
				User:        domain.User{ID: userID1},
			},
			expectedBet: &domain.Bet{
				BetAmount:       utils.PointerOf(100.0),
				BetType:         domain.BetTypeCasino,
				ActualBetAmount: utils.PointerOf(100.0),
				CoinsMultiplier: 1,
				Currency:        "USD",
				ExternalID:      "id10",
				RoundStatus:     "completed",
				Game:            domain.Game{ID: gameID1},
				User:            domain.User{ID: userID1},
			},
			expectedUser:    &user1,
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bet, err := betRepository.UpsertBet(context.Background(), test.bet)
			s.assertBetsAreEqual(test.expectedBet, bet)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestBetRepository_GetBetByID() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		betID           uuid.UUID
		expectedBet     *domain.Bet
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Non Existant",
			betID:           betIDNonExistant,
			expectedBet:     nil,
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bet, err := repository.GetBetByID(context.Background(), test.betID)
			s.assertBetsAreEqual(test.expectedBet, bet)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestBetRepository_GetBetsByUserID() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		params          *domain.GetBetsByUserIDParams
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "ExistantUserWithoutEmptyGames",
			params: &domain.GetBetsByUserIDParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserID:                 userID1,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "ExistantUserWithEmptyGames",
			params: &domain.GetBetsByUserIDParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserID:                 userID4,
				IncludeBetsWithoutGame: true,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet6, bet5},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "NonExistantUser",
			params: &domain.GetBetsByUserIDParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserID:                 userIDNonExistant,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bets, err := repository.GetBetsByUserID(context.Background(), test.params)
			s.assertBetsAreEqual(test.expectedBets, bets)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestBetRepository_GetBets() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		params          *domain.GetBetsParams
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "ExcludeBetsWithoutGames",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet5, bet4, bet3, bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "IncludeBetsWithoutGames",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: true,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet6, bet5, bet4, bet3, bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "FilterByUserExternalID",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserExternalID:         utils.PointerOf("id1"),
				IncludeBetsWithoutGame: true,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "Pagination",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 2,
					PageSize:   1,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet4},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 2,
					PageSize:    1,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "FilterByPayoutOver10",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: false,
				PayoutOver:             utils.PointerOf(10.0),
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet5},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bets, err := repository.GetBets(context.Background(), test.params)
			s.assertBetsAreEqual(test.expectedBets, bets)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestBetRepository_ExportBets() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		params          *domain.ExportBetsParams
		expectedBets    []domain.Bet
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "EmptyInterval",
			params: &domain.ExportBetsParams{
				ExportParams: domain.ExportParams{
					StartDate: parseTime("2006-01-02T15:04:06Z"),
					EndDate:   parseTime("2006-01-02T15:04:05Z"),
				},
				UserExternalID: "id1",
			},
			expectedBets:    []domain.Bet{},
			expectedErrFunc: require.NoError,
		},
		{
			name: "NonEmptyInterval",
			params: &domain.ExportBetsParams{
				ExportParams: domain.ExportParams{
					StartDate: parseTime("2006-01-02T15:04:05Z"),
					EndDate:   parseTime("2006-01-02T15:04:07Z"),
				},
				UserExternalID: "id1",
			},
			expectedBets: []domain.Bet{
				removeBetUser(bet2),
				removeBetUser(bet1),
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bets, err := repository.ExportBets(context.Background(), test.params)
			s.assertBetsAreEqual(test.expectedBets, bets)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) assertBetsAreEqual(expected, actual any) {
	opts := []cmp.Option{
		cmpopts.IgnoreFields(domain.Bet{}, "ID", "Time"),
		cmpopts.IgnoreFields(domain.Bet{}, "ActualBetAmount", "ActualWinAmount", "Multiplier"),

		cmpopts.IgnoreFields(
			domain.User{},
			"LastUpdatedOn",
			"JoinDate",
			"VIPStatus",
			"TotalCoins",
			"HideAllStats",
			"HideTournamentStats",
		),
	}

	s.Assert().True(
		cmp.Equal(expected, actual, opts...),
		cmp.Diff(expected, actual, opts...),
	)
}

func removeBetUser(bet domain.Bet) domain.Bet {
	bet.User = domain.User{}
	return bet
}