package postgres

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
)

// boost entity definition
type boost struct {
	UUIDKeyModel
	UserID             uuid.UUID  `gorm:"type:uuid;not null"`
	BonusStartsAt      time.Time  `gorm:"type:timestamptz;not null"`
	BonusFinishesAt    time.Time  `gorm:"type:timestamptz;not null"`
	BoostDurationHours int        `gorm:"type:integer;default:0;not null"`
	BoostStartedAt     *time.Time `gorm:"type:timestamptz"`
	Multiplier         float64    `gorm:"type:double precision;default:1;not null"`
	User               user
}

func boostsToRows(boosts []domain.Boost) []boost {
	boostRows := make([]boost, len(boosts))
	for i, b := range boosts {
		boostRows[i] = boostToRow(b)
	}

	return boostRows
}

func boostToRow(b domain.Boost) boost {
	return boost{
		UUIDKeyModel:       UUIDKeyModel{ID: b.ID},
		BonusStartsAt:      b.BonusStartsAt,
		BonusFinishesAt:    b.BonusFinishesAt,
		BoostDurationHours: b.BoostDurationHours,
		BoostStartedAt:     b.BoostStartedAt,
		Multiplier:         b.Multiplier,
		UserID:             b.User.ID,
	}
}

func rowToBoost(b boost) *domain.Boost {
	return &domain.Boost{
		ID:                 b.ID,
		BonusStartsAt:      b.BonusStartsAt,
		BonusFinishesAt:    b.BonusFinishesAt,
		BoostDurationHours: b.BoostDurationHours,
		BoostStartedAt:     b.BoostStartedAt,
		Multiplier:         b.Multiplier,
		User:               rowToUser(b.User),
	}
}
