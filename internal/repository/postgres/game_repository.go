package postgres

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.GameRepository = (*GameRepository)(nil)

// GameRepository is a struct that will implement the GameRepository interface.
type GameRepository struct {
	db *gorm.DB
}

// NewGameRepository creates a new instance of GameRepository.
func NewGameRepository(db *gorm.DB) *GameRepository {
	return &GameRepository{db: db}
}

// CreateGame inserts a new Game into the database.
func (r *GameRepository) CreateGame(ctx context.Context, game *domain.Game) (*domain.Game, error) {
	row := gameToRowForUpsert(*game)

	tx := r.db.WithContext(ctx).
		Session(&gorm.Session{
			CreateBatchSize: 1000,
			PrepareStmt:     true, // Prepare statement for better performance
		}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "external_id"}},
			DoNothing: true,
		}).
		Select("id", "created_at", "updated_at", "deleted_at",
			"cms_game_id", "external_id", "game_data", "name",
			"slug", "thumbnail_id", "vendor_game_id").
		Create(&row)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("create game: %w", err)
	}

	return rowToGamePtr(row), nil
}

// UpsertGames inserts or updates the provided games.
// After that, it increments the current games version.
func (r *GameRepository) UpsertGames(ctx context.Context, games []domain.Game) error {
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return upsertMany(tx, games)
	})
	if err != nil {
		return fmt.Errorf("update all games transaction: %w", err)
	}

	return nil
}

// UpsertAllGamesIfNotInitialized is similar to UpsertMany but it only upserts when the games hasn't
// been initialized.
func (r *GameRepository) UpsertAllGamesIfNotInitialized(
	ctx context.Context,
	getAllGames func(context.Context) ([]domain.Game, error),
) error {
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var version gamesVersion
		err := tx.First(&version, 1).Error

		if err == nil {
			return nil
		}

		if !errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Error("Unexpected error when checking games version", "error", err)
			return fmt.Errorf("check games version: %w", err)
		}
		games, err := getAllGames(ctx)
		if err != nil {
			return fmt.Errorf("get all games: %w", err)
		}
		if err := upsertMany(tx, games); err != nil {
			slog.Error("Failed to upsert games", "error", err)
			return fmt.Errorf("upsert games: %w", err)
		}

		// Use an upsert operation for games_versions
		err = tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"version", "updated_at"}),
		}).Create(&gamesVersion{ID: 1, Version: 1}).Error

		if err != nil {
			slog.Error("Failed to upsert games version", "error", err)
			return fmt.Errorf("upsert games version: %w", err)
		}

		slog.Info("Games initialized successfully")
		return nil
	})

	if err != nil {
		slog.Error("Failed to initialize games", "error", err)
		return fmt.Errorf("initialize games transaction: %w", err)
	}
	return nil
}

func upsertMany(tx *gorm.DB, games []domain.Game) error {
	uniqueGames := make(map[string]domain.Game)
	for _, game := range games {
		// If a game with this external_id already exists in the map,
		// keep the one with the higher CMSGameID (assuming this indicates a newer version)
		if existing, ok := uniqueGames[game.ExternalID]; ok {
			if game.CMSGameID > existing.CMSGameID {
				uniqueGames[game.ExternalID] = game
			}
		} else {
			uniqueGames[game.ExternalID] = game
		}
	}

	uniqueGameSlice := make([]domain.Game, 0, len(uniqueGames))
	for _, game := range uniqueGames {
		uniqueGameSlice = append(uniqueGameSlice, game)
	}

	rows := gamesToRowsForUpsert(uniqueGameSlice)

	result := tx.
		Model(&game{}).
		Clauses(
			clause.OnConflict{
				Columns:   []clause.Column{{Name: "external_id"}},
				UpdateAll: true,
			},
		).
		Create(&rows)

	if result.Error != nil {
		return fmt.Errorf("upsert games: %w", result.Error)
	}
	versionResult := tx.
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.Assignments(map[string]interface{}{
				"version":    gorm.Expr("games_versions.version + 1"),
				"updated_at": time.Now(),
			}),
		}).
		Create(&gamesVersion{ID: 1, Version: 1})

	if versionResult.Error != nil {
		return fmt.Errorf("update games version: %w", versionResult.Error)
	}
	return nil
}

func (r *GameRepository) GetGameByGameID(ctx context.Context, id uuid.UUID) (*domain.Game, error) {
	var row game
	tx := r.db.WithContext(ctx).Where("id = ?", id).First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find game by ID '%v' : %w", id, err)
	}

	return rowToGamePtr(row), nil
}

func (r *GameRepository) GetGameByExternalID(ctx context.Context, id string) (*domain.Game, error) {
	var row game
	tx := r.db.WithContext(ctx).Where("external_id = ?", id).First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find game by external ID '%v' : %w", id, err)
	}

	return rowToGamePtr(row), nil
}

func (r *GameRepository) GetGameByVendorGameID(ctx context.Context, vendorGameID string) (*domain.Game, error) {
	var row game
	tx := r.db.WithContext(ctx).Where("vendor_game_id = ?", vendorGameID).First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find game by vendor game ID '%v' : %w", vendorGameID, err)
	}

	return rowToGamePtr(row), nil
}
