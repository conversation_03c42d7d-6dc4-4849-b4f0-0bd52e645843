package postgres

import (
	"time"

	"gorm.io/gorm"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type game struct {
	UUIDKeyModel
	CMSGameID    int64   `gorm:"type:integer;not null"`
	ExternalID   string  `gorm:"type:text;unique;index;not null"`
	GameData     string  `gorm:"type:text;not null"`
	Name         *string `gorm:"type:text;index"`
	Slug         *string `gorm:"type:text"`
	ThumbnailID  *string `gorm:"type:text"`
	VendorGameID string  `gorm:"type:text;index;not null"`
}

func rowToGame(row game) domain.Game {
	return domain.Game{
		ID:           row.ID,
		CMSGameID:    row.CMSGameID,
		ExternalID:   row.ExternalID,
		Name:         row.Name,
		Slug:         row.Slug,
		ThumbnailID:  row.ThumbnailID,
		VendorGameID: row.VendorGameID,
	}
}

func rowToGamePtr(row game) *domain.Game {
	return utils.PointerOf(rowToGame(row))
}

func gameToRow(g domain.Game) game {
	return game{
		UUIDKeyModel: UUIDKeyModel{ID: g.ID},
		CMSGameID:    g.CMSGameID,
		ExternalID:   g.ExternalID,
		Name:         g.Name,
		Slug:         g.Slug,
		ThumbnailID:  g.ThumbnailID,
		VendorGameID: g.VendorGameID,
	}
}

func gamesToRowsForUpsert(games []domain.Game) []game {
	return utils.MapSlice(games, gameToRowForUpsert)
}

func gameToRowForUpsert(g domain.Game) game {
	return game{
		CMSGameID:    g.CMSGameID,
		ExternalID:   g.ExternalID,
		Name:         g.Name,
		Slug:         g.Slug,
		ThumbnailID:  g.ThumbnailID,
		VendorGameID: g.VendorGameID,
	}
}

type gamesVersion struct {
	// This is intended to be a singleton, we won't allow any other ID than "1".
	ID        uint           `gorm:"type:integer;default:1;primaryKey;autoIncrement:false;check:id = 1"`
	CreatedAt time.Time      `gorm:"type:timestamptz;not null"`
	UpdatedAt time.Time      `gorm:"type:timestamptz;not null"`
	DeletedAt gorm.DeletedAt `gorm:"type:timestamptz;index"`
	Version   int            `gorm:"type:integer;default:1;not null"`
}
