package postgres

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func (s *databaseSuite) TestBonusDropRepository_RedeemBonusDrop() {
	alreadyRedeemedUser := BonusDropRedemption{
		UserID:        "user4",
		BonusDropCode: "already_redeemed_code",
		Status:        "completed",
		Amount:        100,
		Username:      "testuser",
	}
	err := s.gormDB.Create(&alreadyRedeemedUser).Error
	require.NoError(s.T(), err)

	s.Run("Valid redemption", func() {
		mockBetRepo := domainmock.NewBetRepository(s.T())
		mockWageringClient := domainmock.NewElantilWageringClient(s.T())
		mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
		
		mockBetRepo.On("GetWageredAmountByTypeForXDays", mock.Anything, "user1", 0).Return(float64(1000), nil)
		mockWageringClient.On("UpdateUserWallet", mock.Anything, "dummy_token", mock.MatchedBy(func(req domain.UserWalletUpdateRequest) bool {
			return req.Amount == "100" && req.CurrencyCode == "USD"
		})).Return(domain.UserWalletUpdateResponse{}, nil)
		mockCMSClient.On("UpdateBonusDropCountAndUsers", mock.Anything, "valid_code", 1, mock.MatchedBy(func(user domain.RedemptionUser) bool {
			return user.UserID == "user1" && user.BonusDropCode == "valid_code" && user.Username == "testuser" && user.Amount == 100 && user.Status == "completed"
		})).Return(nil)
		
		repository := NewBonusDropRepository(s.gormDB, mockWageringClient, mockCMSClient, mockBetRepo)
		
		bonusDrop := &domain.BonusDrop{
			Code:            "valid_code",
			MaxParticipants: 5,
			CodeValue:       100,
			Status:          "active",
		}
		err := repository.UpsertBonusDrop(context.Background(), bonusDrop)
		require.NoError(s.T(), err)

		_, err = repository.RedeemBonusDrop(context.Background(), "user1", "testuser", "valid_code", "dummy_token")
		require.NoError(s.T(), err)
		
		mockBetRepo.AssertExpectations(s.T())
		mockWageringClient.AssertExpectations(s.T())
		mockCMSClient.AssertExpectations(s.T())
	})
	s.Run("Redemption after the code is expired", func() {
		mockBetRepo := domainmock.NewBetRepository(s.T())
		mockWageringClient := domainmock.NewElantilWageringClient(s.T())
		mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
				
		repository := NewBonusDropRepository(s.gormDB, mockWageringClient, mockCMSClient, mockBetRepo)
		
		bonusDrop := &domain.BonusDrop{
			Code:            "expired_code",
			MaxParticipants: 5,
			CodeValue:       100,
			Status:          "expired",
		}
		err := repository.UpsertBonusDrop(context.Background(), bonusDrop)
		require.NoError(s.T(), err)

		_, err = repository.RedeemBonusDrop(context.Background(), "user2", "testuser", "expired_code", "dummy_token")
		require.Error(s.T(), err)
		require.Contains(s.T(), err.Error(), "code expired or inactive")
	})

	s.Run("Redemption when max participants are exceeded", func() {
		mockBetRepo := domainmock.NewBetRepository(s.T())
		mockWageringClient := domainmock.NewElantilWageringClient(s.T())
		mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
		repository := NewBonusDropRepository(s.gormDB, mockWageringClient, mockCMSClient, mockBetRepo)
		
		bonusDrop := &domain.BonusDrop{
			Code:            "max_participants_code",
			MaxParticipants: 0, 
			CodeValue:       100,
			Status:          "active",
		}
		err := repository.UpsertBonusDrop(context.Background(), bonusDrop)
		require.NoError(s.T(), err)

		_, err = repository.RedeemBonusDrop(context.Background(), "user3", "testuser", "max_participants_code", "dummy_token")
		require.Error(s.T(), err)
		require.Contains(s.T(), err.Error(), "all codes have been claimed stay tuned for the next release")
	})

	s.Run("User already redeemed the code", func() {
		mockBetRepo := domainmock.NewBetRepository(s.T())
		mockWageringClient := domainmock.NewElantilWageringClient(s.T())
		mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
		
		repository := NewBonusDropRepository(s.gormDB, mockWageringClient, mockCMSClient, mockBetRepo)
		
		bonusDrop := &domain.BonusDrop{
			Code:            "already_redeemed_code",
			MaxParticipants: 5,
			CodeValue:       100,
			Status:          "active",
		}
		err := repository.UpsertBonusDrop(context.Background(), bonusDrop)
		require.NoError(s.T(), err)

		_, err = repository.RedeemBonusDrop(context.Background(), "user4", "testuser", "already_redeemed_code", "dummy_token")
		require.Error(s.T(), err)
		require.Contains(s.T(), err.Error(), "you have already claimed this bonus")
		
		mockBetRepo.AssertExpectations(s.T())
	})

	s.Run("Wager threshold not met", func() {
		mockBetRepo := domainmock.NewBetRepository(s.T())
		mockWageringClient := domainmock.NewElantilWageringClient(s.T())
		mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
		
		mockBetRepo.On("GetWageredAmountByTypeForXDays", mock.Anything, "user5", 0).Return(float64(100), nil)
		
		repository := NewBonusDropRepository(s.gormDB, mockWageringClient, mockCMSClient, mockBetRepo)
		
		bonusDrop := &domain.BonusDrop{
			Code:            "wager_threshold_code",
			MaxParticipants: 5,
			CodeValue:       100,
			Status:          "active",
			WagerRequired:   500,
		}
		err := repository.UpsertBonusDrop(context.Background(), bonusDrop)
		require.NoError(s.T(), err)
		_, err = repository.RedeemBonusDrop(context.Background(), "user5", "testuser", "wager_threshold_code", "dummy_token")
		require.Error(s.T(), err)
		require.Contains(s.T(), err.Error(), "you do not meet the wagering requirement to redeem this code")
		
		mockBetRepo.AssertExpectations(s.T())
	})
}