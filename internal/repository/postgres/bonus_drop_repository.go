package postgres

import (
	"context"
	"fmt"
	"log/slog"
	"strconv"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type BonusDropRepository struct {
	db             *gorm.DB
	wageringClient domain.ElantilWageringClient
	directusClient domain.DirectusCMSClient
	BetRepository  domain.BetRepository
}

func NewBonusDropRepository(db *gorm.DB, wageringClient domain.ElantilWageringClient, directusClient domain.DirectusCMSClient, betRepository domain.BetRepository) *BonusDropRepository {
	return &BonusDropRepository{
		db:             db,
		wageringClient: wageringClient,
		directusClient: directusClient,
		BetRepository:  betRepository,
	}
}

func (r *BonusDropRepository) UpsertBonusDrop(ctx context.Context, bonusDrop *domain.BonusDrop) error {
	if bonusDrop == nil {
		return fmt.Errorf("bonus drop cannot be nil")
	}
	row := bonusDropToRow(*bonusDrop)
	if err := r.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "code"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"title",
			"total_drop_limit",
			"max_participants",
			"code_value",
			"start_date",
			"end_date",
			"status",
			"wager_required",
			"wager_days",
		}),
	}).Create(&row).Error; err != nil {
		return fmt.Errorf("failed to upsert bonus drop: %w", err)
	}
	return nil
}

func (r *BonusDropRepository) RedeemBonusDrop(ctx context.Context, userId, username, bonusCode, token string) (domain.RedeemBonusResponse, error) {
	var bonusDrop BonusDrop
	var updatedBonusDrop BonusDrop
	var redemption *BonusDropRedemption

	tx := r.db.Begin()
	if tx.Error != nil {
		return domain.RedeemBonusResponse{}, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := func() error {
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("code = ?", bonusCode).First(&bonusDrop).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return domain.ErrBonusDropNotFound
			}
			return err
		}

		if bonusDrop.CurrentRedeems >= bonusDrop.MaxParticipants {
			return domain.ErrBonusDropLimitReached
		}

		if bonusDrop.IsExpired() || !bonusDrop.IsActive() {
			return domain.ErrBonusDropExpiredOrNotActive
		}

		claimed, err := r.HasUserClaimedCode(userId, bonusCode)
		if err != nil {
			return err
		}

		if claimed {
			return domain.ErrBonusDropAlreadyClaimed
		}

		hasMetWagering, err := r.HasUserMetWageringRequirements(ctx, userId, bonusDrop)
		if err != nil {
			return err
		}
		if !hasMetWagering {
			return domain.ErrWageringRequirementsNotMet
		}

		redemption = &BonusDropRedemption{
			UserID:        userId,
			Username:      username,
			BonusDropCode: bonusCode,
			Amount:        bonusDrop.CodeValue,
			Status:        "completed",
		}

		if err := tx.Create(redemption).Error; err != nil {
			return err
		}

		if err := tx.Model(&BonusDrop{}).
			Where("code = ?", bonusCode).
			UpdateColumn("current_redeems", gorm.Expr("current_redeems + ?", 1)).
			First(&updatedBonusDrop).Error; err != nil {
			return err
		}

		if updatedBonusDrop.CurrentRedeems >= updatedBonusDrop.MaxParticipants {
			if err := tx.Model(&BonusDrop{}).
				Where("code = ?", bonusCode).
				Update("status", "completed").Error; err != nil {
				return err
			}
		}

		return nil
	}(); err != nil {
		tx.Rollback()
		return domain.RedeemBonusResponse{}, err
	}

	redemptionUser := bonusRedemptionToDomain(*redemption)
	if err := r.UpdateDirectusOnRedemption(ctx, updatedBonusDrop, redemptionUser); err != nil {
		tx.Rollback()
		return domain.RedeemBonusResponse{}, err
	}

	if updatedBonusDrop.CurrentRedeems >= updatedBonusDrop.MaxParticipants {
		if err := r.directusClient.UpdateBonusDropStatus(ctx, bonusCode, "completed"); err != nil {
			tx.Rollback()
			return domain.RedeemBonusResponse{}, err
		}
	}

	if err := r.UpdateWalletOnBonusDropRedemption(ctx, token, &bonusDrop); err != nil {
		rollbackErr := r.RollBackRedemptionAndCount(ctx, bonusCode, userId, updatedBonusDrop.CurrentRedeems-1)
		if rollbackErr != nil {
			slog.Error("Error rolling back redemption and count", "error", rollbackErr, "bonusCode", bonusCode, "userId", userId)
		}
		tx.Rollback()
		return domain.RedeemBonusResponse{}, err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return domain.RedeemBonusResponse{}, err
	}

	return domain.RedeemBonusResponse{
		BonusDropCode: bonusCode,
		Amount:        bonusDrop.CodeValue,
	}, nil
}

func (r *BonusDropRepository) HasUserClaimedCode(userId, bonusCode string) (bool, error) {
	var count int64
	if err := r.db.Model(&BonusDropRedemption{}).Where("user_id = ? AND bonus_drop_code = ?", userId, bonusCode).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *BonusDropRepository) HasUserMetWageringRequirements(ctx context.Context, userId string, bonusDrop BonusDrop) (bool, error) {
	wageredAmount, err := r.BetRepository.GetWageredAmountByTypeForXDays(ctx, userId, bonusDrop.WagerDays)
	if err != nil {
		return false, err
	}

	return wageredAmount >= bonusDrop.WagerRequired, nil
}

func (r *BonusDropRepository) UpdateWalletOnBonusDropRedemption(ctx context.Context, token string, bonusDrop *BonusDrop) error {
	if bonusDrop == nil {
		return fmt.Errorf("bonus drop cannot be nil")
	}

	walletUpdateRequest := domain.UserWalletUpdateRequest{
		CurrencyCode: "USD",
		Amount:       strconv.FormatInt(int64(bonusDrop.CodeValue), 10),
		Reason:       "Bonus Drop",
		ProductId:    "promotions",
		Category:     "bonus_drop",
	}

	if _, err := r.wageringClient.UpdateUserWallet(ctx, token, walletUpdateRequest); err != nil {
		return fmt.Errorf("failed to update user wallet on bonus drop redemption: %w", err)
	}

	return nil
}

func (r *BonusDropRepository) UpdateDirectusOnRedemption(ctx context.Context, bonusDrop BonusDrop, userBonusRedemption domain.RedemptionUser) error {
	retries := 3
	backoff := 250 * time.Millisecond
	for i := 0; i < retries; i++ {
		if err := r.directusClient.UpdateBonusDropCountAndUsers(ctx, bonusDrop.Code, bonusDrop.CurrentRedeems, userBonusRedemption); err != nil {
			if i == retries-1 {
				slog.Error("Error updating bonus drop count and users",
					"error", err,
					"bonusCode", bonusDrop.Code,
					"currentRedeems", bonusDrop.CurrentRedeems)
				return fmt.Errorf("failed to update bonus drop count and users after %d retries: %w", retries, err)
			}
			slog.Warn("Retrying Directus update", "attempt", i+1, "error", err)
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		break
	}
	return nil
}

func (r *BonusDropRepository) RollBackRedemptionAndCount(ctx context.Context, bonusCode, userId string, currentRedeems int) error {

	retries := 3
	backoff := 250 * time.Millisecond
	for i := 0; i < retries; i++ {
		if err := r.directusClient.RollbackBonusDropCountAndRedemption(ctx, bonusCode, userId, currentRedeems); err != nil {
			if i == retries-1 {
				return fmt.Errorf("failed to rollback bonus drop count in Directus after %d retries: %w", retries, err)
			}
			slog.Warn("Retrying Directus rollback", "attempt", i+1, "error", err)
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		break
	}

	return nil
}
