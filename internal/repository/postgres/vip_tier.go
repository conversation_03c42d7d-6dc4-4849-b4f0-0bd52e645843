//TODO: In progress

package postgres

import (
	// "context"
	// "log/slog"
	// "time"

	// "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	// "github.com/lib/pq"
	"gorm.io/gorm"
)

type UserWageringRepo struct {
	db *gorm.DB
}

func NewUserWageringRepo(db *gorm.DB) *UserWageringRepo {
	return &UserWageringRepo{
		db: db,
	}
}

// func (u *UserWageringRepo) MonitorWageringChanges(ctx context.Context, updates chan<- domain.WageringUpdate) error {
// 	slog.Info("Starting wagering changes monitor")

//     go func() {
//         ticker := time.NewTicker(5 * time.Second)
//         defer ticker.Stop()

//         // Map to track last checked wagered amount per connected user
//         lastWagered := make(map[string]float64)

//         for {
//             select {
//             case <-ctx.Done():
//                 slog.Info("Stopping wagering monitor")
//                 return
//             case <-ticker.C:
//                 // Get the connected user IDs
//                 userIDs := make([]string, 0)
//                 for userID := range lastWagered {
//                     userIDs = append(userIDs, userID)
//                 }

//                 // If no users are connected, skip the query
//                 if len(userIDs) == 0 {
//                     continue
//                 }

//                 type Result struct {
//                     UserID  string  `gorm:"column:user_id"`
//                     Wagered float64 `gorm:"column:wagered"`
//                 }
//                 var results []Result
//                 err := u.db.Raw(`
//                     SELECT
//                         u.external_id as user_id,
//                         COALESCE(ubs.wagered, 0) as wagered
//                     FROM users_bets_summary ubs
//                     JOIN users u ON u.id = ubs.user_id
//                     WHERE u.external_id = ANY(?::text[])
//                 `, pq.Array(userIDs)).Scan(&results).Error

//                 if err != nil {
//                     slog.Error("Failed to query wagering changes", slog.Any("error", err))
//                     continue
//                 }

//                 for _, result := range results {
//                     previousWagered := lastWagered[result.UserID]
//                     if result.Wagered != previousWagered {
//                         slog.Debug("Detected wagering change",
//                             slog.String("userID", result.UserID),
//                             slog.Float64("currentWagered", result.Wagered),
//                             slog.Float64("previousWagered", previousWagered))

//                         select {
//                         case updates <- domain.WageringUpdate{
//                             UserExternalID: result.UserID,
//                             WageredAmount: result.Wagered,
//                             Timestamp:     time.Now(),
//                         }:
//                             lastWagered[result.UserID] = result.Wagered
//                         default:
//                             slog.Warn("Channel full, skipping update",
//                                 slog.String("userID", result.UserID))
//                         }
//                     }
//                 }
//             }
//         }
//     }()

//     return nil
// }
