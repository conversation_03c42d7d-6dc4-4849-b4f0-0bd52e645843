package postgres

type UserBonusTemplates struct {
	UUIDKeyModel
	UserName      string `gorm:"type:text;index;not null"`
	OfferCode     string `gorm:"type:text;index;not null"`
	OfferName     string `gorm:"type:text;index;not null"`
	OwnerBonusID  string `gorm:"type:text"`
	IsModalClosed bool   `gorm:"type:boolean;default:false"`
	Status        string `gorm:"type:text;default:'pending'"`
}

func (s *UserBonusTemplates) TableName() string {
	return "users_bonus_templates"
}
