package postgres

import (
	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ToastRepository struct {
	db *gorm.DB
}

type maintainanceToast struct {
	UUIDKeyModel
	ExternalId int            `json:"ExternalId" gorm:"column:external_id;unique"`
	Message    string         `json:"Message" gorm:"column:message"`
	Published  bool           `json:"Published" gorm:"column:published"`
	Location   pq.StringArray `json:"Location" gorm:"column:location;type:text[]"`
	Type       string         `json:"Type" gorm:"column:type"`
	Catagory   string         `json:"Catagory" gorm:"column:catagory"`
}

func (maintainanceToast) TableName() string {
	return "maintainance_toasts"
}
func NewToastRepository(db *gorm.DB) *ToastRepository {
	return &ToastRepository{
		db: db,
	}
}

func (r *ToastRepository) CreateToast(ExternalId int, message string, Published bool, location []string, type_ string, catagory string) (*maintainanceToast, error) {
	toast := &maintainanceToast{
		UUIDKeyModel: UUIDKeyModel{
			ID: uuid.New(),
		},
		ExternalId: ExternalId,
		Message:    message,
		Published:  Published,
		Location:   pq.StringArray(location),
		Type:       type_,
		Catagory:   catagory,
	}

	result := r.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "external_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"message", "published", "location", "type", "catagory", "updated_at"}),
	}).Create(toast)

	if result.Error != nil {
		return nil, result.Error
	}
	return toast, nil
}

func (r *ToastRepository) GetPublishedToasts() ([]maintainanceToast, error) {
	var toasts []maintainanceToast
	result := r.db.Where("published = ?", true).Find(&toasts)
	if result.Error != nil {
		return nil, result.Error
	}

	return toasts, nil
}

func (r *ToastRepository) UnpublishToast(id uuid.UUID) error {
	result := r.db.Model(&maintainanceToast{}).Where("id = ?", id).Update("published", false)
	return result.Error
}
