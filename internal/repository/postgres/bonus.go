package postgres

import (
	"strconv"
	"strings"

	"github.com/lib/pq"
)

type bonusConfig struct {
	UUIDKeyModel
	ExternalID                  int            `gorm:"type:int;unique;not null"`
	Status                      string         `gorm:"type:text;not null"`
	Category                    string         `gorm:"type:text;not null"`
	ExpiryDuration              int            `gorm:"type:int;not null"`
	VipTiers                    pq.StringArray `gorm:"type:text[];not null"`
	TheoreticalMarginPercentage float64        `gorm:"type:float;not null"`
	LossBackPercentage          float64        `gorm:"type:float;not null"`
}

func (bonusConfig) TableName() string {
	return "bonus_configs"
}

func convertStringToFloat64(value string) float64 {
	value = strings.TrimSpace(value)
	if value == "" {
		return 0
	}
	floatValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0
	}
	return floatValue
}