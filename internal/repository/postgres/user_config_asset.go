package postgres

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type userConfigAsset struct {
	UUIDKeyModel
	Type    string `gorm:"type:asset_type"`
	SubType string `gorm:"type:asset_sub_type"`
	Key     string `gorm:"type:text"`
	Value   string `gorm:"type:text"`
}

func rowsToAssets(rows []userConfigAsset) []domain.UserConfigAsset {
	return utils.MapSlice(rows, rowToAsset)
}

func rowToAsset(row userConfigAsset) domain.UserConfigAsset {
	return domain.UserConfigAsset{
		ID:      row.ID,
		Type:    string(row.Type),
		SubType: string(row.SubType),
		Key:     row.Key,
		Value:   row.Value,
	}
}

func rowToAssetPtr(row userConfigAsset) *domain.UserConfigAsset {
	return utils.PointerOf(rowToAsset(row))
}
