package postgres

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type BonusRepository struct {
	db          *gorm.DB
	vipTiers    domain.VIPTiers
	rest_client domain.DirectusCMSClient
}

func NewBonusRepository(db *gorm.DB, vipTiers domain.VIPTiers, rest_client domain.DirectusCMSClient) *BonusRepository {
	return &BonusRepository{
		db:          db,
		vipTiers:    vipTiers,
		rest_client: rest_client,
	}
}

func (r *BonusRepository) CreateBonusConfig(ctx context.Context) error {
	bonusConfigResponse, err := r.rest_client.GetBonusConfigurationFromCMS()
	if err != nil {
		return fmt.Errorf("failed to get bonus configurations from CMS: %w", err)
	}
	now := time.Now()
	for _, config := range bonusConfigResponse.Data {
		theoMarginPercentage, err := strconv.ParseFloat(config.TheoreticalMarginPercentage, 64)
		if err != nil {
			return fmt.Errorf("failed to parse TheoreticalMarginPercentage: %w", err)
		}
		lossBackPercentage, err := strconv.ParseFloat(config.LossBackPercentage, 64)
		if err != nil {
			return fmt.Errorf("failed to parse LossBackPercentage: %w", err)
		}
		bonusConfig := bonusConfig{
			UUIDKeyModel: UUIDKeyModel{
				ID:        uuid.New(),
				CreatedAt: now,
				UpdatedAt: now,
			},
			ExternalID:                  config.ExternalID,
			Status:                      config.Status,
			Category:                    config.Category,
			ExpiryDuration:              config.ExpiryDuration,
			VipTiers:                    pq.StringArray(config.VipTiers),
			TheoreticalMarginPercentage: theoMarginPercentage,
			LossBackPercentage:          lossBackPercentage,
		}

		result := r.db.WithContext(ctx).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "external_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"status", "category", "expiry_duration", "vip_tiers", "theoretical_margin_percentage", "loss_back_percentage", "updated_at"}),
		}).Create(&bonusConfig)

		if result.Error != nil {
			return fmt.Errorf("failed to upsert bonus config: %w", result.Error)
		}
	}
	return nil
}

func (r *BonusRepository) getBonusConfigByExternalID(ctx context.Context, id int) (domain.BonusConfig, error) {
	var bonusConfig domain.BonusConfig

	query := `
		SELECT id, external_id, status, category,
		       expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage
		FROM bonus_configs
		WHERE external_id = ?
		LIMIT 1
	`
	result := r.db.WithContext(ctx).Raw(query, id).Scan(&bonusConfig)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return domain.BonusConfig{}, domain.ErrResourceNotFound
		}
		return domain.BonusConfig{}, result.Error
	}
	if result.RowsAffected == 0 {
		return domain.BonusConfig{}, domain.ErrResourceNotFound
	}

	return bonusConfig, nil
}

func (r *BonusRepository) CalculateSpecificBonusRewardAmount(ctx context.Context, wager, loss, ngr string, RtpRate float64, catagory string, bonusId int) (float64, error) {
	bonusConfig, err := r.getBonusConfigByExternalID(ctx, bonusId)
	if err != nil {
		slog.Error("Failed to get bonus config by external id: %v", "bonusId", err)
		return 0, err
	}
	wagerAmount := convertStringToFloat64(wager)
	totalLoss := convertStringToFloat64(loss)
	ngrValue := convertStringToFloat64(ngr)
	var theoreticalMargin float64

	if catagory == "casino_vt" {
		theoreticalMargin = 0.01
	} else if catagory == "sport_vt" {
		theoreticalMargin = 0.03
	} else {
		theoreticalMargin = 0.01
		fmt.Printf("catagory: %s \n", catagory)
	}

	theoMarginAmount := (wagerAmount * theoreticalMargin)
	baseReward := theoMarginAmount * (bonusConfig.TheoMarginPercentage / 100)

	var lossbackReward float64
	if ngrValue > 0 && totalLoss > 0 {
		lossbackReward = totalLoss * (bonusConfig.LossbackPercentage / 100)
	}

	var totalReward float64
	switch bonusConfig.Category {
	case "weekly", "monthly":
		totalReward = baseReward + lossbackReward
	case "daily", "instant", "dailyRakeback":
		totalReward = baseReward
	default:
		return 0, fmt.Errorf("unknown bonus category: %s", bonusConfig.Category)
	}
	return totalReward, nil
}

func (r *BonusRepository) GetBonusConfigByType(ctx context.Context, bonusType string) (domain.BonusConfig, error) {
	var bonusConfig domain.BonusConfig

	err := r.db.WithContext(ctx).Raw(`
	SELECT *
	FROM bonus_configs
	WHERE category = ?
`, bonusType).Scan(&bonusConfig).Error

	if err != nil {
		return domain.BonusConfig{}, err
	}
	return bonusConfig, nil
}
