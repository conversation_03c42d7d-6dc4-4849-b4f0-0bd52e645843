package postgres

import (
	"context"
	"time"
	
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

func (s *databaseSuite) TestTransactionRepository_UpsertTransaction() {
	repository := NewTransactionRepository(s.gormDB)

	testTransactionID := uuid.New()
	testTime := time.Now()

	tests := []struct {
		name                string
		transaction         *domain.Transaction
		expectedTransaction *domain.Transaction
		expectedErrFunc     require.ErrorAssertionFunc
	}{
		{
			name: "Create",
			transaction: &domain.Transaction{
				ID:         testTransactionID,
				Amount:     42,
				Category:   "casino",
				ExternalID: "test_upsert_" + testTransactionID.String(),
				InsertedAt: testTime,
				Type:       "debit",
			},
			expectedTransaction: &domain.Transaction{
				ID:         testTransactionID,
				Amount:     42,
				Category:   "casino",
				ExternalID: "test_upsert_" + testTransactionID.String(),
				InsertedAt: testTime,
				Type:       "debit",
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "Update",
			transaction: &domain.Transaction{
				ID:         testTransactionID,
				Amount:     84,
				Category:   "payments",
				ExternalID: "test_upsert_" + testTransactionID.String(),
				InsertedAt: testTime,
				Type:       "credit",
			},
			expectedTransaction: &domain.Transaction{
				ID:         testTransactionID,
				Amount:     84,
				Category:   "payments",
				ExternalID: "test_upsert_" + testTransactionID.String(),
				InsertedAt: testTime,
				Type:       "credit",
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			transaction, err := repository.UpsertTransaction(context.Background(), test.transaction)
			test.expectedErrFunc(s.T(), err)
			if err == nil {
				require.Equal(s.T(), test.expectedTransaction.Amount, transaction.Amount)
				require.Equal(s.T(), test.expectedTransaction.Category, transaction.Category)
				require.Equal(s.T(), test.expectedTransaction.Type, transaction.Type)
				require.Equal(s.T(), test.expectedTransaction.ExternalID, transaction.ExternalID)
			}
		})
	}
}

func (s *databaseSuite) TestTransactionRepository_GetTransactions_WithExistingData() {
	repository := NewTransactionRepository(s.gormDB)
	
	// Test with whatever data already exists in the database
	orderParams := domain.OrderParams{OrderBy: "inserted_at", Order: domain.OrderDirDesc}
	pagingParams := domain.PagingParams{PageNumber: 1, PageSize: 100}

	result, err := repository.GetTransactions(context.Background(), &domain.GetTransactionParams{
		OrderParams:    orderParams,
		PagingParams:   pagingParams,
		UserExternalID: "",
		Types:          []string{},
		Categories:     []string{},
	})
	
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.NotNil(s.T(), result.Items)
	require.GreaterOrEqual(s.T(), result.Paging.TotalCount, int64(0))
	
	// Test filtering by type if we have any transactions
	if result.Paging.TotalCount > 0 {
		debitResult, err := repository.GetTransactions(context.Background(), &domain.GetTransactionParams{
			OrderParams:    orderParams,
			PagingParams:   pagingParams,
			UserExternalID: "",
			Types:          []string{"debit"},
			Categories:     []string{},
		})
		
		require.NoError(s.T(), err)
		require.NotNil(s.T(), debitResult)
		
		// Verify all returned transactions are debit type
		for _, transaction := range debitResult.Items {
			require.Equal(s.T(), "debit", transaction.Type)
		}
	}
}

func (s *databaseSuite) TestTransactionRepository_GetTransactions_WithSpecificUser() {
	repository := NewTransactionRepository(s.gormDB)
	
	// Create a test user and transaction to ensure we have controlled data
	testUserID := uuid.New()
	testTransactionID := uuid.New()
	testExternalID := "test_user_" + testUserID.String()
	testTransExternalID := "test_trans_" + testTransactionID.String()
	
	// Create user
	userRow := user{
		UUIDKeyModel: UUIDKeyModel{ID: testUserID},
		ExternalID:   testExternalID,
		UserName:     "Test User",
		ElantilVipStatus:    "tier1",
		HideAllStats: false,
		HideTournamentStats: false,
	}
	result := s.gormDB.Create(&userRow)
	if result.Error != nil {
		s.T().Skipf("Could not create test user: %v", result.Error)
		return
	}
	
	// Create transaction
	transactionRow := transaction{
		UUIDKeyModel: UUIDKeyModel{ID: testTransactionID},
		Amount:       100,
		Category:     "test",
		ExternalID:   testTransExternalID,
		InsertedAt:   time.Now(),
		Type:         "credit",
		UserID:       &testUserID,
		Status:       "completed",
	}
	result = s.gormDB.Create(&transactionRow)
	if result.Error != nil {
		s.T().Skipf("Could not create test transaction: %v", result.Error)
		return
	}
	
	// Test getting transactions for this specific user
	orderParams := domain.OrderParams{OrderBy: "inserted_at", Order: domain.OrderDirDesc}
	pagingParams := domain.PagingParams{PageNumber: 1, PageSize: 10}

	transactions, err := repository.GetTransactions(context.Background(), &domain.GetTransactionParams{
		OrderParams:    orderParams,
		PagingParams:   pagingParams,
		UserExternalID: testExternalID,
		Types:          []string{},
		Categories:     []string{},
	})
	
	require.NoError(s.T(), err)
	require.NotNil(s.T(), transactions)
	require.GreaterOrEqual(s.T(), transactions.Paging.TotalCount, int64(1))
	
	// Verify we got our test transaction
	found := false
	for _, trans := range transactions.Items {
		if trans.ExternalID == testTransExternalID {
			found = true
			require.Equal(s.T(), float64(100), trans.Amount)
			require.Equal(s.T(), "test", trans.Category)
			require.Equal(s.T(), "credit", trans.Type)
			break
		}
	}
	require.True(s.T(), found, "Should find our test transaction")
	
	// Clean up
	s.gormDB.Where("id = ?", testTransactionID).Delete(&transaction{})
	s.gormDB.Where("id = ?", testUserID).Delete(&user{})
}