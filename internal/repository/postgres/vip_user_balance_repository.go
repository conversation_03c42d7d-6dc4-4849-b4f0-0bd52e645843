package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
)

// Assert interface implementation.
var _ domain.VIPUserBalanceRepository = (*VIPUserBalanceRepository)(nil)

type VIPUserBalanceRepository struct {
	db *gorm.DB
}

func NewVIPUserBalanceRepository(db *gorm.DB) *VIPUserBalanceRepository {
	return &VIPUserBalanceRepository{db: db}
}

func (r *VIPUserBalanceRepository) CreateVIPUserBalance(ctx context.Context, balance *domain.VIPUserBalance) (*domain.VIPUserBalance, error) {
	var vipUserBalanceRow *vipUserBalance

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		userRow := user{}

		if err := tx.
			First(&userRow, "external_id = ?", balance.User.ExternalID).
			Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return domain.ErrResourceNotFound
			}

			return fmt.Errorf("find VIP user: %w", err)
		}

		balance.User.ID = userRow.ID
		vipUserBalanceRow = vipUserBalanceToRow(balance)

		if err := tx.
			Create(&vipUserBalanceRow).
			Error; err != nil {
			return fmt.Errorf("create VIP user balance: %w", err)
		}

		return nil
	})
	if err != nil {
		return nil, fmt.Errorf("create VIP user balanace transaction: %w", err)
	}

	return rowToVIPUserBalance(vipUserBalanceRow), nil
}
