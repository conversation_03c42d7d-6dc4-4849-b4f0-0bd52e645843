package postgres

import (
	"encoding/base64"
	"encoding/csv"
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/stretchr/testify/require"
)



func (s *databaseSuite) TestDropoutUsersRepository_GenerateDropoutReport() {
	config := &utils.SendGridConfig{
		SendGridAPIKey: "test-api-key",
	}
	repository := NewDropoutUsersRepository(s.gormDB, config)
	s.gormDB.Exec(`DELETE FROM registered_emails`)
	targetDate := time.Now().AddDate(0, 0, -3)
	result1 := s.gormDB.Exec(`
		INSERT INTO registered_emails (email, created_at, updated_at, registered)
		VALUES (?, ?, ?, ?)
	`, "<EMAIL>", targetDate, targetDate, false)
	require.NoError(s.T(), result1.Error)

	result2 := s.gormDB.Exec(`
		INSERT INTO registered_emails (email, created_at, updated_at, registered)
		VALUES (?, ?, ?, ?)
	`, "<EMAIL>", targetDate, targetDate, false)
	require.NoError(s.T(), result2.Error)

	result3 := s.gormDB.Exec(`
		INSERT INTO registered_emails (email, created_at, updated_at, registered)
		VALUES (?, ?, ?, ?)
	`, "<EMAIL>", targetDate, targetDate, true)
	require.NoError(s.T(), result3.Error)
	
	otherDate := time.Now().AddDate(0, 0, -5)
	result4 := s.gormDB.Exec(`
		INSERT INTO registered_emails (email, created_at, updated_at, registered)
		VALUES (?, ?, ?, ?)
	`, "<EMAIL>", otherDate, otherDate, false)
	require.NoError(s.T(), result4.Error)

	var count int64
	s.gormDB.Raw("SELECT COUNT(*) FROM registered_emails WHERE DATE(created_at) = ?", targetDate.Format("2006-01-02")).Scan(&count)
	require.Equal(s.T(), int64(3), count, "Expected 3 records for target date")

	report, err := repository.GenerateDropoutReport(3)
	require.NoError(s.T(), err)
	require.NotNil(s.T(), report)
	require.Equal(s.T(), 3, report.DaysAgo)
	require.Equal(s.T(), targetDate.Format("2006-01-02"), report.Date)
	require.Len(s.T(), report.Users, 2)
	
	emails := make([]string, len(report.Users))
	for i, user := range report.Users {
		emails[i] = user.Email
	}
	require.Contains(s.T(), emails, "<EMAIL>")
	require.Contains(s.T(), emails, "<EMAIL>")
	require.NotContains(s.T(), emails, "<EMAIL>") 
	require.NotContains(s.T(), emails, "<EMAIL>") 
}

func (s *databaseSuite) TestDropoutUsersRepository_GenerateDropoutReport_NoData() {
	config := &utils.SendGridConfig{
		SendGridAPIKey: "test-api-key",
	}
	repository := NewDropoutUsersRepository(s.gormDB, config)
	s.gormDB.Exec(`DELETE FROM registered_emails`)

	report, err := repository.GenerateDropoutReport(10)
	require.NoError(s.T(), err)
	require.NotNil(s.T(), report)
	require.Equal(s.T(), 10, report.DaysAgo)
	require.Len(s.T(), report.Users, 0)
}

func (s *databaseSuite) TestDropoutUsersRepository_GenerateDropoutReport_DatabaseError() {
	config := &utils.SendGridConfig{
		SendGridAPIKey: "test-api-key",
	}
	repository := NewDropoutUsersRepository(s.gormDB, config)
	s.gormDB.Exec(`ALTER TABLE registered_emails RENAME TO registered_emails_backup`)
	
	_, err := repository.GenerateDropoutReport(1)
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "failed to query users")
	s.gormDB.Exec(`ALTER TABLE registered_emails_backup RENAME TO registered_emails`)
}

func (s *databaseSuite) TestDropoutUsersRepository_CSVGeneration() {
	testTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	
	report := &UserReport{
		DaysAgo: 1,
		Date:    "2024-01-15",
		Users: []struct {
			Email     string
			CreatedAt time.Time
		}{
			{Email: "<EMAIL>", CreatedAt: testTime},
			{Email: "<EMAIL>", CreatedAt: testTime.Add(time.Hour)},
		},
	}
	var buf strings.Builder
	writer := csv.NewWriter(&buf)
	err := writer.Write([]string{"Email Address", "Date of Sign-Up Start"})
	require.NoError(s.T(), err)
	for _, user := range report.Users {
		err := writer.Write([]string{user.Email, user.CreatedAt.Format("2006-01-02")})
		require.NoError(s.T(), err)
	}
	writer.Flush()

	csvContent := buf.String()
	require.Contains(s.T(), csvContent, "Email Address,Date of Sign-Up Start")
	require.Contains(s.T(), csvContent, "<EMAIL>,2024-01-15")
	require.Contains(s.T(), csvContent, "<EMAIL>,2024-01-15")
}

func (s *databaseSuite) TestDropoutUsersRepository_Base64Encoding() {
	csvContent := "Email Address,Date of Sign-Up Start\<EMAIL>,2024-01-15\n"
	encoded := base64.StdEncoding.EncodeToString([]byte(csvContent))
	decoded, err := base64.StdEncoding.DecodeString(encoded)
	require.NoError(s.T(), err)
	require.Equal(s.T(), csvContent, string(decoded))
}