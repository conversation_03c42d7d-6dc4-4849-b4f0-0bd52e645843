package postgres

import (
	"context"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/stretchr/testify/require"
)

func (s *databaseSuite) TestBonusRepository_CreateBonusConfig() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	mockCMSClient.On("GetBonusConfigurationFromCMS").Return(domain.BonusConfigResponse{
		Data: []domain.BonusConfigDirectus{
			{
				ExternalID:                  1,
				Status:                      "active",
				Category:                    "category1",
				ExpiryDuration:              30,
				VipTiers:                    []string{"tier1", "tier2"},
				TheoreticalMarginPercentage: "5.0",
				LossBackPercentage:          "10.0",
			},
		},
	}, nil)

	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	err := repository.CreateBonusConfig(context.Background())
	require.NoError(s.T(), err)

	mockCMSClient.AssertExpectations(s.T())
}

func (s *databaseSuite) TestBonusRepository_getBonusConfigByExternalID() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)
	mockCMSClient.On("GetBonusConfigurationFromCMS").Return(domain.BonusConfigResponse{
		Data: []domain.BonusConfigDirectus{
			{
				ExternalID:                  1,
				Status:                      "active",
				Category:                    "weekly",
				ExpiryDuration:              30,
				VipTiers:                    []string{"tier1", "tier2"},
				TheoreticalMarginPercentage: "5.0",
				LossBackPercentage:          "10.0",
			},
		},
	}, nil)

	// Create the config first
	err := repository.CreateBonusConfig(context.Background())
	require.NoError(s.T(), err)

	config, err := repository.getBonusConfigByExternalID(context.Background(), 1)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 1, config.ExternalID)
	require.Equal(s.T(), "active", config.Status)
	require.Equal(s.T(), "weekly", config.Category)
	require.Equal(s.T(), 30, config.DaysToExpiry)  
	require.Equal(s.T(), 5.0, config.TheoMarginPercentage)
	require.Equal(s.T(), 10.0, config.LossbackPercentage)
}

func (s *databaseSuite) TestBonusRepository_getBonusConfigByExternalID_NotFound() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	_, err := repository.getBonusConfigByExternalID(context.Background(), 999)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrResourceNotFound, err)
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_CasinoVT() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)
	s.gormDB.Exec(`
		INSERT INTO bonus_configs (id, external_id, status, category, expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage, created_at, updated_at)
		VALUES ('550e8400-e29b-41d4-a716-446655440000', 1, 'active', 'weekly', 30, '{"tier1","tier2"}', 50.0, 20.0, NOW(), NOW())
	`)

	var count int64
	s.gormDB.Raw("SELECT COUNT(*) FROM bonus_configs WHERE external_id = 1").Scan(&count)
	require.Equal(s.T(), int64(1), count, "Bonus config should be inserted")

	reward, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "50", 0.95, "casino_vt", 1)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 25.0, reward)
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_SportVT() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	s.gormDB.Exec(`
		INSERT INTO bonus_configs (id, external_id, status, category, expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage, created_at, updated_at)
		VALUES ('550e8400-e29b-41d4-a716-446655440001', 2, 'active', 'daily', 30, '{"tier1","tier2"}', 50.0, 20.0, NOW(), NOW())
	`)

	reward, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "0", 0.95, "sport_vt", 2)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 15.0, reward)
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_WeeklyWithLossback() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	s.gormDB.Exec(`
		INSERT INTO bonus_configs (id, external_id, status, category, expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage, created_at, updated_at)
		VALUES ('550e8400-e29b-41d4-a716-446655440002', 3, 'active', 'weekly', 30, '{"tier1","tier2"}', 50.0, 20.0, NOW(), NOW())
	`)

	reward, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "50", 0.95, "casino_vt", 3)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 25.0, reward)
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_MonthlyWithLossback() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	s.gormDB.Exec(`
		INSERT INTO bonus_configs (id, external_id, status, category, expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage, created_at, updated_at)
		VALUES ('550e8400-e29b-41d4-a716-446655440003', 4, 'active', 'monthly', 30, '{"tier1","tier2"}', 50.0, 20.0, NOW(), NOW())
	`)

	reward, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "50", 0.95, "casino_vt", 4)
	require.NoError(s.T(), err)
	require.Equal(s.T(), 25.0, reward)
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_UnknownCategory() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	s.gormDB.Exec(`
		INSERT INTO bonus_configs (id, external_id, status, category, expiry_duration, vip_tiers, theoretical_margin_percentage, loss_back_percentage, created_at, updated_at)
		VALUES ('550e8400-e29b-41d4-a716-446655440004', 5, 'active', 'unknown', 30, '{"tier1","tier2"}', 50.0, 20.0, NOW(), NOW())
	`)

	_, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "50", 0.95, "casino_vt", 5)
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "unknown bonus category")
}

func (s *databaseSuite) TestBonusRepository_CalculateSpecificBonusRewardAmount_BonusConfigNotFound() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	_, err := repository.CalculateSpecificBonusRewardAmount(context.Background(), "1000", "100", "50", 0.95, "casino_vt", 999)
	require.Error(s.T(), err)
	require.Equal(s.T(), domain.ErrResourceNotFound, err)
}

func (s *databaseSuite) TestBonusRepository_GetBonusConfigByType() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)
	s.gormDB.Exec("DELETE FROM bonus_configs WHERE category = 'weekly'")
	mockCMSClient.On("GetBonusConfigurationFromCMS").Return(domain.BonusConfigResponse{
		Data: []domain.BonusConfigDirectus{
			{
				ExternalID:                  100, 
				Status:                      "active",
				Category:                    "weekly",
				ExpiryDuration:              30,
				VipTiers:                    []string{"tier1", "tier2"},
				TheoreticalMarginPercentage: "5.0",
				LossBackPercentage:          "10.0",
			},
		},
	}, nil)
	err := repository.CreateBonusConfig(context.Background())
	require.NoError(s.T(), err)

	config, err := repository.GetBonusConfigByType(context.Background(), "weekly")
	require.NoError(s.T(), err)
	require.Equal(s.T(), 100, config.ExternalID)  
	require.Equal(s.T(), "active", config.Status)
	require.Equal(s.T(), "weekly", config.Category)
}

func (s *databaseSuite) TestBonusRepository_GetBonusConfigByType_NotFound() {
	mockCMSClient := domainmock.NewDirectusCMSClient(s.T())
	repository := NewBonusRepository(s.gormDB, nil, mockCMSClient)

	config, err := repository.GetBonusConfigByType(context.Background(), "nonexistent")
	require.NoError(s.T(), err)
	require.Equal(s.T(), domain.BonusConfig{}, config)
}