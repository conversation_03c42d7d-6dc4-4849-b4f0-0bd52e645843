package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
)

type UserBonusQueue struct {
	mut         sync.Mutex
	db          *gorm.DB
	rest_client domain.DirectusCMSClient
	elantil     domain.ElantilWageringClient
}

func NewUserBonusQueue(db *gorm.DB, rest_client domain.DirectusCMSClient, elantil domain.ElantilWageringClient) *UserBonusQueue {
	return &UserBonusQueue{
		db:          db,
		rest_client: rest_client,
		elantil:     elantil,
	}
}

func (q *UserBonusQueue) ProcessBonus(b domain.BonusQueueItem) error {
	q.mut.Lock()
	defer q.mut.Unlock()

	err := q.db.Transaction(func(tx *gorm.DB) error {
		// Check tracking
		var tracking userBonusTracking
		result := tx.Where("bonus_id = ?", b.B.ID.String()).First(&tracking)
		if result.Error != nil {
			if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to fetch tracking record: %w", result.Error)
			}
		}

		if result.RowsAffected > 0 {
			return fmt.Errorf("bonus already claimed or modified")
		}
		slog.Info("bonus not found in tracking", "bonusID", b.B.ID)

		if err := tx.Create(&userBonusTracking{
			BonusId: b.B.ID.String(),
			UserId:  b.B.ExternalID,
			Status:  "processing",
			RewardAmount: b.B.RewardAmount,
		}).Error; err != nil {
			return err
		}

		updateResult := tx.Model(&userBonus{}).
			Where("id = ? AND external_id = ? AND category = ? AND bonus_status = 'active' AND availed = false",
				b.B.ID, b.B.ExternalID, b.B.Category).
			Updates(map[string]interface{}{
				"claim_attempt": true,
				"bonus_status":  "claimed",
				"availed":       true,
			})
		if updateResult.Error != nil {
			return fmt.Errorf("failed to update bonus status: %w", updateResult.Error)
		}
		if updateResult.RowsAffected == 0 {
			return fmt.Errorf("bonus already claimed or modified")
		}
		return nil
	})
	if err != nil {
		return err
	}

	if b.B.Category == "reload" {
		err := q.db.Transaction(func(tx *gorm.DB) error {
			_, err := q.HandleReloadBonusClaim(b.Ctx, b.Token, b.B, tx)
			return err
		})
		if err != nil {
			return err
		}
	}

	if err := q.UpdateTrackingStatus(b.B.ID.String(), "processing_wallet"); err != nil {
		slog.Error("error updating tracking status", "err", err)
		return err
	}

	if _, err := q.rest_client.ClaimBonus(b.B.ExternalID, b.B.Category, b.B.BonusExternalID); err != nil {
		slog.Error("error updating bonus status in directus", "err", err)
		// revert bonus status to active
		if err := q.RevertBonusStatusToActiveAndRemoveEntryFromTracking(b.B.ID.String(), b.B.ExternalID, b.B.Category, b.B.BonusExternalID); err != nil {
			slog.Error("error reverting bonus status to active", "err", err)
		}
		return fmt.Errorf("failed to update bonus status in directus: %w", err)
	}

	walletUpdateRequest := domain.UserWalletUpdateRequest{
		CurrencyCode: "USD",
		Amount:       strconv.FormatFloat(b.B.RewardAmount, 'f', 2, 64),
		Reason:       b.B.Reason,
		ProductId:    "promotions",
		Category:     fmt.Sprintf("bonus claimed for category %s and type %s", b.B.Category, b.B.Type),
	}
	slog.Info("Claiming bonus with the reason and type", "reason", b.B.Reason, "type", b.B.Type)
	slog.Info("updating wallet for the user", "externalID", b.B.ExternalID, "amount", walletUpdateRequest.Amount)

	_, err = q.elantil.UpdateUserWallet(b.Ctx, b.Token, walletUpdateRequest)
	if err != nil {
		slog.Error("error updating user wallet in Elantil", "err", err)
		// revert bonus status to active
		if err := q.RevertBonusStatusToActiveAndRemoveEntryFromTracking(b.B.ID.String(), b.B.ExternalID, b.B.Category, b.B.BonusExternalID); err != nil {
			slog.Error("error reverting bonus status to active", "err", err)
		}
		return fmt.Errorf("failed to update user wallet: %w", err)
	}

	return q.UpdateTrackingStatus(b.B.ID.String(), "completed")

}

func (q *UserBonusQueue) HandleReloadBonusClaim(ctx context.Context, token string, bonus domain.UserBonus, tx *gorm.DB) (string, error) {
	var reloadBonuses []domain.ReloadBonus
	// if err := json.Unmarshal(bonus.ReloadBonuses, &reloadBonuses); err != nil {
	// 	return "", fmt.Errorf("failed to unmarshal reload bonuses: %w", err)
	// }

	if len(reloadBonuses) == 0 {
		return "", fmt.Errorf("no reload bonuses found in the bonus")
	}
	now := time.Now()
	var claimableBonus *domain.ReloadBonus
	var claimedIndex int
	foundClaimable := false
	// expiry check
	for i, rb := range reloadBonuses {
		if rb.Claimed || now.Before(rb.AvailableOn) || now.After(rb.ExpiresOn) {
			continue
		}
		claimableBonus = &reloadBonuses[i]
		claimedIndex = i
		foundClaimable = true
		break
	}

	if !foundClaimable || claimableBonus == nil {
		return "", fmt.Errorf("no claimable reload bonus found")
	}
	_, err := q.rest_client.ClaimReloadBonus(bonus.ExternalID, claimableBonus.AvailableOn)
	if err != nil {
		return "", fmt.Errorf("failed to update CMS: %w", err)
	}

	reloadBonuses[claimedIndex].Claimed = true
	hasRemainingBonuses := false
	for _, rb := range reloadBonuses {
		if !rb.Claimed && !now.After(rb.ExpiresOn) {
			hasRemainingBonuses = true
			break
		}
	}
	updates := map[string]interface{}{
		"reload_bonuses": must(json.Marshal(reloadBonuses)),
		"claim_attempt":  true,
	}

	if !hasRemainingBonuses {
		updates["availed"] = true
		updates["bonus_status"] = "claimed"
	}
	result := tx.Model(&userBonus{}).
		Where("id = ? AND claim_attempt = true", bonus.ID).
		Updates(updates)

	if result.Error != nil {
		slog.Error("Failed to update bonus", "error", result.Error)
		return "", fmt.Errorf("failed to update bonus: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		slog.Error("Bonus was modified by another process")
		return "", fmt.Errorf("bonus was modified by another process")
	}

	// update tracking status
	if err := q.UpdateTrackingStatus(bonus.ID.String(), "processing_wallet"); err != nil {
		slog.Error("error updating tracking status", "err", err)
		return "", err
	}

	walletReq := domain.UserWalletUpdateRequest{
		CurrencyCode: "USD",
		Amount:       strconv.FormatFloat(claimableBonus.Amount, 'f', 2, 64),
		Reason:       bonus.Reason,
		ProductId:    "promotions",
		Category:     fmt.Sprintf("bonus claimed for category %s and type %s", bonus.Category, bonus.Type),
	}

	_, err = q.elantil.UpdateUserWallet(ctx, token, walletReq)
	if err != nil {
		return "", fmt.Errorf("failed to update user wallet: %w", err)
	}

	// update tracking status
	if err := q.UpdateTrackingStatus(bonus.ID.String(), "completed"); err != nil {
		slog.Error("error updating tracking status", "err", err)
		return "", err
	}

	return strconv.FormatFloat(claimableBonus.Amount, 'f', 2, 64), nil
}

func (q *UserBonusQueue) UpdateTrackingStatus(bonusID string, status string) error {
	result := q.db.Model(&userBonusTracking{}).
		Where("bonus_id = ?", bonusID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update tracking status: %w", result.Error)
	}

	return nil
}

func (q *UserBonusQueue) RevertBonusStatusToActiveAndRemoveEntryFromTracking(bonusID string, externalId string, category string, bonusExternalId int) error {
	return q.db.Transaction(func(tx *gorm.DB) error {
		// update bonus status to active
		result := tx.Model(&userBonus{}).
			Where("id = ?", bonusID).
			Updates(map[string]interface{}{
				"claim_attempt": false,
				"bonus_status":  "active",
				"availed":       false,
			})
		if result.Error != nil {
			return fmt.Errorf("failed to update bonus status: %w", result.Error)
		}

		// remove entry from tracking
		result = tx.Unscoped().Where("bonus_id = ?", bonusID).Delete(&userBonusTracking{})
		if result.Error != nil {
			return fmt.Errorf("failed to delete tracking entry: %w", result.Error)
		}
		// unclaim bonus in CMS
		_, err := q.rest_client.UnclaimBonusIfWalletUpdateFails(externalId, category, bonusExternalId)
		if err != nil {
			return fmt.Errorf("failed to unclaim bonus in CMS: %w", err)
		}

		return nil
	})
}
