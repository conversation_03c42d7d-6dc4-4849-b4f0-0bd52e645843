package postgres

import (
	"time"

	"github.com/google/uuid"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type transaction struct {
	UUIDKeyModel
	UserID           *uuid.UUID `gorm:"type:uuid"`
	Amount           float64    `gorm:"type:numeric;not null"`
	CryptoFiatAmount float64    `gorm:"type:numeric"`
	BetExternalID    string     `gorm:"type:text;index;not null"`
	Category         string     `gorm:"type:text;index:idx_transactions_category;not null"`
	Currency         string     `gorm:"type:text;not null"`
	ConvertedTo      string     `gorm:"type:text"`
	ExternalID       string     `gorm:"type:text;unique;index;not null"`
	InsertedAt       time.Time  `gorm:"type:timestamptz; not null"`
	Status           string     `gorm:"type:text;index:idx_transactions_status;not null"`
	Type             string     `gorm:"type:text;index:idx_transactions_type;not null"`
	WalletType       string     `gorm:"type:text;index:idx_transactions_wallet_type"`
	User             *user
}

func transactionToRow(trans domain.Transaction) transaction {
	var userID *uuid.UUID
	if trans.User != nil {
		userID = &trans.User.ID
	}

	return transaction{
		Amount:           trans.Amount,
		CryptoFiatAmount: trans.CryptoFiatAmount,
		BetExternalID:    trans.BetExternalID,
		Category:         trans.Category,
		Currency:         trans.Currency,
		ConvertedTo:      trans.ConvertedTo,
		ExternalID:       trans.ExternalID,
		InsertedAt:       trans.InsertedAt,
		Status:           trans.Status,
		Type:             trans.Type,
		UserID:           userID,
		WalletType:       trans.WalletType,
	}
}

func rowToTransaction(row transaction) domain.Transaction {
	return domain.Transaction{
		ID:               row.ID,
		Amount:           row.Amount,
		BetExternalID:    row.BetExternalID,
		Category:         row.Category,
		Currency:         row.Currency,
		ExternalID:       row.ExternalID,
		InsertedAt:       row.InsertedAt,
		Status:           row.Status,
		Type:             row.Type,
		CryptoFiatAmount: row.CryptoFiatAmount,
		User:             nil,
	}
}

func rowToTransactionPtr(trans transaction) *domain.Transaction {
	return utils.PointerOf(rowToTransaction(trans))
}

func rowsToTransactions(
	rows []transaction,
	totalCount int64,
	pageNum, pageSize int,
	totalDeposit, totalWithdrawal float64,
) *domain.Transactions {
	return &domain.Transactions{
		Items: utils.MapSlice(rows, rowToTransaction),
		Paging: domain.Paging{
			TotalCount:  totalCount,
			CurrentPage: pageNum,
			PageSize:    pageSize,
		},
		Details: domain.Details{
			TotalCredit: totalDeposit,
			TotalDebit:  totalWithdrawal,
		},
	}
}

func rowsToUnpagedTransactions(rows []transaction) []domain.Transaction {
	return utils.MapSlice(rows, rowToTransaction)
}
