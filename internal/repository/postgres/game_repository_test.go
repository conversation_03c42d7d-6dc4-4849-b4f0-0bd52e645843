package postgres

import (
	"context"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

func (s *databaseSuite) TestGameRepository_CreateGame() {
	repository := NewGameRepository(s.gormDB)

	tests := []struct {
		name            string
		game            *domain.Game
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "NonExistant",
			game:            &domain.Game{ExternalID: "id4"},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			_, err := repository.CreateGame(context.Background(), test.game)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestGameRepository_UpsertMany() {
	repository := NewGameRepository(s.gormDB)

	tests := []struct {
		name            string
		games           []domain.Game
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Existant",
			games:           []domain.Game{{ExternalID: "id2"}, {ExternalID: "id3"}},
			expectedErrFunc: require.NoError,
		},
		{
			name:            "NonExistant",
			games:           []domain.Game{{ExternalID: "id4"}, {ExternalID: "id5"}},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			err := repository.UpsertGames(context.Background(), test.games)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestGameRepository_GetGameByGameID() {
	repository := NewGameRepository(s.gormDB)

	tests := []struct {
		name            string
		gameID          uuid.UUID
		expectedGame    *domain.Game
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Existant",
			gameID:          gameID1,
			expectedGame:    &game1,
			expectedErrFunc: require.NoError,
		},
		{
			name:            "NonExistant",
			gameID:          gameNonExistant,
			expectedGame:    nil,
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			game, err := repository.GetGameByGameID(context.Background(), test.gameID)
			s.assertGamesAreEqual(test.expectedGame, game)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestGameRepository_GetGameByExternalID() {
	repository := NewGameRepository(s.gormDB)

	tests := []struct {
		name            string
		expectedGame    *domain.Game
		externalID      string
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Existant",
			expectedGame:    &game1,
			externalID:      "id1",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "NonExistant",
			expectedGame:    nil,
			externalID:      "id5",
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			game, err := repository.GetGameByExternalID(context.Background(), test.externalID)
			s.assertGamesAreEqual(test.expectedGame, game)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestGameRepository_GetGameByVendorGameID() {
	repository := NewGameRepository(s.gormDB)

	tests := []struct {
		name            string
		expectedGame    *domain.Game
		externalID      string
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Existant",
			expectedGame:    &game1,
			externalID:      "id1",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "NonExistant",
			expectedGame:    nil,
			externalID:      "id5",
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			game, err := repository.GetGameByVendorGameID(context.Background(), test.externalID)
			s.assertGamesAreEqual(test.expectedGame, game)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) assertGamesAreEqual(expected, actual *domain.Game) {
	s.Assert().True(
		cmp.Equal(expected, actual),
		cmp.Diff(expected, actual),
	)
}
