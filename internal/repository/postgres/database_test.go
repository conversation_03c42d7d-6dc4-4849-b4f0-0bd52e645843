package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/docker/go-connections/nat"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/wait"
	"gorm.io/gorm"
)

// databaseSuite is the base suite used for PostgreSQL unit testing.
type databaseSuite struct {
	suite.Suite
	ctx       context.Context
	container testcontainers.Container
	gormDB    *gorm.DB
	db        *sql.DB
	
}

// SetupSuite starts the database container, connects to it and creates all the necessary resources.
func (s *databaseSuite) SetupSuite() {
	ctx := context.Background()

	container, dsn, err := setupPostgres(ctx, "testdb")
	s.Require().NoError(err)

	gormDB, err := openDB(dsn)
	s.Require().NoError(err)

	db, err := gormDB.DB()
	s.Require().NoError(err)

	err = autoMigrate(db, gormDB)
	s.Require().NoError(err)

	s.ctx = ctx
	s.container = container
	s.gormDB = gormDB
	s.db = db
}

func setupPostgres(ctx context.Context, database string) (testcontainers.Container, string, error) {
	username := "postgres"
	password := "postgres"
	port := nat.Port("5432/tcp")

	container, err := postgres.RunContainer(
		ctx,
		testcontainers.WithImage("postgres:13-alpine"),
		testcontainers.WithWaitStrategy(wait.ForListeningPort(port)),
		postgres.WithDatabase(database),
		postgres.WithUsername(username),
		postgres.WithPassword(password),
	)
	if err != nil {
		return nil, "", err
	}

	host, err := container.Host(ctx)
	if err != nil {
		return nil, "", err
	}

	mappedPort, err := container.MappedPort(ctx, port)
	if err != nil {
		return nil, "", err
	}

	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s port=%s database=%s",
		host,
		username,
		password,
		mappedPort.Port(),
		database,
	)
	return container, dsn, nil
}

// SetupSuite closes the database connection and terminates its container.
func (s *databaseSuite) TearDownSuite() {
	err := s.db.Close()
	s.Assert().NoError(err)

	err = s.container.Terminate(s.ctx)
	s.Assert().NoError(err)
}

// SetupSubTest inserts data into the tables.
func (s *databaseSuite) SetupSubTest() {
	query, err := os.ReadFile("./test_data/init_db.sql")
	s.Require().NoError(err)

	_, err = s.db.Exec(string(query))
	s.Require().NoError(err)
}

// TearDownSubTest cleans all the data from the tables.
func (s *databaseSuite) TearDownSubTest() {
	query := `
		TRUNCATE
			transactions,
			registered_emails,
			user_claimed_coins,
			vip_user_balances,
			user_assets,
			boosts,
			bets,
			users,
			games_versions,
			games;
	`
	_, err := s.db.Exec(string(query))
	s.Require().NoError(err)
}

func (*databaseSuite) getVIPTiers() domain.VIPTiers {
	return domain.VIPTiers{
		domain.VIPTiersThreshold{Tier: "tier1", Threshold: 5},
		domain.VIPTiersThreshold{Tier: "tier2", Threshold: 20},
		domain.VIPTiersThreshold{Tier: "tier3", Threshold: 30},
		domain.VIPTiersThreshold{Tier: "tier4", Threshold: 40},
		domain.VIPTiersThreshold{Tier: "tier5", Threshold: 50},
	}
}

func TestDatabaseSuite(t *testing.T) {
	suite.Run(t, new(databaseSuite))
}
