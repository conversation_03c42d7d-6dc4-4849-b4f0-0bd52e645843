package postgres

import (
	"context"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// TestBetRepository_GetBetsWithoutTotalCount tests the optimization where TotalCount is removed from pagination
func (s *databaseSuite) TestBetRepository_GetBetsWithoutTotalCount() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		params          *domain.GetBetsParams
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "PaginationWithoutTotalCount",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet5, bet4, bet3, bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "PaginationWithFiltering",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   5,
				},
				UserExternalID:         utils.PointerOf("id1"),
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    5,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "SmallPageSize",
			params: &domain.GetBetsParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 2,
					PageSize:   2,
				},
				UserExternalID:         nil,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet3, bet2},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 2,
					PageSize:    2,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bets, err := repository.GetBets(context.Background(), test.params)
			test.expectedErrFunc(s.T(), err)

			// Verify that the paging structure doesn't contain TotalCount
			assert.IsType(s.T(), domain.PagingWithoutTotalCount{}, bets.Paging)

			// Verify pagination fields
			assert.Equal(s.T(), test.expectedBets.Paging.CurrentPage, bets.Paging.CurrentPage)
			assert.Equal(s.T(), test.expectedBets.Paging.PageSize, bets.Paging.PageSize)

			// Verify the items
			assert.Len(s.T(), bets.Items, len(test.expectedBets.Items))
		})
	}
}

// TestBetRepository_GetBetsByUserIDWithoutTotalCount tests the optimization for user-specific bet queries
func (s *databaseSuite) TestBetRepository_GetBetsByUserIDWithoutTotalCount() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())

	tests := []struct {
		name            string
		params          *domain.GetBetsByUserIDParams
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "UserBetsWithoutTotalCount",
			params: &domain.GetBetsByUserIDParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserID:                 userID1,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{bet2, bet1},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "EmptyResultsWithoutTotalCount",
			params: &domain.GetBetsByUserIDParams{
				OrderParams: domain.OrderParams{
					OrderBy: "time",
					Order:   domain.OrderDirDesc,
				},
				PagingParams: domain.PagingParams{
					PageNumber: 1,
					PageSize:   10,
				},
				UserID:                 userIDNonExistant,
				IncludeBetsWithoutGame: false,
			},
			expectedBets: &domain.Bets{
				Items: []domain.Bet{},
				Paging: domain.PagingWithoutTotalCount{
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			bets, err := repository.GetBetsByUserID(context.Background(), test.params)
			test.expectedErrFunc(s.T(), err)

			// Verify that the paging structure doesn't contain TotalCount
			assert.IsType(s.T(), domain.PagingWithoutTotalCount{}, bets.Paging)

			// Verify pagination fields
			assert.Equal(s.T(), test.expectedBets.Paging.CurrentPage, bets.Paging.CurrentPage)
			assert.Equal(s.T(), test.expectedBets.Paging.PageSize, bets.Paging.PageSize)

			// Verify the items count matches expected
			assert.Len(s.T(), bets.Items, len(test.expectedBets.Items))
		})
	}
}

// TestBetRepository_PerformanceImprovements tests that the new implementation is more efficient
func (s *databaseSuite) TestBetRepository_PerformanceImprovements() {
	repository := NewBetRepository(s.gormDB, s.getVIPTiers())
	ctx := context.Background()

	params := &domain.GetBetsParams{
		OrderParams: domain.OrderParams{
			OrderBy: "time",
			Order:   domain.OrderDirDesc,
		},
		PagingParams: domain.PagingParams{
			PageNumber: 1,
			PageSize:   10,
		},
		IncludeBetsWithoutGame: false,
	}

	// Test that we can retrieve bets without counting total
	bets, err := repository.GetBets(ctx, params)
	require.NoError(s.T(), err)
	require.NotNil(s.T(), bets)

	// Verify the structure is PagingWithoutTotalCount
	assert.IsType(s.T(), domain.PagingWithoutTotalCount{}, bets.Paging)

	// Verify no TotalCount field exists by checking the type
	_, hasTotalCount := interface{}(bets.Paging).(struct{ TotalCount int64 })
	assert.False(s.T(), hasTotalCount, "Paging should not have TotalCount field")
}
