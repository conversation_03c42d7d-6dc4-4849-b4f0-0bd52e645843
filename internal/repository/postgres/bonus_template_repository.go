package postgres

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type BonusTemplateRepository struct {
	db *gorm.DB
}

func NewBonusTemplateRepository(db *gorm.DB) *BonusTemplateRepository {
	return &BonusTemplateRepository{db: db}
}

func (s *BonusTemplateRepository) CreateBonusTemplate(ctx context.Context, bonusTemplate *domain.CreateBonusTemplateRequest) error {
	var existing UserBonusTemplates
	username := strings.ToLower(bonusTemplate.UserName)
	if err := s.db.Where("user_name = ? AND offer_code = ? AND offer_name = ?",
		username, bonusTemplate.OfferCode, bonusTemplate.OfferName).First(&existing).Error; err == nil {
		return nil
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing template: %w", err)
	}

	bonusTemplateModel := UserBonusTemplates{
		UUIDKeyModel: UUIDKeyModel{
			ID: uuid.New(),
		},
		UserName:  username,
		OfferCode: bonusTemplate.OfferCode,
		OfferName: bonusTemplate.OfferName,
		Status:   bonusTemplate.Status,
	}

	if err := s.db.Create(&bonusTemplateModel).Error; err != nil {
		slog.Error("Failed to create bonus template", slog.Any("error", err))
		return fmt.Errorf("failed to create bonus template: %w", err)
	}

	return nil
}

func (s *BonusTemplateRepository) GetUserBonusTemplates(ctx context.Context, username string) (*domain.BonusTemplateResponse, error) {
	var bonusTemplates []UserBonusTemplates
	if err := s.db.Where("user_name = ?", username).Find(&bonusTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to get bonus templates: %w", err)
	}

	if len(bonusTemplates) == 0 {
		return &domain.BonusTemplateResponse{}, nil
	}

	var response domain.BonusTemplateResponse
	for _, bonusTemplate := range bonusTemplates {
		response.BonusTemplates = append(response.BonusTemplates, domain.UserBonusTemplate{
			UserName:      bonusTemplate.UserName,
			OfferCode:     bonusTemplate.OfferCode,
			OfferName:     bonusTemplate.OfferName,
			OwnerBonusID:  bonusTemplate.OwnerBonusID,
			IsModalClosed: bonusTemplate.IsModalClosed,
			Status:        bonusTemplate.Status,
		})
	}

	return &response, nil
}

func (s *BonusTemplateRepository) UpdateBonusTemplate(ctx context.Context, bonusTemplate *domain.UserBonusTemplate) error {
	updates := map[string]interface{}{
		"owner_bonus_id":  bonusTemplate.OwnerBonusID,
		"is_modal_closed": bonusTemplate.IsModalClosed,
	}
	if bonusTemplate.Status != "" {
		updates["status"] = bonusTemplate.Status
	}

	if err := s.db.Model(&UserBonusTemplates{}).
		Where("offer_code = ? AND user_name = ?", bonusTemplate.OfferCode, bonusTemplate.UserName).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update bonus template: %w", err)
	}

	return nil
}

func (s *BonusTemplateRepository) DeleteBonusTemplate(ctx context.Context, offerCode, username string) error {
	result := s.db.Where("offer_code = ? AND user_name = ?", offerCode, username).
		Delete(&UserBonusTemplates{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete bonus template: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("bonus template not found")
	}

	return nil
}
