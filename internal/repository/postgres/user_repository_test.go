package postgres

import (
	"context"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

func (s *databaseSuite) TestUserRepository_UpsertUser() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name            string
		user            *domain.User
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "Update",
			user: &domain.User{
				ExternalID: "id2",
				UserName:   "user2",
			},
			expectedUser: &domain.User{
				ExternalID:          "id2",
				UserName:            "user2",
				VIPStatus:           "",
				HideAllStats:        false,
				HideTournamentStats: false,
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "Create",
			user: &domain.User{
				ExternalID: "id666",
				UserName:   "user666",
			},

			expectedUser: &domain.User{
				ExternalID:          "id666",
				UserName:            "user666",
				VIPStatus:           "",
				HideAllStats:        false,
				HideTournamentStats: false,
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			user, err := repository.UpsertUser(context.Background(), test.user)
			s.assertUserAreEqual(test.expectedUser, user)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_UpdateUser() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name            string
		user            *domain.User
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "UpdateCoinBoost",
			user: &domain.User{
				ID:         userID1,
				ExternalID: "id1",
				UserName:   "user1",
				VIPStatus:  "",
			},
			expectedUser: &domain.User{
				ID:         userID1,
				ExternalID: "id1",
				UserName:   "user1",
				VIPStatus:  "",
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			ctx := context.Background()
			err := repository.UpdateUserByID(ctx, test.user)
			test.expectedErrFunc(s.T(), err)
			user, err := repository.GetUserByExternalID(ctx, test.user.ExternalID)
			s.assertUserAreEqual(test.expectedUser, user)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_GetUserByUserName() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name            string
		userName        string
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:     "Existant",
			userName: "user1",
			expectedUser: func() *domain.User {
				user := user1WithStats
				user.UserAssets = []domain.UserAsset{}
				return &user
			}(),
			expectedErrFunc: require.NoError,
		},
		{
			name:            "Fail_Not_Found",
			userName:        "nonexistant",
			expectedUser:    nil,
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			user, err := repository.GetUserByUserName(context.Background(), test.userName)
			s.assertUserAreEqual(test.expectedUser, user)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_GetUserByID() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name            string
		userID          uuid.UUID
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:   "Existant",
			userID: userID1,
			expectedUser: func() *domain.User {
				user := user1WithStats
				user.UserAssets = []domain.UserAsset{}
				return &user
			}(),
			expectedErrFunc: require.NoError,
		},
		{
			name:            "NonExistant",
			userID:          userIDNonExistant,
			expectedUser:    nil,
			expectedErrFunc: require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			user, err := repository.GetUserByID(context.Background(), test.userID)
			s.assertUserAreEqual(test.expectedUser, user)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_GetRankedUserByExternalID() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name               string
		externalUserID     string
		expectedRankedUser *domain.RankedUser
		expectedErrFunc    require.ErrorAssertionFunc
	}{
		{
			name:           "ExistantWithCoins",
			externalUserID: "id2",
			expectedRankedUser: &domain.RankedUser{
				User:                user2WithStatsWithAssets,
				Rank:                1,
				RemainingToNextRank: 5,
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:           "Existant",
			externalUserID: "id1",
			expectedRankedUser: &domain.RankedUser{
				User:                user1WithStatsWithAssets,
				Rank:                2,
				RemainingToNextRank: 5,
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:               "NonExistant",
			externalUserID:     "id6",
			expectedRankedUser: nil,
			expectedErrFunc:    require.Error,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			rankedUser, err := repository.GetRankedUserByExternalID(context.Background(), test.externalUserID)
			test.expectedErrFunc(s.T(), err)
			s.assertRankedUserAreEqual(test.expectedRankedUser, rankedUser)
		})
	}
}

func (s *databaseSuite) TestUserRepository_GetRankedUsers() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name                string
		getUserParams       *domain.GetUserParams
		expectedRankedUsers *domain.RankedUsers
		expectedErrFunc     require.ErrorAssertionFunc
	}{
		{
			name: "RankedByTotalBets",
			getUserParams: &domain.GetUserParams{
				OrderParams:  domain.OrderParams{OrderBy: "total_bets", Order: domain.OrderDirDesc},
				PagingParams: domain.PagingParams{PageNumber: 1, PageSize: 10},
			},
			expectedRankedUsers: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User:                updateUserVIPStatus(user2WithStatsWithAssets, ""),
						Rank:                1,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user1WithStatsWithAssets, ""),
						Rank:                2,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user4WithStatsWithAssets, ""),
						Rank:                3,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user3WithStatsWithAssets, ""),
						Rank:                4,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user5WithStatsWithAssets, ""),
						Rank:                5,
						RemainingToNextRank: 5,
					},
				},
				Paging: domain.Paging{
					TotalCount:  5,
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "RankedByTotalCoins",
			getUserParams: &domain.GetUserParams{
				OrderParams:  domain.OrderParams{OrderBy: "total_coins", Order: domain.OrderDirDesc},
				PagingParams: domain.PagingParams{PageNumber: 1, PageSize: 10},
			},
			expectedRankedUsers: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User:                updateUserVIPStatus(user2WithStatsWithAssets, ""),
						Rank:                1,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user1WithStatsWithAssets, ""),
						Rank:                2,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user3WithStatsWithAssets, ""),
						Rank:                3,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user4WithStatsWithAssets, ""),
						Rank:                4,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user5WithStatsWithAssets, ""),
						Rank:                5,
						RemainingToNextRank: 5,
					},
				},
				Paging: domain.Paging{
					TotalCount:  5,
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name: "RankedByWagered",
			getUserParams: &domain.GetUserParams{
				OrderParams:  domain.OrderParams{OrderBy: "wagered", Order: domain.OrderDirDesc},
				PagingParams: domain.PagingParams{PageNumber: 1, PageSize: 10},
			},
			expectedRankedUsers: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User:                updateUserVIPStatus(user2WithStatsWithAssets, ""),
						Rank:                1,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user1WithStatsWithAssets, ""),
						Rank:                2,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user3WithStatsWithAssets, ""),
						Rank:                3,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user4WithStatsWithAssets, ""),
						Rank:                4,
						RemainingToNextRank: 5,
					},
					{
						User:                updateUserVIPStatus(user5WithStatsWithAssets, ""),
						Rank:                5,
						RemainingToNextRank: 5,
					},
				},
				Paging: domain.Paging{
					TotalCount:  5,
					CurrentPage: 1,
					PageSize:    10,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			rankedUsers, err := repository.GetRankedUsers(context.Background(), test.getUserParams)
			assert.Equal(s.T(), test.expectedRankedUsers, rankedUsers)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_GetRankedUsersByExternalID() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})

	tests := []struct {
		name                string
		externalUserID      string
		orderBy             string
		retrieveAmount      int
		expectedRankedUsers []domain.RankedUser
		expectedErrFunc     require.ErrorAssertionFunc
	}{
		{
			name:           "RankedByTotalBetsLowestRank",
			externalUserID: "id5",
			orderBy:        "total_bets",
			retrieveAmount: 1,
			expectedRankedUsers: []domain.RankedUser{
				{
					User:                updateUserVIPStatus(user3WithStats, ""),
					Rank:                4,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user5WithStats, ""),
					Rank:                5,
					RemainingToNextRank: 5,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:           "RankedByTotalCoins",
			externalUserID: "id3",
			orderBy:        "total_coins",
			retrieveAmount: 1,
			expectedRankedUsers: []domain.RankedUser{
				{
					User:                updateUserVIPStatus(user1WithStats, ""),
					Rank:                2,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user3WithStats, ""),
					Rank:                3,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user4WithStats, ""),
					Rank:                4,
					RemainingToNextRank: 5,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:           "RankedByWageredHighestRank",
			externalUserID: "id2",
			orderBy:        "wagered",
			retrieveAmount: 1,
			expectedRankedUsers: []domain.RankedUser{
				{
					User:                updateUserVIPStatus(user2WithStats, ""),
					Rank:                1,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user1WithStats, ""),
					Rank:                2,
					RemainingToNextRank: 5,
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:           "RankedByWageredRetrieveAmount2",
			externalUserID: "id3",
			orderBy:        "wagered",
			retrieveAmount: 2,
			expectedRankedUsers: []domain.RankedUser{
				{
					User:                updateUserVIPStatus(user2WithStats, ""),
					Rank:                1,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user1WithStats, ""),
					Rank:                2,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user3WithStats, ""),
					Rank:                3,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user4WithStats, ""),
					Rank:                4,
					RemainingToNextRank: 5,
				},
				{
					User:                updateUserVIPStatus(user5WithStats, ""),
					Rank:                5,
					RemainingToNextRank: 5,
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		s.Run(test.name, func() {
			rankedUsers, err := repository.GetRankedUsersByExternalID(
				context.Background(),
				test.externalUserID,
				test.retrieveAmount,
				test.orderBy,
			)

			assert.Equal(s.T(), test.expectedRankedUsers, rankedUsers)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func (s *databaseSuite) TestUserRepository_SaveRegisteredEmail() {
	repository := NewUserRepository(s.gormDB, s.getVIPTiers(), &domainmock.ElantilWageringClient{})
	tests := []struct {
		name            string
		email           string
		email_marketing bool
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "ValidEmail",
			email:           "<EMAIL>",
			email_marketing: true,
			expectedErrFunc: require.NoError,
		},
	}
	for _, test := range tests {
		s.Run(test.name, func() {
			err := repository.SaveRegisteredEmail(context.Background(), test.email, test.email_marketing)
			test.expectedErrFunc(s.T(), err)
		})
	}
}

func updateUserVIPStatus(user domain.User, vipStatus string) domain.User {
	user.VIPStatus = vipStatus
	return user
}

func (s *databaseSuite) assertClaimedCoins(expected, actual *domain.UserClaimedCoins) {
	opts := []cmp.Option{
		cmpopts.IgnoreFields(domain.UserClaimedCoins{}, "RankedUser", "ID"),
	}

	s.Assert().True(
		cmp.Equal(expected, actual, opts...),
		cmp.Diff(expected, actual, opts...),
	)
}

func (s *databaseSuite) assertUserAreEqual(expected, actual *domain.User) {
	opts := []cmp.Option{
		cmpopts.IgnoreFields(domain.User{}, "ID", "LastUpdatedOn", "JoinDate", "VIPStatus"),
	}

	s.Assert().True(
		cmp.Equal(expected, actual, opts...),
		cmp.Diff(expected, actual, opts...),
	)
}

func (s *databaseSuite) assertRankedUserAreEqual(expected, actual *domain.RankedUser) {
	opts := []cmp.Option{
		cmpopts.IgnoreFields(domain.User{}, "ID", "LastUpdatedOn", "JoinDate", "VIPStatus"),
	}

	s.Assert().True(
		cmp.Equal(expected, actual, opts...),
		cmp.Diff(expected, actual, opts...),
	)
}
