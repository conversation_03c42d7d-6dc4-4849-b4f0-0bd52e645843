package postgres

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"strconv"
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type userBonus struct {
	UUIDKeyModel
	ExternalID      string          `gorm:"column:external_id;not null"`
	BonusConfigID   int             `gorm:"column:bonus_config_id"`
	BonusExternalID int             `gorm:"column:bonus_external_id"`
	Username        string          `gorm:"column:user_name"`
	Category        string          `gorm:"column:category"`
	ExpiresOn       time.Time       `gorm:"column:expires_on"`
	Eligible        bool            `gorm:"column:eligible"`
	Availed         bool            `gorm:"column:availed"`
	BonusStatus     string          `gorm:"column:bonus_status"`
	UserVipStatus   string          `gorm:"column:user_vip_status"`
	RewardAmount    float64         `gorm:"column:reward_amount"`
	ClaimAttempt    bool            `gorm:"column:claim_attempt;default:false"`
	ClaimSuccess    bool            `gorm:"column:claim_success;default:false"`
	ReloadBonuses   json.RawMessage `gorm:"type:jsonb;column:reload_bonuses"`
	Reason          string          `gorm:"column:reason"`
	Note            string          `gorm:"column:note"`
	ModalClosed     bool            `gorm:"column:modal_closed;default:false"`
	Type            string          `gorm:"column:type"`
	TimeRange       string          `gorm:"column:time_range"`
}

func (userBonus) TableName() string {
	return "user_bonus"
}

type userBonusTracking struct {
	UUIDKeyModel
	BonusId      string  `gorm:"column:bonus_id;not null;unique"`
	UserId       string  `gorm:"column:user_id"`
	RewardAmount float64 `gorm:"column:reward_amount"`
	Status       string  `gorm:"column:status"`
}

func (userBonusTracking) TableName() string {
	return "user_bonus_tracking"
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if strings.EqualFold(s, item) {
			return true
		}
	}
	return false
}

func must(data []byte, err error) []byte {
	if err != nil {
		panic(err)
	}
	return data
}

func getBonusStatusAndPeriod(bonusType string) (string, string) {
	switch bonusType {
	case "weekly":
		return "pending_review", "week"
	case "monthly":
		return "pending_review", "month"
	case "daily":
		return "pending_review", "day"
	case "instant":
		return "pending_review", "last 120 minutes"
	default:
		return "pending_review", bonusType
	}
}

// convert userBonus to domain.UserBonus
func toDomain(u userBonus) domain.UserBonus {
	var reloadBonuses []domain.ReloadBonus
	_ = json.Unmarshal(u.ReloadBonuses, &reloadBonuses)
	return domain.UserBonus{
		ID:              u.ID,
		ExternalID:      u.ExternalID,
		BonusExternalID: u.BonusExternalID,
		BonusConfigID:   u.BonusConfigID,
		Username:        u.Username,
		Category:        u.Category,
		ExpiresOn:       u.ExpiresOn,
		Eligible:        u.Eligible,
		Availed:         u.Availed,
		BonusStatus:     u.BonusStatus,
		UserVipStatus:   u.UserVipStatus,
		RewardAmount:    u.RewardAmount,
		ClaimAttempt:    u.ClaimAttempt,
		ClaimSuccess:    u.ClaimSuccess,
		ReloadBonuses:   reloadBonuses,
		Reason:          u.Reason,
		Note:            u.Note,
		ModalClosed:     u.ModalClosed,
		Type:            u.Type,
	}
}

func filterValidReloadBonuses(reloadBonuses []domain.ReloadBonus, now time.Time) []domain.ReloadBonus {
	validReloadBonuses := make([]domain.ReloadBonus, 0)
	for _, rb := range reloadBonuses {
		if !rb.Claimed &&
			(now.Equal(rb.AvailableOn) || now.After(rb.AvailableOn)) &&
			now.Before(rb.ExpiresOn) {
			validReloadBonuses = append(validReloadBonuses, rb)
		}
	}
	return validReloadBonuses
}

func ToDomain(r domain.ReloadBonusRequest) (domain.UserBonus, error) {
	parseTime := func(timeStr string) (time.Time, error) {
		t, err := time.Parse(time.RFC3339, timeStr)
		if err == nil {
			return t, nil
		}
		t, err = time.Parse("2006-01-02T15:04:05", timeStr)
		if err != nil {
			return time.Time{}, err
		}
		return t.UTC(), nil
	}
	expiresOn, err := parseTime(r.ExpiresOn)
	if err != nil {
		return domain.UserBonus{}, fmt.Errorf("failed to parse expiresOn: %w", err)
	}
	rewardAmount, err := strconv.ParseFloat(r.RewardAmount, 64)
	if err != nil {
		return domain.UserBonus{}, fmt.Errorf("failed to parse reward amount: %w", err)
	}
	reloadBonuses := make([]domain.ReloadBonus, len(r.ReloadBonuses))
	for i, rb := range r.ReloadBonuses {
		availableOn, err := parseTime(rb.AvailableOn)
		if err != nil {
			return domain.UserBonus{}, fmt.Errorf("failed to parse availableOn for bonus %d: %w", i, err)
		}

		expiresOn, err := parseTime(rb.ExpiresOn)
		if err != nil {
			return domain.UserBonus{}, fmt.Errorf("failed to parse expiresOn for bonus %d: %w", i, err)
		}

		reloadBonuses[i] = domain.ReloadBonus{
			Amount:      rb.Amount,
			AvailableOn: availableOn,
			ExpiresOn:   expiresOn,
			Claimed:     rb.Claimed,
		}
	}

	return domain.UserBonus{
		ID:              uuid.New(),
		ExternalID:      r.UserID,
		BonusExternalID: r.ID,
		BonusConfigID:   5,
		Username:        r.Username,
		Category:        r.Category,
		ExpiresOn:       expiresOn,
		Eligible:        true,
		Availed:         false,
		BonusStatus:     r.Status,
		RewardAmount:    rewardAmount,
		ClaimAttempt:    false,
		ClaimSuccess:    false,
		ReloadBonuses:   reloadBonuses,
		Reason:          r.Reason,
		Note:            r.Note,
		Type:            r.Type,
	}, nil
}

func ToUserBonusRequest(data domain.UserBonus) domain.UserBonusRequest {
	return domain.UserBonusRequest{
		ExternalID:      data.ExternalID,
		BonusExternalID: data.BonusExternalID,
		BonusConfigID:   data.BonusConfigID,
		Username:        data.Username,
		Category:        data.Category,
		ExpiresOn:       data.ExpiresOn,
		Eligible:        data.Eligible,
		Availed:         data.Availed,
		BonusStatus:     data.BonusStatus,
		UserVipStatus:   data.UserVipStatus,
		RewardAmount:    data.RewardAmount,
		Reason:          data.Reason,
		Note:            data.Note,
		Type:            data.Type,
		TimeRange:       data.TimeRange,
	}
}

func ToDBUserBonus(request domain.UserBonusRequest, id uuid.UUID, now time.Time, reloadBonusesJSON json.RawMessage) userBonus {
	return userBonus{
		UUIDKeyModel: UUIDKeyModel{
			ID:        id,
			CreatedAt: now,
			UpdatedAt: now,
		},
		ExternalID:      request.ExternalID,
		BonusConfigID:   request.BonusConfigID,
		BonusExternalID: request.BonusExternalID,
		Username:        request.Username,
		Category:        request.Category,
		ExpiresOn:       request.ExpiresOn,
		Eligible:        request.Eligible,
		Availed:         request.Availed,
		BonusStatus:     request.BonusStatus,
		UserVipStatus:   request.UserVipStatus,
		RewardAmount:    request.RewardAmount,
		ClaimAttempt:    false,
		ClaimSuccess:    false,
		ReloadBonuses:   reloadBonusesJSON,
		Reason:          request.Reason,
		Note:            request.Note,
		Type:            request.Type,
		TimeRange:       request.TimeRange,
	}
}

func processReloadBonuses(category string, reloadBonuses []domain.ReloadBonus) (json.RawMessage, error) {
	if category == "reload" {
		if len(reloadBonuses) > 0 {
			jsonBytes, err := json.Marshal(reloadBonuses)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal reload bonuses: %w", err)
			}
			return jsonBytes, nil
		}
		return json.RawMessage("[]"), nil
	}
	return json.RawMessage("null"), nil
}
