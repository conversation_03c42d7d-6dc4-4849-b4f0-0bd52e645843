package postgres

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CMSTermsAndConditionsRepository struct {
	db *gorm.DB
}

func NewCMSTermsAndConditionsRepository(db *gorm.DB) *CMSTermsAndConditionsRepository {
	return &CMSTermsAndConditionsRepository{db: db}
}

func (s *CMSTermsAndConditionsRepository) CreateandUpdateCMSTermsAndConditions(ctx context.Context, ContentTemplate *domain.AddCMSTermsAndConditions) error {
	var existing TermsAndConditionTemplate
	category := strings.ToLower(ContentTemplate.Category)
	if err := s.db.Where("category = ?", category).First(&existing).Error; err == nil {
		existing.Content = ContentTemplate.Content
		existing.Status = ContentTemplate.Status
		existing.Version = ContentTemplate.Version
		existing.Type = ContentTemplate.Type
		if err := s.db.Save(&existing).Error; err != nil {
			slog.Error("failed to update existing terms and conditions", slog.Any("error", err))
			return fmt.Errorf("failed to update existing terms and conditions: %w", err)
		}
		return nil
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing category: %w", err)
	}

	termsandconditionModel := TermsAndConditionTemplate{
		UUIDKeyModel: UUIDKeyModel{
			ID: uuid.New(),
		},
		Category: category,
		Content:  ContentTemplate.Content,
		Status:   ContentTemplate.Status,
		Version:  ContentTemplate.Version,
		Type:     ContentTemplate.Type,
	}
	if err := s.db.Create(&termsandconditionModel).Error; err != nil {
		slog.Error("failed to create terms and conditions model", slog.Any("error", err))
		return fmt.Errorf("failed to create terms and conditions model: %w", err)
	}
	return nil
}

func (s *CMSTermsAndConditionsRepository) GetCMSTermsAndConditions(ctx context.Context, category string) (*domain.AddCMSTermsAndConditions, error) {
	var model TermsAndConditionTemplate
	cat := strings.ToLower(category)

	if err := s.db.Where("category = ? AND status = 'published'", cat).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("terms and conditions not found for category: %s", category)
		}
		return nil, fmt.Errorf("failed to fetch terms and conditions: %w", err)
	}

	response := &domain.AddCMSTermsAndConditions{
		Category: model.Category,
		Content:  model.Content,
		Status:   model.Status,
		Version:  model.Version,
	}

	return response, nil
}

func (s *CMSTermsAndConditionsRepository) GetSportsBookCMSTermsAndConditions(ctx context.Context) ([]*domain.AddCMSTermsAndConditions, error) {
	var models []TermsAndConditionTemplate

	if err := s.db.Select("category, status, version, type").Where("type = ? AND status = 'published'", "sportsbook_individual_sports").Find(&models).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("no terms and conditions found for sportsbook category")
		}
		return nil, fmt.Errorf("failed to fetch sportsbook terms and conditions: %w", err)
	}

	var response []*domain.AddCMSTermsAndConditions
	for _, model := range models {
		response = append(response, &domain.AddCMSTermsAndConditions{
			Category: model.Category,
			Status:   model.Status,
			Version:  model.Version,
			Type:     model.Type,
		})
	}

	return response, nil
}
func (s *CMSTermsAndConditionsRepository) DeleteCMSTermsAndConditions(ctx context.Context, category string) error {
	cat := strings.ToLower(category)

	result := s.db.Where("category = ?", cat).Delete(&TermsAndConditionTemplate{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete terms and conditions: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("no terms and conditions found for category")
	}

	return nil
}
