package postgres

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func (s *databaseSuite) TestBonusTemplateRepository_CreateBonusTemplate() {
	repository := NewBonusTemplateRepository(s.gormDB)

	bonusTemplate := &domain.CreateBonusTemplateRequest{
		UserName:  "TestUser",
		OfferCode: "OFFER123",
		OfferName: "Test Offer",
		Status:    "active",
	}

	err := repository.CreateBonusTemplate(context.Background(), bonusTemplate)
	require.NoError(s.T(), err)

	var created UserBonusTemplates
	err = s.gormDB.Where("user_name = ? AND offer_code = ?", "testuser", "OFFER123").First(&created).Error
	require.NoError(s.T(), err)
	require.Equal(s.T(), "testuser", created.UserName)
	require.Equal(s.T(), "OFFER123", created.OfferCode)
	require.Equal(s.T(), "Test Offer", created.OfferName)
	require.Equal(s.T(), "active", created.Status)
}

func (s *databaseSuite) TestBonusTemplateRepository_CreateBonusTemplate_Duplicate() {
	repository := NewBonusTemplateRepository(s.gormDB)
	s.gormDB.Where("user_name = ? AND offer_code = ?", "testuser", "OFFER123").Delete(&UserBonusTemplates{})
	s.gormDB.Create(&UserBonusTemplates{
		UUIDKeyModel: UUIDKeyModel{ID: uuid.New()},
		UserName:     "testuser",
		OfferCode:    "OFFER123",
		OfferName:    "Test Offer",
		Status:       "active",
	})

	var countBefore int64
	s.gormDB.Model(&UserBonusTemplates{}).Where("user_name = ? AND offer_code = ?", "testuser", "OFFER123").Count(&countBefore)
	require.Equal(s.T(), int64(1), countBefore)
	
	bonusTemplate := &domain.CreateBonusTemplateRequest{
		UserName:  "TestUser", 
		OfferCode: "OFFER123",
		OfferName: "Test Offer", 
		Status:    "active",
	}

	err := repository.CreateBonusTemplate(context.Background(), bonusTemplate)
	require.NoError(s.T(), err) 
	var count int64
	s.gormDB.Model(&UserBonusTemplates{}).Where("user_name = ? AND offer_code = ?", "testuser", "OFFER123").Count(&count)
	require.Equal(s.T(), int64(1), count)
}

func (s *databaseSuite) TestBonusTemplateRepository_GetUserBonusTemplates() {
	repository := NewBonusTemplateRepository(s.gormDB)

	templates := []UserBonusTemplates{
		{
			UUIDKeyModel:  UUIDKeyModel{ID: uuid.New()},
			UserName:      "testuser",
			OfferCode:     "OFFER1",
			OfferName:     "Offer 1",
			OwnerBonusID:  "bonus1",
			IsModalClosed: true,
			Status:        "active",
		},
		{
			UUIDKeyModel:  UUIDKeyModel{ID: uuid.New()},
			UserName:      "testuser",
			OfferCode:     "OFFER2",
			OfferName:     "Offer 2",
			OwnerBonusID:  "bonus2",
			IsModalClosed: false,
			Status:        "inactive",
		},
	}

	for _, template := range templates {
		s.gormDB.Create(&template)
	}

	response, err := repository.GetUserBonusTemplates(context.Background(), "testuser")
	require.NoError(s.T(), err)
	require.Len(s.T(), response.BonusTemplates, 2)

	require.Equal(s.T(), "testuser", response.BonusTemplates[0].UserName)
	require.Equal(s.T(), "OFFER1", response.BonusTemplates[0].OfferCode)
	require.Equal(s.T(), "Offer 1", response.BonusTemplates[0].OfferName)
	require.Equal(s.T(), "bonus1", response.BonusTemplates[0].OwnerBonusID)
	require.Equal(s.T(), true, response.BonusTemplates[0].IsModalClosed)
	require.Equal(s.T(), "active", response.BonusTemplates[0].Status)

	require.Equal(s.T(), "testuser", response.BonusTemplates[1].UserName)
	require.Equal(s.T(), "OFFER2", response.BonusTemplates[1].OfferCode)
	require.Equal(s.T(), "Offer 2", response.BonusTemplates[1].OfferName)
	require.Equal(s.T(), "bonus2", response.BonusTemplates[1].OwnerBonusID)
	require.Equal(s.T(), false, response.BonusTemplates[1].IsModalClosed)
	require.Equal(s.T(), "inactive", response.BonusTemplates[1].Status)
}

func (s *databaseSuite) TestBonusTemplateRepository_GetUserBonusTemplates_NotFound() {
	repository := NewBonusTemplateRepository(s.gormDB)

	response, err := repository.GetUserBonusTemplates(context.Background(), "nonexistent")
	require.NoError(s.T(), err)
	require.Empty(s.T(), response.BonusTemplates)
}

func (s *databaseSuite) TestBonusTemplateRepository_UpdateBonusTemplate() {
	repository := NewBonusTemplateRepository(s.gormDB)

	original := UserBonusTemplates{
		UUIDKeyModel:  UUIDKeyModel{ID: uuid.New()},
		UserName:      "testuser",
		OfferCode:     "OFFER123",
		OfferName:     "Test Offer",
		OwnerBonusID:  "old_bonus",
		IsModalClosed: false,
		Status:        "active",
	}
	s.gormDB.Create(&original)

	updateRequest := &domain.UserBonusTemplate{
		UserName:      "testuser",
		OfferCode:     "OFFER123",
		OwnerBonusID:  "new_bonus",
		IsModalClosed: true,
		Status:        "updated",
	}

	err := repository.UpdateBonusTemplate(context.Background(), updateRequest)
	require.NoError(s.T(), err)

	var updated UserBonusTemplates
	err = s.gormDB.Where("offer_code = ? AND user_name = ?", "OFFER123", "testuser").First(&updated).Error
	require.NoError(s.T(), err)
	require.Equal(s.T(), "new_bonus", updated.OwnerBonusID)
	require.Equal(s.T(), true, updated.IsModalClosed)
	require.Equal(s.T(), "updated", updated.Status)
}

func (s *databaseSuite) TestBonusTemplateRepository_UpdateBonusTemplate_WithoutStatus() {
	repository := NewBonusTemplateRepository(s.gormDB)
	s.gormDB.Where("user_name = ? AND offer_code = ?", "testuser", "OFFER123").Delete(&UserBonusTemplates{})

	original := UserBonusTemplates{
		UUIDKeyModel:  UUIDKeyModel{ID: uuid.New()},
		UserName:      "testuser",
		OfferCode:     "OFFER123",
		OfferName:     "Test Offer",
		OwnerBonusID:  "old_bonus",
		IsModalClosed: false,
		Status:        "active",
	}
	s.gormDB.Create(&original)

	var beforeUpdate UserBonusTemplates
	err := s.gormDB.Where("offer_code = ? AND user_name = ?", "OFFER123", "testuser").First(&beforeUpdate).Error
	require.NoError(s.T(), err)
	require.Equal(s.T(), "active", beforeUpdate.Status)

	updateRequest := &domain.UserBonusTemplate{
		UserName:      "testuser",
		OfferCode:     "OFFER123",
		OwnerBonusID:  "new_bonus",
		IsModalClosed: true,
		Status:        "",
	}

	err = repository.UpdateBonusTemplate(context.Background(), updateRequest)
	require.NoError(s.T(), err)

	var updated UserBonusTemplates
	err = s.gormDB.Where("offer_code = ? AND user_name = ?", "OFFER123", "testuser").First(&updated).Error
	require.NoError(s.T(), err)
	require.Equal(s.T(), "new_bonus", updated.OwnerBonusID)
	require.Equal(s.T(), true, updated.IsModalClosed)
	require.Equal(s.T(), "active", updated.Status)
}

func (s *databaseSuite) TestBonusTemplateRepository_DeleteBonusTemplate() {
	repository := NewBonusTemplateRepository(s.gormDB)

	template := UserBonusTemplates{
		UUIDKeyModel: UUIDKeyModel{ID: uuid.New()},
		UserName:     "testuser",
		OfferCode:    "OFFER123",
		OfferName:    "Test Offer",
		Status:       "active",
	}
	s.gormDB.Create(&template)

	err := repository.DeleteBonusTemplate(context.Background(), "OFFER123", "testuser")
	require.NoError(s.T(), err)

	var count int64
	s.gormDB.Model(&UserBonusTemplates{}).Where("offer_code = ? AND user_name = ?", "OFFER123", "testuser").Count(&count)
	require.Equal(s.T(), int64(0), count)
}

func (s *databaseSuite) TestBonusTemplateRepository_DeleteBonusTemplate_NotFound() {
	repository := NewBonusTemplateRepository(s.gormDB)

	err := repository.DeleteBonusTemplate(context.Background(), "NONEXISTENT", "testuser")
	require.Error(s.T(), err)
	require.Contains(s.T(), err.Error(), "bonus template not found")
}