package postgres

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strconv"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserBonusRepository struct {
	db          *gorm.DB
	vipTiers    domain.VIPTiers
	rest_client domain.DirectusCMSClient
	userRepo    domain.UserRepository
	transRepo   domain.TransactionRepository
	bonus       domain.BonusRepository
	elantil     domain.ElantilWageringClient
	slackAlert  domain.SlackAlertClient
	bonusQueue  domain.BonusQueueService
}

func NewUserBonusRepository(db *gorm.DB, vipTiers domain.VIPTiers, rest_client domain.DirectusCMSClient, userRepo domain.UserRepository, transRepo domain.TransactionRepository, bonus domain.BonusRepository, elantil domain.ElantilWageringClient, bonusQueue domain.BonusQueueService, slackAlert domain.SlackAlertClient) *UserBonusRepository {
	repo := &UserBonusRepository{
		db:          db,
		vipTiers:    vipTiers,
		rest_client: rest_client,
		userRepo:    userRepo,
		transRepo:   transRepo,
		bonus:       bonus,
		elantil:     elantil,
		bonusQueue:  bonusQueue,
		slackAlert:  slackAlert,
	}
	return repo
}

func (r *UserBonusRepository) CreateUserBonusInDatabase(ctx context.Context, data domain.UserBonus) error {
	if r.db == nil {
		return fmt.Errorf("db is nil in CreateUserBonusInDatabase")
	}

	reloadBonusesJson, err := processReloadBonuses(data.Category, data.ReloadBonuses)
	if err != nil {
		return fmt.Errorf("failed to process reload bonuses: %w", err)
	}

	request := ToUserBonusRequest(data)
	dbBonus := ToDBUserBonus(request, data.ID, time.Now(), reloadBonusesJson)

	result := r.db.WithContext(ctx).Create(&dbBonus)
	if result.Error != nil {
		return fmt.Errorf("failed to create bonus: %w", result.Error)
	}

	return nil
}

func (r *UserBonusRepository) CreateUserBonusesBatchInDatabase(ctx context.Context, dataSlice []domain.UserBonus) error {
	if r.db == nil {
		slog.Error("db is nil in CreateUserBonusesBatchInDatabase")
		return fmt.Errorf("db is nil in CreateUserBonusesBatchInDatabase")
	}
	userBonuses := make([]userBonus, 0, len(dataSlice))
	now := time.Now()
	for _, data := range dataSlice {
		reloadBonusesJson, err := processReloadBonuses(data.Category, data.ReloadBonuses)
		if err != nil {
			slog.Error("failed to process reload bonuses", "error", err)
			return fmt.Errorf("failed to process reload bonuses: %w", err)
		}
		request := ToUserBonusRequest(data)
		dbBonus := ToDBUserBonus(request, data.ID, now, reloadBonusesJson)
		userBonuses = append(userBonuses, dbBonus)
	}
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	result := tx.CreateInBatches(userBonuses, 100)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create bonuses batch: %w", result.Error)
	}
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	slog.Info("Successfully created batch of user bonuses",
		"count", len(userBonuses))
	return nil
}

func (r *UserBonusRepository) GetUserBonusesByExternalID(ctx context.Context, externalID string) ([]domain.UserBonus, error) {
	if r.db == nil {
		slog.Error("db is nil in GetUserBonusesByExternalID")
		return nil, fmt.Errorf("db is nil in GetUserBonusesByExternalID")
	}

	var userBonuses []userBonus
	result := r.db.WithContext(ctx).Where("external_id = ? AND bonus_status = 'active' AND availed = false AND eligible = true", externalID).Find(&userBonuses)
	if result.Error != nil {
		return nil, result.Error
	}

	now := time.Now()
	bonuses := make([]domain.UserBonus, 0, len(userBonuses))
	for _, bonus := range userBonuses {
		domainBonus := domain.UserBonus{
			ID:            bonus.ID,
			BonusConfigID: bonus.BonusConfigID,
			Username:      bonus.Username,
			Category:      bonus.Category,
			Availed:       bonus.Availed,
			BonusStatus:   bonus.BonusStatus,
			UserVipStatus: bonus.UserVipStatus,
			RewardAmount:  bonus.RewardAmount,
			Reason:        bonus.Reason,
			Note:          bonus.Note,
			Type:          bonus.Type,
		}

		// Handle ReloadBonuses if present
		if bonus.Category == "reload" && bonus.ReloadBonuses != nil && len(bonus.ReloadBonuses) > 0 {
			var reloadBonuses []domain.ReloadBonus
			if err := json.Unmarshal(bonus.ReloadBonuses, &reloadBonuses); err != nil {
				slog.Error("failed to unmarshal reload bonuses",
					"error", err,
					"bonusID", bonus.ID,
					"externalID", externalID,
					"category", bonus.Category)
				return nil, fmt.Errorf("failed to unmarshal reload bonuses for bonus %s: %w", bonus.ID, err)
			}

			// Filter valid reload bonuses
			validReloadBonuses := filterValidReloadBonuses(reloadBonuses, now)

			// Only include the bonus if there are valid reload bonuses
			if len(validReloadBonuses) > 0 {
				domainBonus.ReloadBonuses = validReloadBonuses
				bonuses = append(bonuses, domainBonus)
			}
		} else {
			// For non-reload bonuses
			domainBonus.ReloadBonuses = []domain.ReloadBonus{}
			bonuses = append(bonuses, domainBonus)
		}
	}

	return bonuses, nil
}

func (r *UserBonusRepository) UpdateUserBonusStatus(ctx context.Context, externalBonusId int, externalID string, status string) error {
	if r.db == nil {
		slog.Error("db is nil in UpdateUserBonusStatus")
		return fmt.Errorf("db is nil in UpdateUserBonusStatus")
	}
	result := r.db.WithContext(ctx).Model(&userBonus{}).Where("external_id = ? AND bonus_external_id = ?", externalID, externalBonusId).Updates(map[string]interface{}{"bonus_status": status})
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (r *UserBonusRepository) DeleteExpiredBonuses(ctx context.Context) error {
	if r.db == nil {
		slog.Error("db is nil in DeleteExpiredBonuses")
		return fmt.Errorf("db is nil in DeleteExpiredBonuses")
	}

	result := r.db.WithContext(ctx).Where("expires_on < ?", time.Now()).Delete(&userBonus{})
	if result.Error != nil {
		return result.Error
	}

	err := r.rest_client.DeleteExpiredBonuses()
	if err != nil {
		slog.Error("error deleting expired bonuses in directus CMS: ", "err", err)
		return err
	}

	return nil
}

func (r *UserBonusRepository) GetClaimedBonusesOfUserByExternalID(ctx context.Context, externalID string) ([]domain.UserBonus, error) {
	if r.db == nil {
		slog.Error("db is nil in GetClaimedBonusesOfUserByExternalID")
		return nil, fmt.Errorf("db is nil in GetClaimedBonusesOfUserByExternalID")
	}

	var userBonuses []domain.UserBonus
	result := r.db.WithContext(ctx).Where("external_id IN AND bonus_status = ?", externalID, "claimed").Find(&userBonuses)
	if result.Error != nil {
		return nil, result.Error
	}

	return userBonuses, nil
}

func (r *UserBonusRepository) assignBonusFromWageringByCatagory(ctx context.Context, bonusType string, bonusConfig domain.BonusConfig, startDate, endDate string) error {
	status, periodType := getBonusStatusAndPeriod(bonusType)

	// Get wagering data for sports or casino based on category
	//category is wageringType in elantil
	var casinoWagerData map[string]domain.BatchWageringData
	var sportsWagerData map[string]domain.BatchWageringData

	var timeRange string
	var err error

	if bonusType == "instant" {
		casinoWagerData, _, err = r.transRepo.GetWageringForInstantBonuses(ctx, "casino_vt")
		if err != nil {
			return fmt.Errorf("error getting casino_vt wagering: %w", err)
		}
		sportsWagerData, timeRange, err = r.transRepo.GetWageringForInstantBonuses(ctx, "sport_vt")
		if err != nil {
			return fmt.Errorf("error getting casino_vt wagering: %w", err)
		}
	} else {
		casinoWagerData, _, err = r.elantil.GetUserIdsAndBatchWageringSummary(ctx, "casino_vt", periodType, startDate, endDate)
		if err != nil {
			return fmt.Errorf("error getting casino_vt wagering: %w", err)
		}

		sportsWagerData, timeRange, err = r.elantil.GetUserIdsAndBatchWageringSummary(ctx, "sport_vt", periodType, startDate, endDate)
		if err != nil {
			return fmt.Errorf("error getting sport_vt wagering: %w", err)
		}
	}

	// Extract unique userIDs
	userIDs := make([]string, 0)
	seenUsers := make(map[string]bool)

	for userID := range casinoWagerData {
		if !seenUsers[userID] {
			userIDs = append(userIDs, userID)
			seenUsers[userID] = true
		}
	}

	for userID := range sportsWagerData {
		if !seenUsers[userID] {
			userIDs = append(userIDs, userID)
			seenUsers[userID] = true
		}
	}

	// Process users in batches
	const batchSize = 100
	for i := 0; i < len(userIDs); i += batchSize {
		end := i + batchSize
		if end > len(userIDs) {
			end = len(userIDs)
		}

		batchIDs := userIDs[i:end]
		users, err := r.userRepo.GetUsersByExternalIDs(ctx, batchIDs)
		if err != nil {
			return fmt.Errorf("error getting users: %w", err)
		}

		err = r.processBatchWithCatagory(ctx, users, map[string]domain.BatchWageringData{}, casinoWagerData, sportsWagerData, &bonusConfig, status, periodType, startDate, endDate, timeRange)
		if err != nil {
			return fmt.Errorf("error processing batch: %w", err)
		}
	}

	return nil
}

func (r *UserBonusRepository) AssignBonusByType(ctx context.Context, bonusType, startDate, endDate string) error {
	getBonusConfig, err := r.bonus.GetBonusConfigByType(ctx, bonusType)
	if err != nil {
		slog.Error("Error retrieving bonus config by type", slog.Any("error", err))
		return fmt.Errorf("failed to get bonus config: %w", err)
	}

	if bonusType == "daily" || bonusType == "instant" {
		return r.assignBonusFromWageringByCatagory(ctx, bonusType, getBonusConfig, startDate, endDate)
	}

	status, periodType := getBonusStatusAndPeriod(bonusType)
	assignToAll := contains(getBonusConfig.UserTiers, "all")

	userChan := make(chan domain.GetAllUsersResponse, 10000)
	errorChan := make(chan error, 10000)
	const batchSize = 100
	queue := make(chan []domain.GetAllUsersResponse, 10000)

	go func() {
		defer close(userChan)
		err := r.userRepo.StreamAllUsers(ctx, userChan, !assignToAll)
		if err != nil {
			select {
			case errorChan <- fmt.Errorf("error streaming users: %w", err):
			default:
				slog.Error("Failed to send streaming error", "error", err)
			}
		}
	}()
	currentBatch := make([]domain.GetAllUsersResponse, 0, batchSize)
	for user := range userChan {
		currentBatch = append(currentBatch, user)
		if len(currentBatch) >= batchSize {
			queue <- currentBatch
			currentBatch = make([]domain.GetAllUsersResponse, 0, batchSize)
		}
	}
	if len(currentBatch) > 0 {
		queue <- currentBatch
	}
	close(queue)
	r.processQueueWithWorkers(ctx, queue, getBonusConfig, status, periodType, assignToAll, startDate, endDate, 3)
	select {
	case err := <-errorChan:
		if err != nil {
			return fmt.Errorf("streaming error: %w", err)
		}
	default:
	}

	slog.Info("Bonus assignment completed", "type", bonusType)
	return nil
}

func (r *UserBonusRepository) processQueueWithWorkers(ctx context.Context, queue <-chan []domain.GetAllUsersResponse, getBonusConfig domain.BonusConfig, status string, periodType string, assignToAll bool, startDate string, endDate string, numWorkers int) {

	totalLength := len(queue)
	queueMutex := &sync.Mutex{}
	for i := 0; i < numWorkers; i++ {
		go func(workerID int) {
			for {
				select {
				case <-ctx.Done():
					return
				default:
					queueMutex.Lock()
					batch, ok := <-queue
					queueMutex.Unlock()
					if !ok {
						return
					}
					var retryCount int
					for {
						err := r.processBatchWithCatagory(ctx, batch, map[string]domain.BatchWageringData{}, map[string]domain.BatchWageringData{}, map[string]domain.BatchWageringData{}, &getBonusConfig, status, periodType, startDate, endDate, "")
						if err != nil {
							slog.Error("Failed to process batch, retrying",
								"error", err,
								"batchSize", len(batch),
								"firstUserID", batch[0].ExternalID,
								"workerID", workerID)
							time.Sleep(time.Second * 3)
							retryCount++
						} else {
							break
						}
						if retryCount > 2 {
							break
						}
					}
				}
				if len(queue) == 0 {
					slog.Info(fmt.Sprintf("Processing of all batches for bonus assignment completed. Total batches processed: %d", totalLength))
					break
				}
			}
		}(i)
	}
}

func (r *UserBonusRepository) processBatchWithCatagory(ctx context.Context, users []domain.GetAllUsersResponse, wagerData map[string]domain.BatchWageringData, casinoData map[string]domain.BatchWageringData, sportsData map[string]domain.BatchWageringData, bonusConfig *domain.BonusConfig, status, periodType string, startDate, endDate, timeRange string) error {
	if len(users) == 0 {
		return nil
	}

	slog.Info("Processing user batch",
		"batchSize", len(users),
		"category", bonusConfig.Category)
	externalIDs := make([]string, 0, len(users))
	userMap := make(map[string]domain.GetAllUsersResponse)
	for _, user := range users {
		externalIDs = append(externalIDs, user.ExternalID)
		userMap[user.ExternalID] = user
	}

	if len(externalIDs) == 0 {
		return nil
	}
	slog.Info("Ids to process", "ids", externalIDs)

	if bonusConfig.Category == "weekly" || bonusConfig.Category == "monthly" {
		if len(wagerData) == 0 {
			var err error
			wagerData, timeRange, err = r.elantil.GetBatchWageringSummary(ctx, externalIDs, nil, periodType, startDate, endDate)
			if err != nil {
				return fmt.Errorf("error getting wagering: %w", err)
			}
		}
		if len(casinoData) == 0 {
			var err error
			casinoVt := "casino_vt"
			casinoData, timeRange, err = r.elantil.GetBatchWageringSummary(ctx, externalIDs, &casinoVt, periodType, startDate, endDate)
			if err != nil {
				return fmt.Errorf("error getting casino wagering: %w", err)
			}
		}
		if len(sportsData) == 0 {
			var err error
			sportsVt := "sport_vt"
			sportsData, timeRange, err = r.elantil.GetBatchWageringSummary(ctx, externalIDs, &sportsVt, periodType, startDate, endDate)
			if err != nil {
				return fmt.Errorf("error getting sports wagering: %w", err)
			}
		}
	}

	type bonusTask struct {
		user         domain.GetAllUsersResponse
		rewardAmount float64
		expiryDate   time.Time
		wageringData domain.BonusWageringData
	}

	bonusTasks := make([]bonusTask, 0, len(externalIDs))
	for _, externalID := range externalIDs {

		user := userMap[externalID]
		slog.Info("Bonus details", "category", bonusConfig.Category)
		existingBonus, err := r.checkExistingBonus(ctx, externalID, bonusConfig.Category, time.Now(), "casino_sport")
		if err != nil {
			slog.Error("Error checking existing bonus",
				"error", err,
				"externalID", externalID)
			continue
		}
		if existingBonus {
			slog.Info("Recent bonus exists, skipping",
				"externalID", externalID,
				"username", user.UserName)
			continue
		}
		casinoBetSum := "0"
		casinoLossSum := "0"
		casinoWinSum := "0"
		sportsBetSum := "0"
		sportsLossSum := "0"
		sportsWinSum := "0"

		TotalNGRSum := "0"
		TotalLossSum := "0"
		var RtpRate float64

		if data, exists := casinoData[externalID]; exists {
			casinoBetSum = data.BetSum
			casinoLossSum = data.LossSum
			casinoWinSum = data.WinSum
		}

		slog.Info("Calculating casino bonus with data",
			"externalID", externalID,
			"BetSum", casinoBetSum,
			"LossSum", casinoLossSum,
			"WinSum", casinoWinSum,
			"RtpRate", RtpRate,
		)

		casinoRewardAmount, err := r.bonus.CalculateSpecificBonusRewardAmount(ctx,
			casinoBetSum,
			"0.0",
			"0.0",
			RtpRate,
			"casino_vt",
			bonusConfig.ExternalID)
		if err != nil {
			slog.Error("Error calculating reward amount",
				"error", err,
				"externalID", externalID)
			continue
		}

		slog.Info("Calculated casino reward amount",
			"rewardAmount", casinoRewardAmount,
			"userID", externalID,
			"username", user.UserName,
		)

		if data, exists := sportsData[externalID]; exists {
			sportsBetSum = data.BetSum
			sportsLossSum = data.LossSum
			sportsWinSum = data.WinSum
		}
		slog.Info("Calculating sports bonus with data",
			"externalID", externalID,
			"BetSum", sportsBetSum,
			"LossSum", sportsLossSum,
			"WinSum", sportsWinSum,
		)

		sportsRewardAmount, err := r.bonus.CalculateSpecificBonusRewardAmount(ctx,
			sportsBetSum,
			"0.0",
			"0.0",
			RtpRate,
			"sport_vt",
			bonusConfig.ExternalID)
		if err != nil {
			slog.Error("Error calculating reward amount",
				"error", err,
				"externalID", externalID)
			continue
		}
		slog.Info("Calculated sports reward amount",
			"rewardAmount", sportsRewardAmount,
			"userID", externalID,
			"username", user.UserName,
		)

		var lossbackRewardAmount float64
		if bonusConfig.Category != "instant" && bonusConfig.Category != "daily" {
			if data, exists := wagerData[externalID]; exists {
				TotalNGRSum = data.NGRSum
				TotalLossSum = data.LossSum
			}

			slog.Info("Calculating lossback reward amount with data",
				"externalID", externalID,
				"TotalLossSum", TotalLossSum,
				"TotalNGRSum", TotalNGRSum,
			)

			lossbackRewardAmount, err = r.bonus.CalculateSpecificBonusRewardAmount(ctx,
				"",
				TotalLossSum,
				TotalNGRSum,
				RtpRate,
				"casino_vt",
				bonusConfig.ExternalID)
			if err != nil {
				slog.Error("Error calculating lossback reward amount",
					"error", err,
					"externalID", externalID)
				continue
			}
			slog.Info("Calculated lossback reward amount",
				"rewardAmount", lossbackRewardAmount,
				"userID", externalID,
				"username", user.UserName,
			)

		}

		totalRewardAmount := casinoRewardAmount + sportsRewardAmount + lossbackRewardAmount

		if totalRewardAmount < 0.05 {
			slog.Info("Zero reward amount, skipping",
				"externalID", externalID,
				"username", user.UserName)
			continue
		}

		slog.Info("Adding user to bonus tasks",
			"externalID", externalID,
			"username", user.UserName,
			"rewardAmount", totalRewardAmount)

		bonusTasks = append(bonusTasks, bonusTask{
			user:         user,
			rewardAmount: totalRewardAmount,
			expiryDate:   time.Now().AddDate(0, 0, bonusConfig.DaysToExpiry),
			wageringData: domain.BonusWageringData{
				CasinoBetSum:         casinoBetSum,
				CasinoLossSum:        casinoLossSum,
				CasinoWinSum:         casinoWinSum,
				CasinoRewardAmount:   fmt.Sprintf("%.2f", casinoRewardAmount),
				SportBetSum:          sportsBetSum,
				SportLossSum:         sportsLossSum,
				SportWinSum:          sportsWinSum,
				SportsRewardAmount:   fmt.Sprintf("%.2f", sportsRewardAmount),
				TotalLossSum:         TotalLossSum,
				TotalNgrSum:          TotalNGRSum,
				LossbackRewardAmount: fmt.Sprintf("%.2f", lossbackRewardAmount),
			},
		})

	}

	if len(bonusTasks) == 0 {
		return nil
	}

	// Prepare batch arrays for both Directus and Database
	directusBonuses := make([]domain.UserBonusInDirectus, 0, len(bonusTasks))
	databaseBonuses := make([]domain.UserBonus, 0, len(bonusTasks))

	// Prepare all the bonus objects
	for _, task := range bonusTasks {
		var expiryDate time.Time
		if bonusConfig.Category == "dailyRakeback" {
			expiryDate = time.Time{}
		} else {
			expiryDate = task.expiryDate
		}

		casinoBetSum, _ := strconv.ParseFloat(task.wageringData.CasinoBetSum, 64)
		casinoLossSum, _ := strconv.ParseFloat(task.wageringData.CasinoLossSum, 64)
		sportsBetSum, _ := strconv.ParseFloat(task.wageringData.SportBetSum, 64)
		sportsLossSum, _ := strconv.ParseFloat(task.wageringData.SportLossSum, 64)
		totalLossbackSum, _ := strconv.ParseFloat(task.wageringData.TotalLossSum, 64)
		totalNGRSum, _ := strconv.ParseFloat(task.wageringData.TotalNgrSum, 64)

		directusBonus := domain.UserBonusInDirectus{
			ExternalID:           task.user.ExternalID,
			Username:             task.user.UserName,
			ExpiresOn:            expiryDate,
			BonusStatus:          status,
			Category:             bonusConfig.Category,
			RewardAmount:         task.rewardAmount,
			Reason:               fmt.Sprintf("Automated %s bonus. Casino Reward Amount: %s, Sports Reward Amount: %s, Lossback Reward Amount: %s", bonusConfig.Category, task.wageringData.CasinoRewardAmount, task.wageringData.SportsRewardAmount, task.wageringData.LossbackRewardAmount),
			Note:                 fmt.Sprintf("automated %s bonus", bonusConfig.Category),
			Type:                 "casino_sport",
			RtpRate:              "",
			TimeRange:            timeRange,
			CasinoWageringAmount: fmt.Sprintf("%.2f", casinoBetSum),
			CasinoLossAmount:     fmt.Sprintf("%.2f", casinoLossSum),
			SportsWageringAmount: fmt.Sprintf("%.2f", sportsBetSum),
			SportsLossAmount:     fmt.Sprintf("%.2f", sportsLossSum),
			TotalLossbackSum:     fmt.Sprintf("%.2f", totalLossbackSum),
			TotalNGRSum:          fmt.Sprintf("%.2f", totalNGRSum),
		}

		directusBonuses = append(directusBonuses, directusBonus)
	}

	// // Batch insert into Directus
	slog.Info("Starting batch creation in Directus", "count", len(directusBonuses))
	bonusIDs, err := r.rest_client.CreateUserBonusesBatch(directusBonuses)
	if err != nil {
		return err
	}

	// Prepare database bonuses with the returned IDs
	for i, task := range bonusTasks {
		databaseBonus := domain.UserBonus{
			ExternalID:      task.user.ExternalID,
			BonusConfigID:   bonusConfig.ExternalID,
			BonusExternalID: bonusIDs[i], // Use the ID returned from Directus
			Username:        task.user.UserName,
			Category:        bonusConfig.Category,
			Eligible:        true,
			Availed:         false,
			BonusStatus:     status,
			UserVipStatus:   task.user.ElantilVIpStatus,
			RewardAmount:    task.rewardAmount,
			ExpiresOn:       task.expiryDate,
			Reason:          fmt.Sprintf("Automated %s bonus. Casino Reward Amount: %s, Sports Reward Amount: %s, Lossback Reward Amount: %s", bonusConfig.Category, task.wageringData.CasinoRewardAmount, task.wageringData.SportsRewardAmount, task.wageringData.LossbackRewardAmount),
			Note:            "automated bonus for the type: casino_sport",
			Type:            "casino_sport",
			TimeRange:       timeRange,
		}
		databaseBonuses = append(databaseBonuses, databaseBonus)
	}

	// Batch insert into database
	slog.Info("Starting batch creation in database", "count", len(databaseBonuses))
	if err := r.CreateUserBonusesBatchInDatabase(ctx, databaseBonuses); err != nil {
		return err
	}

	if err := r.slackAlert.SendSlackNotification(databaseBonuses); err != nil {
		slog.Error("Error sending Slack notification for bonus creation",
			"error", err,
			"count", len(databaseBonuses))
	}

	slog.Info("Batch bonus creation completed successfully", "totalBonuses", len(bonusTasks))

	return nil
}

func (r *UserBonusRepository) checkExistingBonus(ctx context.Context, externalID, category string, date time.Time, wageringType string) (bool, error) {
	if category == "reload" {
		return false, nil
	}

	var count int64
	timeWindow := date.Add(-5 * time.Minute)
	result := r.db.WithContext(ctx).Model(&userBonus{}).
		Select("id").
		Where("external_id = ? AND type = ? AND category = ? AND created_at >= ? AND category != 'reload'",
			externalID, wageringType, category, timeWindow).
		Count(&count)

	if result.Error != nil {
		slog.Error("Error checking existing bonus",
			"error", result.Error,
			"externalID", externalID,
			"category", category)
		return false, fmt.Errorf("error checking existing bonus: %w", result.Error)
	}

	return count > 0, nil
}

func (r *UserBonusRepository) ActivateUserBonusesByExternalIds(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int) error {
	if r.db == nil {
		slog.Error("db is nil in ActivateUserBonusesByExternalIds")
		return fmt.Errorf("db is nil in ActivateUserBonusesByExternalIds")
	}
	result := r.db.WithContext(ctx).
		Model(&userBonus{}).
		Where("external_id = ? AND category = ? AND availed = ? AND bonus_external_id = ?", userExternalIDs, category, false, bonusExternalId).
		Updates(map[string]interface{}{"bonus_status": status, "reward_amount": rewardAmount})

	if result.Error != nil {
		slog.Error("error activating user bonuses in database: ", "err", result.Error)
		return result.Error
	}

	return nil
}

func (r *UserBonusRepository) AssignSpecialBonus(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string) error {
	slog.Info("Assigning special bonus to user: ", "userId", userId, "username", username, "category", category, "rewardAmount", rewardAmount)
	if r.db == nil {
		slog.Error("db is nil in SpecialBonusAssign")
		return fmt.Errorf("db is nil in SpecialBonusAssign")
	}

	// get userby ExternalID
	user, err := r.userRepo.GetUserByExternalID(ctx, userId)
	if err != nil {
		slog.Error("error getting user by external id: ", "err", err)
		return err
	}

	slog.Info("User found: ", "user", user)

	userVipStatus := user.ElantilVIpStatus

	expiryDate := time.Now().AddDate(0, 0, 30)

	userBonus := userBonus{
		ExternalID:      userId,
		BonusConfigID:   6,
		BonusExternalID: bonusExternalId,
		Username:        username,
		Category:        "special",
		ExpiresOn:       expiryDate,
		Eligible:        true,
		Availed:         false,
		BonusStatus:     "active",
		UserVipStatus:   userVipStatus,
		RewardAmount:    rewardAmount,
		ClaimAttempt:    false,
		ClaimSuccess:    false,
		Reason:          reason,
		Note:            note,
	}

	result := r.db.Create(&userBonus)
	if result.Error != nil {
		slog.Error("error creating special bonus in database: ", "err", result.Error)
		return result.Error
	}

	slog.Info("Special bonus created successfully in database")
	return nil
}

func (r *UserBonusRepository) CreateReloadBonuses(ctx context.Context, requestData []domain.ReloadBonusRequest) error {
	userBonuses := make([]domain.UserBonus, 0, len(requestData))
	for _, req := range requestData {
		bonus, err := ToDomain(req)
		if err != nil {
			slog.Error("failed to convert request to domain model",
				"error", err,
				"userId", req.UserID)
			return fmt.Errorf("failed to convert request to domain model: %w", err)
		}
		userBonuses = append(userBonuses, bonus)
	}

	// Begin transaction
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create bonuses using existing CreateUserBonusInDatabase
	for _, bonus := range userBonuses {
		if err := r.CreateUserBonusInDatabase(ctx, bonus); err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create user bonus: %w", err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// func (r *UserBonusRepository) AssignDailyRakebackBonus(ctx context.Context) error {
// 	getBonusConfig, err := r.bonus.GetBonusConfigByType(ctx, "dailyRakeback")
// 	if err != nil {
// 		slog.Error("Error retrieving dailyRakeback bonus config", slog.Any("error", err))
// 		return fmt.Errorf("failed to get dailyRakeback bonus config: %w", err)
// 	}

// 	assignToAll := contains(getBonusConfig.UserTiers, "all")

// 	userChan := make(chan domain.GetAllUsersResponse, 100)
// 	errorChan := make(chan error, 1)
// 	const batchSize = 50

// 	go func() {
// 		defer close(userChan)
// 		err := r.userRepo.StreamAllUsers(ctx, userChan, !assignToAll)
// 		if err != nil {
// 			select {
// 			case errorChan <- fmt.Errorf("error streaming users: %w", err):
// 			default:
// 				slog.Error("Failed to send streaming error", "error", err)
// 			}
// 		}
// 	}()

// 	var processWg sync.WaitGroup
// 	processErrors := make(chan error, 10)
// 	const numWorkers = 5

// 	batchChan := make(chan []domain.GetAllUsersResponse, numWorkers)

// 	go func() {
// 		defer close(batchChan)
// 		currentBatch := make([]domain.GetAllUsersResponse, 0, batchSize)

// 		for user := range userChan {
// 			currentBatch = append(currentBatch, user)
// 			if len(currentBatch) >= batchSize {
// 				batchChan <- currentBatch
// 				currentBatch = make([]domain.GetAllUsersResponse, 0, batchSize)
// 			}
// 		}

// 		if len(currentBatch) > 0 {
// 			batchChan <- currentBatch
// 		}
// 	}()

// 	for i := 0; i < numWorkers; i++ {
// 		processWg.Add(1)
// 		go func() {
// 			defer processWg.Done()
// 			for batch := range batchChan {
// 				// Reusing existing processBatch with hardcoded values for dailyRakeback
// 				if err := r.processBatch(ctx, batch, &getBonusConfig, "active", "day", assignToAll, "", ""); err != nil {
// 					select {
// 					case processErrors <- err:
// 					default:
// 						slog.Error("Failed to send process error", "error", err)
// 					}
// 				}
// 			}
// 		}()
// 	}

// 	processWg.Wait()
// 	close(processErrors)

// 	select {
// 	case err := <-errorChan:
// 		if err != nil {
// 			return fmt.Errorf("streaming error: %w", err)
// 		}
// 	default:
// 	}

// 	var errs []error
// 	for err := range processErrors {
// 		errs = append(errs, err)
// 	}

// 	if len(errs) > 0 {
// 		return fmt.Errorf("encountered %d errors while assigning rakeback bonuses", len(errs))
// 	}

// 	slog.Info("Daily rakeback bonus assignment completed")
// 	return nil
// }

func (r *UserBonusRepository) GetLevelUpBonusOfUser(userId string) ([]domain.UserBonus, error) {
	if r.db == nil {
		slog.Error("db is nil in GetLevelUpBonusOfUser")
		return nil, fmt.Errorf("db is nil in GetLevelUpBonusOfUser")
	}

	var userBonus []userBonus
	result := r.db.Where("external_id = ? AND category IN (? , ?)  AND bonus_status = 'active'", userId, "level-up", "campaign-special").Find(&userBonus)

	if result.Error != nil {
		slog.Error("error getting level-up bonus of user: ", "err", result.Error)
		return nil, result.Error
	}

	var domainUserBonuses []domain.UserBonus
	for _, bonus := range userBonus {
		if !bonus.ModalClosed {
			domainUserBonuses = append(domainUserBonuses, domain.UserBonus{
				ID:            bonus.ID,
				BonusConfigID: bonus.BonusConfigID,
				Username:      bonus.Username,
				Category:      bonus.Category,
				Availed:       bonus.Availed,
				BonusStatus:   bonus.BonusStatus,
				UserVipStatus: bonus.UserVipStatus,
				RewardAmount:  bonus.RewardAmount,
				Reason:        bonus.Reason,
				Note:          bonus.Note,
				ModalClosed:   bonus.ModalClosed,
			})
		}
	}

	if len(domainUserBonuses) == 0 {
		return nil, nil
	}

	return domainUserBonuses, nil
}

func (r *UserBonusRepository) UpdateModalPopupClosed(ctx context.Context, userId string) error {
	if r.db == nil {
		slog.Error("db is nil in UpdateModalPopupClosed")
		return fmt.Errorf("db is nil in UpdateModalPopupClosed")
	}

	result := r.db.WithContext(ctx).Model(&userBonus{}).
		Where("external_id = ? AND category IN ('level-up', 'campaign-special')", userId).
		Updates(map[string]interface{}{"modal_closed": true})

	if result.Error != nil {
		slog.Error("error updating modal popup closed in database: ", "err", result.Error)
		return result.Error
	}

	return nil
}

func (r *UserBonusRepository) ClaimAllBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	slog.Info("Start of claiming all bonuses and updating wallet initiated for the user", "externalId", externalId)
	if r.db == nil {
		slog.Error("db is nil in ClaimAllBonusAndUpdateWallet")
		return fmt.Errorf("db is nil in ClaimAllBonusAndUpdateWallet")
	}

	var userBonuses []userBonus
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		query := tx.Where("external_id = ? AND bonus_status = 'active' AND availed = ? AND claim_attempt = ?",
			externalId, false, false)

		result := query.Find(&userBonuses)
		if result.Error != nil {
			slog.Error("error getting active bonuses of user: ", "err", result.Error)
			return result.Error
		}

		if len(userBonuses) == 0 {
			slog.Info("No claimable bonuses found", "externalID", externalId)
			return domain.ErrNoBonusToClaim
		}

		return nil
	})

	if err != nil {
		if err == domain.ErrNoBonusToClaim {
			slog.Info("No claimable bonuses found", "externalID", externalId)
		}
		return err
	}

	for _, bonus := range userBonuses {
		domainBonus := toDomain(bonus)
		r.bonusQueue.AddBonusToQueue(ctx, token, domainBonus)
	}
	slog.Info("End of claiming all bonuses and updating wallet completed for the user", "externalId", externalId)
	return nil
}

func (r *UserBonusRepository) ClaimTiltBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	slog.Info("Start of claiming tilt bonus and updating wallet initiated for the user", "externalId", externalId)
	if r.db == nil {
		slog.Error("db is nil in ClaimAllBonusAndUpdateWallet")
		return fmt.Errorf("db is nil in ClaimAllBonusAndUpdateWallet")
	}

	var userBonuses []userBonus
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		query := tx.Where("external_id = ?", externalId).
			Where("bonus_status = ?", "active").
			Where("availed = ?", false).
			Where("claim_attempt = ?", false).
			Where("category IN ?", []string{"level-up", "special", "campaign-special"})

		result := query.Find(&userBonuses)
		if result.Error != nil {
			return result.Error
		}

		if len(userBonuses) == 0 {
			slog.Info("No claimable tilt bonuses found", "externalId", externalId)
			return domain.ErrNoBonusToClaim
		}

		return nil
	})

	if err != nil {
		if err == domain.ErrNoBonusToClaim {
			slog.Info("No claimable bonuses found", "externalId", externalId)
		}
		return err
	}

	for _, bonus := range userBonuses {
		domainBonus := toDomain(bonus)
		r.bonusQueue.AddBonusToQueue(ctx, token, domainBonus)
	}

	slog.Info("End of claiming tilt bonus and updating wallet completed for the user", "externalId", externalId)
	return nil
}

func (r *UserBonusRepository) SingleClaimUpdate(ctx context.Context, token string, externalID string, bonusConfigID int) error {
	slog.Info("Start of claiming single bonus and updating wallet initiated for the user", "externalID", externalID, "bonusConfigID", bonusConfigID)
	if r.db == nil {
		slog.Error("db is nil in ClaimBonusAndUpdateStatus")
		return fmt.Errorf("db is nil in ClaimBonusAndUpdateStatus")
	}

	var userBonuses []userBonus
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		query := tx.Where("external_id = ?", externalID).
			Where("bonus_status = ?", "active").
			Where("availed = ?", false).
			Where("claim_attempt = ?", false).
			Where("bonus_config_id = ?", bonusConfigID)

		if bonusConfigID != 5 {
			query = query.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate})
		}

		result := query.Find(&userBonuses)
		if result.Error != nil {
			return result.Error
		}

		if len(userBonuses) == 0 {
			return domain.ErrNoBonusToClaim
		}

		return nil
	})

	if err != nil {
		if err == domain.ErrNoBonusToClaim {
			slog.Info("No claimable bonuses found", "externalID", externalID, "bonusConfigID", bonusConfigID)
		}
		return err
	}

	for _, bonus := range userBonuses {
		slog.Info("Adding bonus to queue", "externalID", externalID, "bonusConfigID", bonusConfigID)
		domainBonus := toDomain(bonus)
		r.bonusQueue.AddBonusToQueue(ctx, token, domainBonus)
	}

	slog.Info("End of claiming single bonus and updating wallet completed for the user", "externalID", externalID, "bonusConfigID", bonusConfigID)
	return nil
}

func (r *UserBonusRepository) CreateCampaignBonusForUser(ctx context.Context, externalID string, rewardAmount float64, reason string, note string) error {
	if r.db == nil {
		return fmt.Errorf("db is nil in CreateWeeklyBonusForUser")
	}

	// get user b
	user, err := r.userRepo.GetUserByExternalID(ctx, externalID)
	if err != nil {
		slog.Error("error getting user by external id: ", "err", err)
		return err
	}

	data := domain.UserBonusInDirectus{
		ExternalID:   externalID,
		Username:     user.UserName,
		ExpiresOn:    time.Now().AddDate(0, 0, 7),
		BonusStatus:  "active",
		Category:     "campaign-special",
		RewardAmount: rewardAmount,
		Reason:       reason,
		Note:         note,
	}

	id, err := r.rest_client.CreateUserBonus(data)
	if err != nil {
		slog.Error("error creating weekly bonus in CMS: ", "err", err)
		return err
	}

	userBonus := userBonus{
		ExternalID:      externalID,
		BonusConfigID:   8,
		BonusExternalID: id,
		Username:        user.UserName,
		Category:        "campaign-special",
		ExpiresOn:       time.Now().AddDate(0, 0, 7),
		Eligible:        true,
		Availed:         false,
		BonusStatus:     "active",
		UserVipStatus:   user.ElantilVIpStatus,
		RewardAmount:    rewardAmount,
		ClaimAttempt:    false,
		ClaimSuccess:    false,
	}

	result := r.db.Create(&userBonus)
	if result.Error != nil {
		slog.Error("error creating weekly bonus in database: ", "err", result.Error)
		return result.Error
	}

	slog.Info("Weekly bonus created successfully in database")
	return nil
}
