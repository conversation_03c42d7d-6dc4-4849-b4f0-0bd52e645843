package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.BoostRepository = (*BoostRepository)(nil)

// BoostRepository is a struct that will implement the BoostRepository interface.
type BoostRepository struct {
	db *gorm.DB
}

// NewBoostRepository creates a new instance of BoostRepository.
func NewBoostRepository(db *gorm.DB) *BoostRepository {
	return &BoostRepository{db: db}
}

// CreateBoosts creates a list of boosts.
func (r *BoostRepository) CreateBoosts(ctx context.Context, u []domain.Boost) error {
	if len(u) == 0 {
		return nil
	}
	rows := boostsToRows(u)

	tx := r.db.WithContext(ctx).Create(&rows)
	if err := tx.Error; err != nil {
		return fmt.Errorf("create boosts: %w", err)
	}

	return nil
}

func (r *BoostRepository) UpdateBoosts(ctx context.Context, b domain.Boost) error {
	row := boostToRow(b)
	tx := r.db.
		WithContext(ctx).
		Model(&boost{}).
		Where("id = ?", row.ID).
		Select("BoostStartedAt").
		Updates(row)

	if err := tx.Error; err != nil {
		return fmt.Errorf("update boost by ID '%v': %w", row.ID, err)
	}
	return nil
}

func (r *BoostRepository) GetAvailableBoostByUserID(
	ctx context.Context,
	userID string,
	targetTime time.Time,
	includeStarted bool,
) (*domain.Boost, error) {
	var row boost
	tx := r.db.
		WithContext(ctx).
		Preload("User").
		Joins("LEFT JOIN users ON boosts.user_id = users.id").
		Where("users.external_id = ?", userID)

	if !includeStarted {
		tx.Where("boost_started_at IS NULL")
	}

	tx.Where("? BETWEEN bonus_starts_at AND bonus_finishes_at", targetTime).First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceGone
		}

		return nil, fmt.Errorf("find avaible boost for user external ID '%v': %w", userID, err)
	}

	return rowToBoost(row), nil
}

func (r *BoostRepository) GetOverlapingBoostByUserID(
	ctx context.Context,
	userID string,
	startTime, finishTime time.Time,
) (*domain.Boost, error) {
	var row boost
	tx := r.db.
		WithContext(ctx).
		Preload("User").
		Joins("LEFT JOIN users ON boosts.user_id = users.id").
		Where("users.external_id = ?", userID).
		Where("boost_started_at IS NULL").
		Where("? BETWEEN bonus_starts_at AND bonus_finishes_at OR ? BETWEEN bonus_starts_at AND bonus_finishes_at", startTime, finishTime).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("finding overlaping boost for user external ID '%v': %w", userID, err)
	}

	return rowToBoost(row), nil
}

func (r *BoostRepository) GetActiveBoostByUserID(
	ctx context.Context,
	userID string,
	targetTime time.Time,
) (*domain.Boost, error) {
	var row boost
	tx := r.db.
		WithContext(ctx).
		Preload("User").
		Joins("LEFT JOIN users ON boosts.user_id = users.id").
		Where("users.external_id = ?", userID).
		Where("boost_started_at IS NOT NULL").
		Where("? BETWEEN boost_started_at AND boost_started_at + (boost_duration_hours || ' hours')::INTERVAL", targetTime).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find active boost for user external ID '%v': %w", userID, err)
	}

	return rowToBoost(row), nil
}
