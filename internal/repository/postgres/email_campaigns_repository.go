package postgres

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type EmailCampaignsRepository struct {
	db *gorm.DB
}

func NewEmailCampaignsRepository(db *gorm.DB) *EmailCampaignsRepository {
	return &EmailCampaignsRepository{db: db}
}

func (s *EmailCampaignsRepository) CreateEmailCampaigns(ctx context.Context, contentTemplate *domain.GetUserInformation) error {

	if contentTemplate == nil || len(*contentTemplate) == 0 {
		return fmt.Errorf("no email campaigns to insert")
	}

	batchSize := 100
	for i := 0; i < len(*contentTemplate); i += batchSize {
		end := i + batchSize
		if end > len(*contentTemplate) {
			end = len(*contentTemplate)
		}

		var batchModels []EmailCampaigns
		for _, item := range (*contentTemplate)[i:end] {
			ScheduledTime, err := time.Parse("2006-01-02 15:04:05", item.ScheduledTime)
			if err != nil {
				slog.Error("failed to parse scheduled time", slog.Any("error", err))
				return fmt.Errorf("failed to parse scheduled time: %w", err)
			}
			batchModels = append(batchModels, EmailCampaigns{
				UUIDKeyModel: UUIDKeyModel{
					ID: uuid.New(),
				},
				CustomerID:    item.CustomerID,
				TemplateID:    int(item.TemplateID),
				ScheduledTime: ScheduledTime,
				Email:         item.CustomerAttributes[0], 
			})
		}

		if err := s.db.Create(&batchModels).Error; err != nil {
			slog.Error("failed to create email campaigns batch", slog.Any("error", err))
			return fmt.Errorf("failed to create email campaigns batch: %w", err)
		}
	}

	return nil
}

func (s *EmailCampaignsRepository) GetEmailCampaigns(ctx context.Context, email string) (*domain.EmailCampaignsResponse, error) {
	var model EmailCampaigns
	if err := s.db.Where("email = ?", email).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("record not found for given email: %s", email)
		}
		return nil, fmt.Errorf("failed to fetch record for given email: %w", err)
	}

	response := &domain.EmailCampaignsResponse{
		CustomerID:    model.CustomerID,
		TemplateID:    model.TemplateID,
		ScheduledTime: model.ScheduledTime,
		Email:         model.Email,
	}

	return response, nil
}
