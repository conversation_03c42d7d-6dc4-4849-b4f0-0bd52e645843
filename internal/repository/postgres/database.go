package postgres

import (
	"gorm.io/gorm/clause"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

func getOrderBy(order domain.OrderParams) clause.OrderByColumn {
	return clause.OrderByColumn{
		Column: clause.Column{Name: order.OrderBy},
		Desc:   order.Order == domain.OrderDirDesc,
	}
}

func calculateOffset(params domain.PagingParams) int {
	return (params.PageNumber - 1) * params.PageSize
}
