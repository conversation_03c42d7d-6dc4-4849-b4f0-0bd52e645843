package postgres

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"reflect"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"gorm.io/gorm"
)

type SettingsRepository struct {
	db *gorm.DB
}

func NewSettingsRepository(db *gorm.DB) *SettingsRepository {
	return &SettingsRepository{db: db}
}

func (s *SettingsRepository) CreateSettings(ctx context.Context, userID string, newSettings domain.UserSettingsPayload) (domain.SettingsResponse, error) {
	var existingSettings Settings
	result := s.db.Where("user_id = ?", userID).First(&existingSettings)

	if result.Error == nil {
		var currentMap map[string]interface{}
		if err := json.Unmarshal(existingSettings.Settings, &currentMap); err != nil {
			return domain.SettingsResponse{}, err
		}

		// Convert new settings to map
		var newMap map[string]interface{}
		newJSON, err := json.Marshal(newSettings)
		if err != nil {
			return domain.SettingsResponse{}, err
		}
		if err := json.Unmarshal(newJSON, &newMap); err != nil {
			return domain.SettingsResponse{}, err
		}

		for key, value := range newMap {
			if value != nil {
				if currentVal, exists := currentMap[key]; !exists || currentVal != value {
					currentMap[key] = value
				}
			}
		}

		mergedJSON, err := json.Marshal(currentMap)
		if err != nil {
			return domain.SettingsResponse{}, err
		}

		if err := s.db.WithContext(ctx).Model(&existingSettings).
			Update("settings", mergedJSON).Error; err != nil {
			slog.Error("Error updating settings record", "error", err)
			return domain.SettingsResponse{}, fmt.Errorf("failed to update settings record: %w", err)
		}

		var updatedSettings domain.UserSettings
		if err := json.Unmarshal(mergedJSON, &updatedSettings); err != nil {
			return domain.SettingsResponse{}, fmt.Errorf("error unmarshalling updated settings: %w", err)
		}

		return domain.SettingsResponse{
			Settings: updatedSettings,
		}, nil

	} else if result.Error == gorm.ErrRecordNotFound {
		settingsJSON, err := json.Marshal(newSettings)
		if err != nil {
			return domain.SettingsResponse{}, err
		}

		newSettingsRecord := Settings{
			UserID:   userID,
			Settings: settingsJSON,
		}

		if err := s.db.Create(&newSettingsRecord).Error; err != nil {
			return domain.SettingsResponse{}, fmt.Errorf("failed to create settings record: %w", err)
		}

		var createdSettings domain.UserSettings
		if err := json.Unmarshal(newSettingsRecord.Settings, &createdSettings); err != nil {
			return domain.SettingsResponse{}, fmt.Errorf("error unmarshalling created settings: %w", err)
		}

		return domain.SettingsResponse{
			Settings: createdSettings,
		}, nil
	}

	return domain.SettingsResponse{}, result.Error
}

func (s *SettingsRepository) GetSettings(userID string) (domain.SettingsResponse, error) {
	var settings Settings
	result := s.db.Where("user_id = ?", userID).First(&settings)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {

			newSettings := domain.UserSettingsPayload{
				FirstLogin:              nil,
				PreferredCryptoCurrency: "USD",
				PreferredFiatCurrency:   "USD",
				ShowInFiat:              new(bool),
				HideZeroBalances:        new(bool),
				WalletModal:             func() *bool { b := true; return &b }(),
			}

			resp, err := s.CreateSettings(context.Background(), userID, newSettings)
			if err != nil {
				return domain.SettingsResponse{}, fmt.Errorf("failed to create settings record: %w", err)
			}

			return resp, nil
		}
		return domain.SettingsResponse{}, result.Error
	}

	var userSettings *domain.UserSettings
	if err := json.Unmarshal(settings.Settings, &userSettings); err != nil {
		return domain.SettingsResponse{}, fmt.Errorf("error unmarshalling settings: %w", err)
	}

	return domain.SettingsResponse{
		Settings: *userSettings,
	}, nil
}

func (s *SettingsRepository) UpdateSettings(userID string, settings domain.UserSettings) error {
	var existingSettings Settings
	result := s.db.Where("user_id = ?", userID).First(&existingSettings)

	if result.Error != nil {
		return result.Error
	}

	var currentSettings []domain.UserSettings
	if err := json.Unmarshal(existingSettings.Settings, &currentSettings); err != nil {
		return err
	}

	for i, setting := range currentSettings {
		if *setting.FirstLogin {
			currentSettings[i] = settings
			break
		}
	}

	settingsJSON, err := json.Marshal(currentSettings)
	if err != nil {
		return err
	}

	existingSettings.Settings = settingsJSON
	return s.db.Save(&existingSettings).Error
}

func (s *SettingsRepository) DeleteSettings(userID string, key string) error {
	var existingSettings Settings
	result := s.db.Where("user_id = ?", userID).First(&existingSettings)

	if result.Error != nil {
		return result.Error
	}

	var currentSettings []domain.UserSettings
	if err := json.Unmarshal(existingSettings.Settings, &currentSettings); err != nil {
		return err
	}

	foundIndex := -1
	for i, setting := range currentSettings {
		t := reflect.TypeOf(setting)
		for j := 0; j < t.NumField(); j++ {
			field := t.Field(j)
			jsonTag := field.Tag.Get("json")
			if jsonTag == key {
				foundIndex = i
				break
			}
		}
		if foundIndex >= 0 {
			break
		}
	}

	if foundIndex >= 0 {
		currentSettings = append(currentSettings[:foundIndex], currentSettings[foundIndex+1:]...)
	} else {
		return fmt.Errorf("setting with key '%s' not found", key)
	}

	settingsJSON, err := json.Marshal(currentSettings)
	if err != nil {
		return err
	}

	existingSettings.Settings = settingsJSON
	return s.db.Save(&existingSettings).Error
}
