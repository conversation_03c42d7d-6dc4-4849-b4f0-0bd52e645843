// Code generated by mockery v2.53.3. DO NOT EDIT.

package websocketmock

import mock "github.com/stretchr/testify/mock"

// WebSocketConn is an autogenerated mock type for the WebSocketConn type
type WebSocketConn struct {
	mock.Mock
}

type WebSocketConn_Expecter struct {
	mock *mock.Mock
}

func (_m *WebSocketConn) EXPECT() *WebSocketConn_Expecter {
	return &WebSocketConn_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *WebSocketConn) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// WebSocketConn_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type WebSocketConn_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *WebSocketConn_Expecter) Close() *WebSocketConn_Close_Call {
	return &WebSocketConn_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *WebSocketConn_Close_Call) Run(run func()) *WebSocketConn_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *WebSocketConn_Close_Call) Return(_a0 error) *WebSocketConn_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *WebSocketConn_Close_Call) RunAndReturn(run func() error) *WebSocketConn_Close_Call {
	_c.Call.Return(run)
	return _c
}

// ReadMessage provides a mock function with no fields
func (_m *WebSocketConn) ReadMessage() (int, []byte, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ReadMessage")
	}

	var r0 int
	var r1 []byte
	var r2 error
	if rf, ok := ret.Get(0).(func() (int, []byte, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() []byte); ok {
		r1 = rf()
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]byte)
		}
	}

	if rf, ok := ret.Get(2).(func() error); ok {
		r2 = rf()
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// WebSocketConn_ReadMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReadMessage'
type WebSocketConn_ReadMessage_Call struct {
	*mock.Call
}

// ReadMessage is a helper method to define mock.On call
func (_e *WebSocketConn_Expecter) ReadMessage() *WebSocketConn_ReadMessage_Call {
	return &WebSocketConn_ReadMessage_Call{Call: _e.mock.On("ReadMessage")}
}

func (_c *WebSocketConn_ReadMessage_Call) Run(run func()) *WebSocketConn_ReadMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *WebSocketConn_ReadMessage_Call) Return(messageType int, p []byte, err error) *WebSocketConn_ReadMessage_Call {
	_c.Call.Return(messageType, p, err)
	return _c
}

func (_c *WebSocketConn_ReadMessage_Call) RunAndReturn(run func() (int, []byte, error)) *WebSocketConn_ReadMessage_Call {
	_c.Call.Return(run)
	return _c
}

// WriteMessage provides a mock function with given fields: messageType, data
func (_m *WebSocketConn) WriteMessage(messageType int, data []byte) error {
	ret := _m.Called(messageType, data)

	if len(ret) == 0 {
		panic("no return value specified for WriteMessage")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, []byte) error); ok {
		r0 = rf(messageType, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// WebSocketConn_WriteMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteMessage'
type WebSocketConn_WriteMessage_Call struct {
	*mock.Call
}

// WriteMessage is a helper method to define mock.On call
//   - messageType int
//   - data []byte
func (_e *WebSocketConn_Expecter) WriteMessage(messageType interface{}, data interface{}) *WebSocketConn_WriteMessage_Call {
	return &WebSocketConn_WriteMessage_Call{Call: _e.mock.On("WriteMessage", messageType, data)}
}

func (_c *WebSocketConn_WriteMessage_Call) Run(run func(messageType int, data []byte)) *WebSocketConn_WriteMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].([]byte))
	})
	return _c
}

func (_c *WebSocketConn_WriteMessage_Call) Return(_a0 error) *WebSocketConn_WriteMessage_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *WebSocketConn_WriteMessage_Call) RunAndReturn(run func(int, []byte) error) *WebSocketConn_WriteMessage_Call {
	_c.Call.Return(run)
	return _c
}

// NewWebSocketConn creates a new instance of WebSocketConn. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewWebSocketConn(t interface {
	mock.TestingT
	Cleanup(func())
}) *WebSocketConn {
	mock := &WebSocketConn{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
