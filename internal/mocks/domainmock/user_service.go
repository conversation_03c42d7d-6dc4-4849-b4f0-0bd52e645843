// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// UserService is an autogenerated mock type for the UserService type
type UserService struct {
	mock.Mock
}

type UserService_Expecter struct {
	mock *mock.Mock
}

func (_m *UserService) EXPECT() *UserService_Expecter {
	return &UserService_Expecter{mock: &_m.Mock}
}

// GetRankedUserByExternalID provides a mock function with given fields: ictx, d
func (_m *UserService) GetRankedUserByExternalID(ictx context.Context, d string) (*domain.RankedUser, error) {
	ret := _m.Called(ictx, d)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUserByExternalID")
	}

	var r0 *domain.RankedUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.RankedUser, error)); ok {
		return rf(ictx, d)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.RankedUser); ok {
		r0 = rf(ictx, d)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RankedUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ictx, d)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetRankedUserByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUserByExternalID'
type UserService_GetRankedUserByExternalID_Call struct {
	*mock.Call
}

// GetRankedUserByExternalID is a helper method to define mock.On call
//   - ictx context.Context
//   - d string
func (_e *UserService_Expecter) GetRankedUserByExternalID(ictx interface{}, d interface{}) *UserService_GetRankedUserByExternalID_Call {
	return &UserService_GetRankedUserByExternalID_Call{Call: _e.mock.On("GetRankedUserByExternalID", ictx, d)}
}

func (_c *UserService_GetRankedUserByExternalID_Call) Run(run func(ictx context.Context, d string)) *UserService_GetRankedUserByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserService_GetRankedUserByExternalID_Call) Return(_a0 *domain.RankedUser, _a1 error) *UserService_GetRankedUserByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetRankedUserByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.RankedUser, error)) *UserService_GetRankedUserByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRankedUsers provides a mock function with given fields: ctx, params
func (_m *UserService) GetRankedUsers(ctx context.Context, params *domain.GetUserParams) (*domain.RankedUsers, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUsers")
	}

	var r0 *domain.RankedUsers
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetUserParams) (*domain.RankedUsers, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetUserParams) *domain.RankedUsers); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RankedUsers)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetUserParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetRankedUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUsers'
type UserService_GetRankedUsers_Call struct {
	*mock.Call
}

// GetRankedUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetUserParams
func (_e *UserService_Expecter) GetRankedUsers(ctx interface{}, params interface{}) *UserService_GetRankedUsers_Call {
	return &UserService_GetRankedUsers_Call{Call: _e.mock.On("GetRankedUsers", ctx, params)}
}

func (_c *UserService_GetRankedUsers_Call) Run(run func(ctx context.Context, params *domain.GetUserParams)) *UserService_GetRankedUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetUserParams))
	})
	return _c
}

func (_c *UserService_GetRankedUsers_Call) Return(_a0 *domain.RankedUsers, _a1 error) *UserService_GetRankedUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetRankedUsers_Call) RunAndReturn(run func(context.Context, *domain.GetUserParams) (*domain.RankedUsers, error)) *UserService_GetRankedUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetRankedUsersByExternalID provides a mock function with given fields: ctx, id, retrieveAmount, orderBy
func (_m *UserService) GetRankedUsersByExternalID(ctx context.Context, id string, retrieveAmount int, orderBy string) ([]domain.RankedUser, error) {
	ret := _m.Called(ctx, id, retrieveAmount, orderBy)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUsersByExternalID")
	}

	var r0 []domain.RankedUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) ([]domain.RankedUser, error)); ok {
		return rf(ctx, id, retrieveAmount, orderBy)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) []domain.RankedUser); ok {
		r0 = rf(ctx, id, retrieveAmount, orderBy)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.RankedUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string) error); ok {
		r1 = rf(ctx, id, retrieveAmount, orderBy)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetRankedUsersByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUsersByExternalID'
type UserService_GetRankedUsersByExternalID_Call struct {
	*mock.Call
}

// GetRankedUsersByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
//   - retrieveAmount int
//   - orderBy string
func (_e *UserService_Expecter) GetRankedUsersByExternalID(ctx interface{}, id interface{}, retrieveAmount interface{}, orderBy interface{}) *UserService_GetRankedUsersByExternalID_Call {
	return &UserService_GetRankedUsersByExternalID_Call{Call: _e.mock.On("GetRankedUsersByExternalID", ctx, id, retrieveAmount, orderBy)}
}

func (_c *UserService_GetRankedUsersByExternalID_Call) Run(run func(ctx context.Context, id string, retrieveAmount int, orderBy string)) *UserService_GetRankedUsersByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int), args[3].(string))
	})
	return _c
}

func (_c *UserService_GetRankedUsersByExternalID_Call) Return(_a0 []domain.RankedUser, _a1 error) *UserService_GetRankedUsersByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetRankedUsersByExternalID_Call) RunAndReturn(run func(context.Context, string, int, string) ([]domain.RankedUser, error)) *UserService_GetRankedUsersByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRegisteredEmail provides a mock function with given fields: ctx, email, emailMarketing
func (_m *UserService) GetRegisteredEmail(ctx context.Context, email string, emailMarketing bool) (*domain.RegisteredEmail, error) {
	ret := _m.Called(ctx, email, emailMarketing)

	if len(ret) == 0 {
		panic("no return value specified for GetRegisteredEmail")
	}

	var r0 *domain.RegisteredEmail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) (*domain.RegisteredEmail, error)); ok {
		return rf(ctx, email, emailMarketing)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) *domain.RegisteredEmail); ok {
		r0 = rf(ctx, email, emailMarketing)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RegisteredEmail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool) error); ok {
		r1 = rf(ctx, email, emailMarketing)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetRegisteredEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRegisteredEmail'
type UserService_GetRegisteredEmail_Call struct {
	*mock.Call
}

// GetRegisteredEmail is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
//   - emailMarketing bool
func (_e *UserService_Expecter) GetRegisteredEmail(ctx interface{}, email interface{}, emailMarketing interface{}) *UserService_GetRegisteredEmail_Call {
	return &UserService_GetRegisteredEmail_Call{Call: _e.mock.On("GetRegisteredEmail", ctx, email, emailMarketing)}
}

func (_c *UserService_GetRegisteredEmail_Call) Run(run func(ctx context.Context, email string, emailMarketing bool)) *UserService_GetRegisteredEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserService_GetRegisteredEmail_Call) Return(_a0 *domain.RegisteredEmail, _a1 error) *UserService_GetRegisteredEmail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetRegisteredEmail_Call) RunAndReturn(run func(context.Context, string, bool) (*domain.RegisteredEmail, error)) *UserService_GetRegisteredEmail_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserAndGameByExternalId provides a mock function with given fields: ctx, userId, gameId
func (_m *UserService) GetUserAndGameByExternalId(ctx context.Context, userId string, gameId string) (domain.UserGame, error) {
	ret := _m.Called(ctx, userId, gameId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserAndGameByExternalId")
	}

	var r0 domain.UserGame
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.UserGame, error)); ok {
		return rf(ctx, userId, gameId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.UserGame); ok {
		r0 = rf(ctx, userId, gameId)
	} else {
		r0 = ret.Get(0).(domain.UserGame)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userId, gameId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetUserAndGameByExternalId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserAndGameByExternalId'
type UserService_GetUserAndGameByExternalId_Call struct {
	*mock.Call
}

// GetUserAndGameByExternalId is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - gameId string
func (_e *UserService_Expecter) GetUserAndGameByExternalId(ctx interface{}, userId interface{}, gameId interface{}) *UserService_GetUserAndGameByExternalId_Call {
	return &UserService_GetUserAndGameByExternalId_Call{Call: _e.mock.On("GetUserAndGameByExternalId", ctx, userId, gameId)}
}

func (_c *UserService_GetUserAndGameByExternalId_Call) Run(run func(ctx context.Context, userId string, gameId string)) *UserService_GetUserAndGameByExternalId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserService_GetUserAndGameByExternalId_Call) Return(_a0 domain.UserGame, _a1 error) *UserService_GetUserAndGameByExternalId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetUserAndGameByExternalId_Call) RunAndReturn(run func(context.Context, string, string) (domain.UserGame, error)) *UserService_GetUserAndGameByExternalId_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByExternalID provides a mock function with given fields: ctx, id
func (_m *UserService) GetUserByExternalID(ctx context.Context, id string) (*domain.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByExternalID")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetUserByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByExternalID'
type UserService_GetUserByExternalID_Call struct {
	*mock.Call
}

// GetUserByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *UserService_Expecter) GetUserByExternalID(ctx interface{}, id interface{}) *UserService_GetUserByExternalID_Call {
	return &UserService_GetUserByExternalID_Call{Call: _e.mock.On("GetUserByExternalID", ctx, id)}
}

func (_c *UserService_GetUserByExternalID_Call) Run(run func(ctx context.Context, id string)) *UserService_GetUserByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserService_GetUserByExternalID_Call) Return(_a0 *domain.User, _a1 error) *UserService_GetUserByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetUserByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.User, error)) *UserService_GetUserByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByID provides a mock function with given fields: ctx, id
func (_m *UserService) GetUserByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByID")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByID'
type UserService_GetUserByID_Call struct {
	*mock.Call
}

// GetUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *UserService_Expecter) GetUserByID(ctx interface{}, id interface{}) *UserService_GetUserByID_Call {
	return &UserService_GetUserByID_Call{Call: _e.mock.On("GetUserByID", ctx, id)}
}

func (_c *UserService_GetUserByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *UserService_GetUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *UserService_GetUserByID_Call) Return(_a0 *domain.User, _a1 error) *UserService_GetUserByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetUserByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.User, error)) *UserService_GetUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByUserName provides a mock function with given fields: ctx, userName
func (_m *UserService) GetUserByUserName(ctx context.Context, userName string) (*domain.User, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByUserName")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.User, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.User); ok {
		r0 = rf(ctx, userName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetUserByUserName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByUserName'
type UserService_GetUserByUserName_Call struct {
	*mock.Call
}

// GetUserByUserName is a helper method to define mock.On call
//   - ctx context.Context
//   - userName string
func (_e *UserService_Expecter) GetUserByUserName(ctx interface{}, userName interface{}) *UserService_GetUserByUserName_Call {
	return &UserService_GetUserByUserName_Call{Call: _e.mock.On("GetUserByUserName", ctx, userName)}
}

func (_c *UserService_GetUserByUserName_Call) Run(run func(ctx context.Context, userName string)) *UserService_GetUserByUserName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserService_GetUserByUserName_Call) Return(_a0 *domain.User, _a1 error) *UserService_GetUserByUserName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetUserByUserName_Call) RunAndReturn(run func(context.Context, string) (*domain.User, error)) *UserService_GetUserByUserName_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersXPAndUpdateInElantil provides a mock function with given fields: ctx
func (_m *UserService) GetUsersXPAndUpdateInElantil(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersXPAndUpdateInElantil")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserService_GetUsersXPAndUpdateInElantil_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersXPAndUpdateInElantil'
type UserService_GetUsersXPAndUpdateInElantil_Call struct {
	*mock.Call
}

// GetUsersXPAndUpdateInElantil is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserService_Expecter) GetUsersXPAndUpdateInElantil(ctx interface{}) *UserService_GetUsersXPAndUpdateInElantil_Call {
	return &UserService_GetUsersXPAndUpdateInElantil_Call{Call: _e.mock.On("GetUsersXPAndUpdateInElantil", ctx)}
}

func (_c *UserService_GetUsersXPAndUpdateInElantil_Call) Run(run func(ctx context.Context)) *UserService_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserService_GetUsersXPAndUpdateInElantil_Call) Return(_a0 error) *UserService_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserService_GetUsersXPAndUpdateInElantil_Call) RunAndReturn(run func(context.Context) error) *UserService_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Return(run)
	return _c
}

// GetWinnerOfTheMonth provides a mock function with given fields: ctx
func (_m *UserService) GetWinnerOfTheMonth(ctx context.Context) ([]domain.WinnerDetails, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetWinnerOfTheMonth")
	}

	var r0 []domain.WinnerDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]domain.WinnerDetails, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []domain.WinnerDetails); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.WinnerDetails)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_GetWinnerOfTheMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWinnerOfTheMonth'
type UserService_GetWinnerOfTheMonth_Call struct {
	*mock.Call
}

// GetWinnerOfTheMonth is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserService_Expecter) GetWinnerOfTheMonth(ctx interface{}) *UserService_GetWinnerOfTheMonth_Call {
	return &UserService_GetWinnerOfTheMonth_Call{Call: _e.mock.On("GetWinnerOfTheMonth", ctx)}
}

func (_c *UserService_GetWinnerOfTheMonth_Call) Run(run func(ctx context.Context)) *UserService_GetWinnerOfTheMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserService_GetWinnerOfTheMonth_Call) Return(_a0 []domain.WinnerDetails, _a1 error) *UserService_GetWinnerOfTheMonth_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_GetWinnerOfTheMonth_Call) RunAndReturn(run func(context.Context) ([]domain.WinnerDetails, error)) *UserService_GetWinnerOfTheMonth_Call {
	_c.Call.Return(run)
	return _c
}

// IsUserNameUnique provides a mock function with given fields: ctx, userName
func (_m *UserService) IsUserNameUnique(ctx context.Context, userName string) (bool, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for IsUserNameUnique")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, userName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_IsUserNameUnique_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUserNameUnique'
type UserService_IsUserNameUnique_Call struct {
	*mock.Call
}

// IsUserNameUnique is a helper method to define mock.On call
//   - ctx context.Context
//   - userName string
func (_e *UserService_Expecter) IsUserNameUnique(ctx interface{}, userName interface{}) *UserService_IsUserNameUnique_Call {
	return &UserService_IsUserNameUnique_Call{Call: _e.mock.On("IsUserNameUnique", ctx, userName)}
}

func (_c *UserService_IsUserNameUnique_Call) Run(run func(ctx context.Context, userName string)) *UserService_IsUserNameUnique_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserService_IsUserNameUnique_Call) Return(_a0 bool, _a1 error) *UserService_IsUserNameUnique_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_IsUserNameUnique_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *UserService_IsUserNameUnique_Call {
	_c.Call.Return(run)
	return _c
}

// SaveRegisteredEmail provides a mock function with given fields: ctx, email, emailMarketing
func (_m *UserService) SaveRegisteredEmail(ctx context.Context, email string, emailMarketing bool) error {
	ret := _m.Called(ctx, email, emailMarketing)

	if len(ret) == 0 {
		panic("no return value specified for SaveRegisteredEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) error); ok {
		r0 = rf(ctx, email, emailMarketing)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserService_SaveRegisteredEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveRegisteredEmail'
type UserService_SaveRegisteredEmail_Call struct {
	*mock.Call
}

// SaveRegisteredEmail is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
//   - emailMarketing bool
func (_e *UserService_Expecter) SaveRegisteredEmail(ctx interface{}, email interface{}, emailMarketing interface{}) *UserService_SaveRegisteredEmail_Call {
	return &UserService_SaveRegisteredEmail_Call{Call: _e.mock.On("SaveRegisteredEmail", ctx, email, emailMarketing)}
}

func (_c *UserService_SaveRegisteredEmail_Call) Run(run func(ctx context.Context, email string, emailMarketing bool)) *UserService_SaveRegisteredEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserService_SaveRegisteredEmail_Call) Return(_a0 error) *UserService_SaveRegisteredEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserService_SaveRegisteredEmail_Call) RunAndReturn(run func(context.Context, string, bool) error) *UserService_SaveRegisteredEmail_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function with given fields: ctx, user
func (_m *UserService) UpdateUser(ctx context.Context, user *domain.User) error {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.User) error); ok {
		r0 = rf(ctx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserService_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type UserService_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - ctx context.Context
//   - user *domain.User
func (_e *UserService_Expecter) UpdateUser(ctx interface{}, user interface{}) *UserService_UpdateUser_Call {
	return &UserService_UpdateUser_Call{Call: _e.mock.On("UpdateUser", ctx, user)}
}

func (_c *UserService_UpdateUser_Call) Run(run func(ctx context.Context, user *domain.User)) *UserService_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserService_UpdateUser_Call) Return(_a0 error) *UserService_UpdateUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserService_UpdateUser_Call) RunAndReturn(run func(context.Context, *domain.User) error) *UserService_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserMultiCurrency provides a mock function with given fields: ctx, userID, multiCurrency
func (_m *UserService) UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error {
	ret := _m.Called(ctx, userID, multiCurrency)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserMultiCurrency")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) error); ok {
		r0 = rf(ctx, userID, multiCurrency)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserService_UpdateUserMultiCurrency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserMultiCurrency'
type UserService_UpdateUserMultiCurrency_Call struct {
	*mock.Call
}

// UpdateUserMultiCurrency is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - multiCurrency bool
func (_e *UserService_Expecter) UpdateUserMultiCurrency(ctx interface{}, userID interface{}, multiCurrency interface{}) *UserService_UpdateUserMultiCurrency_Call {
	return &UserService_UpdateUserMultiCurrency_Call{Call: _e.mock.On("UpdateUserMultiCurrency", ctx, userID, multiCurrency)}
}

func (_c *UserService_UpdateUserMultiCurrency_Call) Run(run func(ctx context.Context, userID string, multiCurrency bool)) *UserService_UpdateUserMultiCurrency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserService_UpdateUserMultiCurrency_Call) Return(_a0 error) *UserService_UpdateUserMultiCurrency_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserService_UpdateUserMultiCurrency_Call) RunAndReturn(run func(context.Context, string, bool) error) *UserService_UpdateUserMultiCurrency_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserPreferences provides a mock function with given fields: ctx, userExternalID, ghostMode, hideStats
func (_m *UserService) UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode *bool, hideStats *bool) error {
	ret := _m.Called(ctx, userExternalID, ghostMode, hideStats)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserPreferences")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *bool, *bool) error); ok {
		r0 = rf(ctx, userExternalID, ghostMode, hideStats)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserService_UpdateUserPreferences_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserPreferences'
type UserService_UpdateUserPreferences_Call struct {
	*mock.Call
}

// UpdateUserPreferences is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - ghostMode *bool
//   - hideStats *bool
func (_e *UserService_Expecter) UpdateUserPreferences(ctx interface{}, userExternalID interface{}, ghostMode interface{}, hideStats interface{}) *UserService_UpdateUserPreferences_Call {
	return &UserService_UpdateUserPreferences_Call{Call: _e.mock.On("UpdateUserPreferences", ctx, userExternalID, ghostMode, hideStats)}
}

func (_c *UserService_UpdateUserPreferences_Call) Run(run func(ctx context.Context, userExternalID string, ghostMode *bool, hideStats *bool)) *UserService_UpdateUserPreferences_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*bool), args[3].(*bool))
	})
	return _c
}

func (_c *UserService_UpdateUserPreferences_Call) Return(_a0 error) *UserService_UpdateUserPreferences_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserService_UpdateUserPreferences_Call) RunAndReturn(run func(context.Context, string, *bool, *bool) error) *UserService_UpdateUserPreferences_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserXP provides a mock function with given fields: ctx, userID, xpToAdd
func (_m *UserService) UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	ret := _m.Called(ctx, userID, xpToAdd)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserXP")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) (float64, error)); ok {
		return rf(ctx, userID, xpToAdd)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) float64); ok {
		r0 = rf(ctx, userID, xpToAdd)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, float64) error); ok {
		r1 = rf(ctx, userID, xpToAdd)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserService_UpdateUserXP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserXP'
type UserService_UpdateUserXP_Call struct {
	*mock.Call
}

// UpdateUserXP is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - xpToAdd float64
func (_e *UserService_Expecter) UpdateUserXP(ctx interface{}, userID interface{}, xpToAdd interface{}) *UserService_UpdateUserXP_Call {
	return &UserService_UpdateUserXP_Call{Call: _e.mock.On("UpdateUserXP", ctx, userID, xpToAdd)}
}

func (_c *UserService_UpdateUserXP_Call) Run(run func(ctx context.Context, userID string, xpToAdd float64)) *UserService_UpdateUserXP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(float64))
	})
	return _c
}

func (_c *UserService_UpdateUserXP_Call) Return(_a0 float64, _a1 error) *UserService_UpdateUserXP_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserService_UpdateUserXP_Call) RunAndReturn(run func(context.Context, string, float64) (float64, error)) *UserService_UpdateUserXP_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserService creates a new instance of UserService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserService(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserService {
	mock := &UserService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
