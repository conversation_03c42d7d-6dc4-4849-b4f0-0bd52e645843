// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// BonusRepository is an autogenerated mock type for the BonusRepository type
type BonusRepository struct {
	mock.Mock
}

type BonusRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *BonusRepository) EXPECT() *BonusRepository_Expecter {
	return &BonusRepository_Expecter{mock: &_m.Mock}
}

// CalculateSpecificBonusRewardAmount provides a mock function with given fields: ctx, wager, loss, ngr, RtpRate, catagory, bonusId
func (_m *BonusRepository) CalculateSpecificBonusRewardAmount(ctx context.Context, wager string, loss string, ngr string, RtpRate float64, catagory string, bonusId int) (float64, error) {
	ret := _m.Called(ctx, wager, loss, ngr, RtpRate, catagory, bonusId)

	if len(ret) == 0 {
		panic("no return value specified for CalculateSpecificBonusRewardAmount")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, float64, string, int) (float64, error)); ok {
		return rf(ctx, wager, loss, ngr, RtpRate, catagory, bonusId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, float64, string, int) float64); ok {
		r0 = rf(ctx, wager, loss, ngr, RtpRate, catagory, bonusId)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, float64, string, int) error); ok {
		r1 = rf(ctx, wager, loss, ngr, RtpRate, catagory, bonusId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BonusRepository_CalculateSpecificBonusRewardAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateSpecificBonusRewardAmount'
type BonusRepository_CalculateSpecificBonusRewardAmount_Call struct {
	*mock.Call
}

// CalculateSpecificBonusRewardAmount is a helper method to define mock.On call
//   - ctx context.Context
//   - wager string
//   - loss string
//   - ngr string
//   - RtpRate float64
//   - catagory string
//   - bonusId int
func (_e *BonusRepository_Expecter) CalculateSpecificBonusRewardAmount(ctx interface{}, wager interface{}, loss interface{}, ngr interface{}, RtpRate interface{}, catagory interface{}, bonusId interface{}) *BonusRepository_CalculateSpecificBonusRewardAmount_Call {
	return &BonusRepository_CalculateSpecificBonusRewardAmount_Call{Call: _e.mock.On("CalculateSpecificBonusRewardAmount", ctx, wager, loss, ngr, RtpRate, catagory, bonusId)}
}

func (_c *BonusRepository_CalculateSpecificBonusRewardAmount_Call) Run(run func(ctx context.Context, wager string, loss string, ngr string, RtpRate float64, catagory string, bonusId int)) *BonusRepository_CalculateSpecificBonusRewardAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(float64), args[5].(string), args[6].(int))
	})
	return _c
}

func (_c *BonusRepository_CalculateSpecificBonusRewardAmount_Call) Return(_a0 float64, _a1 error) *BonusRepository_CalculateSpecificBonusRewardAmount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BonusRepository_CalculateSpecificBonusRewardAmount_Call) RunAndReturn(run func(context.Context, string, string, string, float64, string, int) (float64, error)) *BonusRepository_CalculateSpecificBonusRewardAmount_Call {
	_c.Call.Return(run)
	return _c
}

// CreateBonusConfig provides a mock function with given fields: ctx
func (_m *BonusRepository) CreateBonusConfig(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CreateBonusConfig")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BonusRepository_CreateBonusConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBonusConfig'
type BonusRepository_CreateBonusConfig_Call struct {
	*mock.Call
}

// CreateBonusConfig is a helper method to define mock.On call
//   - ctx context.Context
func (_e *BonusRepository_Expecter) CreateBonusConfig(ctx interface{}) *BonusRepository_CreateBonusConfig_Call {
	return &BonusRepository_CreateBonusConfig_Call{Call: _e.mock.On("CreateBonusConfig", ctx)}
}

func (_c *BonusRepository_CreateBonusConfig_Call) Run(run func(ctx context.Context)) *BonusRepository_CreateBonusConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *BonusRepository_CreateBonusConfig_Call) Return(_a0 error) *BonusRepository_CreateBonusConfig_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BonusRepository_CreateBonusConfig_Call) RunAndReturn(run func(context.Context) error) *BonusRepository_CreateBonusConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetBonusConfigByType provides a mock function with given fields: ctx, bonusType
func (_m *BonusRepository) GetBonusConfigByType(ctx context.Context, bonusType string) (domain.BonusConfig, error) {
	ret := _m.Called(ctx, bonusType)

	if len(ret) == 0 {
		panic("no return value specified for GetBonusConfigByType")
	}

	var r0 domain.BonusConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (domain.BonusConfig, error)); ok {
		return rf(ctx, bonusType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) domain.BonusConfig); ok {
		r0 = rf(ctx, bonusType)
	} else {
		r0 = ret.Get(0).(domain.BonusConfig)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, bonusType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BonusRepository_GetBonusConfigByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBonusConfigByType'
type BonusRepository_GetBonusConfigByType_Call struct {
	*mock.Call
}

// GetBonusConfigByType is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusType string
func (_e *BonusRepository_Expecter) GetBonusConfigByType(ctx interface{}, bonusType interface{}) *BonusRepository_GetBonusConfigByType_Call {
	return &BonusRepository_GetBonusConfigByType_Call{Call: _e.mock.On("GetBonusConfigByType", ctx, bonusType)}
}

func (_c *BonusRepository_GetBonusConfigByType_Call) Run(run func(ctx context.Context, bonusType string)) *BonusRepository_GetBonusConfigByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BonusRepository_GetBonusConfigByType_Call) Return(_a0 domain.BonusConfig, _a1 error) *BonusRepository_GetBonusConfigByType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BonusRepository_GetBonusConfigByType_Call) RunAndReturn(run func(context.Context, string) (domain.BonusConfig, error)) *BonusRepository_GetBonusConfigByType_Call {
	_c.Call.Return(run)
	return _c
}

// NewBonusRepository creates a new instance of BonusRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBonusRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *BonusRepository {
	mock := &BonusRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
