// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// BetService is an autogenerated mock type for the BetService type
type BetService struct {
	mock.Mock
}

type BetService_Expecter struct {
	mock *mock.Mock
}

func (_m *BetService) EXPECT() *BetService_Expecter {
	return &BetService_Expecter{mock: &_m.Mock}
}

// ExportBets provides a mock function with given fields: ctx, params
func (_m *BetService) ExportBets(ctx context.Context, params *domain.ExportBetsParams) ([]domain.Bet, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ExportBets")
	}

	var r0 []domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportBetsParams) ([]domain.Bet, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportBetsParams) []domain.Bet); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.ExportBetsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_ExportBets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportBets'
type BetService_ExportBets_Call struct {
	*mock.Call
}

// ExportBets is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.ExportBetsParams
func (_e *BetService_Expecter) ExportBets(ctx interface{}, params interface{}) *BetService_ExportBets_Call {
	return &BetService_ExportBets_Call{Call: _e.mock.On("ExportBets", ctx, params)}
}

func (_c *BetService_ExportBets_Call) Run(run func(ctx context.Context, params *domain.ExportBetsParams)) *BetService_ExportBets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.ExportBetsParams))
	})
	return _c
}

func (_c *BetService_ExportBets_Call) Return(_a0 []domain.Bet, _a1 error) *BetService_ExportBets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_ExportBets_Call) RunAndReturn(run func(context.Context, *domain.ExportBetsParams) ([]domain.Bet, error)) *BetService_ExportBets_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetByExternalID provides a mock function with given fields: ctx, externalID
func (_m *BetService) GetBetByExternalID(ctx context.Context, externalID string) (*domain.Bet, error) {
	ret := _m.Called(ctx, externalID)

	if len(ret) == 0 {
		panic("no return value specified for GetBetByExternalID")
	}

	var r0 *domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.Bet, error)); ok {
		return rf(ctx, externalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.Bet); ok {
		r0 = rf(ctx, externalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_GetBetByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetByExternalID'
type BetService_GetBetByExternalID_Call struct {
	*mock.Call
}

// GetBetByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
func (_e *BetService_Expecter) GetBetByExternalID(ctx interface{}, externalID interface{}) *BetService_GetBetByExternalID_Call {
	return &BetService_GetBetByExternalID_Call{Call: _e.mock.On("GetBetByExternalID", ctx, externalID)}
}

func (_c *BetService_GetBetByExternalID_Call) Run(run func(ctx context.Context, externalID string)) *BetService_GetBetByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BetService_GetBetByExternalID_Call) Return(_a0 *domain.Bet, _a1 error) *BetService_GetBetByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_GetBetByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.Bet, error)) *BetService_GetBetByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetByID provides a mock function with given fields: ctx, id
func (_m *BetService) GetBetByID(ctx context.Context, id uuid.UUID) (*domain.Bet, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetBetByID")
	}

	var r0 *domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.Bet, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.Bet); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_GetBetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetByID'
type BetService_GetBetByID_Call struct {
	*mock.Call
}

// GetBetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *BetService_Expecter) GetBetByID(ctx interface{}, id interface{}) *BetService_GetBetByID_Call {
	return &BetService_GetBetByID_Call{Call: _e.mock.On("GetBetByID", ctx, id)}
}

func (_c *BetService_GetBetByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *BetService_GetBetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *BetService_GetBetByID_Call) Return(_a0 *domain.Bet, _a1 error) *BetService_GetBetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_GetBetByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.Bet, error)) *BetService_GetBetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetBets provides a mock function with given fields: ctx, params
func (_m *BetService) GetBets(ctx context.Context, params *domain.GetBetsParams) (*domain.Bets, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetBets")
	}

	var r0 *domain.Bets
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsParams) (*domain.Bets, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsParams) *domain.Bets); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bets)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetBetsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_GetBets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBets'
type BetService_GetBets_Call struct {
	*mock.Call
}

// GetBets is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetBetsParams
func (_e *BetService_Expecter) GetBets(ctx interface{}, params interface{}) *BetService_GetBets_Call {
	return &BetService_GetBets_Call{Call: _e.mock.On("GetBets", ctx, params)}
}

func (_c *BetService_GetBets_Call) Run(run func(ctx context.Context, params *domain.GetBetsParams)) *BetService_GetBets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetBetsParams))
	})
	return _c
}

func (_c *BetService_GetBets_Call) Return(_a0 *domain.Bets, _a1 error) *BetService_GetBets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_GetBets_Call) RunAndReturn(run func(context.Context, *domain.GetBetsParams) (*domain.Bets, error)) *BetService_GetBets_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetsByUserID provides a mock function with given fields: ctx, params
func (_m *BetService) GetBetsByUserID(ctx context.Context, params *domain.GetBetsByUserIDParams) (*domain.UserBets, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetBetsByUserID")
	}

	var r0 *domain.UserBets
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsByUserIDParams) (*domain.UserBets, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsByUserIDParams) *domain.UserBets); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserBets)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetBetsByUserIDParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_GetBetsByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetsByUserID'
type BetService_GetBetsByUserID_Call struct {
	*mock.Call
}

// GetBetsByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetBetsByUserIDParams
func (_e *BetService_Expecter) GetBetsByUserID(ctx interface{}, params interface{}) *BetService_GetBetsByUserID_Call {
	return &BetService_GetBetsByUserID_Call{Call: _e.mock.On("GetBetsByUserID", ctx, params)}
}

func (_c *BetService_GetBetsByUserID_Call) Run(run func(ctx context.Context, params *domain.GetBetsByUserIDParams)) *BetService_GetBetsByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetBetsByUserIDParams))
	})
	return _c
}

func (_c *BetService_GetBetsByUserID_Call) Return(_a0 *domain.UserBets, _a1 error) *BetService_GetBetsByUserID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_GetBetsByUserID_Call) RunAndReturn(run func(context.Context, *domain.GetBetsByUserIDParams) (*domain.UserBets, error)) *BetService_GetBetsByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestBetsByUserId provides a mock function with given fields: ctx, externalID
func (_m *BetService) GetLatestBetsByUserId(ctx context.Context, externalID string) ([]domain.Bet, error) {
	ret := _m.Called(ctx, externalID)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestBetsByUserId")
	}

	var r0 []domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]domain.Bet, error)); ok {
		return rf(ctx, externalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []domain.Bet); ok {
		r0 = rf(ctx, externalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetService_GetLatestBetsByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestBetsByUserId'
type BetService_GetLatestBetsByUserId_Call struct {
	*mock.Call
}

// GetLatestBetsByUserId is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
func (_e *BetService_Expecter) GetLatestBetsByUserId(ctx interface{}, externalID interface{}) *BetService_GetLatestBetsByUserId_Call {
	return &BetService_GetLatestBetsByUserId_Call{Call: _e.mock.On("GetLatestBetsByUserId", ctx, externalID)}
}

func (_c *BetService_GetLatestBetsByUserId_Call) Run(run func(ctx context.Context, externalID string)) *BetService_GetLatestBetsByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BetService_GetLatestBetsByUserId_Call) Return(_a0 []domain.Bet, _a1 error) *BetService_GetLatestBetsByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetService_GetLatestBetsByUserId_Call) RunAndReturn(run func(context.Context, string) ([]domain.Bet, error)) *BetService_GetLatestBetsByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// ShareUserBetByBetId provides a mock function with given fields: ctx, externalID, betID
func (_m *BetService) ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (domain.Bet, bool, error) {
	ret := _m.Called(ctx, externalID, betID)

	if len(ret) == 0 {
		panic("no return value specified for ShareUserBetByBetId")
	}

	var r0 domain.Bet
	var r1 bool
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uuid.UUID) (domain.Bet, bool, error)); ok {
		return rf(ctx, externalID, betID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, uuid.UUID) domain.Bet); ok {
		r0 = rf(ctx, externalID, betID)
	} else {
		r0 = ret.Get(0).(domain.Bet)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, uuid.UUID) bool); ok {
		r1 = rf(ctx, externalID, betID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, uuid.UUID) error); ok {
		r2 = rf(ctx, externalID, betID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// BetService_ShareUserBetByBetId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ShareUserBetByBetId'
type BetService_ShareUserBetByBetId_Call struct {
	*mock.Call
}

// ShareUserBetByBetId is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - betID uuid.UUID
func (_e *BetService_Expecter) ShareUserBetByBetId(ctx interface{}, externalID interface{}, betID interface{}) *BetService_ShareUserBetByBetId_Call {
	return &BetService_ShareUserBetByBetId_Call{Call: _e.mock.On("ShareUserBetByBetId", ctx, externalID, betID)}
}

func (_c *BetService_ShareUserBetByBetId_Call) Run(run func(ctx context.Context, externalID string, betID uuid.UUID)) *BetService_ShareUserBetByBetId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *BetService_ShareUserBetByBetId_Call) Return(_a0 domain.Bet, _a1 bool, _a2 error) *BetService_ShareUserBetByBetId_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *BetService_ShareUserBetByBetId_Call) RunAndReturn(run func(context.Context, string, uuid.UUID) (domain.Bet, bool, error)) *BetService_ShareUserBetByBetId_Call {
	_c.Call.Return(run)
	return _c
}

// NewBetService creates a new instance of BetService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBetService(t interface {
	mock.TestingT
	Cleanup(func())
}) *BetService {
	mock := &BetService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
