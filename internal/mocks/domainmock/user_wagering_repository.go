// Code generated by mockery v2.45.0. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// UserWageringRepository is an autogenerated mock type for the UserWageringRepository type
type UserWageringRepository struct {
	mock.Mock
}

type UserWageringRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *UserWageringRepository) EXPECT() *UserWageringRepository_Expecter {
	return &UserWageringRepository_Expecter{mock: &_m.Mock}
}

// UpdateUserLifeTimeWageringSummary provides a mock function with given fields: ctx
func (_m *UserWageringRepository) UpdateUserLifeTimeWageringSummary(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserLifeTimeWageringSummary")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserLifeTimeWageringSummary'
type UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call struct {
	*mock.Call
}

// UpdateUserLifeTimeWageringSummary is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserWageringRepository_Expecter) UpdateUserLifeTimeWageringSummary(ctx interface{}) *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call {
	return &UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call{Call: _e.mock.On("UpdateUserLifeTimeWageringSummary", ctx)}
}

func (_c *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call) Run(run func(ctx context.Context)) *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call) Return(_a0 error) *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call) RunAndReturn(run func(context.Context) error) *UserWageringRepository_UpdateUserLifeTimeWageringSummary_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserWallet provides a mock function with given fields: ctx, token, data
func (_m *UserWageringRepository) UpdateUserWallet(ctx context.Context, token string, data domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error) {
	ret := _m.Called(ctx, token, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserWallet")
	}

	var r0 domain.UserWalletUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error)); ok {
		return rf(ctx, token, data)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserWalletUpdateRequest) domain.UserWalletUpdateResponse); ok {
		r0 = rf(ctx, token, data)
	} else {
		r0 = ret.Get(0).(domain.UserWalletUpdateResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, domain.UserWalletUpdateRequest) error); ok {
		r1 = rf(ctx, token, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserWageringRepository_UpdateUserWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserWallet'
type UserWageringRepository_UpdateUserWallet_Call struct {
	*mock.Call
}

// UpdateUserWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - data domain.UserWalletUpdateRequest
func (_e *UserWageringRepository_Expecter) UpdateUserWallet(ctx interface{}, token interface{}, data interface{}) *UserWageringRepository_UpdateUserWallet_Call {
	return &UserWageringRepository_UpdateUserWallet_Call{Call: _e.mock.On("UpdateUserWallet", ctx, token, data)}
}

func (_c *UserWageringRepository_UpdateUserWallet_Call) Run(run func(ctx context.Context, token string, data domain.UserWalletUpdateRequest)) *UserWageringRepository_UpdateUserWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.UserWalletUpdateRequest))
	})
	return _c
}

func (_c *UserWageringRepository_UpdateUserWallet_Call) Return(_a0 domain.UserWalletUpdateResponse, _a1 error) *UserWageringRepository_UpdateUserWallet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserWageringRepository_UpdateUserWallet_Call) RunAndReturn(run func(context.Context, string, domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error)) *UserWageringRepository_UpdateUserWallet_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserWageringRepository creates a new instance of UserWageringRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserWageringRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserWageringRepository {
	mock := &UserWageringRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
