// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// ElantilWageringClient is an autogenerated mock type for the ElantilWageringClient type
type ElantilWageringClient struct {
	mock.Mock
}

type ElantilWageringClient_Expecter struct {
	mock *mock.Mock
}

func (_m *ElantilWageringClient) EXPECT() *ElantilWageringClient_Expecter {
	return &ElantilWageringClient_Expecter{mock: &_m.Mock}
}

// AssignBonusTemplate provides a mock function with given fields: ctx, templateKey, ownerId, ownerType, userToken
func (_m *ElantilWageringClient) AssignBonusTemplate(ctx context.Context, templateKey string, ownerId string, ownerType string, userToken string) error {
	ret := _m.Called(ctx, templateKey, ownerId, ownerType, userToken)

	if len(ret) == 0 {
		panic("no return value specified for AssignBonusTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) error); ok {
		r0 = rf(ctx, templateKey, ownerId, ownerType, userToken)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_AssignBonusTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignBonusTemplate'
type ElantilWageringClient_AssignBonusTemplate_Call struct {
	*mock.Call
}

// AssignBonusTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - templateKey string
//   - ownerId string
//   - ownerType string
//   - userToken string
func (_e *ElantilWageringClient_Expecter) AssignBonusTemplate(ctx interface{}, templateKey interface{}, ownerId interface{}, ownerType interface{}, userToken interface{}) *ElantilWageringClient_AssignBonusTemplate_Call {
	return &ElantilWageringClient_AssignBonusTemplate_Call{Call: _e.mock.On("AssignBonusTemplate", ctx, templateKey, ownerId, ownerType, userToken)}
}

func (_c *ElantilWageringClient_AssignBonusTemplate_Call) Run(run func(ctx context.Context, templateKey string, ownerId string, ownerType string, userToken string)) *ElantilWageringClient_AssignBonusTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_AssignBonusTemplate_Call) Return(_a0 error) *ElantilWageringClient_AssignBonusTemplate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_AssignBonusTemplate_Call) RunAndReturn(run func(context.Context, string, string, string, string) error) *ElantilWageringClient_AssignBonusTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// BatchGetWageringSummary provides a mock function with given fields: ctx, userIDs
func (_m *ElantilWageringClient) BatchGetWageringSummary(ctx context.Context, userIDs []string) (map[string]domain.BatchWageringData, error) {
	ret := _m.Called(ctx, userIDs)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetWageringSummary")
	}

	var r0 map[string]domain.BatchWageringData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (map[string]domain.BatchWageringData, error)); ok {
		return rf(ctx, userIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) map[string]domain.BatchWageringData); ok {
		r0 = rf(ctx, userIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]domain.BatchWageringData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, userIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_BatchGetWageringSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetWageringSummary'
type ElantilWageringClient_BatchGetWageringSummary_Call struct {
	*mock.Call
}

// BatchGetWageringSummary is a helper method to define mock.On call
//   - ctx context.Context
//   - userIDs []string
func (_e *ElantilWageringClient_Expecter) BatchGetWageringSummary(ctx interface{}, userIDs interface{}) *ElantilWageringClient_BatchGetWageringSummary_Call {
	return &ElantilWageringClient_BatchGetWageringSummary_Call{Call: _e.mock.On("BatchGetWageringSummary", ctx, userIDs)}
}

func (_c *ElantilWageringClient_BatchGetWageringSummary_Call) Run(run func(ctx context.Context, userIDs []string)) *ElantilWageringClient_BatchGetWageringSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *ElantilWageringClient_BatchGetWageringSummary_Call) Return(_a0 map[string]domain.BatchWageringData, _a1 error) *ElantilWageringClient_BatchGetWageringSummary_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_BatchGetWageringSummary_Call) RunAndReturn(run func(context.Context, []string) (map[string]domain.BatchWageringData, error)) *ElantilWageringClient_BatchGetWageringSummary_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfUserExistsInElantilSystemAndUpdatePlayerTags provides a mock function with given fields: userName
func (_m *ElantilWageringClient) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName string) (bool, error) {
	ret := _m.Called(userName)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfUserExistsInElantilSystemAndUpdatePlayerTags")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(userName)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(userName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfUserExistsInElantilSystemAndUpdatePlayerTags'
type ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call struct {
	*mock.Call
}

// CheckIfUserExistsInElantilSystemAndUpdatePlayerTags is a helper method to define mock.On call
//   - userName string
func (_e *ElantilWageringClient_Expecter) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName interface{}) *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call {
	return &ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call{Call: _e.mock.On("CheckIfUserExistsInElantilSystemAndUpdatePlayerTags", userName)}
}

func (_c *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call) Run(run func(userName string)) *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call) Return(_a0 bool, _a1 error) *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call) RunAndReturn(run func(string) (bool, error)) *ElantilWageringClient_CheckIfUserExistsInElantilSystemAndUpdatePlayerTags_Call {
	_c.Call.Return(run)
	return _c
}

// CheckUserWalletBalance provides a mock function with given fields: ctx, userExternalID, currency, withdrawalAmount
func (_m *ElantilWageringClient) CheckUserWalletBalance(ctx context.Context, userExternalID string, currency string, withdrawalAmount string) error {
	ret := _m.Called(ctx, userExternalID, currency, withdrawalAmount)

	if len(ret) == 0 {
		panic("no return value specified for CheckUserWalletBalance")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, userExternalID, currency, withdrawalAmount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_CheckUserWalletBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckUserWalletBalance'
type ElantilWageringClient_CheckUserWalletBalance_Call struct {
	*mock.Call
}

// CheckUserWalletBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - currency string
//   - withdrawalAmount string
func (_e *ElantilWageringClient_Expecter) CheckUserWalletBalance(ctx interface{}, userExternalID interface{}, currency interface{}, withdrawalAmount interface{}) *ElantilWageringClient_CheckUserWalletBalance_Call {
	return &ElantilWageringClient_CheckUserWalletBalance_Call{Call: _e.mock.On("CheckUserWalletBalance", ctx, userExternalID, currency, withdrawalAmount)}
}

func (_c *ElantilWageringClient_CheckUserWalletBalance_Call) Run(run func(ctx context.Context, userExternalID string, currency string, withdrawalAmount string)) *ElantilWageringClient_CheckUserWalletBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_CheckUserWalletBalance_Call) Return(_a0 error) *ElantilWageringClient_CheckUserWalletBalance_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_CheckUserWalletBalance_Call) RunAndReturn(run func(context.Context, string, string, string) error) *ElantilWageringClient_CheckUserWalletBalance_Call {
	_c.Call.Return(run)
	return _c
}

// CompleteSocialProfile provides a mock function with given fields: ctx, userID, email, completeProfile, accessToken
func (_m *ElantilWageringClient) CompleteSocialProfile(ctx context.Context, userID string, email string, completeProfile domain.SocialProfileCompletionRequest, accessToken string) error {
	ret := _m.Called(ctx, userID, email, completeProfile, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for CompleteSocialProfile")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, domain.SocialProfileCompletionRequest, string) error); ok {
		r0 = rf(ctx, userID, email, completeProfile, accessToken)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_CompleteSocialProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CompleteSocialProfile'
type ElantilWageringClient_CompleteSocialProfile_Call struct {
	*mock.Call
}

// CompleteSocialProfile is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - email string
//   - completeProfile domain.SocialProfileCompletionRequest
//   - accessToken string
func (_e *ElantilWageringClient_Expecter) CompleteSocialProfile(ctx interface{}, userID interface{}, email interface{}, completeProfile interface{}, accessToken interface{}) *ElantilWageringClient_CompleteSocialProfile_Call {
	return &ElantilWageringClient_CompleteSocialProfile_Call{Call: _e.mock.On("CompleteSocialProfile", ctx, userID, email, completeProfile, accessToken)}
}

func (_c *ElantilWageringClient_CompleteSocialProfile_Call) Run(run func(ctx context.Context, userID string, email string, completeProfile domain.SocialProfileCompletionRequest, accessToken string)) *ElantilWageringClient_CompleteSocialProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(domain.SocialProfileCompletionRequest), args[4].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_CompleteSocialProfile_Call) Return(_a0 error) *ElantilWageringClient_CompleteSocialProfile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_CompleteSocialProfile_Call) RunAndReturn(run func(context.Context, string, string, domain.SocialProfileCompletionRequest, string) error) *ElantilWageringClient_CompleteSocialProfile_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserLocalWallet provides a mock function with given fields: ctx, userID, token
func (_m *ElantilWageringClient) CreateUserLocalWallet(ctx context.Context, userID string, token string) (domain.GetUserWalletResponse, error) {
	ret := _m.Called(ctx, userID, token)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserLocalWallet")
	}

	var r0 domain.GetUserWalletResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.GetUserWalletResponse, error)); ok {
		return rf(ctx, userID, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.GetUserWalletResponse); ok {
		r0 = rf(ctx, userID, token)
	} else {
		r0 = ret.Get(0).(domain.GetUserWalletResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userID, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_CreateUserLocalWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserLocalWallet'
type ElantilWageringClient_CreateUserLocalWallet_Call struct {
	*mock.Call
}

// CreateUserLocalWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - token string
func (_e *ElantilWageringClient_Expecter) CreateUserLocalWallet(ctx interface{}, userID interface{}, token interface{}) *ElantilWageringClient_CreateUserLocalWallet_Call {
	return &ElantilWageringClient_CreateUserLocalWallet_Call{Call: _e.mock.On("CreateUserLocalWallet", ctx, userID, token)}
}

func (_c *ElantilWageringClient_CreateUserLocalWallet_Call) Run(run func(ctx context.Context, userID string, token string)) *ElantilWageringClient_CreateUserLocalWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_CreateUserLocalWallet_Call) Return(_a0 domain.GetUserWalletResponse, _a1 error) *ElantilWageringClient_CreateUserLocalWallet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_CreateUserLocalWallet_Call) RunAndReturn(run func(context.Context, string, string) (domain.GetUserWalletResponse, error)) *ElantilWageringClient_CreateUserLocalWallet_Call {
	_c.Call.Return(run)
	return _c
}

// ForfeitBonus provides a mock function with given fields: ctx, bonusId, token
func (_m *ElantilWageringClient) ForfeitBonus(ctx context.Context, bonusId string, token string) error {
	ret := _m.Called(ctx, bonusId, token)

	if len(ret) == 0 {
		panic("no return value specified for ForfeitBonus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, bonusId, token)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_ForfeitBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForfeitBonus'
type ElantilWageringClient_ForfeitBonus_Call struct {
	*mock.Call
}

// ForfeitBonus is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusId string
//   - token string
func (_e *ElantilWageringClient_Expecter) ForfeitBonus(ctx interface{}, bonusId interface{}, token interface{}) *ElantilWageringClient_ForfeitBonus_Call {
	return &ElantilWageringClient_ForfeitBonus_Call{Call: _e.mock.On("ForfeitBonus", ctx, bonusId, token)}
}

func (_c *ElantilWageringClient_ForfeitBonus_Call) Run(run func(ctx context.Context, bonusId string, token string)) *ElantilWageringClient_ForfeitBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_ForfeitBonus_Call) Return(_a0 error) *ElantilWageringClient_ForfeitBonus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_ForfeitBonus_Call) RunAndReturn(run func(context.Context, string, string) error) *ElantilWageringClient_ForfeitBonus_Call {
	_c.Call.Return(run)
	return _c
}

// GetBatchWageringSummary provides a mock function with given fields: ctx, externalIDs, wageringType, duration, startDate, endDate
func (_m *ElantilWageringClient) GetBatchWageringSummary(ctx context.Context, externalIDs []string, wageringType *string, duration string, startDate string, endDate string) (map[string]domain.BatchWageringData, string, error) {
	ret := _m.Called(ctx, externalIDs, wageringType, duration, startDate, endDate)

	if len(ret) == 0 {
		panic("no return value specified for GetBatchWageringSummary")
	}

	var r0 map[string]domain.BatchWageringData
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, []string, *string, string, string, string) (map[string]domain.BatchWageringData, string, error)); ok {
		return rf(ctx, externalIDs, wageringType, duration, startDate, endDate)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string, *string, string, string, string) map[string]domain.BatchWageringData); ok {
		r0 = rf(ctx, externalIDs, wageringType, duration, startDate, endDate)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]domain.BatchWageringData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string, *string, string, string, string) string); ok {
		r1 = rf(ctx, externalIDs, wageringType, duration, startDate, endDate)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, []string, *string, string, string, string) error); ok {
		r2 = rf(ctx, externalIDs, wageringType, duration, startDate, endDate)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ElantilWageringClient_GetBatchWageringSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBatchWageringSummary'
type ElantilWageringClient_GetBatchWageringSummary_Call struct {
	*mock.Call
}

// GetBatchWageringSummary is a helper method to define mock.On call
//   - ctx context.Context
//   - externalIDs []string
//   - wageringType *string
//   - duration string
//   - startDate string
//   - endDate string
func (_e *ElantilWageringClient_Expecter) GetBatchWageringSummary(ctx interface{}, externalIDs interface{}, wageringType interface{}, duration interface{}, startDate interface{}, endDate interface{}) *ElantilWageringClient_GetBatchWageringSummary_Call {
	return &ElantilWageringClient_GetBatchWageringSummary_Call{Call: _e.mock.On("GetBatchWageringSummary", ctx, externalIDs, wageringType, duration, startDate, endDate)}
}

func (_c *ElantilWageringClient_GetBatchWageringSummary_Call) Run(run func(ctx context.Context, externalIDs []string, wageringType *string, duration string, startDate string, endDate string)) *ElantilWageringClient_GetBatchWageringSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string), args[2].(*string), args[3].(string), args[4].(string), args[5].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetBatchWageringSummary_Call) Return(_a0 map[string]domain.BatchWageringData, _a1 string, _a2 error) *ElantilWageringClient_GetBatchWageringSummary_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ElantilWageringClient_GetBatchWageringSummary_Call) RunAndReturn(run func(context.Context, []string, *string, string, string, string) (map[string]domain.BatchWageringData, string, error)) *ElantilWageringClient_GetBatchWageringSummary_Call {
	_c.Call.Return(run)
	return _c
}

// GetConversionRates provides a mock function with given fields: ctx, fiatCurrency
func (_m *ElantilWageringClient) GetConversionRates(ctx context.Context, fiatCurrency string) (domain.ConversionResponse, error) {
	ret := _m.Called(ctx, fiatCurrency)

	if len(ret) == 0 {
		panic("no return value specified for GetConversionRates")
	}

	var r0 domain.ConversionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (domain.ConversionResponse, error)); ok {
		return rf(ctx, fiatCurrency)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) domain.ConversionResponse); ok {
		r0 = rf(ctx, fiatCurrency)
	} else {
		r0 = ret.Get(0).(domain.ConversionResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, fiatCurrency)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetConversionRates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConversionRates'
type ElantilWageringClient_GetConversionRates_Call struct {
	*mock.Call
}

// GetConversionRates is a helper method to define mock.On call
//   - ctx context.Context
//   - fiatCurrency string
func (_e *ElantilWageringClient_Expecter) GetConversionRates(ctx interface{}, fiatCurrency interface{}) *ElantilWageringClient_GetConversionRates_Call {
	return &ElantilWageringClient_GetConversionRates_Call{Call: _e.mock.On("GetConversionRates", ctx, fiatCurrency)}
}

func (_c *ElantilWageringClient_GetConversionRates_Call) Run(run func(ctx context.Context, fiatCurrency string)) *ElantilWageringClient_GetConversionRates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetConversionRates_Call) Return(_a0 domain.ConversionResponse, _a1 error) *ElantilWageringClient_GetConversionRates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetConversionRates_Call) RunAndReturn(run func(context.Context, string) (domain.ConversionResponse, error)) *ElantilWageringClient_GetConversionRates_Call {
	_c.Call.Return(run)
	return _c
}

// GetOwnerBonusesByUserId provides a mock function with given fields: ctx, userId, token
func (_m *ElantilWageringClient) GetOwnerBonusesByUserId(ctx context.Context, userId string, token string) (*domain.OwnerBonusResponse, error) {
	ret := _m.Called(ctx, userId, token)

	if len(ret) == 0 {
		panic("no return value specified for GetOwnerBonusesByUserId")
	}

	var r0 *domain.OwnerBonusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*domain.OwnerBonusResponse, error)); ok {
		return rf(ctx, userId, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *domain.OwnerBonusResponse); ok {
		r0 = rf(ctx, userId, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.OwnerBonusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userId, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetOwnerBonusesByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOwnerBonusesByUserId'
type ElantilWageringClient_GetOwnerBonusesByUserId_Call struct {
	*mock.Call
}

// GetOwnerBonusesByUserId is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - token string
func (_e *ElantilWageringClient_Expecter) GetOwnerBonusesByUserId(ctx interface{}, userId interface{}, token interface{}) *ElantilWageringClient_GetOwnerBonusesByUserId_Call {
	return &ElantilWageringClient_GetOwnerBonusesByUserId_Call{Call: _e.mock.On("GetOwnerBonusesByUserId", ctx, userId, token)}
}

func (_c *ElantilWageringClient_GetOwnerBonusesByUserId_Call) Run(run func(ctx context.Context, userId string, token string)) *ElantilWageringClient_GetOwnerBonusesByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetOwnerBonusesByUserId_Call) Return(_a0 *domain.OwnerBonusResponse, _a1 error) *ElantilWageringClient_GetOwnerBonusesByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetOwnerBonusesByUserId_Call) RunAndReturn(run func(context.Context, string, string) (*domain.OwnerBonusResponse, error)) *ElantilWageringClient_GetOwnerBonusesByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// GetPlayerActivityTagsByUserExternalID provides a mock function with given fields: userExternalID
func (_m *ElantilWageringClient) GetPlayerActivityTagsByUserExternalID(userExternalID string) (interface{}, error) {
	ret := _m.Called(userExternalID)

	if len(ret) == 0 {
		panic("no return value specified for GetPlayerActivityTagsByUserExternalID")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (interface{}, error)); ok {
		return rf(userExternalID)
	}
	if rf, ok := ret.Get(0).(func(string) interface{}); ok {
		r0 = rf(userExternalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userExternalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPlayerActivityTagsByUserExternalID'
type ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call struct {
	*mock.Call
}

// GetPlayerActivityTagsByUserExternalID is a helper method to define mock.On call
//   - userExternalID string
func (_e *ElantilWageringClient_Expecter) GetPlayerActivityTagsByUserExternalID(userExternalID interface{}) *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call {
	return &ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call{Call: _e.mock.On("GetPlayerActivityTagsByUserExternalID", userExternalID)}
}

func (_c *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call) Run(run func(userExternalID string)) *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call) Return(_a0 interface{}, _a1 error) *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call) RunAndReturn(run func(string) (interface{}, error)) *ElantilWageringClient_GetPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserIdsAndBatchWageringSummary provides a mock function with given fields: ctx, wageringType, duration, startDate, endDate
func (_m *ElantilWageringClient) GetUserIdsAndBatchWageringSummary(ctx context.Context, wageringType string, duration string, startDate string, endDate string) (map[string]domain.BatchWageringData, string, error) {
	ret := _m.Called(ctx, wageringType, duration, startDate, endDate)

	if len(ret) == 0 {
		panic("no return value specified for GetUserIdsAndBatchWageringSummary")
	}

	var r0 map[string]domain.BatchWageringData
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (map[string]domain.BatchWageringData, string, error)); ok {
		return rf(ctx, wageringType, duration, startDate, endDate)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) map[string]domain.BatchWageringData); ok {
		r0 = rf(ctx, wageringType, duration, startDate, endDate)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]domain.BatchWageringData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) string); ok {
		r1 = rf(ctx, wageringType, duration, startDate, endDate)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, string, string) error); ok {
		r2 = rf(ctx, wageringType, duration, startDate, endDate)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserIdsAndBatchWageringSummary'
type ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call struct {
	*mock.Call
}

// GetUserIdsAndBatchWageringSummary is a helper method to define mock.On call
//   - ctx context.Context
//   - wageringType string
//   - duration string
//   - startDate string
//   - endDate string
func (_e *ElantilWageringClient_Expecter) GetUserIdsAndBatchWageringSummary(ctx interface{}, wageringType interface{}, duration interface{}, startDate interface{}, endDate interface{}) *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call {
	return &ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call{Call: _e.mock.On("GetUserIdsAndBatchWageringSummary", ctx, wageringType, duration, startDate, endDate)}
}

func (_c *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call) Run(run func(ctx context.Context, wageringType string, duration string, startDate string, endDate string)) *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call) Return(_a0 map[string]domain.BatchWageringData, _a1 string, _a2 error) *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call) RunAndReturn(run func(context.Context, string, string, string, string) (map[string]domain.BatchWageringData, string, error)) *ElantilWageringClient_GetUserIdsAndBatchWageringSummary_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserInformationFromJWTToken provides a mock function with given fields: token
func (_m *ElantilWageringClient) GetUserInformationFromJWTToken(token string) (string, string, error) {
	ret := _m.Called(token)

	if len(ret) == 0 {
		panic("no return value specified for GetUserInformationFromJWTToken")
	}

	var r0 string
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(string) (string, string, error)); ok {
		return rf(token)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(token)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) string); ok {
		r1 = rf(token)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(string) error); ok {
		r2 = rf(token)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ElantilWageringClient_GetUserInformationFromJWTToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserInformationFromJWTToken'
type ElantilWageringClient_GetUserInformationFromJWTToken_Call struct {
	*mock.Call
}

// GetUserInformationFromJWTToken is a helper method to define mock.On call
//   - token string
func (_e *ElantilWageringClient_Expecter) GetUserInformationFromJWTToken(token interface{}) *ElantilWageringClient_GetUserInformationFromJWTToken_Call {
	return &ElantilWageringClient_GetUserInformationFromJWTToken_Call{Call: _e.mock.On("GetUserInformationFromJWTToken", token)}
}

func (_c *ElantilWageringClient_GetUserInformationFromJWTToken_Call) Run(run func(token string)) *ElantilWageringClient_GetUserInformationFromJWTToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetUserInformationFromJWTToken_Call) Return(_a0 string, _a1 string, _a2 error) *ElantilWageringClient_GetUserInformationFromJWTToken_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ElantilWageringClient_GetUserInformationFromJWTToken_Call) RunAndReturn(run func(string) (string, string, error)) *ElantilWageringClient_GetUserInformationFromJWTToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserProfile provides a mock function with given fields: ctx, userExternalID, token
func (_m *ElantilWageringClient) GetUserProfile(ctx context.Context, userExternalID string, token string) (domain.GetUserProfileResponse, error) {
	ret := _m.Called(ctx, userExternalID, token)

	if len(ret) == 0 {
		panic("no return value specified for GetUserProfile")
	}

	var r0 domain.GetUserProfileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.GetUserProfileResponse, error)); ok {
		return rf(ctx, userExternalID, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.GetUserProfileResponse); ok {
		r0 = rf(ctx, userExternalID, token)
	} else {
		r0 = ret.Get(0).(domain.GetUserProfileResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userExternalID, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetUserProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserProfile'
type ElantilWageringClient_GetUserProfile_Call struct {
	*mock.Call
}

// GetUserProfile is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - token string
func (_e *ElantilWageringClient_Expecter) GetUserProfile(ctx interface{}, userExternalID interface{}, token interface{}) *ElantilWageringClient_GetUserProfile_Call {
	return &ElantilWageringClient_GetUserProfile_Call{Call: _e.mock.On("GetUserProfile", ctx, userExternalID, token)}
}

func (_c *ElantilWageringClient_GetUserProfile_Call) Run(run func(ctx context.Context, userExternalID string, token string)) *ElantilWageringClient_GetUserProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetUserProfile_Call) Return(_a0 domain.GetUserProfileResponse, _a1 error) *ElantilWageringClient_GetUserProfile_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetUserProfile_Call) RunAndReturn(run func(context.Context, string, string) (domain.GetUserProfileResponse, error)) *ElantilWageringClient_GetUserProfile_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserTransactions provides a mock function with given fields: ctx, userID, page, size
func (_m *ElantilWageringClient) GetUserTransactions(ctx context.Context, userID string, page int64, size int64) (domain.TransactionResult, error) {
	ret := _m.Called(ctx, userID, page, size)

	if len(ret) == 0 {
		panic("no return value specified for GetUserTransactions")
	}

	var r0 domain.TransactionResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, int64) (domain.TransactionResult, error)); ok {
		return rf(ctx, userID, page, size)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, int64) domain.TransactionResult); ok {
		r0 = rf(ctx, userID, page, size)
	} else {
		r0 = ret.Get(0).(domain.TransactionResult)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = rf(ctx, userID, page, size)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetUserTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserTransactions'
type ElantilWageringClient_GetUserTransactions_Call struct {
	*mock.Call
}

// GetUserTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - page int64
//   - size int64
func (_e *ElantilWageringClient_Expecter) GetUserTransactions(ctx interface{}, userID interface{}, page interface{}, size interface{}) *ElantilWageringClient_GetUserTransactions_Call {
	return &ElantilWageringClient_GetUserTransactions_Call{Call: _e.mock.On("GetUserTransactions", ctx, userID, page, size)}
}

func (_c *ElantilWageringClient_GetUserTransactions_Call) Run(run func(ctx context.Context, userID string, page int64, size int64)) *ElantilWageringClient_GetUserTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(int64))
	})
	return _c
}

func (_c *ElantilWageringClient_GetUserTransactions_Call) Return(_a0 domain.TransactionResult, _a1 error) *ElantilWageringClient_GetUserTransactions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetUserTransactions_Call) RunAndReturn(run func(context.Context, string, int64, int64) (domain.TransactionResult, error)) *ElantilWageringClient_GetUserTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserWallet provides a mock function with given fields: ctx, userExternalID, token
func (_m *ElantilWageringClient) GetUserWallet(ctx context.Context, userExternalID string, token string) (domain.GetUserWalletResponse, error) {
	ret := _m.Called(ctx, userExternalID, token)

	if len(ret) == 0 {
		panic("no return value specified for GetUserWallet")
	}

	var r0 domain.GetUserWalletResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.GetUserWalletResponse, error)); ok {
		return rf(ctx, userExternalID, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.GetUserWalletResponse); ok {
		r0 = rf(ctx, userExternalID, token)
	} else {
		r0 = ret.Get(0).(domain.GetUserWalletResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userExternalID, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_GetUserWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserWallet'
type ElantilWageringClient_GetUserWallet_Call struct {
	*mock.Call
}

// GetUserWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - token string
func (_e *ElantilWageringClient_Expecter) GetUserWallet(ctx interface{}, userExternalID interface{}, token interface{}) *ElantilWageringClient_GetUserWallet_Call {
	return &ElantilWageringClient_GetUserWallet_Call{Call: _e.mock.On("GetUserWallet", ctx, userExternalID, token)}
}

func (_c *ElantilWageringClient_GetUserWallet_Call) Run(run func(ctx context.Context, userExternalID string, token string)) *ElantilWageringClient_GetUserWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_GetUserWallet_Call) Return(_a0 domain.GetUserWalletResponse, _a1 error) *ElantilWageringClient_GetUserWallet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_GetUserWallet_Call) RunAndReturn(run func(context.Context, string, string) (domain.GetUserWalletResponse, error)) *ElantilWageringClient_GetUserWallet_Call {
	_c.Call.Return(run)
	return _c
}

// SendVerificationEmail provides a mock function with given fields: userExternalID
func (_m *ElantilWageringClient) SendVerificationEmail(userExternalID string) error {
	ret := _m.Called(userExternalID)

	if len(ret) == 0 {
		panic("no return value specified for SendVerificationEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(userExternalID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_SendVerificationEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendVerificationEmail'
type ElantilWageringClient_SendVerificationEmail_Call struct {
	*mock.Call
}

// SendVerificationEmail is a helper method to define mock.On call
//   - userExternalID string
func (_e *ElantilWageringClient_Expecter) SendVerificationEmail(userExternalID interface{}) *ElantilWageringClient_SendVerificationEmail_Call {
	return &ElantilWageringClient_SendVerificationEmail_Call{Call: _e.mock.On("SendVerificationEmail", userExternalID)}
}

func (_c *ElantilWageringClient_SendVerificationEmail_Call) Run(run func(userExternalID string)) *ElantilWageringClient_SendVerificationEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_SendVerificationEmail_Call) Return(_a0 error) *ElantilWageringClient_SendVerificationEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_SendVerificationEmail_Call) RunAndReturn(run func(string) error) *ElantilWageringClient_SendVerificationEmail_Call {
	_c.Call.Return(run)
	return _c
}

// SyncPlayerReferralsFromDB provides a mock function with given fields: userExternalID, dbReferrals
func (_m *ElantilWageringClient) SyncPlayerReferralsFromDB(userExternalID string, dbReferrals []domain.ReferredUser) error {
	ret := _m.Called(userExternalID, dbReferrals)

	if len(ret) == 0 {
		panic("no return value specified for SyncPlayerReferralsFromDB")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, []domain.ReferredUser) error); ok {
		r0 = rf(userExternalID, dbReferrals)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_SyncPlayerReferralsFromDB_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncPlayerReferralsFromDB'
type ElantilWageringClient_SyncPlayerReferralsFromDB_Call struct {
	*mock.Call
}

// SyncPlayerReferralsFromDB is a helper method to define mock.On call
//   - userExternalID string
//   - dbReferrals []domain.ReferredUser
func (_e *ElantilWageringClient_Expecter) SyncPlayerReferralsFromDB(userExternalID interface{}, dbReferrals interface{}) *ElantilWageringClient_SyncPlayerReferralsFromDB_Call {
	return &ElantilWageringClient_SyncPlayerReferralsFromDB_Call{Call: _e.mock.On("SyncPlayerReferralsFromDB", userExternalID, dbReferrals)}
}

func (_c *ElantilWageringClient_SyncPlayerReferralsFromDB_Call) Run(run func(userExternalID string, dbReferrals []domain.ReferredUser)) *ElantilWageringClient_SyncPlayerReferralsFromDB_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].([]domain.ReferredUser))
	})
	return _c
}

func (_c *ElantilWageringClient_SyncPlayerReferralsFromDB_Call) Return(_a0 error) *ElantilWageringClient_SyncPlayerReferralsFromDB_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_SyncPlayerReferralsFromDB_Call) RunAndReturn(run func(string, []domain.ReferredUser) error) *ElantilWageringClient_SyncPlayerReferralsFromDB_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePassword provides a mock function with given fields: email
func (_m *ElantilWageringClient) UpdatePassword(email string) error {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePassword")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_UpdatePassword_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePassword'
type ElantilWageringClient_UpdatePassword_Call struct {
	*mock.Call
}

// UpdatePassword is a helper method to define mock.On call
//   - email string
func (_e *ElantilWageringClient_Expecter) UpdatePassword(email interface{}) *ElantilWageringClient_UpdatePassword_Call {
	return &ElantilWageringClient_UpdatePassword_Call{Call: _e.mock.On("UpdatePassword", email)}
}

func (_c *ElantilWageringClient_UpdatePassword_Call) Run(run func(email string)) *ElantilWageringClient_UpdatePassword_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_UpdatePassword_Call) Return(_a0 error) *ElantilWageringClient_UpdatePassword_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_UpdatePassword_Call) RunAndReturn(run func(string) error) *ElantilWageringClient_UpdatePassword_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserProfile provides a mock function with given fields: ctx, userExternalID, token, data
func (_m *ElantilWageringClient) UpdateUserProfile(ctx context.Context, userExternalID string, token string, data domain.UserProfileUpdateRequest) error {
	ret := _m.Called(ctx, userExternalID, token, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserProfile")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, domain.UserProfileUpdateRequest) error); ok {
		r0 = rf(ctx, userExternalID, token, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_UpdateUserProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserProfile'
type ElantilWageringClient_UpdateUserProfile_Call struct {
	*mock.Call
}

// UpdateUserProfile is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - token string
//   - data domain.UserProfileUpdateRequest
func (_e *ElantilWageringClient_Expecter) UpdateUserProfile(ctx interface{}, userExternalID interface{}, token interface{}, data interface{}) *ElantilWageringClient_UpdateUserProfile_Call {
	return &ElantilWageringClient_UpdateUserProfile_Call{Call: _e.mock.On("UpdateUserProfile", ctx, userExternalID, token, data)}
}

func (_c *ElantilWageringClient_UpdateUserProfile_Call) Run(run func(ctx context.Context, userExternalID string, token string, data domain.UserProfileUpdateRequest)) *ElantilWageringClient_UpdateUserProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(domain.UserProfileUpdateRequest))
	})
	return _c
}

func (_c *ElantilWageringClient_UpdateUserProfile_Call) Return(_a0 error) *ElantilWageringClient_UpdateUserProfile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_UpdateUserProfile_Call) RunAndReturn(run func(context.Context, string, string, domain.UserProfileUpdateRequest) error) *ElantilWageringClient_UpdateUserProfile_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserWallet provides a mock function with given fields: ctx, token, data
func (_m *ElantilWageringClient) UpdateUserWallet(ctx context.Context, token string, data domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error) {
	ret := _m.Called(ctx, token, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserWallet")
	}

	var r0 domain.UserWalletUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error)); ok {
		return rf(ctx, token, data)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserWalletUpdateRequest) domain.UserWalletUpdateResponse); ok {
		r0 = rf(ctx, token, data)
	} else {
		r0 = ret.Get(0).(domain.UserWalletUpdateResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, domain.UserWalletUpdateRequest) error); ok {
		r1 = rf(ctx, token, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_UpdateUserWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserWallet'
type ElantilWageringClient_UpdateUserWallet_Call struct {
	*mock.Call
}

// UpdateUserWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - data domain.UserWalletUpdateRequest
func (_e *ElantilWageringClient_Expecter) UpdateUserWallet(ctx interface{}, token interface{}, data interface{}) *ElantilWageringClient_UpdateUserWallet_Call {
	return &ElantilWageringClient_UpdateUserWallet_Call{Call: _e.mock.On("UpdateUserWallet", ctx, token, data)}
}

func (_c *ElantilWageringClient_UpdateUserWallet_Call) Run(run func(ctx context.Context, token string, data domain.UserWalletUpdateRequest)) *ElantilWageringClient_UpdateUserWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.UserWalletUpdateRequest))
	})
	return _c
}

func (_c *ElantilWageringClient_UpdateUserWallet_Call) Return(_a0 domain.UserWalletUpdateResponse, _a1 error) *ElantilWageringClient_UpdateUserWallet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_UpdateUserWallet_Call) RunAndReturn(run func(context.Context, string, domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error)) *ElantilWageringClient_UpdateUserWallet_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertPlayerActivityTagsByUserExternalID provides a mock function with given fields: userExternalID, categoryKey, keyToSet, value
func (_m *ElantilWageringClient) UpsertPlayerActivityTagsByUserExternalID(userExternalID string, categoryKey string, keyToSet string, value string) error {
	ret := _m.Called(userExternalID, categoryKey, keyToSet, value)

	if len(ret) == 0 {
		panic("no return value specified for UpsertPlayerActivityTagsByUserExternalID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string, string) error); ok {
		r0 = rf(userExternalID, categoryKey, keyToSet, value)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertPlayerActivityTagsByUserExternalID'
type ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call struct {
	*mock.Call
}

// UpsertPlayerActivityTagsByUserExternalID is a helper method to define mock.On call
//   - userExternalID string
//   - categoryKey string
//   - keyToSet string
//   - value string
func (_e *ElantilWageringClient_Expecter) UpsertPlayerActivityTagsByUserExternalID(userExternalID interface{}, categoryKey interface{}, keyToSet interface{}, value interface{}) *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call {
	return &ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call{Call: _e.mock.On("UpsertPlayerActivityTagsByUserExternalID", userExternalID, categoryKey, keyToSet, value)}
}

func (_c *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call) Run(run func(userExternalID string, categoryKey string, keyToSet string, value string)) *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call) Return(_a0 error) *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call) RunAndReturn(run func(string, string, string, string) error) *ElantilWageringClient_UpsertPlayerActivityTagsByUserExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertPlayerRefferals provides a mock function with given fields: userExternalID, referralId, username
func (_m *ElantilWageringClient) UpsertPlayerRefferals(userExternalID string, referralId string, username string) error {
	ret := _m.Called(userExternalID, referralId, username)

	if len(ret) == 0 {
		panic("no return value specified for UpsertPlayerRefferals")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(userExternalID, referralId, username)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ElantilWageringClient_UpsertPlayerRefferals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertPlayerRefferals'
type ElantilWageringClient_UpsertPlayerRefferals_Call struct {
	*mock.Call
}

// UpsertPlayerRefferals is a helper method to define mock.On call
//   - userExternalID string
//   - referralId string
//   - username string
func (_e *ElantilWageringClient_Expecter) UpsertPlayerRefferals(userExternalID interface{}, referralId interface{}, username interface{}) *ElantilWageringClient_UpsertPlayerRefferals_Call {
	return &ElantilWageringClient_UpsertPlayerRefferals_Call{Call: _e.mock.On("UpsertPlayerRefferals", userExternalID, referralId, username)}
}

func (_c *ElantilWageringClient_UpsertPlayerRefferals_Call) Run(run func(userExternalID string, referralId string, username string)) *ElantilWageringClient_UpsertPlayerRefferals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_UpsertPlayerRefferals_Call) Return(_a0 error) *ElantilWageringClient_UpsertPlayerRefferals_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ElantilWageringClient_UpsertPlayerRefferals_Call) RunAndReturn(run func(string, string, string) error) *ElantilWageringClient_UpsertPlayerRefferals_Call {
	_c.Call.Return(run)
	return _c
}

// ValidateToken provides a mock function with given fields: ctx, token
func (_m *ElantilWageringClient) ValidateToken(ctx context.Context, token string) (bool, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for ValidateToken")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, token)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ElantilWageringClient_ValidateToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateToken'
type ElantilWageringClient_ValidateToken_Call struct {
	*mock.Call
}

// ValidateToken is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *ElantilWageringClient_Expecter) ValidateToken(ctx interface{}, token interface{}) *ElantilWageringClient_ValidateToken_Call {
	return &ElantilWageringClient_ValidateToken_Call{Call: _e.mock.On("ValidateToken", ctx, token)}
}

func (_c *ElantilWageringClient_ValidateToken_Call) Run(run func(ctx context.Context, token string)) *ElantilWageringClient_ValidateToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ElantilWageringClient_ValidateToken_Call) Return(_a0 bool, _a1 error) *ElantilWageringClient_ValidateToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ElantilWageringClient_ValidateToken_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *ElantilWageringClient_ValidateToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewElantilWageringClient creates a new instance of ElantilWageringClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewElantilWageringClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ElantilWageringClient {
	mock := &ElantilWageringClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
