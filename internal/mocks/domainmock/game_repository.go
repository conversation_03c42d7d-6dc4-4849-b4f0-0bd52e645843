// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// GameRepository is an autogenerated mock type for the GameRepository type
type GameRepository struct {
	mock.Mock
}

type GameRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *GameRepository) EXPECT() *GameRepository_Expecter {
	return &GameRepository_Expecter{mock: &_m.Mock}
}

// CreateGame provides a mock function with given fields: ctx, game
func (_m *GameRepository) CreateGame(ctx context.Context, game *domain.Game) (*domain.Game, error) {
	ret := _m.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for CreateGame")
	}

	var r0 *domain.Game
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Game) (*domain.Game, error)); ok {
		return rf(ctx, game)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Game) *domain.Game); ok {
		r0 = rf(ctx, game)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Game)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.Game) error); ok {
		r1 = rf(ctx, game)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GameRepository_CreateGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGame'
type GameRepository_CreateGame_Call struct {
	*mock.Call
}

// CreateGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *domain.Game
func (_e *GameRepository_Expecter) CreateGame(ctx interface{}, game interface{}) *GameRepository_CreateGame_Call {
	return &GameRepository_CreateGame_Call{Call: _e.mock.On("CreateGame", ctx, game)}
}

func (_c *GameRepository_CreateGame_Call) Run(run func(ctx context.Context, game *domain.Game)) *GameRepository_CreateGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Game))
	})
	return _c
}

func (_c *GameRepository_CreateGame_Call) Return(_a0 *domain.Game, _a1 error) *GameRepository_CreateGame_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GameRepository_CreateGame_Call) RunAndReturn(run func(context.Context, *domain.Game) (*domain.Game, error)) *GameRepository_CreateGame_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByExternalID provides a mock function with given fields: ctx, id
func (_m *GameRepository) GetGameByExternalID(ctx context.Context, id string) (*domain.Game, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByExternalID")
	}

	var r0 *domain.Game
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.Game, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.Game); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Game)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GameRepository_GetGameByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByExternalID'
type GameRepository_GetGameByExternalID_Call struct {
	*mock.Call
}

// GetGameByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *GameRepository_Expecter) GetGameByExternalID(ctx interface{}, id interface{}) *GameRepository_GetGameByExternalID_Call {
	return &GameRepository_GetGameByExternalID_Call{Call: _e.mock.On("GetGameByExternalID", ctx, id)}
}

func (_c *GameRepository_GetGameByExternalID_Call) Run(run func(ctx context.Context, id string)) *GameRepository_GetGameByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *GameRepository_GetGameByExternalID_Call) Return(_a0 *domain.Game, _a1 error) *GameRepository_GetGameByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GameRepository_GetGameByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.Game, error)) *GameRepository_GetGameByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByGameID provides a mock function with given fields: ctx, id
func (_m *GameRepository) GetGameByGameID(ctx context.Context, id uuid.UUID) (*domain.Game, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByGameID")
	}

	var r0 *domain.Game
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.Game, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.Game); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Game)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GameRepository_GetGameByGameID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByGameID'
type GameRepository_GetGameByGameID_Call struct {
	*mock.Call
}

// GetGameByGameID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *GameRepository_Expecter) GetGameByGameID(ctx interface{}, id interface{}) *GameRepository_GetGameByGameID_Call {
	return &GameRepository_GetGameByGameID_Call{Call: _e.mock.On("GetGameByGameID", ctx, id)}
}

func (_c *GameRepository_GetGameByGameID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *GameRepository_GetGameByGameID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *GameRepository_GetGameByGameID_Call) Return(_a0 *domain.Game, _a1 error) *GameRepository_GetGameByGameID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GameRepository_GetGameByGameID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.Game, error)) *GameRepository_GetGameByGameID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByVendorGameID provides a mock function with given fields: ctx, id
func (_m *GameRepository) GetGameByVendorGameID(ctx context.Context, id string) (*domain.Game, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByVendorGameID")
	}

	var r0 *domain.Game
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.Game, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.Game); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Game)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GameRepository_GetGameByVendorGameID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByVendorGameID'
type GameRepository_GetGameByVendorGameID_Call struct {
	*mock.Call
}

// GetGameByVendorGameID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *GameRepository_Expecter) GetGameByVendorGameID(ctx interface{}, id interface{}) *GameRepository_GetGameByVendorGameID_Call {
	return &GameRepository_GetGameByVendorGameID_Call{Call: _e.mock.On("GetGameByVendorGameID", ctx, id)}
}

func (_c *GameRepository_GetGameByVendorGameID_Call) Run(run func(ctx context.Context, id string)) *GameRepository_GetGameByVendorGameID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *GameRepository_GetGameByVendorGameID_Call) Return(_a0 *domain.Game, _a1 error) *GameRepository_GetGameByVendorGameID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GameRepository_GetGameByVendorGameID_Call) RunAndReturn(run func(context.Context, string) (*domain.Game, error)) *GameRepository_GetGameByVendorGameID_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertAllGamesIfNotInitialized provides a mock function with given fields: ctx, getAllGames
func (_m *GameRepository) UpsertAllGamesIfNotInitialized(ctx context.Context, getAllGames func(context.Context) ([]domain.Game, error)) error {
	ret := _m.Called(ctx, getAllGames)

	if len(ret) == 0 {
		panic("no return value specified for UpsertAllGamesIfNotInitialized")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(context.Context) ([]domain.Game, error)) error); ok {
		r0 = rf(ctx, getAllGames)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GameRepository_UpsertAllGamesIfNotInitialized_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertAllGamesIfNotInitialized'
type GameRepository_UpsertAllGamesIfNotInitialized_Call struct {
	*mock.Call
}

// UpsertAllGamesIfNotInitialized is a helper method to define mock.On call
//   - ctx context.Context
//   - getAllGames func(context.Context)([]domain.Game , error)
func (_e *GameRepository_Expecter) UpsertAllGamesIfNotInitialized(ctx interface{}, getAllGames interface{}) *GameRepository_UpsertAllGamesIfNotInitialized_Call {
	return &GameRepository_UpsertAllGamesIfNotInitialized_Call{Call: _e.mock.On("UpsertAllGamesIfNotInitialized", ctx, getAllGames)}
}

func (_c *GameRepository_UpsertAllGamesIfNotInitialized_Call) Run(run func(ctx context.Context, getAllGames func(context.Context) ([]domain.Game, error))) *GameRepository_UpsertAllGamesIfNotInitialized_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(context.Context) ([]domain.Game, error)))
	})
	return _c
}

func (_c *GameRepository_UpsertAllGamesIfNotInitialized_Call) Return(_a0 error) *GameRepository_UpsertAllGamesIfNotInitialized_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GameRepository_UpsertAllGamesIfNotInitialized_Call) RunAndReturn(run func(context.Context, func(context.Context) ([]domain.Game, error)) error) *GameRepository_UpsertAllGamesIfNotInitialized_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertGames provides a mock function with given fields: ctx, games
func (_m *GameRepository) UpsertGames(ctx context.Context, games []domain.Game) error {
	ret := _m.Called(ctx, games)

	if len(ret) == 0 {
		panic("no return value specified for UpsertGames")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []domain.Game) error); ok {
		r0 = rf(ctx, games)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GameRepository_UpsertGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertGames'
type GameRepository_UpsertGames_Call struct {
	*mock.Call
}

// UpsertGames is a helper method to define mock.On call
//   - ctx context.Context
//   - games []domain.Game
func (_e *GameRepository_Expecter) UpsertGames(ctx interface{}, games interface{}) *GameRepository_UpsertGames_Call {
	return &GameRepository_UpsertGames_Call{Call: _e.mock.On("UpsertGames", ctx, games)}
}

func (_c *GameRepository_UpsertGames_Call) Run(run func(ctx context.Context, games []domain.Game)) *GameRepository_UpsertGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]domain.Game))
	})
	return _c
}

func (_c *GameRepository_UpsertGames_Call) Return(_a0 error) *GameRepository_UpsertGames_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GameRepository_UpsertGames_Call) RunAndReturn(run func(context.Context, []domain.Game) error) *GameRepository_UpsertGames_Call {
	_c.Call.Return(run)
	return _c
}

// NewGameRepository creates a new instance of GameRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewGameRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *GameRepository {
	mock := &GameRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
