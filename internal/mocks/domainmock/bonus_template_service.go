// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// BonusTemplateService is an autogenerated mock type for the BonusTemplateService type
type BonusTemplateService struct {
	mock.Mock
}

type BonusTemplateService_Expecter struct {
	mock *mock.Mock
}

func (_m *BonusTemplateService) EXPECT() *BonusTemplateService_Expecter {
	return &BonusTemplateService_Expecter{mock: &_m.Mock}
}

// CreateBonusTemplate provides a mock function with given fields: ctx, bonusTemplate
func (_m *BonusTemplateService) CreateBonusTemplate(ctx context.Context, bonusTemplate *domain.CreateBonusTemplateRequest) error {
	ret := _m.Called(ctx, bonusTemplate)

	if len(ret) == 0 {
		panic("no return value specified for CreateBonusTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.CreateBonusTemplateRequest) error); ok {
		r0 = rf(ctx, bonusTemplate)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BonusTemplateService_CreateBonusTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBonusTemplate'
type BonusTemplateService_CreateBonusTemplate_Call struct {
	*mock.Call
}

// CreateBonusTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusTemplate *domain.CreateBonusTemplateRequest
func (_e *BonusTemplateService_Expecter) CreateBonusTemplate(ctx interface{}, bonusTemplate interface{}) *BonusTemplateService_CreateBonusTemplate_Call {
	return &BonusTemplateService_CreateBonusTemplate_Call{Call: _e.mock.On("CreateBonusTemplate", ctx, bonusTemplate)}
}

func (_c *BonusTemplateService_CreateBonusTemplate_Call) Run(run func(ctx context.Context, bonusTemplate *domain.CreateBonusTemplateRequest)) *BonusTemplateService_CreateBonusTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.CreateBonusTemplateRequest))
	})
	return _c
}

func (_c *BonusTemplateService_CreateBonusTemplate_Call) Return(_a0 error) *BonusTemplateService_CreateBonusTemplate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BonusTemplateService_CreateBonusTemplate_Call) RunAndReturn(run func(context.Context, *domain.CreateBonusTemplateRequest) error) *BonusTemplateService_CreateBonusTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteBonusTemplate provides a mock function with given fields: ctx, offerCode, username
func (_m *BonusTemplateService) DeleteBonusTemplate(ctx context.Context, offerCode string, username string) error {
	ret := _m.Called(ctx, offerCode, username)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBonusTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, offerCode, username)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BonusTemplateService_DeleteBonusTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteBonusTemplate'
type BonusTemplateService_DeleteBonusTemplate_Call struct {
	*mock.Call
}

// DeleteBonusTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - offerCode string
//   - username string
func (_e *BonusTemplateService_Expecter) DeleteBonusTemplate(ctx interface{}, offerCode interface{}, username interface{}) *BonusTemplateService_DeleteBonusTemplate_Call {
	return &BonusTemplateService_DeleteBonusTemplate_Call{Call: _e.mock.On("DeleteBonusTemplate", ctx, offerCode, username)}
}

func (_c *BonusTemplateService_DeleteBonusTemplate_Call) Run(run func(ctx context.Context, offerCode string, username string)) *BonusTemplateService_DeleteBonusTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *BonusTemplateService_DeleteBonusTemplate_Call) Return(_a0 error) *BonusTemplateService_DeleteBonusTemplate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BonusTemplateService_DeleteBonusTemplate_Call) RunAndReturn(run func(context.Context, string, string) error) *BonusTemplateService_DeleteBonusTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserBonusTemplates provides a mock function with given fields: ctx, username
func (_m *BonusTemplateService) GetUserBonusTemplates(ctx context.Context, username string) (*domain.BonusTemplateResponse, error) {
	ret := _m.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetUserBonusTemplates")
	}

	var r0 *domain.BonusTemplateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.BonusTemplateResponse, error)); ok {
		return rf(ctx, username)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.BonusTemplateResponse); ok {
		r0 = rf(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.BonusTemplateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BonusTemplateService_GetUserBonusTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserBonusTemplates'
type BonusTemplateService_GetUserBonusTemplates_Call struct {
	*mock.Call
}

// GetUserBonusTemplates is a helper method to define mock.On call
//   - ctx context.Context
//   - username string
func (_e *BonusTemplateService_Expecter) GetUserBonusTemplates(ctx interface{}, username interface{}) *BonusTemplateService_GetUserBonusTemplates_Call {
	return &BonusTemplateService_GetUserBonusTemplates_Call{Call: _e.mock.On("GetUserBonusTemplates", ctx, username)}
}

func (_c *BonusTemplateService_GetUserBonusTemplates_Call) Run(run func(ctx context.Context, username string)) *BonusTemplateService_GetUserBonusTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BonusTemplateService_GetUserBonusTemplates_Call) Return(_a0 *domain.BonusTemplateResponse, _a1 error) *BonusTemplateService_GetUserBonusTemplates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BonusTemplateService_GetUserBonusTemplates_Call) RunAndReturn(run func(context.Context, string) (*domain.BonusTemplateResponse, error)) *BonusTemplateService_GetUserBonusTemplates_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBonusTemplate provides a mock function with given fields: ctx, bonusTemplate
func (_m *BonusTemplateService) UpdateBonusTemplate(ctx context.Context, bonusTemplate *domain.UserBonusTemplate) error {
	ret := _m.Called(ctx, bonusTemplate)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBonusTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.UserBonusTemplate) error); ok {
		r0 = rf(ctx, bonusTemplate)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BonusTemplateService_UpdateBonusTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBonusTemplate'
type BonusTemplateService_UpdateBonusTemplate_Call struct {
	*mock.Call
}

// UpdateBonusTemplate is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusTemplate *domain.UserBonusTemplate
func (_e *BonusTemplateService_Expecter) UpdateBonusTemplate(ctx interface{}, bonusTemplate interface{}) *BonusTemplateService_UpdateBonusTemplate_Call {
	return &BonusTemplateService_UpdateBonusTemplate_Call{Call: _e.mock.On("UpdateBonusTemplate", ctx, bonusTemplate)}
}

func (_c *BonusTemplateService_UpdateBonusTemplate_Call) Run(run func(ctx context.Context, bonusTemplate *domain.UserBonusTemplate)) *BonusTemplateService_UpdateBonusTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.UserBonusTemplate))
	})
	return _c
}

func (_c *BonusTemplateService_UpdateBonusTemplate_Call) Return(_a0 error) *BonusTemplateService_UpdateBonusTemplate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BonusTemplateService_UpdateBonusTemplate_Call) RunAndReturn(run func(context.Context, *domain.UserBonusTemplate) error) *BonusTemplateService_UpdateBonusTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// NewBonusTemplateService creates a new instance of BonusTemplateService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBonusTemplateService(t interface {
	mock.TestingT
	Cleanup(func())
}) *BonusTemplateService {
	mock := &BonusTemplateService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
