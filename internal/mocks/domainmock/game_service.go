// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// GameService is an autogenerated mock type for the GameService type
type GameService struct {
	mock.Mock
}

type GameService_Expecter struct {
	mock *mock.Mock
}

func (_m *GameService) EXPECT() *GameService_Expecter {
	return &GameService_Expecter{mock: &_m.Mock}
}

// UpsertAllGames provides a mock function with given fields: ctx
func (_m *GameService) UpsertAllGames(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for UpsertAllGames")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GameService_UpsertAllGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertAllGames'
type GameService_UpsertAllGames_Call struct {
	*mock.Call
}

// UpsertAllGames is a helper method to define mock.On call
//   - ctx context.Context
func (_e *GameService_Expecter) UpsertAllGames(ctx interface{}) *GameService_UpsertAllGames_Call {
	return &GameService_UpsertAllGames_Call{Call: _e.mock.On("UpsertAllGames", ctx)}
}

func (_c *GameService_UpsertAllGames_Call) Run(run func(ctx context.Context)) *GameService_UpsertAllGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *GameService_UpsertAllGames_Call) Return(_a0 error) *GameService_UpsertAllGames_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GameService_UpsertAllGames_Call) RunAndReturn(run func(context.Context) error) *GameService_UpsertAllGames_Call {
	_c.Call.Return(run)
	return _c
}

// NewGameService creates a new instance of GameService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewGameService(t interface {
	mock.TestingT
	Cleanup(func())
}) *GameService {
	mock := &GameService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
