// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// BonusQueueService is an autogenerated mock type for the BonusQueueService type
type BonusQueueService struct {
	mock.Mock
}

type BonusQueueService_Expecter struct {
	mock *mock.Mock
}

func (_m *BonusQueueService) EXPECT() *BonusQueueService_Expecter {
	return &BonusQueueService_Expecter{mock: &_m.Mock}
}

// AddBonusToQueue provides a mock function with given fields: ctx, token, b
func (_m *BonusQueueService) AddBonusToQueue(ctx context.Context, token string, b domain.UserBonus) {
	_m.Called(ctx, token, b)
}

// BonusQueueService_AddBonusToQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddBonusToQueue'
type BonusQueueService_AddBonusToQueue_Call struct {
	*mock.Call
}

// AddBonusToQueue is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - b domain.UserBonus
func (_e *BonusQueueService_Expecter) AddBonusToQueue(ctx interface{}, token interface{}, b interface{}) *BonusQueueService_AddBonusToQueue_Call {
	return &BonusQueueService_AddBonusToQueue_Call{Call: _e.mock.On("AddBonusToQueue", ctx, token, b)}
}

func (_c *BonusQueueService_AddBonusToQueue_Call) Run(run func(ctx context.Context, token string, b domain.UserBonus)) *BonusQueueService_AddBonusToQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.UserBonus))
	})
	return _c
}

func (_c *BonusQueueService_AddBonusToQueue_Call) Return() *BonusQueueService_AddBonusToQueue_Call {
	_c.Call.Return()
	return _c
}

func (_c *BonusQueueService_AddBonusToQueue_Call) RunAndReturn(run func(context.Context, string, domain.UserBonus)) *BonusQueueService_AddBonusToQueue_Call {
	_c.Run(run)
	return _c
}

// StartBonusConsumer provides a mock function with no fields
func (_m *BonusQueueService) StartBonusConsumer() {
	_m.Called()
}

// BonusQueueService_StartBonusConsumer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartBonusConsumer'
type BonusQueueService_StartBonusConsumer_Call struct {
	*mock.Call
}

// StartBonusConsumer is a helper method to define mock.On call
func (_e *BonusQueueService_Expecter) StartBonusConsumer() *BonusQueueService_StartBonusConsumer_Call {
	return &BonusQueueService_StartBonusConsumer_Call{Call: _e.mock.On("StartBonusConsumer")}
}

func (_c *BonusQueueService_StartBonusConsumer_Call) Run(run func()) *BonusQueueService_StartBonusConsumer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *BonusQueueService_StartBonusConsumer_Call) Return() *BonusQueueService_StartBonusConsumer_Call {
	_c.Call.Return()
	return _c
}

func (_c *BonusQueueService_StartBonusConsumer_Call) RunAndReturn(run func()) *BonusQueueService_StartBonusConsumer_Call {
	_c.Run(run)
	return _c
}

// NewBonusQueueService creates a new instance of BonusQueueService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBonusQueueService(t interface {
	mock.TestingT
	Cleanup(func())
}) *BonusQueueService {
	mock := &BonusQueueService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
