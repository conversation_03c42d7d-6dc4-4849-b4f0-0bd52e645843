// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// TransactionRepository is an autogenerated mock type for the TransactionRepository type
type TransactionRepository struct {
	mock.Mock
}

type TransactionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *TransactionRepository) EXPECT() *TransactionRepository_Expecter {
	return &TransactionRepository_Expecter{mock: &_m.Mock}
}

// ExportTransactions provides a mock function with given fields: ctx, params
func (_m *TransactionRepository) ExportTransactions(ctx context.Context, params *domain.ExportTransactionsParams) ([]domain.Transaction, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ExportTransactions")
	}

	var r0 []domain.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportTransactionsParams) ([]domain.Transaction, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportTransactionsParams) []domain.Transaction); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.ExportTransactionsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_ExportTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportTransactions'
type TransactionRepository_ExportTransactions_Call struct {
	*mock.Call
}

// ExportTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.ExportTransactionsParams
func (_e *TransactionRepository_Expecter) ExportTransactions(ctx interface{}, params interface{}) *TransactionRepository_ExportTransactions_Call {
	return &TransactionRepository_ExportTransactions_Call{Call: _e.mock.On("ExportTransactions", ctx, params)}
}

func (_c *TransactionRepository_ExportTransactions_Call) Run(run func(ctx context.Context, params *domain.ExportTransactionsParams)) *TransactionRepository_ExportTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.ExportTransactionsParams))
	})
	return _c
}

func (_c *TransactionRepository_ExportTransactions_Call) Return(_a0 []domain.Transaction, _a1 error) *TransactionRepository_ExportTransactions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_ExportTransactions_Call) RunAndReturn(run func(context.Context, *domain.ExportTransactionsParams) ([]domain.Transaction, error)) *TransactionRepository_ExportTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetFirstCreditTransaction provides a mock function with given fields: ctx, userId
func (_m *TransactionRepository) GetFirstCreditTransaction(ctx context.Context, userId string) (float64, error) {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetFirstCreditTransaction")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (float64, error)); ok {
		return rf(ctx, userId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) float64); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_GetFirstCreditTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFirstCreditTransaction'
type TransactionRepository_GetFirstCreditTransaction_Call struct {
	*mock.Call
}

// GetFirstCreditTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
func (_e *TransactionRepository_Expecter) GetFirstCreditTransaction(ctx interface{}, userId interface{}) *TransactionRepository_GetFirstCreditTransaction_Call {
	return &TransactionRepository_GetFirstCreditTransaction_Call{Call: _e.mock.On("GetFirstCreditTransaction", ctx, userId)}
}

func (_c *TransactionRepository_GetFirstCreditTransaction_Call) Run(run func(ctx context.Context, userId string)) *TransactionRepository_GetFirstCreditTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *TransactionRepository_GetFirstCreditTransaction_Call) Return(_a0 float64, _a1 error) *TransactionRepository_GetFirstCreditTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_GetFirstCreditTransaction_Call) RunAndReturn(run func(context.Context, string) (float64, error)) *TransactionRepository_GetFirstCreditTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// GetTransactionByType provides a mock function with given fields: ctx, transType, category, currency, userId, offset, limit
func (_m *TransactionRepository) GetTransactionByType(ctx context.Context, transType string, category string, currency string, userId string, offset int, limit int) ([]domain.Transaction, error) {
	ret := _m.Called(ctx, transType, category, currency, userId, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetTransactionByType")
	}

	var r0 []domain.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, int, int) ([]domain.Transaction, error)); ok {
		return rf(ctx, transType, category, currency, userId, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, int, int) []domain.Transaction); ok {
		r0 = rf(ctx, transType, category, currency, userId, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string, int, int) error); ok {
		r1 = rf(ctx, transType, category, currency, userId, offset, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_GetTransactionByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTransactionByType'
type TransactionRepository_GetTransactionByType_Call struct {
	*mock.Call
}

// GetTransactionByType is a helper method to define mock.On call
//   - ctx context.Context
//   - transType string
//   - category string
//   - currency string
//   - userId string
//   - offset int
//   - limit int
func (_e *TransactionRepository_Expecter) GetTransactionByType(ctx interface{}, transType interface{}, category interface{}, currency interface{}, userId interface{}, offset interface{}, limit interface{}) *TransactionRepository_GetTransactionByType_Call {
	return &TransactionRepository_GetTransactionByType_Call{Call: _e.mock.On("GetTransactionByType", ctx, transType, category, currency, userId, offset, limit)}
}

func (_c *TransactionRepository_GetTransactionByType_Call) Run(run func(ctx context.Context, transType string, category string, currency string, userId string, offset int, limit int)) *TransactionRepository_GetTransactionByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string), args[5].(int), args[6].(int))
	})
	return _c
}

func (_c *TransactionRepository_GetTransactionByType_Call) Return(_a0 []domain.Transaction, _a1 error) *TransactionRepository_GetTransactionByType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_GetTransactionByType_Call) RunAndReturn(run func(context.Context, string, string, string, string, int, int) ([]domain.Transaction, error)) *TransactionRepository_GetTransactionByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetTransactions provides a mock function with given fields: ctx, params
func (_m *TransactionRepository) GetTransactions(ctx context.Context, params *domain.GetTransactionParams) (*domain.Transactions, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetTransactions")
	}

	var r0 *domain.Transactions
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetTransactionParams) (*domain.Transactions, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetTransactionParams) *domain.Transactions); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Transactions)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetTransactionParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_GetTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTransactions'
type TransactionRepository_GetTransactions_Call struct {
	*mock.Call
}

// GetTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetTransactionParams
func (_e *TransactionRepository_Expecter) GetTransactions(ctx interface{}, params interface{}) *TransactionRepository_GetTransactions_Call {
	return &TransactionRepository_GetTransactions_Call{Call: _e.mock.On("GetTransactions", ctx, params)}
}

func (_c *TransactionRepository_GetTransactions_Call) Run(run func(ctx context.Context, params *domain.GetTransactionParams)) *TransactionRepository_GetTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetTransactionParams))
	})
	return _c
}

func (_c *TransactionRepository_GetTransactions_Call) Return(_a0 *domain.Transactions, _a1 error) *TransactionRepository_GetTransactions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_GetTransactions_Call) RunAndReturn(run func(context.Context, *domain.GetTransactionParams) (*domain.Transactions, error)) *TransactionRepository_GetTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetWageringForInstantBonuses provides a mock function with given fields: ctx, category
func (_m *TransactionRepository) GetWageringForInstantBonuses(ctx context.Context, category string) (map[string]domain.BatchWageringData, string, error) {
	ret := _m.Called(ctx, category)

	if len(ret) == 0 {
		panic("no return value specified for GetWageringForInstantBonuses")
	}

	var r0 map[string]domain.BatchWageringData
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (map[string]domain.BatchWageringData, string, error)); ok {
		return rf(ctx, category)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) map[string]domain.BatchWageringData); ok {
		r0 = rf(ctx, category)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]domain.BatchWageringData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) string); ok {
		r1 = rf(ctx, category)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string) error); ok {
		r2 = rf(ctx, category)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// TransactionRepository_GetWageringForInstantBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWageringForInstantBonuses'
type TransactionRepository_GetWageringForInstantBonuses_Call struct {
	*mock.Call
}

// GetWageringForInstantBonuses is a helper method to define mock.On call
//   - ctx context.Context
//   - category string
func (_e *TransactionRepository_Expecter) GetWageringForInstantBonuses(ctx interface{}, category interface{}) *TransactionRepository_GetWageringForInstantBonuses_Call {
	return &TransactionRepository_GetWageringForInstantBonuses_Call{Call: _e.mock.On("GetWageringForInstantBonuses", ctx, category)}
}

func (_c *TransactionRepository_GetWageringForInstantBonuses_Call) Run(run func(ctx context.Context, category string)) *TransactionRepository_GetWageringForInstantBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *TransactionRepository_GetWageringForInstantBonuses_Call) Return(_a0 map[string]domain.BatchWageringData, _a1 string, _a2 error) *TransactionRepository_GetWageringForInstantBonuses_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *TransactionRepository_GetWageringForInstantBonuses_Call) RunAndReturn(run func(context.Context, string) (map[string]domain.BatchWageringData, string, error)) *TransactionRepository_GetWageringForInstantBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// HasOnlyOneCreditTransaction provides a mock function with given fields: ctx, userId
func (_m *TransactionRepository) HasOnlyOneCreditTransaction(ctx context.Context, userId uuid.UUID) (bool, error) {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for HasOnlyOneCreditTransaction")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (bool, error)); ok {
		return rf(ctx, userId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) bool); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_HasOnlyOneCreditTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HasOnlyOneCreditTransaction'
type TransactionRepository_HasOnlyOneCreditTransaction_Call struct {
	*mock.Call
}

// HasOnlyOneCreditTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - userId uuid.UUID
func (_e *TransactionRepository_Expecter) HasOnlyOneCreditTransaction(ctx interface{}, userId interface{}) *TransactionRepository_HasOnlyOneCreditTransaction_Call {
	return &TransactionRepository_HasOnlyOneCreditTransaction_Call{Call: _e.mock.On("HasOnlyOneCreditTransaction", ctx, userId)}
}

func (_c *TransactionRepository_HasOnlyOneCreditTransaction_Call) Run(run func(ctx context.Context, userId uuid.UUID)) *TransactionRepository_HasOnlyOneCreditTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *TransactionRepository_HasOnlyOneCreditTransaction_Call) Return(_a0 bool, _a1 error) *TransactionRepository_HasOnlyOneCreditTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_HasOnlyOneCreditTransaction_Call) RunAndReturn(run func(context.Context, uuid.UUID) (bool, error)) *TransactionRepository_HasOnlyOneCreditTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertTransaction provides a mock function with given fields: ctx, trans
func (_m *TransactionRepository) UpsertTransaction(ctx context.Context, trans *domain.Transaction) (*domain.Transaction, error) {
	ret := _m.Called(ctx, trans)

	if len(ret) == 0 {
		panic("no return value specified for UpsertTransaction")
	}

	var r0 *domain.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Transaction) (*domain.Transaction, error)); ok {
		return rf(ctx, trans)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Transaction) *domain.Transaction); ok {
		r0 = rf(ctx, trans)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.Transaction) error); ok {
		r1 = rf(ctx, trans)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransactionRepository_UpsertTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertTransaction'
type TransactionRepository_UpsertTransaction_Call struct {
	*mock.Call
}

// UpsertTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - trans *domain.Transaction
func (_e *TransactionRepository_Expecter) UpsertTransaction(ctx interface{}, trans interface{}) *TransactionRepository_UpsertTransaction_Call {
	return &TransactionRepository_UpsertTransaction_Call{Call: _e.mock.On("UpsertTransaction", ctx, trans)}
}

func (_c *TransactionRepository_UpsertTransaction_Call) Run(run func(ctx context.Context, trans *domain.Transaction)) *TransactionRepository_UpsertTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Transaction))
	})
	return _c
}

func (_c *TransactionRepository_UpsertTransaction_Call) Return(_a0 *domain.Transaction, _a1 error) *TransactionRepository_UpsertTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TransactionRepository_UpsertTransaction_Call) RunAndReturn(run func(context.Context, *domain.Transaction) (*domain.Transaction, error)) *TransactionRepository_UpsertTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// NewTransactionRepository creates a new instance of TransactionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTransactionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TransactionRepository {
	mock := &TransactionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
