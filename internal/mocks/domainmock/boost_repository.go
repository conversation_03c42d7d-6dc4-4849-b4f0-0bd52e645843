// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	time "time"
)

// BoostRepository is an autogenerated mock type for the BoostRepository type
type BoostRepository struct {
	mock.Mock
}

type BoostRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *BoostRepository) EXPECT() *BoostRepository_Expecter {
	return &BoostRepository_Expecter{mock: &_m.Mock}
}

// CreateBoosts provides a mock function with given fields: ctx, boost
func (_m *BoostRepository) CreateBoosts(ctx context.Context, boost []domain.Boost) error {
	ret := _m.Called(ctx, boost)

	if len(ret) == 0 {
		panic("no return value specified for CreateBoosts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []domain.Boost) error); ok {
		r0 = rf(ctx, boost)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BoostRepository_CreateBoosts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBoosts'
type BoostRepository_CreateBoosts_Call struct {
	*mock.Call
}

// CreateBoosts is a helper method to define mock.On call
//   - ctx context.Context
//   - boost []domain.Boost
func (_e *BoostRepository_Expecter) CreateBoosts(ctx interface{}, boost interface{}) *BoostRepository_CreateBoosts_Call {
	return &BoostRepository_CreateBoosts_Call{Call: _e.mock.On("CreateBoosts", ctx, boost)}
}

func (_c *BoostRepository_CreateBoosts_Call) Run(run func(ctx context.Context, boost []domain.Boost)) *BoostRepository_CreateBoosts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]domain.Boost))
	})
	return _c
}

func (_c *BoostRepository_CreateBoosts_Call) Return(_a0 error) *BoostRepository_CreateBoosts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BoostRepository_CreateBoosts_Call) RunAndReturn(run func(context.Context, []domain.Boost) error) *BoostRepository_CreateBoosts_Call {
	_c.Call.Return(run)
	return _c
}

// GetActiveBoostByUserID provides a mock function with given fields: ctx, userID, targetTime
func (_m *BoostRepository) GetActiveBoostByUserID(ctx context.Context, userID string, targetTime time.Time) (*domain.Boost, error) {
	ret := _m.Called(ctx, userID, targetTime)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveBoostByUserID")
	}

	var r0 *domain.Boost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time) (*domain.Boost, error)); ok {
		return rf(ctx, userID, targetTime)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time) *domain.Boost); ok {
		r0 = rf(ctx, userID, targetTime)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Boost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, time.Time) error); ok {
		r1 = rf(ctx, userID, targetTime)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BoostRepository_GetActiveBoostByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActiveBoostByUserID'
type BoostRepository_GetActiveBoostByUserID_Call struct {
	*mock.Call
}

// GetActiveBoostByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - targetTime time.Time
func (_e *BoostRepository_Expecter) GetActiveBoostByUserID(ctx interface{}, userID interface{}, targetTime interface{}) *BoostRepository_GetActiveBoostByUserID_Call {
	return &BoostRepository_GetActiveBoostByUserID_Call{Call: _e.mock.On("GetActiveBoostByUserID", ctx, userID, targetTime)}
}

func (_c *BoostRepository_GetActiveBoostByUserID_Call) Run(run func(ctx context.Context, userID string, targetTime time.Time)) *BoostRepository_GetActiveBoostByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Time))
	})
	return _c
}

func (_c *BoostRepository_GetActiveBoostByUserID_Call) Return(_a0 *domain.Boost, _a1 error) *BoostRepository_GetActiveBoostByUserID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BoostRepository_GetActiveBoostByUserID_Call) RunAndReturn(run func(context.Context, string, time.Time) (*domain.Boost, error)) *BoostRepository_GetActiveBoostByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetAvailableBoostByUserID provides a mock function with given fields: ctx, userID, targetTime, includeStarted
func (_m *BoostRepository) GetAvailableBoostByUserID(ctx context.Context, userID string, targetTime time.Time, includeStarted bool) (*domain.Boost, error) {
	ret := _m.Called(ctx, userID, targetTime, includeStarted)

	if len(ret) == 0 {
		panic("no return value specified for GetAvailableBoostByUserID")
	}

	var r0 *domain.Boost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, bool) (*domain.Boost, error)); ok {
		return rf(ctx, userID, targetTime, includeStarted)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, bool) *domain.Boost); ok {
		r0 = rf(ctx, userID, targetTime, includeStarted)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Boost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, time.Time, bool) error); ok {
		r1 = rf(ctx, userID, targetTime, includeStarted)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BoostRepository_GetAvailableBoostByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAvailableBoostByUserID'
type BoostRepository_GetAvailableBoostByUserID_Call struct {
	*mock.Call
}

// GetAvailableBoostByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - targetTime time.Time
//   - includeStarted bool
func (_e *BoostRepository_Expecter) GetAvailableBoostByUserID(ctx interface{}, userID interface{}, targetTime interface{}, includeStarted interface{}) *BoostRepository_GetAvailableBoostByUserID_Call {
	return &BoostRepository_GetAvailableBoostByUserID_Call{Call: _e.mock.On("GetAvailableBoostByUserID", ctx, userID, targetTime, includeStarted)}
}

func (_c *BoostRepository_GetAvailableBoostByUserID_Call) Run(run func(ctx context.Context, userID string, targetTime time.Time, includeStarted bool)) *BoostRepository_GetAvailableBoostByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Time), args[3].(bool))
	})
	return _c
}

func (_c *BoostRepository_GetAvailableBoostByUserID_Call) Return(_a0 *domain.Boost, _a1 error) *BoostRepository_GetAvailableBoostByUserID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BoostRepository_GetAvailableBoostByUserID_Call) RunAndReturn(run func(context.Context, string, time.Time, bool) (*domain.Boost, error)) *BoostRepository_GetAvailableBoostByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetOverlapingBoostByUserID provides a mock function with given fields: ctx, userID, startTime, finishTime
func (_m *BoostRepository) GetOverlapingBoostByUserID(ctx context.Context, userID string, startTime time.Time, finishTime time.Time) (*domain.Boost, error) {
	ret := _m.Called(ctx, userID, startTime, finishTime)

	if len(ret) == 0 {
		panic("no return value specified for GetOverlapingBoostByUserID")
	}

	var r0 *domain.Boost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, time.Time) (*domain.Boost, error)); ok {
		return rf(ctx, userID, startTime, finishTime)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time, time.Time) *domain.Boost); ok {
		r0 = rf(ctx, userID, startTime, finishTime)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Boost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, time.Time, time.Time) error); ok {
		r1 = rf(ctx, userID, startTime, finishTime)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BoostRepository_GetOverlapingBoostByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOverlapingBoostByUserID'
type BoostRepository_GetOverlapingBoostByUserID_Call struct {
	*mock.Call
}

// GetOverlapingBoostByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - startTime time.Time
//   - finishTime time.Time
func (_e *BoostRepository_Expecter) GetOverlapingBoostByUserID(ctx interface{}, userID interface{}, startTime interface{}, finishTime interface{}) *BoostRepository_GetOverlapingBoostByUserID_Call {
	return &BoostRepository_GetOverlapingBoostByUserID_Call{Call: _e.mock.On("GetOverlapingBoostByUserID", ctx, userID, startTime, finishTime)}
}

func (_c *BoostRepository_GetOverlapingBoostByUserID_Call) Run(run func(ctx context.Context, userID string, startTime time.Time, finishTime time.Time)) *BoostRepository_GetOverlapingBoostByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(time.Time), args[3].(time.Time))
	})
	return _c
}

func (_c *BoostRepository_GetOverlapingBoostByUserID_Call) Return(_a0 *domain.Boost, _a1 error) *BoostRepository_GetOverlapingBoostByUserID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BoostRepository_GetOverlapingBoostByUserID_Call) RunAndReturn(run func(context.Context, string, time.Time, time.Time) (*domain.Boost, error)) *BoostRepository_GetOverlapingBoostByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBoosts provides a mock function with given fields: ctx, boost
func (_m *BoostRepository) UpdateBoosts(ctx context.Context, boost domain.Boost) error {
	ret := _m.Called(ctx, boost)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBoosts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, domain.Boost) error); ok {
		r0 = rf(ctx, boost)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BoostRepository_UpdateBoosts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBoosts'
type BoostRepository_UpdateBoosts_Call struct {
	*mock.Call
}

// UpdateBoosts is a helper method to define mock.On call
//   - ctx context.Context
//   - boost domain.Boost
func (_e *BoostRepository_Expecter) UpdateBoosts(ctx interface{}, boost interface{}) *BoostRepository_UpdateBoosts_Call {
	return &BoostRepository_UpdateBoosts_Call{Call: _e.mock.On("UpdateBoosts", ctx, boost)}
}

func (_c *BoostRepository_UpdateBoosts_Call) Run(run func(ctx context.Context, boost domain.Boost)) *BoostRepository_UpdateBoosts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(domain.Boost))
	})
	return _c
}

func (_c *BoostRepository_UpdateBoosts_Call) Return(_a0 error) *BoostRepository_UpdateBoosts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BoostRepository_UpdateBoosts_Call) RunAndReturn(run func(context.Context, domain.Boost) error) *BoostRepository_UpdateBoosts_Call {
	_c.Call.Return(run)
	return _c
}

// NewBoostRepository creates a new instance of BoostRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBoostRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *BoostRepository {
	mock := &BoostRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
