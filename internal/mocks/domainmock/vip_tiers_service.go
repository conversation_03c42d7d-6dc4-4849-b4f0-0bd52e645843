// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// VIPTiersService is an autogenerated mock type for the VIPTiersService type
type VIPTiersService struct {
	mock.Mock
}

type VIPTiersService_Expecter struct {
	mock *mock.Mock
}

func (_m *VIPTiersService) EXPECT() *VIPTiersService_Expecter {
	return &VIPTiersService_Expecter{mock: &_m.Mock}
}

// GetVIPTierByName provides a mock function with given fields: name
func (_m *VIPTiersService) GetVIPTierByName(name string) (*domain.VIPTiersThreshold, bool) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetVIPTierByName")
	}

	var r0 *domain.VIPTiersThreshold
	var r1 bool
	if rf, ok := ret.Get(0).(func(string) (*domain.VIPTiersThreshold, bool)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) *domain.VIPTiersThreshold); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.VIPTiersThreshold)
		}
	}

	if rf, ok := ret.Get(1).(func(string) bool); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// VIPTiersService_GetVIPTierByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetVIPTierByName'
type VIPTiersService_GetVIPTierByName_Call struct {
	*mock.Call
}

// GetVIPTierByName is a helper method to define mock.On call
//   - name string
func (_e *VIPTiersService_Expecter) GetVIPTierByName(name interface{}) *VIPTiersService_GetVIPTierByName_Call {
	return &VIPTiersService_GetVIPTierByName_Call{Call: _e.mock.On("GetVIPTierByName", name)}
}

func (_c *VIPTiersService_GetVIPTierByName_Call) Run(run func(name string)) *VIPTiersService_GetVIPTierByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *VIPTiersService_GetVIPTierByName_Call) Return(_a0 *domain.VIPTiersThreshold, _a1 bool) *VIPTiersService_GetVIPTierByName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VIPTiersService_GetVIPTierByName_Call) RunAndReturn(run func(string) (*domain.VIPTiersThreshold, bool)) *VIPTiersService_GetVIPTierByName_Call {
	_c.Call.Return(run)
	return _c
}

// NewVIPTiersService creates a new instance of VIPTiersService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewVIPTiersService(t interface {
	mock.TestingT
	Cleanup(func())
}) *VIPTiersService {
	mock := &VIPTiersService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
