// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// SettingsRepository is an autogenerated mock type for the SettingsRepository type
type SettingsRepository struct {
	mock.Mock
}

type SettingsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *SettingsRepository) EXPECT() *SettingsRepository_Expecter {
	return &SettingsRepository_Expecter{mock: &_m.Mock}
}

// CreateSettings provides a mock function with given fields: ctx, userID, settings
func (_m *SettingsRepository) CreateSettings(ctx context.Context, userID string, settings domain.UserSettingsPayload) (domain.SettingsResponse, error) {
	ret := _m.Called(ctx, userID, settings)

	if len(ret) == 0 {
		panic("no return value specified for CreateSettings")
	}

	var r0 domain.SettingsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserSettingsPayload) (domain.SettingsResponse, error)); ok {
		return rf(ctx, userID, settings)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserSettingsPayload) domain.SettingsResponse); ok {
		r0 = rf(ctx, userID, settings)
	} else {
		r0 = ret.Get(0).(domain.SettingsResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, domain.UserSettingsPayload) error); ok {
		r1 = rf(ctx, userID, settings)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SettingsRepository_CreateSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSettings'
type SettingsRepository_CreateSettings_Call struct {
	*mock.Call
}

// CreateSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - settings domain.UserSettingsPayload
func (_e *SettingsRepository_Expecter) CreateSettings(ctx interface{}, userID interface{}, settings interface{}) *SettingsRepository_CreateSettings_Call {
	return &SettingsRepository_CreateSettings_Call{Call: _e.mock.On("CreateSettings", ctx, userID, settings)}
}

func (_c *SettingsRepository_CreateSettings_Call) Run(run func(ctx context.Context, userID string, settings domain.UserSettingsPayload)) *SettingsRepository_CreateSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.UserSettingsPayload))
	})
	return _c
}

func (_c *SettingsRepository_CreateSettings_Call) Return(_a0 domain.SettingsResponse, _a1 error) *SettingsRepository_CreateSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SettingsRepository_CreateSettings_Call) RunAndReturn(run func(context.Context, string, domain.UserSettingsPayload) (domain.SettingsResponse, error)) *SettingsRepository_CreateSettings_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSettings provides a mock function with given fields: userID, key
func (_m *SettingsRepository) DeleteSettings(userID string, key string) error {
	ret := _m.Called(userID, key)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(userID, key)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SettingsRepository_DeleteSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSettings'
type SettingsRepository_DeleteSettings_Call struct {
	*mock.Call
}

// DeleteSettings is a helper method to define mock.On call
//   - userID string
//   - key string
func (_e *SettingsRepository_Expecter) DeleteSettings(userID interface{}, key interface{}) *SettingsRepository_DeleteSettings_Call {
	return &SettingsRepository_DeleteSettings_Call{Call: _e.mock.On("DeleteSettings", userID, key)}
}

func (_c *SettingsRepository_DeleteSettings_Call) Run(run func(userID string, key string)) *SettingsRepository_DeleteSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *SettingsRepository_DeleteSettings_Call) Return(_a0 error) *SettingsRepository_DeleteSettings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SettingsRepository_DeleteSettings_Call) RunAndReturn(run func(string, string) error) *SettingsRepository_DeleteSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetSettings provides a mock function with given fields: userID
func (_m *SettingsRepository) GetSettings(userID string) (domain.SettingsResponse, error) {
	ret := _m.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GetSettings")
	}

	var r0 domain.SettingsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.SettingsResponse, error)); ok {
		return rf(userID)
	}
	if rf, ok := ret.Get(0).(func(string) domain.SettingsResponse); ok {
		r0 = rf(userID)
	} else {
		r0 = ret.Get(0).(domain.SettingsResponse)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SettingsRepository_GetSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSettings'
type SettingsRepository_GetSettings_Call struct {
	*mock.Call
}

// GetSettings is a helper method to define mock.On call
//   - userID string
func (_e *SettingsRepository_Expecter) GetSettings(userID interface{}) *SettingsRepository_GetSettings_Call {
	return &SettingsRepository_GetSettings_Call{Call: _e.mock.On("GetSettings", userID)}
}

func (_c *SettingsRepository_GetSettings_Call) Run(run func(userID string)) *SettingsRepository_GetSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *SettingsRepository_GetSettings_Call) Return(_a0 domain.SettingsResponse, _a1 error) *SettingsRepository_GetSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SettingsRepository_GetSettings_Call) RunAndReturn(run func(string) (domain.SettingsResponse, error)) *SettingsRepository_GetSettings_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSettings provides a mock function with given fields: userID, settings
func (_m *SettingsRepository) UpdateSettings(userID string, settings domain.UserSettings) error {
	ret := _m.Called(userID, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, domain.UserSettings) error); ok {
		r0 = rf(userID, settings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SettingsRepository_UpdateSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSettings'
type SettingsRepository_UpdateSettings_Call struct {
	*mock.Call
}

// UpdateSettings is a helper method to define mock.On call
//   - userID string
//   - settings domain.UserSettings
func (_e *SettingsRepository_Expecter) UpdateSettings(userID interface{}, settings interface{}) *SettingsRepository_UpdateSettings_Call {
	return &SettingsRepository_UpdateSettings_Call{Call: _e.mock.On("UpdateSettings", userID, settings)}
}

func (_c *SettingsRepository_UpdateSettings_Call) Run(run func(userID string, settings domain.UserSettings)) *SettingsRepository_UpdateSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(domain.UserSettings))
	})
	return _c
}

func (_c *SettingsRepository_UpdateSettings_Call) Return(_a0 error) *SettingsRepository_UpdateSettings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SettingsRepository_UpdateSettings_Call) RunAndReturn(run func(string, domain.UserSettings) error) *SettingsRepository_UpdateSettings_Call {
	_c.Call.Return(run)
	return _c
}

// NewSettingsRepository creates a new instance of SettingsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSettingsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SettingsRepository {
	mock := &SettingsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
