// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// BonusDropRepository is an autogenerated mock type for the BonusDropRepository type
type BonusDropRepository struct {
	mock.Mock
}

type BonusDropRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *BonusDropRepository) EXPECT() *BonusDropRepository_Expecter {
	return &BonusDropRepository_Expecter{mock: &_m.Mock}
}

// RedeemBonusDrop provides a mock function with given fields: ctx, userId, username, bonusCode, token
func (_m *BonusDropRepository) RedeemBonusDrop(ctx context.Context, userId string, username string, bonusCode string, token string) (domain.RedeemBonusResponse, error) {
	ret := _m.Called(ctx, userId, username, bonusCode, token)

	if len(ret) == 0 {
		panic("no return value specified for RedeemBonusDrop")
	}

	var r0 domain.RedeemBonusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (domain.RedeemBonusResponse, error)); ok {
		return rf(ctx, userId, username, bonusCode, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) domain.RedeemBonusResponse); ok {
		r0 = rf(ctx, userId, username, bonusCode, token)
	} else {
		r0 = ret.Get(0).(domain.RedeemBonusResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, userId, username, bonusCode, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BonusDropRepository_RedeemBonusDrop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RedeemBonusDrop'
type BonusDropRepository_RedeemBonusDrop_Call struct {
	*mock.Call
}

// RedeemBonusDrop is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - username string
//   - bonusCode string
//   - token string
func (_e *BonusDropRepository_Expecter) RedeemBonusDrop(ctx interface{}, userId interface{}, username interface{}, bonusCode interface{}, token interface{}) *BonusDropRepository_RedeemBonusDrop_Call {
	return &BonusDropRepository_RedeemBonusDrop_Call{Call: _e.mock.On("RedeemBonusDrop", ctx, userId, username, bonusCode, token)}
}

func (_c *BonusDropRepository_RedeemBonusDrop_Call) Run(run func(ctx context.Context, userId string, username string, bonusCode string, token string)) *BonusDropRepository_RedeemBonusDrop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string))
	})
	return _c
}

func (_c *BonusDropRepository_RedeemBonusDrop_Call) Return(_a0 domain.RedeemBonusResponse, _a1 error) *BonusDropRepository_RedeemBonusDrop_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BonusDropRepository_RedeemBonusDrop_Call) RunAndReturn(run func(context.Context, string, string, string, string) (domain.RedeemBonusResponse, error)) *BonusDropRepository_RedeemBonusDrop_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertBonusDrop provides a mock function with given fields: ctx, bonusDrop
func (_m *BonusDropRepository) UpsertBonusDrop(ctx context.Context, bonusDrop *domain.BonusDrop) error {
	ret := _m.Called(ctx, bonusDrop)

	if len(ret) == 0 {
		panic("no return value specified for UpsertBonusDrop")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.BonusDrop) error); ok {
		r0 = rf(ctx, bonusDrop)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BonusDropRepository_UpsertBonusDrop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertBonusDrop'
type BonusDropRepository_UpsertBonusDrop_Call struct {
	*mock.Call
}

// UpsertBonusDrop is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusDrop *domain.BonusDrop
func (_e *BonusDropRepository_Expecter) UpsertBonusDrop(ctx interface{}, bonusDrop interface{}) *BonusDropRepository_UpsertBonusDrop_Call {
	return &BonusDropRepository_UpsertBonusDrop_Call{Call: _e.mock.On("UpsertBonusDrop", ctx, bonusDrop)}
}

func (_c *BonusDropRepository_UpsertBonusDrop_Call) Run(run func(ctx context.Context, bonusDrop *domain.BonusDrop)) *BonusDropRepository_UpsertBonusDrop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.BonusDrop))
	})
	return _c
}

func (_c *BonusDropRepository_UpsertBonusDrop_Call) Return(_a0 error) *BonusDropRepository_UpsertBonusDrop_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BonusDropRepository_UpsertBonusDrop_Call) RunAndReturn(run func(context.Context, *domain.BonusDrop) error) *BonusDropRepository_UpsertBonusDrop_Call {
	_c.Call.Return(run)
	return _c
}

// NewBonusDropRepository creates a new instance of BonusDropRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBonusDropRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *BonusDropRepository {
	mock := &BonusDropRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
