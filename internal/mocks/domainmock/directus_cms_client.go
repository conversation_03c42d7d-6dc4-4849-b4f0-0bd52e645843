// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	time "time"
)

// DirectusCMSClient is an autogenerated mock type for the DirectusCMSClient type
type DirectusCMSClient struct {
	mock.Mock
}

type DirectusCMSClient_Expecter struct {
	mock *mock.Mock
}

func (_m *DirectusCMSClient) EXPECT() *DirectusCMSClient_Expecter {
	return &DirectusCMSClient_Expecter{mock: &_m.Mock}
}

// CheckExistingTierUpgradeBonus provides a mock function with given fields: externalID, reason
func (_m *DirectusCMSClient) CheckExistingTierUpgradeBonus(externalID string, reason string) (bool, error) {
	ret := _m.Called(externalID, reason)

	if len(ret) == 0 {
		panic("no return value specified for CheckExistingTierUpgradeBonus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (bool, error)); ok {
		return rf(externalID, reason)
	}
	if rf, ok := ret.Get(0).(func(string, string) bool); ok {
		r0 = rf(externalID, reason)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(externalID, reason)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_CheckExistingTierUpgradeBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckExistingTierUpgradeBonus'
type DirectusCMSClient_CheckExistingTierUpgradeBonus_Call struct {
	*mock.Call
}

// CheckExistingTierUpgradeBonus is a helper method to define mock.On call
//   - externalID string
//   - reason string
func (_e *DirectusCMSClient_Expecter) CheckExistingTierUpgradeBonus(externalID interface{}, reason interface{}) *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call {
	return &DirectusCMSClient_CheckExistingTierUpgradeBonus_Call{Call: _e.mock.On("CheckExistingTierUpgradeBonus", externalID, reason)}
}

func (_c *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call) Run(run func(externalID string, reason string)) *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call) Return(_a0 bool, _a1 error) *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call) RunAndReturn(run func(string, string) (bool, error)) *DirectusCMSClient_CheckExistingTierUpgradeBonus_Call {
	_c.Call.Return(run)
	return _c
}

// ClaimBonus provides a mock function with given fields: externalID, category, id
func (_m *DirectusCMSClient) ClaimBonus(externalID string, category string, id int) (bool, error) {
	ret := _m.Called(externalID, category, id)

	if len(ret) == 0 {
		panic("no return value specified for ClaimBonus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, int) (bool, error)); ok {
		return rf(externalID, category, id)
	}
	if rf, ok := ret.Get(0).(func(string, string, int) bool); ok {
		r0 = rf(externalID, category, id)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string, int) error); ok {
		r1 = rf(externalID, category, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_ClaimBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClaimBonus'
type DirectusCMSClient_ClaimBonus_Call struct {
	*mock.Call
}

// ClaimBonus is a helper method to define mock.On call
//   - externalID string
//   - category string
//   - id int
func (_e *DirectusCMSClient_Expecter) ClaimBonus(externalID interface{}, category interface{}, id interface{}) *DirectusCMSClient_ClaimBonus_Call {
	return &DirectusCMSClient_ClaimBonus_Call{Call: _e.mock.On("ClaimBonus", externalID, category, id)}
}

func (_c *DirectusCMSClient_ClaimBonus_Call) Run(run func(externalID string, category string, id int)) *DirectusCMSClient_ClaimBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(int))
	})
	return _c
}

func (_c *DirectusCMSClient_ClaimBonus_Call) Return(_a0 bool, _a1 error) *DirectusCMSClient_ClaimBonus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_ClaimBonus_Call) RunAndReturn(run func(string, string, int) (bool, error)) *DirectusCMSClient_ClaimBonus_Call {
	_c.Call.Return(run)
	return _c
}

// ClaimMultipleBonuses provides a mock function with given fields: userExternalID, categories
func (_m *DirectusCMSClient) ClaimMultipleBonuses(userExternalID string, categories []string) (bool, error) {
	ret := _m.Called(userExternalID, categories)

	if len(ret) == 0 {
		panic("no return value specified for ClaimMultipleBonuses")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, []string) (bool, error)); ok {
		return rf(userExternalID, categories)
	}
	if rf, ok := ret.Get(0).(func(string, []string) bool); ok {
		r0 = rf(userExternalID, categories)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, []string) error); ok {
		r1 = rf(userExternalID, categories)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_ClaimMultipleBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClaimMultipleBonuses'
type DirectusCMSClient_ClaimMultipleBonuses_Call struct {
	*mock.Call
}

// ClaimMultipleBonuses is a helper method to define mock.On call
//   - userExternalID string
//   - categories []string
func (_e *DirectusCMSClient_Expecter) ClaimMultipleBonuses(userExternalID interface{}, categories interface{}) *DirectusCMSClient_ClaimMultipleBonuses_Call {
	return &DirectusCMSClient_ClaimMultipleBonuses_Call{Call: _e.mock.On("ClaimMultipleBonuses", userExternalID, categories)}
}

func (_c *DirectusCMSClient_ClaimMultipleBonuses_Call) Run(run func(userExternalID string, categories []string)) *DirectusCMSClient_ClaimMultipleBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].([]string))
	})
	return _c
}

func (_c *DirectusCMSClient_ClaimMultipleBonuses_Call) Return(_a0 bool, _a1 error) *DirectusCMSClient_ClaimMultipleBonuses_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_ClaimMultipleBonuses_Call) RunAndReturn(run func(string, []string) (bool, error)) *DirectusCMSClient_ClaimMultipleBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// ClaimReloadBonus provides a mock function with given fields: externalID, availableOn
func (_m *DirectusCMSClient) ClaimReloadBonus(externalID string, availableOn time.Time) (bool, error) {
	ret := _m.Called(externalID, availableOn)

	if len(ret) == 0 {
		panic("no return value specified for ClaimReloadBonus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, time.Time) (bool, error)); ok {
		return rf(externalID, availableOn)
	}
	if rf, ok := ret.Get(0).(func(string, time.Time) bool); ok {
		r0 = rf(externalID, availableOn)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, time.Time) error); ok {
		r1 = rf(externalID, availableOn)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_ClaimReloadBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClaimReloadBonus'
type DirectusCMSClient_ClaimReloadBonus_Call struct {
	*mock.Call
}

// ClaimReloadBonus is a helper method to define mock.On call
//   - externalID string
//   - availableOn time.Time
func (_e *DirectusCMSClient_Expecter) ClaimReloadBonus(externalID interface{}, availableOn interface{}) *DirectusCMSClient_ClaimReloadBonus_Call {
	return &DirectusCMSClient_ClaimReloadBonus_Call{Call: _e.mock.On("ClaimReloadBonus", externalID, availableOn)}
}

func (_c *DirectusCMSClient_ClaimReloadBonus_Call) Run(run func(externalID string, availableOn time.Time)) *DirectusCMSClient_ClaimReloadBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(time.Time))
	})
	return _c
}

func (_c *DirectusCMSClient_ClaimReloadBonus_Call) Return(_a0 bool, _a1 error) *DirectusCMSClient_ClaimReloadBonus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_ClaimReloadBonus_Call) RunAndReturn(run func(string, time.Time) (bool, error)) *DirectusCMSClient_ClaimReloadBonus_Call {
	_c.Call.Return(run)
	return _c
}

// CreateCampaignInDirectus provides a mock function with given fields: referralCampaign
func (_m *DirectusCMSClient) CreateCampaignInDirectus(referralCampaign domain.ReferralCampaign) error {
	ret := _m.Called(referralCampaign)

	if len(ret) == 0 {
		panic("no return value specified for CreateCampaignInDirectus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.ReferralCampaign) error); ok {
		r0 = rf(referralCampaign)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_CreateCampaignInDirectus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateCampaignInDirectus'
type DirectusCMSClient_CreateCampaignInDirectus_Call struct {
	*mock.Call
}

// CreateCampaignInDirectus is a helper method to define mock.On call
//   - referralCampaign domain.ReferralCampaign
func (_e *DirectusCMSClient_Expecter) CreateCampaignInDirectus(referralCampaign interface{}) *DirectusCMSClient_CreateCampaignInDirectus_Call {
	return &DirectusCMSClient_CreateCampaignInDirectus_Call{Call: _e.mock.On("CreateCampaignInDirectus", referralCampaign)}
}

func (_c *DirectusCMSClient_CreateCampaignInDirectus_Call) Run(run func(referralCampaign domain.ReferralCampaign)) *DirectusCMSClient_CreateCampaignInDirectus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.ReferralCampaign))
	})
	return _c
}

func (_c *DirectusCMSClient_CreateCampaignInDirectus_Call) Return(_a0 error) *DirectusCMSClient_CreateCampaignInDirectus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_CreateCampaignInDirectus_Call) RunAndReturn(run func(domain.ReferralCampaign) error) *DirectusCMSClient_CreateCampaignInDirectus_Call {
	_c.Call.Return(run)
	return _c
}

// CreateDefaultCampaign provides a mock function with given fields: ctx, userID, campaignName, code, username, parentId
func (_m *DirectusCMSClient) CreateDefaultCampaign(ctx context.Context, userID string, campaignName string, code string, username string, parentId string) error {
	ret := _m.Called(ctx, userID, campaignName, code, username, parentId)

	if len(ret) == 0 {
		panic("no return value specified for CreateDefaultCampaign")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string, string) error); ok {
		r0 = rf(ctx, userID, campaignName, code, username, parentId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_CreateDefaultCampaign_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateDefaultCampaign'
type DirectusCMSClient_CreateDefaultCampaign_Call struct {
	*mock.Call
}

// CreateDefaultCampaign is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - campaignName string
//   - code string
//   - username string
//   - parentId string
func (_e *DirectusCMSClient_Expecter) CreateDefaultCampaign(ctx interface{}, userID interface{}, campaignName interface{}, code interface{}, username interface{}, parentId interface{}) *DirectusCMSClient_CreateDefaultCampaign_Call {
	return &DirectusCMSClient_CreateDefaultCampaign_Call{Call: _e.mock.On("CreateDefaultCampaign", ctx, userID, campaignName, code, username, parentId)}
}

func (_c *DirectusCMSClient_CreateDefaultCampaign_Call) Run(run func(ctx context.Context, userID string, campaignName string, code string, username string, parentId string)) *DirectusCMSClient_CreateDefaultCampaign_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(string), args[5].(string))
	})
	return _c
}

func (_c *DirectusCMSClient_CreateDefaultCampaign_Call) Return(_a0 error) *DirectusCMSClient_CreateDefaultCampaign_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_CreateDefaultCampaign_Call) RunAndReturn(run func(context.Context, string, string, string, string, string) error) *DirectusCMSClient_CreateDefaultCampaign_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFirstCampaignOfUser provides a mock function with given fields: data
func (_m *DirectusCMSClient) CreateFirstCampaignOfUser(data domain.ReferralCampaign) error {
	ret := _m.Called(data)

	if len(ret) == 0 {
		panic("no return value specified for CreateFirstCampaignOfUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.ReferralCampaign) error); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_CreateFirstCampaignOfUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFirstCampaignOfUser'
type DirectusCMSClient_CreateFirstCampaignOfUser_Call struct {
	*mock.Call
}

// CreateFirstCampaignOfUser is a helper method to define mock.On call
//   - data domain.ReferralCampaign
func (_e *DirectusCMSClient_Expecter) CreateFirstCampaignOfUser(data interface{}) *DirectusCMSClient_CreateFirstCampaignOfUser_Call {
	return &DirectusCMSClient_CreateFirstCampaignOfUser_Call{Call: _e.mock.On("CreateFirstCampaignOfUser", data)}
}

func (_c *DirectusCMSClient_CreateFirstCampaignOfUser_Call) Run(run func(data domain.ReferralCampaign)) *DirectusCMSClient_CreateFirstCampaignOfUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.ReferralCampaign))
	})
	return _c
}

func (_c *DirectusCMSClient_CreateFirstCampaignOfUser_Call) Return(_a0 error) *DirectusCMSClient_CreateFirstCampaignOfUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_CreateFirstCampaignOfUser_Call) RunAndReturn(run func(domain.ReferralCampaign) error) *DirectusCMSClient_CreateFirstCampaignOfUser_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserBonus provides a mock function with given fields: data
func (_m *DirectusCMSClient) CreateUserBonus(data domain.UserBonusInDirectus) (int, error) {
	ret := _m.Called(data)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserBonus")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(domain.UserBonusInDirectus) (int, error)); ok {
		return rf(data)
	}
	if rf, ok := ret.Get(0).(func(domain.UserBonusInDirectus) int); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(domain.UserBonusInDirectus) error); ok {
		r1 = rf(data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_CreateUserBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserBonus'
type DirectusCMSClient_CreateUserBonus_Call struct {
	*mock.Call
}

// CreateUserBonus is a helper method to define mock.On call
//   - data domain.UserBonusInDirectus
func (_e *DirectusCMSClient_Expecter) CreateUserBonus(data interface{}) *DirectusCMSClient_CreateUserBonus_Call {
	return &DirectusCMSClient_CreateUserBonus_Call{Call: _e.mock.On("CreateUserBonus", data)}
}

func (_c *DirectusCMSClient_CreateUserBonus_Call) Run(run func(data domain.UserBonusInDirectus)) *DirectusCMSClient_CreateUserBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.UserBonusInDirectus))
	})
	return _c
}

func (_c *DirectusCMSClient_CreateUserBonus_Call) Return(_a0 int, _a1 error) *DirectusCMSClient_CreateUserBonus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_CreateUserBonus_Call) RunAndReturn(run func(domain.UserBonusInDirectus) (int, error)) *DirectusCMSClient_CreateUserBonus_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserBonusesBatch provides a mock function with given fields: data
func (_m *DirectusCMSClient) CreateUserBonusesBatch(data []domain.UserBonusInDirectus) ([]int, error) {
	ret := _m.Called(data)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserBonusesBatch")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]domain.UserBonusInDirectus) ([]int, error)); ok {
		return rf(data)
	}
	if rf, ok := ret.Get(0).(func([]domain.UserBonusInDirectus) []int); ok {
		r0 = rf(data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]domain.UserBonusInDirectus) error); ok {
		r1 = rf(data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_CreateUserBonusesBatch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserBonusesBatch'
type DirectusCMSClient_CreateUserBonusesBatch_Call struct {
	*mock.Call
}

// CreateUserBonusesBatch is a helper method to define mock.On call
//   - data []domain.UserBonusInDirectus
func (_e *DirectusCMSClient_Expecter) CreateUserBonusesBatch(data interface{}) *DirectusCMSClient_CreateUserBonusesBatch_Call {
	return &DirectusCMSClient_CreateUserBonusesBatch_Call{Call: _e.mock.On("CreateUserBonusesBatch", data)}
}

func (_c *DirectusCMSClient_CreateUserBonusesBatch_Call) Run(run func(data []domain.UserBonusInDirectus)) *DirectusCMSClient_CreateUserBonusesBatch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]domain.UserBonusInDirectus))
	})
	return _c
}

func (_c *DirectusCMSClient_CreateUserBonusesBatch_Call) Return(_a0 []int, _a1 error) *DirectusCMSClient_CreateUserBonusesBatch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_CreateUserBonusesBatch_Call) RunAndReturn(run func([]domain.UserBonusInDirectus) ([]int, error)) *DirectusCMSClient_CreateUserBonusesBatch_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllBonuses provides a mock function with no fields
func (_m *DirectusCMSClient) DeleteAllBonuses() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllBonuses")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_DeleteAllBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllBonuses'
type DirectusCMSClient_DeleteAllBonuses_Call struct {
	*mock.Call
}

// DeleteAllBonuses is a helper method to define mock.On call
func (_e *DirectusCMSClient_Expecter) DeleteAllBonuses() *DirectusCMSClient_DeleteAllBonuses_Call {
	return &DirectusCMSClient_DeleteAllBonuses_Call{Call: _e.mock.On("DeleteAllBonuses")}
}

func (_c *DirectusCMSClient_DeleteAllBonuses_Call) Run(run func()) *DirectusCMSClient_DeleteAllBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DirectusCMSClient_DeleteAllBonuses_Call) Return(_a0 error) *DirectusCMSClient_DeleteAllBonuses_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_DeleteAllBonuses_Call) RunAndReturn(run func() error) *DirectusCMSClient_DeleteAllBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteExpiredBonuses provides a mock function with no fields
func (_m *DirectusCMSClient) DeleteExpiredBonuses() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DeleteExpiredBonuses")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_DeleteExpiredBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteExpiredBonuses'
type DirectusCMSClient_DeleteExpiredBonuses_Call struct {
	*mock.Call
}

// DeleteExpiredBonuses is a helper method to define mock.On call
func (_e *DirectusCMSClient_Expecter) DeleteExpiredBonuses() *DirectusCMSClient_DeleteExpiredBonuses_Call {
	return &DirectusCMSClient_DeleteExpiredBonuses_Call{Call: _e.mock.On("DeleteExpiredBonuses")}
}

func (_c *DirectusCMSClient_DeleteExpiredBonuses_Call) Run(run func()) *DirectusCMSClient_DeleteExpiredBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DirectusCMSClient_DeleteExpiredBonuses_Call) Return(_a0 error) *DirectusCMSClient_DeleteExpiredBonuses_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_DeleteExpiredBonuses_Call) RunAndReturn(run func() error) *DirectusCMSClient_DeleteExpiredBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllCampaignsFromCMS provides a mock function with no fields
func (_m *DirectusCMSClient) GetAllCampaignsFromCMS() (*domain.CampaignCMSResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllCampaignsFromCMS")
	}

	var r0 *domain.CampaignCMSResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (*domain.CampaignCMSResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *domain.CampaignCMSResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.CampaignCMSResponse)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_GetAllCampaignsFromCMS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllCampaignsFromCMS'
type DirectusCMSClient_GetAllCampaignsFromCMS_Call struct {
	*mock.Call
}

// GetAllCampaignsFromCMS is a helper method to define mock.On call
func (_e *DirectusCMSClient_Expecter) GetAllCampaignsFromCMS() *DirectusCMSClient_GetAllCampaignsFromCMS_Call {
	return &DirectusCMSClient_GetAllCampaignsFromCMS_Call{Call: _e.mock.On("GetAllCampaignsFromCMS")}
}

func (_c *DirectusCMSClient_GetAllCampaignsFromCMS_Call) Run(run func()) *DirectusCMSClient_GetAllCampaignsFromCMS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DirectusCMSClient_GetAllCampaignsFromCMS_Call) Return(_a0 *domain.CampaignCMSResponse, _a1 error) *DirectusCMSClient_GetAllCampaignsFromCMS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_GetAllCampaignsFromCMS_Call) RunAndReturn(run func() (*domain.CampaignCMSResponse, error)) *DirectusCMSClient_GetAllCampaignsFromCMS_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllCampaignsOfUserFromCMS provides a mock function with given fields: userID
func (_m *DirectusCMSClient) GetAllCampaignsOfUserFromCMS(userID string) (*domain.CampaignCMSResponse, error) {
	ret := _m.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllCampaignsOfUserFromCMS")
	}

	var r0 *domain.CampaignCMSResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*domain.CampaignCMSResponse, error)); ok {
		return rf(userID)
	}
	if rf, ok := ret.Get(0).(func(string) *domain.CampaignCMSResponse); ok {
		r0 = rf(userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.CampaignCMSResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllCampaignsOfUserFromCMS'
type DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call struct {
	*mock.Call
}

// GetAllCampaignsOfUserFromCMS is a helper method to define mock.On call
//   - userID string
func (_e *DirectusCMSClient_Expecter) GetAllCampaignsOfUserFromCMS(userID interface{}) *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call {
	return &DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call{Call: _e.mock.On("GetAllCampaignsOfUserFromCMS", userID)}
}

func (_c *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call) Run(run func(userID string)) *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call) Return(_a0 *domain.CampaignCMSResponse, _a1 error) *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call) RunAndReturn(run func(string) (*domain.CampaignCMSResponse, error)) *DirectusCMSClient_GetAllCampaignsOfUserFromCMS_Call {
	_c.Call.Return(run)
	return _c
}

// GetBonusConfigurationFromCMS provides a mock function with no fields
func (_m *DirectusCMSClient) GetBonusConfigurationFromCMS() (domain.BonusConfigResponse, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetBonusConfigurationFromCMS")
	}

	var r0 domain.BonusConfigResponse
	var r1 error
	if rf, ok := ret.Get(0).(func() (domain.BonusConfigResponse, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() domain.BonusConfigResponse); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(domain.BonusConfigResponse)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_GetBonusConfigurationFromCMS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBonusConfigurationFromCMS'
type DirectusCMSClient_GetBonusConfigurationFromCMS_Call struct {
	*mock.Call
}

// GetBonusConfigurationFromCMS is a helper method to define mock.On call
func (_e *DirectusCMSClient_Expecter) GetBonusConfigurationFromCMS() *DirectusCMSClient_GetBonusConfigurationFromCMS_Call {
	return &DirectusCMSClient_GetBonusConfigurationFromCMS_Call{Call: _e.mock.On("GetBonusConfigurationFromCMS")}
}

func (_c *DirectusCMSClient_GetBonusConfigurationFromCMS_Call) Run(run func()) *DirectusCMSClient_GetBonusConfigurationFromCMS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DirectusCMSClient_GetBonusConfigurationFromCMS_Call) Return(_a0 domain.BonusConfigResponse, _a1 error) *DirectusCMSClient_GetBonusConfigurationFromCMS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_GetBonusConfigurationFromCMS_Call) RunAndReturn(run func() (domain.BonusConfigResponse, error)) *DirectusCMSClient_GetBonusConfigurationFromCMS_Call {
	_c.Call.Return(run)
	return _c
}

// RollbackBonusDropCountAndRedemption provides a mock function with given fields: ctx, bonusCode, userId, dropCount
func (_m *DirectusCMSClient) RollbackBonusDropCountAndRedemption(ctx context.Context, bonusCode string, userId string, dropCount int) error {
	ret := _m.Called(ctx, bonusCode, userId, dropCount)

	if len(ret) == 0 {
		panic("no return value specified for RollbackBonusDropCountAndRedemption")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int) error); ok {
		r0 = rf(ctx, bonusCode, userId, dropCount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RollbackBonusDropCountAndRedemption'
type DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call struct {
	*mock.Call
}

// RollbackBonusDropCountAndRedemption is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusCode string
//   - userId string
//   - dropCount int
func (_e *DirectusCMSClient_Expecter) RollbackBonusDropCountAndRedemption(ctx interface{}, bonusCode interface{}, userId interface{}, dropCount interface{}) *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call {
	return &DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call{Call: _e.mock.On("RollbackBonusDropCountAndRedemption", ctx, bonusCode, userId, dropCount)}
}

func (_c *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call) Run(run func(ctx context.Context, bonusCode string, userId string, dropCount int)) *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int))
	})
	return _c
}

func (_c *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call) Return(_a0 error) *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call) RunAndReturn(run func(context.Context, string, string, int) error) *DirectusCMSClient_RollbackBonusDropCountAndRedemption_Call {
	_c.Call.Return(run)
	return _c
}

// SetSpecialBonusExpiryDate provides a mock function with given fields: category, expiryDate, bonusId
func (_m *DirectusCMSClient) SetSpecialBonusExpiryDate(category string, expiryDate time.Time, bonusId int) error {
	ret := _m.Called(category, expiryDate, bonusId)

	if len(ret) == 0 {
		panic("no return value specified for SetSpecialBonusExpiryDate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, time.Time, int) error); ok {
		r0 = rf(category, expiryDate, bonusId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_SetSpecialBonusExpiryDate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetSpecialBonusExpiryDate'
type DirectusCMSClient_SetSpecialBonusExpiryDate_Call struct {
	*mock.Call
}

// SetSpecialBonusExpiryDate is a helper method to define mock.On call
//   - category string
//   - expiryDate time.Time
//   - bonusId int
func (_e *DirectusCMSClient_Expecter) SetSpecialBonusExpiryDate(category interface{}, expiryDate interface{}, bonusId interface{}) *DirectusCMSClient_SetSpecialBonusExpiryDate_Call {
	return &DirectusCMSClient_SetSpecialBonusExpiryDate_Call{Call: _e.mock.On("SetSpecialBonusExpiryDate", category, expiryDate, bonusId)}
}

func (_c *DirectusCMSClient_SetSpecialBonusExpiryDate_Call) Run(run func(category string, expiryDate time.Time, bonusId int)) *DirectusCMSClient_SetSpecialBonusExpiryDate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(time.Time), args[2].(int))
	})
	return _c
}

func (_c *DirectusCMSClient_SetSpecialBonusExpiryDate_Call) Return(_a0 error) *DirectusCMSClient_SetSpecialBonusExpiryDate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_SetSpecialBonusExpiryDate_Call) RunAndReturn(run func(string, time.Time, int) error) *DirectusCMSClient_SetSpecialBonusExpiryDate_Call {
	_c.Call.Return(run)
	return _c
}

// UnclaimBonusIfWalletUpdateFails provides a mock function with given fields: externalID, category, id
func (_m *DirectusCMSClient) UnclaimBonusIfWalletUpdateFails(externalID string, category string, id int) (bool, error) {
	ret := _m.Called(externalID, category, id)

	if len(ret) == 0 {
		panic("no return value specified for UnclaimBonusIfWalletUpdateFails")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, int) (bool, error)); ok {
		return rf(externalID, category, id)
	}
	if rf, ok := ret.Get(0).(func(string, string, int) bool); ok {
		r0 = rf(externalID, category, id)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string, int) error); ok {
		r1 = rf(externalID, category, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnclaimBonusIfWalletUpdateFails'
type DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call struct {
	*mock.Call
}

// UnclaimBonusIfWalletUpdateFails is a helper method to define mock.On call
//   - externalID string
//   - category string
//   - id int
func (_e *DirectusCMSClient_Expecter) UnclaimBonusIfWalletUpdateFails(externalID interface{}, category interface{}, id interface{}) *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call {
	return &DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call{Call: _e.mock.On("UnclaimBonusIfWalletUpdateFails", externalID, category, id)}
}

func (_c *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call) Run(run func(externalID string, category string, id int)) *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].(int))
	})
	return _c
}

func (_c *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call) Return(_a0 bool, _a1 error) *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call) RunAndReturn(run func(string, string, int) (bool, error)) *DirectusCMSClient_UnclaimBonusIfWalletUpdateFails_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBonusDropCountAndUsers provides a mock function with given fields: ctx, bonusCode, dropCount, users
func (_m *DirectusCMSClient) UpdateBonusDropCountAndUsers(ctx context.Context, bonusCode string, dropCount int, users domain.RedemptionUser) error {
	ret := _m.Called(ctx, bonusCode, dropCount, users)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBonusDropCountAndUsers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, domain.RedemptionUser) error); ok {
		r0 = rf(ctx, bonusCode, dropCount, users)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_UpdateBonusDropCountAndUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBonusDropCountAndUsers'
type DirectusCMSClient_UpdateBonusDropCountAndUsers_Call struct {
	*mock.Call
}

// UpdateBonusDropCountAndUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusCode string
//   - dropCount int
//   - users domain.RedemptionUser
func (_e *DirectusCMSClient_Expecter) UpdateBonusDropCountAndUsers(ctx interface{}, bonusCode interface{}, dropCount interface{}, users interface{}) *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call {
	return &DirectusCMSClient_UpdateBonusDropCountAndUsers_Call{Call: _e.mock.On("UpdateBonusDropCountAndUsers", ctx, bonusCode, dropCount, users)}
}

func (_c *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call) Run(run func(ctx context.Context, bonusCode string, dropCount int, users domain.RedemptionUser)) *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int), args[3].(domain.RedemptionUser))
	})
	return _c
}

func (_c *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call) Return(_a0 error) *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call) RunAndReturn(run func(context.Context, string, int, domain.RedemptionUser) error) *DirectusCMSClient_UpdateBonusDropCountAndUsers_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBonusDropStatus provides a mock function with given fields: ctx, bonusCode, status
func (_m *DirectusCMSClient) UpdateBonusDropStatus(ctx context.Context, bonusCode string, status string) error {
	ret := _m.Called(ctx, bonusCode, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBonusDropStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, bonusCode, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_UpdateBonusDropStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBonusDropStatus'
type DirectusCMSClient_UpdateBonusDropStatus_Call struct {
	*mock.Call
}

// UpdateBonusDropStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusCode string
//   - status string
func (_e *DirectusCMSClient_Expecter) UpdateBonusDropStatus(ctx interface{}, bonusCode interface{}, status interface{}) *DirectusCMSClient_UpdateBonusDropStatus_Call {
	return &DirectusCMSClient_UpdateBonusDropStatus_Call{Call: _e.mock.On("UpdateBonusDropStatus", ctx, bonusCode, status)}
}

func (_c *DirectusCMSClient_UpdateBonusDropStatus_Call) Run(run func(ctx context.Context, bonusCode string, status string)) *DirectusCMSClient_UpdateBonusDropStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *DirectusCMSClient_UpdateBonusDropStatus_Call) Return(_a0 error) *DirectusCMSClient_UpdateBonusDropStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_UpdateBonusDropStatus_Call) RunAndReturn(run func(context.Context, string, string) error) *DirectusCMSClient_UpdateBonusDropStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateParentIdInDirectus provides a mock function with given fields: ctx, userId, parentId
func (_m *DirectusCMSClient) UpdateParentIdInDirectus(ctx context.Context, userId string, parentId string) error {
	ret := _m.Called(ctx, userId, parentId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateParentIdInDirectus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, userId, parentId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_UpdateParentIdInDirectus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateParentIdInDirectus'
type DirectusCMSClient_UpdateParentIdInDirectus_Call struct {
	*mock.Call
}

// UpdateParentIdInDirectus is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - parentId string
func (_e *DirectusCMSClient_Expecter) UpdateParentIdInDirectus(ctx interface{}, userId interface{}, parentId interface{}) *DirectusCMSClient_UpdateParentIdInDirectus_Call {
	return &DirectusCMSClient_UpdateParentIdInDirectus_Call{Call: _e.mock.On("UpdateParentIdInDirectus", ctx, userId, parentId)}
}

func (_c *DirectusCMSClient_UpdateParentIdInDirectus_Call) Run(run func(ctx context.Context, userId string, parentId string)) *DirectusCMSClient_UpdateParentIdInDirectus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *DirectusCMSClient_UpdateParentIdInDirectus_Call) Return(_a0 error) *DirectusCMSClient_UpdateParentIdInDirectus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_UpdateParentIdInDirectus_Call) RunAndReturn(run func(context.Context, string, string) error) *DirectusCMSClient_UpdateParentIdInDirectus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateVipTiersConfig provides a mock function with no fields
func (_m *DirectusCMSClient) UpdateVipTiersConfig() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UpdateVipTiersConfig")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_UpdateVipTiersConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateVipTiersConfig'
type DirectusCMSClient_UpdateVipTiersConfig_Call struct {
	*mock.Call
}

// UpdateVipTiersConfig is a helper method to define mock.On call
func (_e *DirectusCMSClient_Expecter) UpdateVipTiersConfig() *DirectusCMSClient_UpdateVipTiersConfig_Call {
	return &DirectusCMSClient_UpdateVipTiersConfig_Call{Call: _e.mock.On("UpdateVipTiersConfig")}
}

func (_c *DirectusCMSClient_UpdateVipTiersConfig_Call) Run(run func()) *DirectusCMSClient_UpdateVipTiersConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *DirectusCMSClient_UpdateVipTiersConfig_Call) Return(_a0 error) *DirectusCMSClient_UpdateVipTiersConfig_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_UpdateVipTiersConfig_Call) RunAndReturn(run func() error) *DirectusCMSClient_UpdateVipTiersConfig_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertReferredUsersInDirectus provides a mock function with given fields: userID, campaignCode, newReferredUsers
func (_m *DirectusCMSClient) UpsertReferredUsersInDirectus(userID string, campaignCode string, newReferredUsers []domain.ReferredUser) error {
	ret := _m.Called(userID, campaignCode, newReferredUsers)

	if len(ret) == 0 {
		panic("no return value specified for UpsertReferredUsersInDirectus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, []domain.ReferredUser) error); ok {
		r0 = rf(userID, campaignCode, newReferredUsers)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DirectusCMSClient_UpsertReferredUsersInDirectus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertReferredUsersInDirectus'
type DirectusCMSClient_UpsertReferredUsersInDirectus_Call struct {
	*mock.Call
}

// UpsertReferredUsersInDirectus is a helper method to define mock.On call
//   - userID string
//   - campaignCode string
//   - newReferredUsers []domain.ReferredUser
func (_e *DirectusCMSClient_Expecter) UpsertReferredUsersInDirectus(userID interface{}, campaignCode interface{}, newReferredUsers interface{}) *DirectusCMSClient_UpsertReferredUsersInDirectus_Call {
	return &DirectusCMSClient_UpsertReferredUsersInDirectus_Call{Call: _e.mock.On("UpsertReferredUsersInDirectus", userID, campaignCode, newReferredUsers)}
}

func (_c *DirectusCMSClient_UpsertReferredUsersInDirectus_Call) Run(run func(userID string, campaignCode string, newReferredUsers []domain.ReferredUser)) *DirectusCMSClient_UpsertReferredUsersInDirectus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string), args[2].([]domain.ReferredUser))
	})
	return _c
}

func (_c *DirectusCMSClient_UpsertReferredUsersInDirectus_Call) Return(_a0 error) *DirectusCMSClient_UpsertReferredUsersInDirectus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DirectusCMSClient_UpsertReferredUsersInDirectus_Call) RunAndReturn(run func(string, string, []domain.ReferredUser) error) *DirectusCMSClient_UpsertReferredUsersInDirectus_Call {
	_c.Call.Return(run)
	return _c
}

// NewDirectusCMSClient creates a new instance of DirectusCMSClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDirectusCMSClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *DirectusCMSClient {
	mock := &DirectusCMSClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
