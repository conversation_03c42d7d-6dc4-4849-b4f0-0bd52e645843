// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// UserRepository is an autogenerated mock type for the UserRepository type
type UserRepository struct {
	mock.Mock
}

type UserRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *UserRepository) EXPECT() *UserRepository_Expecter {
	return &UserRepository_Expecter{mock: &_m.Mock}
}

// AssignSpecialBonusToUserOnTierUpgrade provides a mock function with given fields: ctx, userExternalID, bonusAmount, newVipStatus
func (_m *UserRepository) AssignSpecialBonusToUserOnTierUpgrade(ctx context.Context, userExternalID string, bonusAmount float64, newVipStatus string) error {
	ret := _m.Called(ctx, userExternalID, bonusAmount, newVipStatus)

	if len(ret) == 0 {
		panic("no return value specified for AssignSpecialBonusToUserOnTierUpgrade")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, float64, string) error); ok {
		r0 = rf(ctx, userExternalID, bonusAmount, newVipStatus)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignSpecialBonusToUserOnTierUpgrade'
type UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call struct {
	*mock.Call
}

// AssignSpecialBonusToUserOnTierUpgrade is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - bonusAmount float64
//   - newVipStatus string
func (_e *UserRepository_Expecter) AssignSpecialBonusToUserOnTierUpgrade(ctx interface{}, userExternalID interface{}, bonusAmount interface{}, newVipStatus interface{}) *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call {
	return &UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call{Call: _e.mock.On("AssignSpecialBonusToUserOnTierUpgrade", ctx, userExternalID, bonusAmount, newVipStatus)}
}

func (_c *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call) Run(run func(ctx context.Context, userExternalID string, bonusAmount float64, newVipStatus string)) *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(float64), args[3].(string))
	})
	return _c
}

func (_c *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call) Return(_a0 error) *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call) RunAndReturn(run func(context.Context, string, float64, string) error) *UserRepository_AssignSpecialBonusToUserOnTierUpgrade_Call {
	_c.Call.Return(run)
	return _c
}

// BeginTransaction provides a mock function with given fields: ctx
func (_m *UserRepository) BeginTransaction(ctx context.Context) (*gorm.DB, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for BeginTransaction")
	}

	var r0 *gorm.DB
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*gorm.DB, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *gorm.DB); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_BeginTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BeginTransaction'
type UserRepository_BeginTransaction_Call struct {
	*mock.Call
}

// BeginTransaction is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserRepository_Expecter) BeginTransaction(ctx interface{}) *UserRepository_BeginTransaction_Call {
	return &UserRepository_BeginTransaction_Call{Call: _e.mock.On("BeginTransaction", ctx)}
}

func (_c *UserRepository_BeginTransaction_Call) Run(run func(ctx context.Context)) *UserRepository_BeginTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserRepository_BeginTransaction_Call) Return(_a0 *gorm.DB, _a1 error) *UserRepository_BeginTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_BeginTransaction_Call) RunAndReturn(run func(context.Context) (*gorm.DB, error)) *UserRepository_BeginTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// DB provides a mock function with no fields
func (_m *UserRepository) DB() *gorm.DB {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DB")
	}

	var r0 *gorm.DB
	if rf, ok := ret.Get(0).(func() *gorm.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*gorm.DB)
		}
	}

	return r0
}

// UserRepository_DB_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DB'
type UserRepository_DB_Call struct {
	*mock.Call
}

// DB is a helper method to define mock.On call
func (_e *UserRepository_Expecter) DB() *UserRepository_DB_Call {
	return &UserRepository_DB_Call{Call: _e.mock.On("DB")}
}

func (_c *UserRepository_DB_Call) Run(run func()) *UserRepository_DB_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *UserRepository_DB_Call) Return(_a0 *gorm.DB) *UserRepository_DB_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_DB_Call) RunAndReturn(run func() *gorm.DB) *UserRepository_DB_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUsersWithPagination provides a mock function with given fields: ctx, limit, offset
func (_m *UserRepository) GetAllUsersWithPagination(ctx context.Context, limit int, offset int) ([]domain.GetUsersResponseForXP, error) {
	ret := _m.Called(ctx, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for GetAllUsersWithPagination")
	}

	var r0 []domain.GetUsersResponseForXP
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]domain.GetUsersResponseForXP, error)); ok {
		return rf(ctx, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []domain.GetUsersResponseForXP); ok {
		r0 = rf(ctx, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.GetUsersResponseForXP)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = rf(ctx, limit, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetAllUsersWithPagination_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUsersWithPagination'
type UserRepository_GetAllUsersWithPagination_Call struct {
	*mock.Call
}

// GetAllUsersWithPagination is a helper method to define mock.On call
//   - ctx context.Context
//   - limit int
//   - offset int
func (_e *UserRepository_Expecter) GetAllUsersWithPagination(ctx interface{}, limit interface{}, offset interface{}) *UserRepository_GetAllUsersWithPagination_Call {
	return &UserRepository_GetAllUsersWithPagination_Call{Call: _e.mock.On("GetAllUsersWithPagination", ctx, limit, offset)}
}

func (_c *UserRepository_GetAllUsersWithPagination_Call) Run(run func(ctx context.Context, limit int, offset int)) *UserRepository_GetAllUsersWithPagination_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *UserRepository_GetAllUsersWithPagination_Call) Return(_a0 []domain.GetUsersResponseForXP, _a1 error) *UserRepository_GetAllUsersWithPagination_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetAllUsersWithPagination_Call) RunAndReturn(run func(context.Context, int, int) ([]domain.GetUsersResponseForXP, error)) *UserRepository_GetAllUsersWithPagination_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentVipStatus provides a mock function with given fields: ctx, userExternalID
func (_m *UserRepository) GetCurrentVipStatus(ctx context.Context, userExternalID string) (string, error) {
	ret := _m.Called(ctx, userExternalID)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentVipStatus")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, userExternalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, userExternalID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userExternalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetCurrentVipStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentVipStatus'
type UserRepository_GetCurrentVipStatus_Call struct {
	*mock.Call
}

// GetCurrentVipStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
func (_e *UserRepository_Expecter) GetCurrentVipStatus(ctx interface{}, userExternalID interface{}) *UserRepository_GetCurrentVipStatus_Call {
	return &UserRepository_GetCurrentVipStatus_Call{Call: _e.mock.On("GetCurrentVipStatus", ctx, userExternalID)}
}

func (_c *UserRepository_GetCurrentVipStatus_Call) Run(run func(ctx context.Context, userExternalID string)) *UserRepository_GetCurrentVipStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetCurrentVipStatus_Call) Return(_a0 string, _a1 error) *UserRepository_GetCurrentVipStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetCurrentVipStatus_Call) RunAndReturn(run func(context.Context, string) (string, error)) *UserRepository_GetCurrentVipStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetRankedUserByExternalID provides a mock function with given fields: ctx, id
func (_m *UserRepository) GetRankedUserByExternalID(ctx context.Context, id string) (*domain.RankedUser, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUserByExternalID")
	}

	var r0 *domain.RankedUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.RankedUser, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.RankedUser); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RankedUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetRankedUserByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUserByExternalID'
type UserRepository_GetRankedUserByExternalID_Call struct {
	*mock.Call
}

// GetRankedUserByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *UserRepository_Expecter) GetRankedUserByExternalID(ctx interface{}, id interface{}) *UserRepository_GetRankedUserByExternalID_Call {
	return &UserRepository_GetRankedUserByExternalID_Call{Call: _e.mock.On("GetRankedUserByExternalID", ctx, id)}
}

func (_c *UserRepository_GetRankedUserByExternalID_Call) Run(run func(ctx context.Context, id string)) *UserRepository_GetRankedUserByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetRankedUserByExternalID_Call) Return(_a0 *domain.RankedUser, _a1 error) *UserRepository_GetRankedUserByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetRankedUserByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.RankedUser, error)) *UserRepository_GetRankedUserByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRankedUsers provides a mock function with given fields: ctx, params
func (_m *UserRepository) GetRankedUsers(ctx context.Context, params *domain.GetUserParams) (*domain.RankedUsers, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUsers")
	}

	var r0 *domain.RankedUsers
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetUserParams) (*domain.RankedUsers, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetUserParams) *domain.RankedUsers); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RankedUsers)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetUserParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetRankedUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUsers'
type UserRepository_GetRankedUsers_Call struct {
	*mock.Call
}

// GetRankedUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetUserParams
func (_e *UserRepository_Expecter) GetRankedUsers(ctx interface{}, params interface{}) *UserRepository_GetRankedUsers_Call {
	return &UserRepository_GetRankedUsers_Call{Call: _e.mock.On("GetRankedUsers", ctx, params)}
}

func (_c *UserRepository_GetRankedUsers_Call) Run(run func(ctx context.Context, params *domain.GetUserParams)) *UserRepository_GetRankedUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetUserParams))
	})
	return _c
}

func (_c *UserRepository_GetRankedUsers_Call) Return(_a0 *domain.RankedUsers, _a1 error) *UserRepository_GetRankedUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetRankedUsers_Call) RunAndReturn(run func(context.Context, *domain.GetUserParams) (*domain.RankedUsers, error)) *UserRepository_GetRankedUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetRankedUsersByExternalID provides a mock function with given fields: ctx, id, retrieveAmount, orderBy
func (_m *UserRepository) GetRankedUsersByExternalID(ctx context.Context, id string, retrieveAmount int, orderBy string) ([]domain.RankedUser, error) {
	ret := _m.Called(ctx, id, retrieveAmount, orderBy)

	if len(ret) == 0 {
		panic("no return value specified for GetRankedUsersByExternalID")
	}

	var r0 []domain.RankedUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) ([]domain.RankedUser, error)); ok {
		return rf(ctx, id, retrieveAmount, orderBy)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, string) []domain.RankedUser); ok {
		r0 = rf(ctx, id, retrieveAmount, orderBy)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.RankedUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, string) error); ok {
		r1 = rf(ctx, id, retrieveAmount, orderBy)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetRankedUsersByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRankedUsersByExternalID'
type UserRepository_GetRankedUsersByExternalID_Call struct {
	*mock.Call
}

// GetRankedUsersByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
//   - retrieveAmount int
//   - orderBy string
func (_e *UserRepository_Expecter) GetRankedUsersByExternalID(ctx interface{}, id interface{}, retrieveAmount interface{}, orderBy interface{}) *UserRepository_GetRankedUsersByExternalID_Call {
	return &UserRepository_GetRankedUsersByExternalID_Call{Call: _e.mock.On("GetRankedUsersByExternalID", ctx, id, retrieveAmount, orderBy)}
}

func (_c *UserRepository_GetRankedUsersByExternalID_Call) Run(run func(ctx context.Context, id string, retrieveAmount int, orderBy string)) *UserRepository_GetRankedUsersByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int), args[3].(string))
	})
	return _c
}

func (_c *UserRepository_GetRankedUsersByExternalID_Call) Return(_a0 []domain.RankedUser, _a1 error) *UserRepository_GetRankedUsersByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetRankedUsersByExternalID_Call) RunAndReturn(run func(context.Context, string, int, string) ([]domain.RankedUser, error)) *UserRepository_GetRankedUsersByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetRegisteredEmail provides a mock function with given fields: ctx, email
func (_m *UserRepository) GetRegisteredEmail(ctx context.Context, email string) (*domain.RegisteredEmail, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for GetRegisteredEmail")
	}

	var r0 *domain.RegisteredEmail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.RegisteredEmail, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.RegisteredEmail); ok {
		r0 = rf(ctx, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.RegisteredEmail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetRegisteredEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRegisteredEmail'
type UserRepository_GetRegisteredEmail_Call struct {
	*mock.Call
}

// GetRegisteredEmail is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
func (_e *UserRepository_Expecter) GetRegisteredEmail(ctx interface{}, email interface{}) *UserRepository_GetRegisteredEmail_Call {
	return &UserRepository_GetRegisteredEmail_Call{Call: _e.mock.On("GetRegisteredEmail", ctx, email)}
}

func (_c *UserRepository_GetRegisteredEmail_Call) Run(run func(ctx context.Context, email string)) *UserRepository_GetRegisteredEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetRegisteredEmail_Call) Return(_a0 *domain.RegisteredEmail, _a1 error) *UserRepository_GetRegisteredEmail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetRegisteredEmail_Call) RunAndReturn(run func(context.Context, string) (*domain.RegisteredEmail, error)) *UserRepository_GetRegisteredEmail_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserAndGameByExternalId provides a mock function with given fields: ctx, userId, gameId
func (_m *UserRepository) GetUserAndGameByExternalId(ctx context.Context, userId string, gameId string) (domain.UserGame, error) {
	ret := _m.Called(ctx, userId, gameId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserAndGameByExternalId")
	}

	var r0 domain.UserGame
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.UserGame, error)); ok {
		return rf(ctx, userId, gameId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.UserGame); ok {
		r0 = rf(ctx, userId, gameId)
	} else {
		r0 = ret.Get(0).(domain.UserGame)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userId, gameId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUserAndGameByExternalId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserAndGameByExternalId'
type UserRepository_GetUserAndGameByExternalId_Call struct {
	*mock.Call
}

// GetUserAndGameByExternalId is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - gameId string
func (_e *UserRepository_Expecter) GetUserAndGameByExternalId(ctx interface{}, userId interface{}, gameId interface{}) *UserRepository_GetUserAndGameByExternalId_Call {
	return &UserRepository_GetUserAndGameByExternalId_Call{Call: _e.mock.On("GetUserAndGameByExternalId", ctx, userId, gameId)}
}

func (_c *UserRepository_GetUserAndGameByExternalId_Call) Run(run func(ctx context.Context, userId string, gameId string)) *UserRepository_GetUserAndGameByExternalId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserRepository_GetUserAndGameByExternalId_Call) Return(_a0 domain.UserGame, _a1 error) *UserRepository_GetUserAndGameByExternalId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUserAndGameByExternalId_Call) RunAndReturn(run func(context.Context, string, string) (domain.UserGame, error)) *UserRepository_GetUserAndGameByExternalId_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserAndGameInfo provides a mock function with given fields: ctx, userID, gameID
func (_m *UserRepository) GetUserAndGameInfo(ctx context.Context, userID string, gameID string) (domain.UserGameDTO, error) {
	ret := _m.Called(ctx, userID, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserAndGameInfo")
	}

	var r0 domain.UserGameDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (domain.UserGameDTO, error)); ok {
		return rf(ctx, userID, gameID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) domain.UserGameDTO); ok {
		r0 = rf(ctx, userID, gameID)
	} else {
		r0 = ret.Get(0).(domain.UserGameDTO)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userID, gameID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUserAndGameInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserAndGameInfo'
type UserRepository_GetUserAndGameInfo_Call struct {
	*mock.Call
}

// GetUserAndGameInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - gameID string
func (_e *UserRepository_Expecter) GetUserAndGameInfo(ctx interface{}, userID interface{}, gameID interface{}) *UserRepository_GetUserAndGameInfo_Call {
	return &UserRepository_GetUserAndGameInfo_Call{Call: _e.mock.On("GetUserAndGameInfo", ctx, userID, gameID)}
}

func (_c *UserRepository_GetUserAndGameInfo_Call) Run(run func(ctx context.Context, userID string, gameID string)) *UserRepository_GetUserAndGameInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserRepository_GetUserAndGameInfo_Call) Return(_a0 domain.UserGameDTO, _a1 error) *UserRepository_GetUserAndGameInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUserAndGameInfo_Call) RunAndReturn(run func(context.Context, string, string) (domain.UserGameDTO, error)) *UserRepository_GetUserAndGameInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByExternalID provides a mock function with given fields: ctx, id
func (_m *UserRepository) GetUserByExternalID(ctx context.Context, id string) (*domain.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByExternalID")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUserByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByExternalID'
type UserRepository_GetUserByExternalID_Call struct {
	*mock.Call
}

// GetUserByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *UserRepository_Expecter) GetUserByExternalID(ctx interface{}, id interface{}) *UserRepository_GetUserByExternalID_Call {
	return &UserRepository_GetUserByExternalID_Call{Call: _e.mock.On("GetUserByExternalID", ctx, id)}
}

func (_c *UserRepository_GetUserByExternalID_Call) Run(run func(ctx context.Context, id string)) *UserRepository_GetUserByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetUserByExternalID_Call) Return(_a0 *domain.User, _a1 error) *UserRepository_GetUserByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUserByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.User, error)) *UserRepository_GetUserByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByID provides a mock function with given fields: ctx, id
func (_m *UserRepository) GetUserByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByID")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.User, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.User); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByID'
type UserRepository_GetUserByID_Call struct {
	*mock.Call
}

// GetUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *UserRepository_Expecter) GetUserByID(ctx interface{}, id interface{}) *UserRepository_GetUserByID_Call {
	return &UserRepository_GetUserByID_Call{Call: _e.mock.On("GetUserByID", ctx, id)}
}

func (_c *UserRepository_GetUserByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *UserRepository_GetUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *UserRepository_GetUserByID_Call) Return(_a0 *domain.User, _a1 error) *UserRepository_GetUserByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUserByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.User, error)) *UserRepository_GetUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByUserName provides a mock function with given fields: ctx, userName
func (_m *UserRepository) GetUserByUserName(ctx context.Context, userName string) (*domain.User, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByUserName")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.User, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.User); ok {
		r0 = rf(ctx, userName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUserByUserName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByUserName'
type UserRepository_GetUserByUserName_Call struct {
	*mock.Call
}

// GetUserByUserName is a helper method to define mock.On call
//   - ctx context.Context
//   - userName string
func (_e *UserRepository_Expecter) GetUserByUserName(ctx interface{}, userName interface{}) *UserRepository_GetUserByUserName_Call {
	return &UserRepository_GetUserByUserName_Call{Call: _e.mock.On("GetUserByUserName", ctx, userName)}
}

func (_c *UserRepository_GetUserByUserName_Call) Run(run func(ctx context.Context, userName string)) *UserRepository_GetUserByUserName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetUserByUserName_Call) Return(_a0 *domain.User, _a1 error) *UserRepository_GetUserByUserName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUserByUserName_Call) RunAndReturn(run func(context.Context, string) (*domain.User, error)) *UserRepository_GetUserByUserName_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserWageringAndVIPStatus provides a mock function with given fields: ctx, userExternalID
func (_m *UserRepository) GetUserWageringAndVIPStatus(ctx context.Context, userExternalID string) (float64, string, error) {
	ret := _m.Called(ctx, userExternalID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserWageringAndVIPStatus")
	}

	var r0 float64
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (float64, string, error)); ok {
		return rf(ctx, userExternalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) float64); ok {
		r0 = rf(ctx, userExternalID)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) string); ok {
		r1 = rf(ctx, userExternalID)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string) error); ok {
		r2 = rf(ctx, userExternalID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// UserRepository_GetUserWageringAndVIPStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserWageringAndVIPStatus'
type UserRepository_GetUserWageringAndVIPStatus_Call struct {
	*mock.Call
}

// GetUserWageringAndVIPStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
func (_e *UserRepository_Expecter) GetUserWageringAndVIPStatus(ctx interface{}, userExternalID interface{}) *UserRepository_GetUserWageringAndVIPStatus_Call {
	return &UserRepository_GetUserWageringAndVIPStatus_Call{Call: _e.mock.On("GetUserWageringAndVIPStatus", ctx, userExternalID)}
}

func (_c *UserRepository_GetUserWageringAndVIPStatus_Call) Run(run func(ctx context.Context, userExternalID string)) *UserRepository_GetUserWageringAndVIPStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_GetUserWageringAndVIPStatus_Call) Return(_a0 float64, _a1 string, _a2 error) *UserRepository_GetUserWageringAndVIPStatus_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *UserRepository_GetUserWageringAndVIPStatus_Call) RunAndReturn(run func(context.Context, string) (float64, string, error)) *UserRepository_GetUserWageringAndVIPStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersByExternalIDs provides a mock function with given fields: ctx, externalIDs
func (_m *UserRepository) GetUsersByExternalIDs(ctx context.Context, externalIDs []string) ([]domain.GetAllUsersResponse, error) {
	ret := _m.Called(ctx, externalIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersByExternalIDs")
	}

	var r0 []domain.GetAllUsersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]domain.GetAllUsersResponse, error)); ok {
		return rf(ctx, externalIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []domain.GetAllUsersResponse); ok {
		r0 = rf(ctx, externalIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.GetAllUsersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, externalIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetUsersByExternalIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersByExternalIDs'
type UserRepository_GetUsersByExternalIDs_Call struct {
	*mock.Call
}

// GetUsersByExternalIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - externalIDs []string
func (_e *UserRepository_Expecter) GetUsersByExternalIDs(ctx interface{}, externalIDs interface{}) *UserRepository_GetUsersByExternalIDs_Call {
	return &UserRepository_GetUsersByExternalIDs_Call{Call: _e.mock.On("GetUsersByExternalIDs", ctx, externalIDs)}
}

func (_c *UserRepository_GetUsersByExternalIDs_Call) Run(run func(ctx context.Context, externalIDs []string)) *UserRepository_GetUsersByExternalIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]string))
	})
	return _c
}

func (_c *UserRepository_GetUsersByExternalIDs_Call) Return(_a0 []domain.GetAllUsersResponse, _a1 error) *UserRepository_GetUsersByExternalIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetUsersByExternalIDs_Call) RunAndReturn(run func(context.Context, []string) ([]domain.GetAllUsersResponse, error)) *UserRepository_GetUsersByExternalIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersXPAndUpdateInElantil provides a mock function with given fields: ctx
func (_m *UserRepository) GetUsersXPAndUpdateInElantil(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersXPAndUpdateInElantil")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_GetUsersXPAndUpdateInElantil_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersXPAndUpdateInElantil'
type UserRepository_GetUsersXPAndUpdateInElantil_Call struct {
	*mock.Call
}

// GetUsersXPAndUpdateInElantil is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserRepository_Expecter) GetUsersXPAndUpdateInElantil(ctx interface{}) *UserRepository_GetUsersXPAndUpdateInElantil_Call {
	return &UserRepository_GetUsersXPAndUpdateInElantil_Call{Call: _e.mock.On("GetUsersXPAndUpdateInElantil", ctx)}
}

func (_c *UserRepository_GetUsersXPAndUpdateInElantil_Call) Run(run func(ctx context.Context)) *UserRepository_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserRepository_GetUsersXPAndUpdateInElantil_Call) Return(_a0 error) *UserRepository_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_GetUsersXPAndUpdateInElantil_Call) RunAndReturn(run func(context.Context) error) *UserRepository_GetUsersXPAndUpdateInElantil_Call {
	_c.Call.Return(run)
	return _c
}

// GetWinnerOfTheMonth provides a mock function with given fields: ctx
func (_m *UserRepository) GetWinnerOfTheMonth(ctx context.Context) ([]domain.WinnerDetails, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetWinnerOfTheMonth")
	}

	var r0 []domain.WinnerDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]domain.WinnerDetails, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []domain.WinnerDetails); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.WinnerDetails)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_GetWinnerOfTheMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWinnerOfTheMonth'
type UserRepository_GetWinnerOfTheMonth_Call struct {
	*mock.Call
}

// GetWinnerOfTheMonth is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserRepository_Expecter) GetWinnerOfTheMonth(ctx interface{}) *UserRepository_GetWinnerOfTheMonth_Call {
	return &UserRepository_GetWinnerOfTheMonth_Call{Call: _e.mock.On("GetWinnerOfTheMonth", ctx)}
}

func (_c *UserRepository_GetWinnerOfTheMonth_Call) Run(run func(ctx context.Context)) *UserRepository_GetWinnerOfTheMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserRepository_GetWinnerOfTheMonth_Call) Return(_a0 []domain.WinnerDetails, _a1 error) *UserRepository_GetWinnerOfTheMonth_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_GetWinnerOfTheMonth_Call) RunAndReturn(run func(context.Context) ([]domain.WinnerDetails, error)) *UserRepository_GetWinnerOfTheMonth_Call {
	_c.Call.Return(run)
	return _c
}

// IsUserNameUnique provides a mock function with given fields: ctx, userName
func (_m *UserRepository) IsUserNameUnique(ctx context.Context, userName string) (bool, error) {
	ret := _m.Called(ctx, userName)

	if len(ret) == 0 {
		panic("no return value specified for IsUserNameUnique")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, userName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, userName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_IsUserNameUnique_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUserNameUnique'
type UserRepository_IsUserNameUnique_Call struct {
	*mock.Call
}

// IsUserNameUnique is a helper method to define mock.On call
//   - ctx context.Context
//   - userName string
func (_e *UserRepository_Expecter) IsUserNameUnique(ctx interface{}, userName interface{}) *UserRepository_IsUserNameUnique_Call {
	return &UserRepository_IsUserNameUnique_Call{Call: _e.mock.On("IsUserNameUnique", ctx, userName)}
}

func (_c *UserRepository_IsUserNameUnique_Call) Run(run func(ctx context.Context, userName string)) *UserRepository_IsUserNameUnique_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_IsUserNameUnique_Call) Return(_a0 bool, _a1 error) *UserRepository_IsUserNameUnique_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_IsUserNameUnique_Call) RunAndReturn(run func(context.Context, string) (bool, error)) *UserRepository_IsUserNameUnique_Call {
	_c.Call.Return(run)
	return _c
}

// SaveRegisteredEmail provides a mock function with given fields: ctx, email, emailMarketing
func (_m *UserRepository) SaveRegisteredEmail(ctx context.Context, email string, emailMarketing bool) error {
	ret := _m.Called(ctx, email, emailMarketing)

	if len(ret) == 0 {
		panic("no return value specified for SaveRegisteredEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) error); ok {
		r0 = rf(ctx, email, emailMarketing)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_SaveRegisteredEmail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveRegisteredEmail'
type UserRepository_SaveRegisteredEmail_Call struct {
	*mock.Call
}

// SaveRegisteredEmail is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
//   - emailMarketing bool
func (_e *UserRepository_Expecter) SaveRegisteredEmail(ctx interface{}, email interface{}, emailMarketing interface{}) *UserRepository_SaveRegisteredEmail_Call {
	return &UserRepository_SaveRegisteredEmail_Call{Call: _e.mock.On("SaveRegisteredEmail", ctx, email, emailMarketing)}
}

func (_c *UserRepository_SaveRegisteredEmail_Call) Run(run func(ctx context.Context, email string, emailMarketing bool)) *UserRepository_SaveRegisteredEmail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserRepository_SaveRegisteredEmail_Call) Return(_a0 error) *UserRepository_SaveRegisteredEmail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_SaveRegisteredEmail_Call) RunAndReturn(run func(context.Context, string, bool) error) *UserRepository_SaveRegisteredEmail_Call {
	_c.Call.Return(run)
	return _c
}

// StreamAllUsers provides a mock function with given fields: ctx, userChan, needsVipStatus
func (_m *UserRepository) StreamAllUsers(ctx context.Context, userChan chan<- domain.GetAllUsersResponse, needsVipStatus bool) error {
	ret := _m.Called(ctx, userChan, needsVipStatus)

	if len(ret) == 0 {
		panic("no return value specified for StreamAllUsers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, chan<- domain.GetAllUsersResponse, bool) error); ok {
		r0 = rf(ctx, userChan, needsVipStatus)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_StreamAllUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StreamAllUsers'
type UserRepository_StreamAllUsers_Call struct {
	*mock.Call
}

// StreamAllUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - userChan chan<- domain.GetAllUsersResponse
//   - needsVipStatus bool
func (_e *UserRepository_Expecter) StreamAllUsers(ctx interface{}, userChan interface{}, needsVipStatus interface{}) *UserRepository_StreamAllUsers_Call {
	return &UserRepository_StreamAllUsers_Call{Call: _e.mock.On("StreamAllUsers", ctx, userChan, needsVipStatus)}
}

func (_c *UserRepository_StreamAllUsers_Call) Run(run func(ctx context.Context, userChan chan<- domain.GetAllUsersResponse, needsVipStatus bool)) *UserRepository_StreamAllUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(chan<- domain.GetAllUsersResponse), args[2].(bool))
	})
	return _c
}

func (_c *UserRepository_StreamAllUsers_Call) Return(_a0 error) *UserRepository_StreamAllUsers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_StreamAllUsers_Call) RunAndReturn(run func(context.Context, chan<- domain.GetAllUsersResponse, bool) error) *UserRepository_StreamAllUsers_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUpsertUserXP provides a mock function with given fields: ctx, userID, xpToAdd
func (_m *UserRepository) UpdateUpsertUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	ret := _m.Called(ctx, userID, xpToAdd)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUpsertUserXP")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) (float64, error)); ok {
		return rf(ctx, userID, xpToAdd)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) float64); ok {
		r0 = rf(ctx, userID, xpToAdd)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, float64) error); ok {
		r1 = rf(ctx, userID, xpToAdd)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_UpdateUpsertUserXP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUpsertUserXP'
type UserRepository_UpdateUpsertUserXP_Call struct {
	*mock.Call
}

// UpdateUpsertUserXP is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - xpToAdd float64
func (_e *UserRepository_Expecter) UpdateUpsertUserXP(ctx interface{}, userID interface{}, xpToAdd interface{}) *UserRepository_UpdateUpsertUserXP_Call {
	return &UserRepository_UpdateUpsertUserXP_Call{Call: _e.mock.On("UpdateUpsertUserXP", ctx, userID, xpToAdd)}
}

func (_c *UserRepository_UpdateUpsertUserXP_Call) Run(run func(ctx context.Context, userID string, xpToAdd float64)) *UserRepository_UpdateUpsertUserXP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(float64))
	})
	return _c
}

func (_c *UserRepository_UpdateUpsertUserXP_Call) Return(_a0 float64, _a1 error) *UserRepository_UpdateUpsertUserXP_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_UpdateUpsertUserXP_Call) RunAndReturn(run func(context.Context, string, float64) (float64, error)) *UserRepository_UpdateUpsertUserXP_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserByID provides a mock function with given fields: ctx, user
func (_m *UserRepository) UpdateUserByID(ctx context.Context, user *domain.User) error {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.User) error); ok {
		r0 = rf(ctx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_UpdateUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserByID'
type UserRepository_UpdateUserByID_Call struct {
	*mock.Call
}

// UpdateUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - user *domain.User
func (_e *UserRepository_Expecter) UpdateUserByID(ctx interface{}, user interface{}) *UserRepository_UpdateUserByID_Call {
	return &UserRepository_UpdateUserByID_Call{Call: _e.mock.On("UpdateUserByID", ctx, user)}
}

func (_c *UserRepository_UpdateUserByID_Call) Run(run func(ctx context.Context, user *domain.User)) *UserRepository_UpdateUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserRepository_UpdateUserByID_Call) Return(_a0 error) *UserRepository_UpdateUserByID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_UpdateUserByID_Call) RunAndReturn(run func(context.Context, *domain.User) error) *UserRepository_UpdateUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserElantilVIPStatus provides a mock function with given fields: externalID, vipStatus
func (_m *UserRepository) UpdateUserElantilVIPStatus(externalID string, vipStatus string) error {
	ret := _m.Called(externalID, vipStatus)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserElantilVIPStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(externalID, vipStatus)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_UpdateUserElantilVIPStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserElantilVIPStatus'
type UserRepository_UpdateUserElantilVIPStatus_Call struct {
	*mock.Call
}

// UpdateUserElantilVIPStatus is a helper method to define mock.On call
//   - externalID string
//   - vipStatus string
func (_e *UserRepository_Expecter) UpdateUserElantilVIPStatus(externalID interface{}, vipStatus interface{}) *UserRepository_UpdateUserElantilVIPStatus_Call {
	return &UserRepository_UpdateUserElantilVIPStatus_Call{Call: _e.mock.On("UpdateUserElantilVIPStatus", externalID, vipStatus)}
}

func (_c *UserRepository_UpdateUserElantilVIPStatus_Call) Run(run func(externalID string, vipStatus string)) *UserRepository_UpdateUserElantilVIPStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *UserRepository_UpdateUserElantilVIPStatus_Call) Return(_a0 error) *UserRepository_UpdateUserElantilVIPStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_UpdateUserElantilVIPStatus_Call) RunAndReturn(run func(string, string) error) *UserRepository_UpdateUserElantilVIPStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserEmailVerificationStatus provides a mock function with given fields: ctx, username, verified
func (_m *UserRepository) UpdateUserEmailVerificationStatus(ctx context.Context, username string, verified bool) error {
	ret := _m.Called(ctx, username, verified)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserEmailVerificationStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) error); ok {
		r0 = rf(ctx, username, verified)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_UpdateUserEmailVerificationStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserEmailVerificationStatus'
type UserRepository_UpdateUserEmailVerificationStatus_Call struct {
	*mock.Call
}

// UpdateUserEmailVerificationStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - username string
//   - verified bool
func (_e *UserRepository_Expecter) UpdateUserEmailVerificationStatus(ctx interface{}, username interface{}, verified interface{}) *UserRepository_UpdateUserEmailVerificationStatus_Call {
	return &UserRepository_UpdateUserEmailVerificationStatus_Call{Call: _e.mock.On("UpdateUserEmailVerificationStatus", ctx, username, verified)}
}

func (_c *UserRepository_UpdateUserEmailVerificationStatus_Call) Run(run func(ctx context.Context, username string, verified bool)) *UserRepository_UpdateUserEmailVerificationStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserRepository_UpdateUserEmailVerificationStatus_Call) Return(_a0 error) *UserRepository_UpdateUserEmailVerificationStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_UpdateUserEmailVerificationStatus_Call) RunAndReturn(run func(context.Context, string, bool) error) *UserRepository_UpdateUserEmailVerificationStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserMultiCurrency provides a mock function with given fields: ctx, userID, multiCurrency
func (_m *UserRepository) UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error {
	ret := _m.Called(ctx, userID, multiCurrency)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserMultiCurrency")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) error); ok {
		r0 = rf(ctx, userID, multiCurrency)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_UpdateUserMultiCurrency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserMultiCurrency'
type UserRepository_UpdateUserMultiCurrency_Call struct {
	*mock.Call
}

// UpdateUserMultiCurrency is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - multiCurrency bool
func (_e *UserRepository_Expecter) UpdateUserMultiCurrency(ctx interface{}, userID interface{}, multiCurrency interface{}) *UserRepository_UpdateUserMultiCurrency_Call {
	return &UserRepository_UpdateUserMultiCurrency_Call{Call: _e.mock.On("UpdateUserMultiCurrency", ctx, userID, multiCurrency)}
}

func (_c *UserRepository_UpdateUserMultiCurrency_Call) Run(run func(ctx context.Context, userID string, multiCurrency bool)) *UserRepository_UpdateUserMultiCurrency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *UserRepository_UpdateUserMultiCurrency_Call) Return(_a0 error) *UserRepository_UpdateUserMultiCurrency_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_UpdateUserMultiCurrency_Call) RunAndReturn(run func(context.Context, string, bool) error) *UserRepository_UpdateUserMultiCurrency_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserPreferences provides a mock function with given fields: ctx, userExternalID, ghostMode, hideStats
func (_m *UserRepository) UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode *bool, hideStats *bool) error {
	ret := _m.Called(ctx, userExternalID, ghostMode, hideStats)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserPreferences")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *bool, *bool) error); ok {
		r0 = rf(ctx, userExternalID, ghostMode, hideStats)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserRepository_UpdateUserPreferences_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserPreferences'
type UserRepository_UpdateUserPreferences_Call struct {
	*mock.Call
}

// UpdateUserPreferences is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalID string
//   - ghostMode *bool
//   - hideStats *bool
func (_e *UserRepository_Expecter) UpdateUserPreferences(ctx interface{}, userExternalID interface{}, ghostMode interface{}, hideStats interface{}) *UserRepository_UpdateUserPreferences_Call {
	return &UserRepository_UpdateUserPreferences_Call{Call: _e.mock.On("UpdateUserPreferences", ctx, userExternalID, ghostMode, hideStats)}
}

func (_c *UserRepository_UpdateUserPreferences_Call) Run(run func(ctx context.Context, userExternalID string, ghostMode *bool, hideStats *bool)) *UserRepository_UpdateUserPreferences_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*bool), args[3].(*bool))
	})
	return _c
}

func (_c *UserRepository_UpdateUserPreferences_Call) Return(_a0 error) *UserRepository_UpdateUserPreferences_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserRepository_UpdateUserPreferences_Call) RunAndReturn(run func(context.Context, string, *bool, *bool) error) *UserRepository_UpdateUserPreferences_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserXP provides a mock function with given fields: ctx, userID, xpToAdd
func (_m *UserRepository) UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	ret := _m.Called(ctx, userID, xpToAdd)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserXP")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) (float64, error)); ok {
		return rf(ctx, userID, xpToAdd)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, float64) float64); ok {
		r0 = rf(ctx, userID, xpToAdd)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, float64) error); ok {
		r1 = rf(ctx, userID, xpToAdd)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_UpdateUserXP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserXP'
type UserRepository_UpdateUserXP_Call struct {
	*mock.Call
}

// UpdateUserXP is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - xpToAdd float64
func (_e *UserRepository_Expecter) UpdateUserXP(ctx interface{}, userID interface{}, xpToAdd interface{}) *UserRepository_UpdateUserXP_Call {
	return &UserRepository_UpdateUserXP_Call{Call: _e.mock.On("UpdateUserXP", ctx, userID, xpToAdd)}
}

func (_c *UserRepository_UpdateUserXP_Call) Run(run func(ctx context.Context, userID string, xpToAdd float64)) *UserRepository_UpdateUserXP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(float64))
	})
	return _c
}

func (_c *UserRepository_UpdateUserXP_Call) Return(_a0 float64, _a1 error) *UserRepository_UpdateUserXP_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_UpdateUserXP_Call) RunAndReturn(run func(context.Context, string, float64) (float64, error)) *UserRepository_UpdateUserXP_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertUser provides a mock function with given fields: ctx, user
func (_m *UserRepository) UpsertUser(ctx context.Context, user *domain.User) (*domain.User, error) {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for UpsertUser")
	}

	var r0 *domain.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.User) (*domain.User, error)); ok {
		return rf(ctx, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.User) *domain.User); ok {
		r0 = rf(ctx, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.User) error); ok {
		r1 = rf(ctx, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserRepository_UpsertUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertUser'
type UserRepository_UpsertUser_Call struct {
	*mock.Call
}

// UpsertUser is a helper method to define mock.On call
//   - ctx context.Context
//   - user *domain.User
func (_e *UserRepository_Expecter) UpsertUser(ctx interface{}, user interface{}) *UserRepository_UpsertUser_Call {
	return &UserRepository_UpsertUser_Call{Call: _e.mock.On("UpsertUser", ctx, user)}
}

func (_c *UserRepository_UpsertUser_Call) Run(run func(ctx context.Context, user *domain.User)) *UserRepository_UpsertUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.User))
	})
	return _c
}

func (_c *UserRepository_UpsertUser_Call) Return(_a0 *domain.User, _a1 error) *UserRepository_UpsertUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserRepository_UpsertUser_Call) RunAndReturn(run func(context.Context, *domain.User) (*domain.User, error)) *UserRepository_UpsertUser_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserRepository creates a new instance of UserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserRepository {
	mock := &UserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
