// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// SettingsService is an autogenerated mock type for the SettingsService type
type SettingsService struct {
	mock.Mock
}

type SettingsService_Expecter struct {
	mock *mock.Mock
}

func (_m *SettingsService) EXPECT() *SettingsService_Expecter {
	return &SettingsService_Expecter{mock: &_m.Mock}
}

// CreateSettings provides a mock function with given fields: ctx, userID, settings
func (_m *SettingsService) CreateSettings(ctx context.Context, userID string, settings domain.UserSettingsPayload) (domain.SettingsResponse, error) {
	ret := _m.Called(ctx, userID, settings)

	if len(ret) == 0 {
		panic("no return value specified for CreateSettings")
	}

	var r0 domain.SettingsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserSettingsPayload) (domain.SettingsResponse, error)); ok {
		return rf(ctx, userID, settings)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, domain.UserSettingsPayload) domain.SettingsResponse); ok {
		r0 = rf(ctx, userID, settings)
	} else {
		r0 = ret.Get(0).(domain.SettingsResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, domain.UserSettingsPayload) error); ok {
		r1 = rf(ctx, userID, settings)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SettingsService_CreateSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSettings'
type SettingsService_CreateSettings_Call struct {
	*mock.Call
}

// CreateSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - settings domain.UserSettingsPayload
func (_e *SettingsService_Expecter) CreateSettings(ctx interface{}, userID interface{}, settings interface{}) *SettingsService_CreateSettings_Call {
	return &SettingsService_CreateSettings_Call{Call: _e.mock.On("CreateSettings", ctx, userID, settings)}
}

func (_c *SettingsService_CreateSettings_Call) Run(run func(ctx context.Context, userID string, settings domain.UserSettingsPayload)) *SettingsService_CreateSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.UserSettingsPayload))
	})
	return _c
}

func (_c *SettingsService_CreateSettings_Call) Return(_a0 domain.SettingsResponse, _a1 error) *SettingsService_CreateSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SettingsService_CreateSettings_Call) RunAndReturn(run func(context.Context, string, domain.UserSettingsPayload) (domain.SettingsResponse, error)) *SettingsService_CreateSettings_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSettings provides a mock function with given fields: userID, key
func (_m *SettingsService) DeleteSettings(userID string, key string) error {
	ret := _m.Called(userID, key)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(userID, key)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SettingsService_DeleteSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSettings'
type SettingsService_DeleteSettings_Call struct {
	*mock.Call
}

// DeleteSettings is a helper method to define mock.On call
//   - userID string
//   - key string
func (_e *SettingsService_Expecter) DeleteSettings(userID interface{}, key interface{}) *SettingsService_DeleteSettings_Call {
	return &SettingsService_DeleteSettings_Call{Call: _e.mock.On("DeleteSettings", userID, key)}
}

func (_c *SettingsService_DeleteSettings_Call) Run(run func(userID string, key string)) *SettingsService_DeleteSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *SettingsService_DeleteSettings_Call) Return(_a0 error) *SettingsService_DeleteSettings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SettingsService_DeleteSettings_Call) RunAndReturn(run func(string, string) error) *SettingsService_DeleteSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetSettings provides a mock function with given fields: userID
func (_m *SettingsService) GetSettings(userID string) (domain.SettingsResponse, error) {
	ret := _m.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GetSettings")
	}

	var r0 domain.SettingsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (domain.SettingsResponse, error)); ok {
		return rf(userID)
	}
	if rf, ok := ret.Get(0).(func(string) domain.SettingsResponse); ok {
		r0 = rf(userID)
	} else {
		r0 = ret.Get(0).(domain.SettingsResponse)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SettingsService_GetSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSettings'
type SettingsService_GetSettings_Call struct {
	*mock.Call
}

// GetSettings is a helper method to define mock.On call
//   - userID string
func (_e *SettingsService_Expecter) GetSettings(userID interface{}) *SettingsService_GetSettings_Call {
	return &SettingsService_GetSettings_Call{Call: _e.mock.On("GetSettings", userID)}
}

func (_c *SettingsService_GetSettings_Call) Run(run func(userID string)) *SettingsService_GetSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *SettingsService_GetSettings_Call) Return(_a0 domain.SettingsResponse, _a1 error) *SettingsService_GetSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *SettingsService_GetSettings_Call) RunAndReturn(run func(string) (domain.SettingsResponse, error)) *SettingsService_GetSettings_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSettings provides a mock function with given fields: userID, settings
func (_m *SettingsService) UpdateSettings(userID string, settings domain.UserSettings) error {
	ret := _m.Called(userID, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSettings")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, domain.UserSettings) error); ok {
		r0 = rf(userID, settings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SettingsService_UpdateSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSettings'
type SettingsService_UpdateSettings_Call struct {
	*mock.Call
}

// UpdateSettings is a helper method to define mock.On call
//   - userID string
//   - settings domain.UserSettings
func (_e *SettingsService_Expecter) UpdateSettings(userID interface{}, settings interface{}) *SettingsService_UpdateSettings_Call {
	return &SettingsService_UpdateSettings_Call{Call: _e.mock.On("UpdateSettings", userID, settings)}
}

func (_c *SettingsService_UpdateSettings_Call) Run(run func(userID string, settings domain.UserSettings)) *SettingsService_UpdateSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(domain.UserSettings))
	})
	return _c
}

func (_c *SettingsService_UpdateSettings_Call) Return(_a0 error) *SettingsService_UpdateSettings_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SettingsService_UpdateSettings_Call) RunAndReturn(run func(string, domain.UserSettings) error) *SettingsService_UpdateSettings_Call {
	_c.Call.Return(run)
	return _c
}

// NewSettingsService creates a new instance of SettingsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSettingsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SettingsService {
	mock := &SettingsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
