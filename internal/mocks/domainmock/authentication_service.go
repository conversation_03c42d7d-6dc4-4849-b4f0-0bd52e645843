// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// AuthenticationService is an autogenerated mock type for the AuthenticationService type
type AuthenticationService struct {
	mock.Mock
}

type AuthenticationService_Expecter struct {
	mock *mock.Mock
}

func (_m *AuthenticationService) EXPECT() *AuthenticationService_Expecter {
	return &AuthenticationService_Expecter{mock: &_m.Mock}
}

// ValidateAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *AuthenticationService) ValidateAccessToken(ctx context.Context, accessToken string) (domain.ValidateTokenResponse, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for ValidateAccessToken")
	}

	var r0 domain.ValidateTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (domain.ValidateTokenResponse, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) domain.ValidateTokenResponse); ok {
		r0 = rf(ctx, accessToken)
	} else {
		r0 = ret.Get(0).(domain.ValidateTokenResponse)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AuthenticationService_ValidateAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateAccessToken'
type AuthenticationService_ValidateAccessToken_Call struct {
	*mock.Call
}

// ValidateAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *AuthenticationService_Expecter) ValidateAccessToken(ctx interface{}, accessToken interface{}) *AuthenticationService_ValidateAccessToken_Call {
	return &AuthenticationService_ValidateAccessToken_Call{Call: _e.mock.On("ValidateAccessToken", ctx, accessToken)}
}

func (_c *AuthenticationService_ValidateAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *AuthenticationService_ValidateAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *AuthenticationService_ValidateAccessToken_Call) Return(_a0 domain.ValidateTokenResponse, _a1 error) *AuthenticationService_ValidateAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AuthenticationService_ValidateAccessToken_Call) RunAndReturn(run func(context.Context, string) (domain.ValidateTokenResponse, error)) *AuthenticationService_ValidateAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// NewAuthenticationService creates a new instance of AuthenticationService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAuthenticationService(t interface {
	mock.TestingT
	Cleanup(func())
}) *AuthenticationService {
	mock := &AuthenticationService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
