// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// VIPUserBalanceRepository is an autogenerated mock type for the VIPUserBalanceRepository type
type VIPUserBalanceRepository struct {
	mock.Mock
}

type VIPUserBalanceRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *VIPUserBalanceRepository) EXPECT() *VIPUserBalanceRepository_Expecter {
	return &VIPUserBalanceRepository_Expecter{mock: &_m.Mock}
}

// CreateVIPUserBalance provides a mock function with given fields: ctx, user
func (_m *VIPUserBalanceRepository) CreateVIPUserBalance(ctx context.Context, user *domain.VIPUserBalance) (*domain.VIPUserBalance, error) {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for CreateVIPUserBalance")
	}

	var r0 *domain.VIPUserBalance
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.VIPUserBalance) (*domain.VIPUserBalance, error)); ok {
		return rf(ctx, user)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.VIPUserBalance) *domain.VIPUserBalance); ok {
		r0 = rf(ctx, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.VIPUserBalance)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.VIPUserBalance) error); ok {
		r1 = rf(ctx, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VIPUserBalanceRepository_CreateVIPUserBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateVIPUserBalance'
type VIPUserBalanceRepository_CreateVIPUserBalance_Call struct {
	*mock.Call
}

// CreateVIPUserBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - user *domain.VIPUserBalance
func (_e *VIPUserBalanceRepository_Expecter) CreateVIPUserBalance(ctx interface{}, user interface{}) *VIPUserBalanceRepository_CreateVIPUserBalance_Call {
	return &VIPUserBalanceRepository_CreateVIPUserBalance_Call{Call: _e.mock.On("CreateVIPUserBalance", ctx, user)}
}

func (_c *VIPUserBalanceRepository_CreateVIPUserBalance_Call) Run(run func(ctx context.Context, user *domain.VIPUserBalance)) *VIPUserBalanceRepository_CreateVIPUserBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.VIPUserBalance))
	})
	return _c
}

func (_c *VIPUserBalanceRepository_CreateVIPUserBalance_Call) Return(_a0 *domain.VIPUserBalance, _a1 error) *VIPUserBalanceRepository_CreateVIPUserBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VIPUserBalanceRepository_CreateVIPUserBalance_Call) RunAndReturn(run func(context.Context, *domain.VIPUserBalance) (*domain.VIPUserBalance, error)) *VIPUserBalanceRepository_CreateVIPUserBalance_Call {
	_c.Call.Return(run)
	return _c
}

// NewVIPUserBalanceRepository creates a new instance of VIPUserBalanceRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewVIPUserBalanceRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *VIPUserBalanceRepository {
	mock := &VIPUserBalanceRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
