// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// UserBonusQueue is an autogenerated mock type for the UserBonusQueue type
type UserBonusQueue struct {
	mock.Mock
}

type UserBonusQueue_Expecter struct {
	mock *mock.Mock
}

func (_m *UserBonusQueue) EXPECT() *UserBonusQueue_Expecter {
	return &UserBonusQueue_Expecter{mock: &_m.Mock}
}

// ProcessBonus provides a mock function with given fields: b
func (_m *UserBonusQueue) ProcessBonus(b domain.BonusQueueItem) error {
	ret := _m.Called(b)

	if len(ret) == 0 {
		panic("no return value specified for ProcessBonus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.BonusQueueItem) error); ok {
		r0 = rf(b)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusQueue_ProcessBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessBonus'
type UserBonusQueue_ProcessBonus_Call struct {
	*mock.Call
}

// ProcessBonus is a helper method to define mock.On call
//   - b domain.BonusQueueItem
func (_e *UserBonusQueue_Expecter) ProcessBonus(b interface{}) *UserBonusQueue_ProcessBonus_Call {
	return &UserBonusQueue_ProcessBonus_Call{Call: _e.mock.On("ProcessBonus", b)}
}

func (_c *UserBonusQueue_ProcessBonus_Call) Run(run func(b domain.BonusQueueItem)) *UserBonusQueue_ProcessBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.BonusQueueItem))
	})
	return _c
}

func (_c *UserBonusQueue_ProcessBonus_Call) Return(_a0 error) *UserBonusQueue_ProcessBonus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusQueue_ProcessBonus_Call) RunAndReturn(run func(domain.BonusQueueItem) error) *UserBonusQueue_ProcessBonus_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserBonusQueue creates a new instance of UserBonusQueue. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserBonusQueue(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserBonusQueue {
	mock := &UserBonusQueue{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
