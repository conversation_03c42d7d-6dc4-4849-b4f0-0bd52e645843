// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// GameProvider is an autogenerated mock type for the GameProvider type
type GameProvider struct {
	mock.Mock
}

type GameProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *GameProvider) EXPECT() *GameProvider_Expecter {
	return &GameProvider_Expecter{mock: &_m.Mock}
}

// GetAllGames provides a mock function with given fields: ctx
func (_m *GameProvider) GetAllGames(ctx context.Context) ([]domain.Game, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllGames")
	}

	var r0 []domain.Game
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]domain.Game, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []domain.Game); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Game)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GameProvider_GetAllGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllGames'
type GameProvider_GetAllGames_Call struct {
	*mock.Call
}

// GetAllGames is a helper method to define mock.On call
//   - ctx context.Context
func (_e *GameProvider_Expecter) GetAllGames(ctx interface{}) *GameProvider_GetAllGames_Call {
	return &GameProvider_GetAllGames_Call{Call: _e.mock.On("GetAllGames", ctx)}
}

func (_c *GameProvider_GetAllGames_Call) Run(run func(ctx context.Context)) *GameProvider_GetAllGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *GameProvider_GetAllGames_Call) Return(_a0 []domain.Game, _a1 error) *GameProvider_GetAllGames_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GameProvider_GetAllGames_Call) RunAndReturn(run func(context.Context) ([]domain.Game, error)) *GameProvider_GetAllGames_Call {
	_c.Call.Return(run)
	return _c
}

// NewGameProvider creates a new instance of GameProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewGameProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *GameProvider {
	mock := &GameProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
