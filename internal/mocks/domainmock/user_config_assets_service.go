// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// UserConfigAssetsService is an autogenerated mock type for the UserConfigAssetsService type
type UserConfigAssetsService struct {
	mock.Mock
}

type UserConfigAssetsService_Expecter struct {
	mock *mock.Mock
}

func (_m *UserConfigAssetsService) EXPECT() *UserConfigAssetsService_Expecter {
	return &UserConfigAssetsService_Expecter{mock: &_m.Mock}
}

// GetAsset provides a mock function with given fields: ctx, id
func (_m *UserConfigAssetsService) GetAsset(ctx context.Context, id uuid.UUID) (*domain.UserConfigAsset, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetAsset")
	}

	var r0 *domain.UserConfigAsset
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.UserConfigAsset, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.UserConfigAsset); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserConfigAsset)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserConfigAssetsService_GetAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAsset'
type UserConfigAssetsService_GetAsset_Call struct {
	*mock.Call
}

// GetAsset is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *UserConfigAssetsService_Expecter) GetAsset(ctx interface{}, id interface{}) *UserConfigAssetsService_GetAsset_Call {
	return &UserConfigAssetsService_GetAsset_Call{Call: _e.mock.On("GetAsset", ctx, id)}
}

func (_c *UserConfigAssetsService_GetAsset_Call) Run(run func(ctx context.Context, id uuid.UUID)) *UserConfigAssetsService_GetAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *UserConfigAssetsService_GetAsset_Call) Return(_a0 *domain.UserConfigAsset, _a1 error) *UserConfigAssetsService_GetAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserConfigAssetsService_GetAsset_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.UserConfigAsset, error)) *UserConfigAssetsService_GetAsset_Call {
	_c.Call.Return(run)
	return _c
}

// GetAssets provides a mock function with given fields: ctx
func (_m *UserConfigAssetsService) GetAssets(ctx context.Context) ([]domain.UserConfigAsset, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAssets")
	}

	var r0 []domain.UserConfigAsset
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]domain.UserConfigAsset, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []domain.UserConfigAsset); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.UserConfigAsset)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserConfigAssetsService_GetAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAssets'
type UserConfigAssetsService_GetAssets_Call struct {
	*mock.Call
}

// GetAssets is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserConfigAssetsService_Expecter) GetAssets(ctx interface{}) *UserConfigAssetsService_GetAssets_Call {
	return &UserConfigAssetsService_GetAssets_Call{Call: _e.mock.On("GetAssets", ctx)}
}

func (_c *UserConfigAssetsService_GetAssets_Call) Run(run func(ctx context.Context)) *UserConfigAssetsService_GetAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserConfigAssetsService_GetAssets_Call) Return(_a0 []domain.UserConfigAsset, _a1 error) *UserConfigAssetsService_GetAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserConfigAssetsService_GetAssets_Call) RunAndReturn(run func(context.Context) ([]domain.UserConfigAsset, error)) *UserConfigAssetsService_GetAssets_Call {
	_c.Call.Return(run)
	return _c
}

// GetAssetsSubTypes provides a mock function with given fields: ctx
func (_m *UserConfigAssetsService) GetAssetsSubTypes(ctx context.Context) ([]string, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetsSubTypes")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]string, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserConfigAssetsService_GetAssetsSubTypes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAssetsSubTypes'
type UserConfigAssetsService_GetAssetsSubTypes_Call struct {
	*mock.Call
}

// GetAssetsSubTypes is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserConfigAssetsService_Expecter) GetAssetsSubTypes(ctx interface{}) *UserConfigAssetsService_GetAssetsSubTypes_Call {
	return &UserConfigAssetsService_GetAssetsSubTypes_Call{Call: _e.mock.On("GetAssetsSubTypes", ctx)}
}

func (_c *UserConfigAssetsService_GetAssetsSubTypes_Call) Run(run func(ctx context.Context)) *UserConfigAssetsService_GetAssetsSubTypes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserConfigAssetsService_GetAssetsSubTypes_Call) Return(_a0 []string, _a1 error) *UserConfigAssetsService_GetAssetsSubTypes_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserConfigAssetsService_GetAssetsSubTypes_Call) RunAndReturn(run func(context.Context) ([]string, error)) *UserConfigAssetsService_GetAssetsSubTypes_Call {
	_c.Call.Return(run)
	return _c
}

// GetAssetsTypes provides a mock function with given fields: ctx
func (_m *UserConfigAssetsService) GetAssetsTypes(ctx context.Context) ([]string, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAssetsTypes")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]string, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserConfigAssetsService_GetAssetsTypes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAssetsTypes'
type UserConfigAssetsService_GetAssetsTypes_Call struct {
	*mock.Call
}

// GetAssetsTypes is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserConfigAssetsService_Expecter) GetAssetsTypes(ctx interface{}) *UserConfigAssetsService_GetAssetsTypes_Call {
	return &UserConfigAssetsService_GetAssetsTypes_Call{Call: _e.mock.On("GetAssetsTypes", ctx)}
}

func (_c *UserConfigAssetsService_GetAssetsTypes_Call) Run(run func(ctx context.Context)) *UserConfigAssetsService_GetAssetsTypes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserConfigAssetsService_GetAssetsTypes_Call) Return(_a0 []string, _a1 error) *UserConfigAssetsService_GetAssetsTypes_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserConfigAssetsService_GetAssetsTypes_Call) RunAndReturn(run func(context.Context) ([]string, error)) *UserConfigAssetsService_GetAssetsTypes_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserConfigAssetsService creates a new instance of UserConfigAssetsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserConfigAssetsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserConfigAssetsService {
	mock := &UserConfigAssetsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
