// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// SlackAlertClient is an autogenerated mock type for the SlackAlertClient type
type SlackAlertClient struct {
	mock.Mock
}

type SlackAlertClient_Expecter struct {
	mock *mock.Mock
}

func (_m *SlackAlertClient) EXPECT() *SlackAlertClient_Expecter {
	return &SlackAlertClient_Expecter{mock: &_m.Mock}
}

// SendSlackNotification provides a mock function with given fields: UserBonus
func (_m *SlackAlertClient) SendSlackNotification(UserBonus []domain.UserBonus) error {
	ret := _m.Called(UserBonus)

	if len(ret) == 0 {
		panic("no return value specified for SendSlackNotification")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func([]domain.UserBonus) error); ok {
		r0 = rf(UserBonus)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SlackAlertClient_SendSlackNotification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendSlackNotification'
type SlackAlertClient_SendSlackNotification_Call struct {
	*mock.Call
}

// SendSlackNotification is a helper method to define mock.On call
//   - UserBonus []domain.UserBonus
func (_e *SlackAlertClient_Expecter) SendSlackNotification(UserBonus interface{}) *SlackAlertClient_SendSlackNotification_Call {
	return &SlackAlertClient_SendSlackNotification_Call{Call: _e.mock.On("SendSlackNotification", UserBonus)}
}

func (_c *SlackAlertClient_SendSlackNotification_Call) Run(run func(UserBonus []domain.UserBonus)) *SlackAlertClient_SendSlackNotification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]domain.UserBonus))
	})
	return _c
}

func (_c *SlackAlertClient_SendSlackNotification_Call) Return(_a0 error) *SlackAlertClient_SendSlackNotification_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *SlackAlertClient_SendSlackNotification_Call) RunAndReturn(run func([]domain.UserBonus) error) *SlackAlertClient_SendSlackNotification_Call {
	_c.Call.Return(run)
	return _c
}

// NewSlackAlertClient creates a new instance of SlackAlertClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSlackAlertClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SlackAlertClient {
	mock := &SlackAlertClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
