// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"
)

// UserBonusService is an autogenerated mock type for the UserBonusService type
type UserBonusService struct {
	mock.Mock
}

type UserBonusService_Expecter struct {
	mock *mock.Mock
}

func (_m *UserBonusService) EXPECT() *UserBonusService_Expecter {
	return &UserBonusService_Expecter{mock: &_m.Mock}
}

// ActivateUserBonusesByExternalIds provides a mock function with given fields: ctx, userExternalIDs, category, status, rewardAmount, bonusExternalId
func (_m *UserBonusService) ActivateUserBonusesByExternalIds(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int) error {
	ret := _m.Called(ctx, userExternalIDs, category, status, rewardAmount, bonusExternalId)

	if len(ret) == 0 {
		panic("no return value specified for ActivateUserBonusesByExternalIds")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, float64, int) error); ok {
		r0 = rf(ctx, userExternalIDs, category, status, rewardAmount, bonusExternalId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_ActivateUserBonusesByExternalIds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ActivateUserBonusesByExternalIds'
type UserBonusService_ActivateUserBonusesByExternalIds_Call struct {
	*mock.Call
}

// ActivateUserBonusesByExternalIds is a helper method to define mock.On call
//   - ctx context.Context
//   - userExternalIDs string
//   - category string
//   - status string
//   - rewardAmount float64
//   - bonusExternalId int
func (_e *UserBonusService_Expecter) ActivateUserBonusesByExternalIds(ctx interface{}, userExternalIDs interface{}, category interface{}, status interface{}, rewardAmount interface{}, bonusExternalId interface{}) *UserBonusService_ActivateUserBonusesByExternalIds_Call {
	return &UserBonusService_ActivateUserBonusesByExternalIds_Call{Call: _e.mock.On("ActivateUserBonusesByExternalIds", ctx, userExternalIDs, category, status, rewardAmount, bonusExternalId)}
}

func (_c *UserBonusService_ActivateUserBonusesByExternalIds_Call) Run(run func(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int)) *UserBonusService_ActivateUserBonusesByExternalIds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(float64), args[5].(int))
	})
	return _c
}

func (_c *UserBonusService_ActivateUserBonusesByExternalIds_Call) Return(_a0 error) *UserBonusService_ActivateUserBonusesByExternalIds_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_ActivateUserBonusesByExternalIds_Call) RunAndReturn(run func(context.Context, string, string, string, float64, int) error) *UserBonusService_ActivateUserBonusesByExternalIds_Call {
	_c.Call.Return(run)
	return _c
}

// AssignBonusByType provides a mock function with given fields: ctx, bonusType, startDate, endDate
func (_m *UserBonusService) AssignBonusByType(ctx context.Context, bonusType string, startDate string, endDate string) error {
	ret := _m.Called(ctx, bonusType, startDate, endDate)

	if len(ret) == 0 {
		panic("no return value specified for AssignBonusByType")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, bonusType, startDate, endDate)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_AssignBonusByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignBonusByType'
type UserBonusService_AssignBonusByType_Call struct {
	*mock.Call
}

// AssignBonusByType is a helper method to define mock.On call
//   - ctx context.Context
//   - bonusType string
//   - startDate string
//   - endDate string
func (_e *UserBonusService_Expecter) AssignBonusByType(ctx interface{}, bonusType interface{}, startDate interface{}, endDate interface{}) *UserBonusService_AssignBonusByType_Call {
	return &UserBonusService_AssignBonusByType_Call{Call: _e.mock.On("AssignBonusByType", ctx, bonusType, startDate, endDate)}
}

func (_c *UserBonusService_AssignBonusByType_Call) Run(run func(ctx context.Context, bonusType string, startDate string, endDate string)) *UserBonusService_AssignBonusByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *UserBonusService_AssignBonusByType_Call) Return(_a0 error) *UserBonusService_AssignBonusByType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_AssignBonusByType_Call) RunAndReturn(run func(context.Context, string, string, string) error) *UserBonusService_AssignBonusByType_Call {
	_c.Call.Return(run)
	return _c
}

// AssignSpecialBonus provides a mock function with given fields: ctx, userId, username, category, rewardAmount, bonusExternalId, reason, note
func (_m *UserBonusService) AssignSpecialBonus(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string) error {
	ret := _m.Called(ctx, userId, username, category, rewardAmount, bonusExternalId, reason, note)

	if len(ret) == 0 {
		panic("no return value specified for AssignSpecialBonus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, float64, int, string, string) error); ok {
		r0 = rf(ctx, userId, username, category, rewardAmount, bonusExternalId, reason, note)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_AssignSpecialBonus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignSpecialBonus'
type UserBonusService_AssignSpecialBonus_Call struct {
	*mock.Call
}

// AssignSpecialBonus is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
//   - username string
//   - category string
//   - rewardAmount float64
//   - bonusExternalId int
//   - reason string
//   - note string
func (_e *UserBonusService_Expecter) AssignSpecialBonus(ctx interface{}, userId interface{}, username interface{}, category interface{}, rewardAmount interface{}, bonusExternalId interface{}, reason interface{}, note interface{}) *UserBonusService_AssignSpecialBonus_Call {
	return &UserBonusService_AssignSpecialBonus_Call{Call: _e.mock.On("AssignSpecialBonus", ctx, userId, username, category, rewardAmount, bonusExternalId, reason, note)}
}

func (_c *UserBonusService_AssignSpecialBonus_Call) Run(run func(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string)) *UserBonusService_AssignSpecialBonus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(float64), args[5].(int), args[6].(string), args[7].(string))
	})
	return _c
}

func (_c *UserBonusService_AssignSpecialBonus_Call) Return(_a0 error) *UserBonusService_AssignSpecialBonus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_AssignSpecialBonus_Call) RunAndReturn(run func(context.Context, string, string, string, float64, int, string, string) error) *UserBonusService_AssignSpecialBonus_Call {
	_c.Call.Return(run)
	return _c
}

// ClaimAllBonusAndUpdateWallet provides a mock function with given fields: ctx, externalId, token
func (_m *UserBonusService) ClaimAllBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	ret := _m.Called(ctx, externalId, token)

	if len(ret) == 0 {
		panic("no return value specified for ClaimAllBonusAndUpdateWallet")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, externalId, token)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_ClaimAllBonusAndUpdateWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClaimAllBonusAndUpdateWallet'
type UserBonusService_ClaimAllBonusAndUpdateWallet_Call struct {
	*mock.Call
}

// ClaimAllBonusAndUpdateWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - externalId string
//   - token string
func (_e *UserBonusService_Expecter) ClaimAllBonusAndUpdateWallet(ctx interface{}, externalId interface{}, token interface{}) *UserBonusService_ClaimAllBonusAndUpdateWallet_Call {
	return &UserBonusService_ClaimAllBonusAndUpdateWallet_Call{Call: _e.mock.On("ClaimAllBonusAndUpdateWallet", ctx, externalId, token)}
}

func (_c *UserBonusService_ClaimAllBonusAndUpdateWallet_Call) Run(run func(ctx context.Context, externalId string, token string)) *UserBonusService_ClaimAllBonusAndUpdateWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserBonusService_ClaimAllBonusAndUpdateWallet_Call) Return(_a0 error) *UserBonusService_ClaimAllBonusAndUpdateWallet_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_ClaimAllBonusAndUpdateWallet_Call) RunAndReturn(run func(context.Context, string, string) error) *UserBonusService_ClaimAllBonusAndUpdateWallet_Call {
	_c.Call.Return(run)
	return _c
}

// ClaimTiltBonusAndUpdateWallet provides a mock function with given fields: ctx, externalId, token
func (_m *UserBonusService) ClaimTiltBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	ret := _m.Called(ctx, externalId, token)

	if len(ret) == 0 {
		panic("no return value specified for ClaimTiltBonusAndUpdateWallet")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, externalId, token)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_ClaimTiltBonusAndUpdateWallet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClaimTiltBonusAndUpdateWallet'
type UserBonusService_ClaimTiltBonusAndUpdateWallet_Call struct {
	*mock.Call
}

// ClaimTiltBonusAndUpdateWallet is a helper method to define mock.On call
//   - ctx context.Context
//   - externalId string
//   - token string
func (_e *UserBonusService_Expecter) ClaimTiltBonusAndUpdateWallet(ctx interface{}, externalId interface{}, token interface{}) *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call {
	return &UserBonusService_ClaimTiltBonusAndUpdateWallet_Call{Call: _e.mock.On("ClaimTiltBonusAndUpdateWallet", ctx, externalId, token)}
}

func (_c *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call) Run(run func(ctx context.Context, externalId string, token string)) *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call) Return(_a0 error) *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call) RunAndReturn(run func(context.Context, string, string) error) *UserBonusService_ClaimTiltBonusAndUpdateWallet_Call {
	_c.Call.Return(run)
	return _c
}

// CreateReloadBonuses provides a mock function with given fields: ctx, requestData
func (_m *UserBonusService) CreateReloadBonuses(ctx context.Context, requestData []domain.ReloadBonusRequest) error {
	ret := _m.Called(ctx, requestData)

	if len(ret) == 0 {
		panic("no return value specified for CreateReloadBonuses")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []domain.ReloadBonusRequest) error); ok {
		r0 = rf(ctx, requestData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_CreateReloadBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateReloadBonuses'
type UserBonusService_CreateReloadBonuses_Call struct {
	*mock.Call
}

// CreateReloadBonuses is a helper method to define mock.On call
//   - ctx context.Context
//   - requestData []domain.ReloadBonusRequest
func (_e *UserBonusService_Expecter) CreateReloadBonuses(ctx interface{}, requestData interface{}) *UserBonusService_CreateReloadBonuses_Call {
	return &UserBonusService_CreateReloadBonuses_Call{Call: _e.mock.On("CreateReloadBonuses", ctx, requestData)}
}

func (_c *UserBonusService_CreateReloadBonuses_Call) Run(run func(ctx context.Context, requestData []domain.ReloadBonusRequest)) *UserBonusService_CreateReloadBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]domain.ReloadBonusRequest))
	})
	return _c
}

func (_c *UserBonusService_CreateReloadBonuses_Call) Return(_a0 error) *UserBonusService_CreateReloadBonuses_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_CreateReloadBonuses_Call) RunAndReturn(run func(context.Context, []domain.ReloadBonusRequest) error) *UserBonusService_CreateReloadBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteExpiredBonuses provides a mock function with given fields: ctx
func (_m *UserBonusService) DeleteExpiredBonuses(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteExpiredBonuses")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_DeleteExpiredBonuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteExpiredBonuses'
type UserBonusService_DeleteExpiredBonuses_Call struct {
	*mock.Call
}

// DeleteExpiredBonuses is a helper method to define mock.On call
//   - ctx context.Context
func (_e *UserBonusService_Expecter) DeleteExpiredBonuses(ctx interface{}) *UserBonusService_DeleteExpiredBonuses_Call {
	return &UserBonusService_DeleteExpiredBonuses_Call{Call: _e.mock.On("DeleteExpiredBonuses", ctx)}
}

func (_c *UserBonusService_DeleteExpiredBonuses_Call) Run(run func(ctx context.Context)) *UserBonusService_DeleteExpiredBonuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *UserBonusService_DeleteExpiredBonuses_Call) Return(_a0 error) *UserBonusService_DeleteExpiredBonuses_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_DeleteExpiredBonuses_Call) RunAndReturn(run func(context.Context) error) *UserBonusService_DeleteExpiredBonuses_Call {
	_c.Call.Return(run)
	return _c
}

// GetLevelUpBonusOfUser provides a mock function with given fields: userId
func (_m *UserBonusService) GetLevelUpBonusOfUser(userId string) ([]domain.UserBonus, error) {
	ret := _m.Called(userId)

	if len(ret) == 0 {
		panic("no return value specified for GetLevelUpBonusOfUser")
	}

	var r0 []domain.UserBonus
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]domain.UserBonus, error)); ok {
		return rf(userId)
	}
	if rf, ok := ret.Get(0).(func(string) []domain.UserBonus); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.UserBonus)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserBonusService_GetLevelUpBonusOfUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLevelUpBonusOfUser'
type UserBonusService_GetLevelUpBonusOfUser_Call struct {
	*mock.Call
}

// GetLevelUpBonusOfUser is a helper method to define mock.On call
//   - userId string
func (_e *UserBonusService_Expecter) GetLevelUpBonusOfUser(userId interface{}) *UserBonusService_GetLevelUpBonusOfUser_Call {
	return &UserBonusService_GetLevelUpBonusOfUser_Call{Call: _e.mock.On("GetLevelUpBonusOfUser", userId)}
}

func (_c *UserBonusService_GetLevelUpBonusOfUser_Call) Run(run func(userId string)) *UserBonusService_GetLevelUpBonusOfUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *UserBonusService_GetLevelUpBonusOfUser_Call) Return(_a0 []domain.UserBonus, _a1 error) *UserBonusService_GetLevelUpBonusOfUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserBonusService_GetLevelUpBonusOfUser_Call) RunAndReturn(run func(string) ([]domain.UserBonus, error)) *UserBonusService_GetLevelUpBonusOfUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserBonusesByExternalID provides a mock function with given fields: ctx, externalID
func (_m *UserBonusService) GetUserBonusesByExternalID(ctx context.Context, externalID string) ([]domain.UserBonus, error) {
	ret := _m.Called(ctx, externalID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserBonusesByExternalID")
	}

	var r0 []domain.UserBonus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]domain.UserBonus, error)); ok {
		return rf(ctx, externalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []domain.UserBonus); ok {
		r0 = rf(ctx, externalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.UserBonus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UserBonusService_GetUserBonusesByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserBonusesByExternalID'
type UserBonusService_GetUserBonusesByExternalID_Call struct {
	*mock.Call
}

// GetUserBonusesByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
func (_e *UserBonusService_Expecter) GetUserBonusesByExternalID(ctx interface{}, externalID interface{}) *UserBonusService_GetUserBonusesByExternalID_Call {
	return &UserBonusService_GetUserBonusesByExternalID_Call{Call: _e.mock.On("GetUserBonusesByExternalID", ctx, externalID)}
}

func (_c *UserBonusService_GetUserBonusesByExternalID_Call) Run(run func(ctx context.Context, externalID string)) *UserBonusService_GetUserBonusesByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserBonusService_GetUserBonusesByExternalID_Call) Return(_a0 []domain.UserBonus, _a1 error) *UserBonusService_GetUserBonusesByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UserBonusService_GetUserBonusesByExternalID_Call) RunAndReturn(run func(context.Context, string) ([]domain.UserBonus, error)) *UserBonusService_GetUserBonusesByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// SingleClaimUpdate provides a mock function with given fields: ctx, token, externalID, bonusConfigID
func (_m *UserBonusService) SingleClaimUpdate(ctx context.Context, token string, externalID string, bonusConfigID int) error {
	ret := _m.Called(ctx, token, externalID, bonusConfigID)

	if len(ret) == 0 {
		panic("no return value specified for SingleClaimUpdate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int) error); ok {
		r0 = rf(ctx, token, externalID, bonusConfigID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_SingleClaimUpdate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SingleClaimUpdate'
type UserBonusService_SingleClaimUpdate_Call struct {
	*mock.Call
}

// SingleClaimUpdate is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
//   - externalID string
//   - bonusConfigID int
func (_e *UserBonusService_Expecter) SingleClaimUpdate(ctx interface{}, token interface{}, externalID interface{}, bonusConfigID interface{}) *UserBonusService_SingleClaimUpdate_Call {
	return &UserBonusService_SingleClaimUpdate_Call{Call: _e.mock.On("SingleClaimUpdate", ctx, token, externalID, bonusConfigID)}
}

func (_c *UserBonusService_SingleClaimUpdate_Call) Run(run func(ctx context.Context, token string, externalID string, bonusConfigID int)) *UserBonusService_SingleClaimUpdate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(int))
	})
	return _c
}

func (_c *UserBonusService_SingleClaimUpdate_Call) Return(_a0 error) *UserBonusService_SingleClaimUpdate_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_SingleClaimUpdate_Call) RunAndReturn(run func(context.Context, string, string, int) error) *UserBonusService_SingleClaimUpdate_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateModalPopupClosed provides a mock function with given fields: ctx, userId
func (_m *UserBonusService) UpdateModalPopupClosed(ctx context.Context, userId string) error {
	ret := _m.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateModalPopupClosed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_UpdateModalPopupClosed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateModalPopupClosed'
type UserBonusService_UpdateModalPopupClosed_Call struct {
	*mock.Call
}

// UpdateModalPopupClosed is a helper method to define mock.On call
//   - ctx context.Context
//   - userId string
func (_e *UserBonusService_Expecter) UpdateModalPopupClosed(ctx interface{}, userId interface{}) *UserBonusService_UpdateModalPopupClosed_Call {
	return &UserBonusService_UpdateModalPopupClosed_Call{Call: _e.mock.On("UpdateModalPopupClosed", ctx, userId)}
}

func (_c *UserBonusService_UpdateModalPopupClosed_Call) Run(run func(ctx context.Context, userId string)) *UserBonusService_UpdateModalPopupClosed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *UserBonusService_UpdateModalPopupClosed_Call) Return(_a0 error) *UserBonusService_UpdateModalPopupClosed_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_UpdateModalPopupClosed_Call) RunAndReturn(run func(context.Context, string) error) *UserBonusService_UpdateModalPopupClosed_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserBonusStatus provides a mock function with given fields: ctx, externalBonusId, externalID, status
func (_m *UserBonusService) UpdateUserBonusStatus(ctx context.Context, externalBonusId int, externalID string, status string) error {
	ret := _m.Called(ctx, externalBonusId, externalID, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserBonusStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, string) error); ok {
		r0 = rf(ctx, externalBonusId, externalID, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UserBonusService_UpdateUserBonusStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserBonusStatus'
type UserBonusService_UpdateUserBonusStatus_Call struct {
	*mock.Call
}

// UpdateUserBonusStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - externalBonusId int
//   - externalID string
//   - status string
func (_e *UserBonusService_Expecter) UpdateUserBonusStatus(ctx interface{}, externalBonusId interface{}, externalID interface{}, status interface{}) *UserBonusService_UpdateUserBonusStatus_Call {
	return &UserBonusService_UpdateUserBonusStatus_Call{Call: _e.mock.On("UpdateUserBonusStatus", ctx, externalBonusId, externalID, status)}
}

func (_c *UserBonusService_UpdateUserBonusStatus_Call) Run(run func(ctx context.Context, externalBonusId int, externalID string, status string)) *UserBonusService_UpdateUserBonusStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *UserBonusService_UpdateUserBonusStatus_Call) Return(_a0 error) *UserBonusService_UpdateUserBonusStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *UserBonusService_UpdateUserBonusStatus_Call) RunAndReturn(run func(context.Context, int, string, string) error) *UserBonusService_UpdateUserBonusStatus_Call {
	_c.Call.Return(run)
	return _c
}

// NewUserBonusService creates a new instance of UserBonusService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserBonusService(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserBonusService {
	mock := &UserBonusService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
