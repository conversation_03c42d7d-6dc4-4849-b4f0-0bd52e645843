// Code generated by mockery v2.53.3. DO NOT EDIT.

package domainmock

import (
	context "context"

	domain "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/google/uuid"
)

// BetRepository is an autogenerated mock type for the BetRepository type
type BetRepository struct {
	mock.Mock
}

type BetRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *BetRepository) EXPECT() *BetRepository_Expecter {
	return &BetRepository_Expecter{mock: &_m.Mock}
}

// ExportBets provides a mock function with given fields: ctx, params
func (_m *BetRepository) ExportBets(ctx context.Context, params *domain.ExportBetsParams) ([]domain.Bet, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ExportBets")
	}

	var r0 []domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportBetsParams) ([]domain.Bet, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.ExportBetsParams) []domain.Bet); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.ExportBetsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_ExportBets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExportBets'
type BetRepository_ExportBets_Call struct {
	*mock.Call
}

// ExportBets is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.ExportBetsParams
func (_e *BetRepository_Expecter) ExportBets(ctx interface{}, params interface{}) *BetRepository_ExportBets_Call {
	return &BetRepository_ExportBets_Call{Call: _e.mock.On("ExportBets", ctx, params)}
}

func (_c *BetRepository_ExportBets_Call) Run(run func(ctx context.Context, params *domain.ExportBetsParams)) *BetRepository_ExportBets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.ExportBetsParams))
	})
	return _c
}

func (_c *BetRepository_ExportBets_Call) Return(_a0 []domain.Bet, _a1 error) *BetRepository_ExportBets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_ExportBets_Call) RunAndReturn(run func(context.Context, *domain.ExportBetsParams) ([]domain.Bet, error)) *BetRepository_ExportBets_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetByExternalID provides a mock function with given fields: ctx, externalID
func (_m *BetRepository) GetBetByExternalID(ctx context.Context, externalID string) (*domain.Bet, error) {
	ret := _m.Called(ctx, externalID)

	if len(ret) == 0 {
		panic("no return value specified for GetBetByExternalID")
	}

	var r0 *domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*domain.Bet, error)); ok {
		return rf(ctx, externalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *domain.Bet); ok {
		r0 = rf(ctx, externalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetBetByExternalID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetByExternalID'
type BetRepository_GetBetByExternalID_Call struct {
	*mock.Call
}

// GetBetByExternalID is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
func (_e *BetRepository_Expecter) GetBetByExternalID(ctx interface{}, externalID interface{}) *BetRepository_GetBetByExternalID_Call {
	return &BetRepository_GetBetByExternalID_Call{Call: _e.mock.On("GetBetByExternalID", ctx, externalID)}
}

func (_c *BetRepository_GetBetByExternalID_Call) Run(run func(ctx context.Context, externalID string)) *BetRepository_GetBetByExternalID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BetRepository_GetBetByExternalID_Call) Return(_a0 *domain.Bet, _a1 error) *BetRepository_GetBetByExternalID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetBetByExternalID_Call) RunAndReturn(run func(context.Context, string) (*domain.Bet, error)) *BetRepository_GetBetByExternalID_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetByID provides a mock function with given fields: ctx, id
func (_m *BetRepository) GetBetByID(ctx context.Context, id uuid.UUID) (*domain.Bet, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetBetByID")
	}

	var r0 *domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*domain.Bet, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *domain.Bet); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetBetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetByID'
type BetRepository_GetBetByID_Call struct {
	*mock.Call
}

// GetBetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *BetRepository_Expecter) GetBetByID(ctx interface{}, id interface{}) *BetRepository_GetBetByID_Call {
	return &BetRepository_GetBetByID_Call{Call: _e.mock.On("GetBetByID", ctx, id)}
}

func (_c *BetRepository_GetBetByID_Call) Run(run func(ctx context.Context, id uuid.UUID)) *BetRepository_GetBetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *BetRepository_GetBetByID_Call) Return(_a0 *domain.Bet, _a1 error) *BetRepository_GetBetByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetBetByID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*domain.Bet, error)) *BetRepository_GetBetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetBets provides a mock function with given fields: ctx, params
func (_m *BetRepository) GetBets(ctx context.Context, params *domain.GetBetsParams) (*domain.Bets, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetBets")
	}

	var r0 *domain.Bets
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsParams) (*domain.Bets, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsParams) *domain.Bets); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bets)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetBetsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetBets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBets'
type BetRepository_GetBets_Call struct {
	*mock.Call
}

// GetBets is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetBetsParams
func (_e *BetRepository_Expecter) GetBets(ctx interface{}, params interface{}) *BetRepository_GetBets_Call {
	return &BetRepository_GetBets_Call{Call: _e.mock.On("GetBets", ctx, params)}
}

func (_c *BetRepository_GetBets_Call) Run(run func(ctx context.Context, params *domain.GetBetsParams)) *BetRepository_GetBets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetBetsParams))
	})
	return _c
}

func (_c *BetRepository_GetBets_Call) Return(_a0 *domain.Bets, _a1 error) *BetRepository_GetBets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetBets_Call) RunAndReturn(run func(context.Context, *domain.GetBetsParams) (*domain.Bets, error)) *BetRepository_GetBets_Call {
	_c.Call.Return(run)
	return _c
}

// GetBetsByUserID provides a mock function with given fields: ctx, params
func (_m *BetRepository) GetBetsByUserID(ctx context.Context, params *domain.GetBetsByUserIDParams) (*domain.UserBets, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetBetsByUserID")
	}

	var r0 *domain.UserBets
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsByUserIDParams) (*domain.UserBets, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.GetBetsByUserIDParams) *domain.UserBets); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.UserBets)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.GetBetsByUserIDParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetBetsByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBetsByUserID'
type BetRepository_GetBetsByUserID_Call struct {
	*mock.Call
}

// GetBetsByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - params *domain.GetBetsByUserIDParams
func (_e *BetRepository_Expecter) GetBetsByUserID(ctx interface{}, params interface{}) *BetRepository_GetBetsByUserID_Call {
	return &BetRepository_GetBetsByUserID_Call{Call: _e.mock.On("GetBetsByUserID", ctx, params)}
}

func (_c *BetRepository_GetBetsByUserID_Call) Run(run func(ctx context.Context, params *domain.GetBetsByUserIDParams)) *BetRepository_GetBetsByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.GetBetsByUserIDParams))
	})
	return _c
}

func (_c *BetRepository_GetBetsByUserID_Call) Return(_a0 *domain.UserBets, _a1 error) *BetRepository_GetBetsByUserID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetBetsByUserID_Call) RunAndReturn(run func(context.Context, *domain.GetBetsByUserIDParams) (*domain.UserBets, error)) *BetRepository_GetBetsByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestBetsByUserId provides a mock function with given fields: ctx, externalID
func (_m *BetRepository) GetLatestBetsByUserId(ctx context.Context, externalID string) ([]domain.Bet, error) {
	ret := _m.Called(ctx, externalID)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestBetsByUserId")
	}

	var r0 []domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]domain.Bet, error)); ok {
		return rf(ctx, externalID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []domain.Bet); ok {
		r0 = rf(ctx, externalID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, externalID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetLatestBetsByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestBetsByUserId'
type BetRepository_GetLatestBetsByUserId_Call struct {
	*mock.Call
}

// GetLatestBetsByUserId is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
func (_e *BetRepository_Expecter) GetLatestBetsByUserId(ctx interface{}, externalID interface{}) *BetRepository_GetLatestBetsByUserId_Call {
	return &BetRepository_GetLatestBetsByUserId_Call{Call: _e.mock.On("GetLatestBetsByUserId", ctx, externalID)}
}

func (_c *BetRepository_GetLatestBetsByUserId_Call) Run(run func(ctx context.Context, externalID string)) *BetRepository_GetLatestBetsByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BetRepository_GetLatestBetsByUserId_Call) Return(_a0 []domain.Bet, _a1 error) *BetRepository_GetLatestBetsByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetLatestBetsByUserId_Call) RunAndReturn(run func(context.Context, string) ([]domain.Bet, error)) *BetRepository_GetLatestBetsByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// GetWageredAmountByTypeForXDays provides a mock function with given fields: ctx, userID, days
func (_m *BetRepository) GetWageredAmountByTypeForXDays(ctx context.Context, userID string, days int) (float64, error) {
	ret := _m.Called(ctx, userID, days)

	if len(ret) == 0 {
		panic("no return value specified for GetWageredAmountByTypeForXDays")
	}

	var r0 float64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int) (float64, error)); ok {
		return rf(ctx, userID, days)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int) float64); ok {
		r0 = rf(ctx, userID, days)
	} else {
		r0 = ret.Get(0).(float64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int) error); ok {
		r1 = rf(ctx, userID, days)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_GetWageredAmountByTypeForXDays_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWageredAmountByTypeForXDays'
type BetRepository_GetWageredAmountByTypeForXDays_Call struct {
	*mock.Call
}

// GetWageredAmountByTypeForXDays is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
//   - days int
func (_e *BetRepository_Expecter) GetWageredAmountByTypeForXDays(ctx interface{}, userID interface{}, days interface{}) *BetRepository_GetWageredAmountByTypeForXDays_Call {
	return &BetRepository_GetWageredAmountByTypeForXDays_Call{Call: _e.mock.On("GetWageredAmountByTypeForXDays", ctx, userID, days)}
}

func (_c *BetRepository_GetWageredAmountByTypeForXDays_Call) Run(run func(ctx context.Context, userID string, days int)) *BetRepository_GetWageredAmountByTypeForXDays_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int))
	})
	return _c
}

func (_c *BetRepository_GetWageredAmountByTypeForXDays_Call) Return(_a0 float64, _a1 error) *BetRepository_GetWageredAmountByTypeForXDays_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_GetWageredAmountByTypeForXDays_Call) RunAndReturn(run func(context.Context, string, int) (float64, error)) *BetRepository_GetWageredAmountByTypeForXDays_Call {
	_c.Call.Return(run)
	return _c
}

// ShareUserBetByBetId provides a mock function with given fields: ctx, externalID, betID
func (_m *BetRepository) ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (domain.Bet, bool, error) {
	ret := _m.Called(ctx, externalID, betID)

	if len(ret) == 0 {
		panic("no return value specified for ShareUserBetByBetId")
	}

	var r0 domain.Bet
	var r1 bool
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uuid.UUID) (domain.Bet, bool, error)); ok {
		return rf(ctx, externalID, betID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, uuid.UUID) domain.Bet); ok {
		r0 = rf(ctx, externalID, betID)
	} else {
		r0 = ret.Get(0).(domain.Bet)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, uuid.UUID) bool); ok {
		r1 = rf(ctx, externalID, betID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, uuid.UUID) error); ok {
		r2 = rf(ctx, externalID, betID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// BetRepository_ShareUserBetByBetId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ShareUserBetByBetId'
type BetRepository_ShareUserBetByBetId_Call struct {
	*mock.Call
}

// ShareUserBetByBetId is a helper method to define mock.On call
//   - ctx context.Context
//   - externalID string
//   - betID uuid.UUID
func (_e *BetRepository_Expecter) ShareUserBetByBetId(ctx interface{}, externalID interface{}, betID interface{}) *BetRepository_ShareUserBetByBetId_Call {
	return &BetRepository_ShareUserBetByBetId_Call{Call: _e.mock.On("ShareUserBetByBetId", ctx, externalID, betID)}
}

func (_c *BetRepository_ShareUserBetByBetId_Call) Run(run func(ctx context.Context, externalID string, betID uuid.UUID)) *BetRepository_ShareUserBetByBetId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *BetRepository_ShareUserBetByBetId_Call) Return(_a0 domain.Bet, _a1 bool, _a2 error) *BetRepository_ShareUserBetByBetId_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *BetRepository_ShareUserBetByBetId_Call) RunAndReturn(run func(context.Context, string, uuid.UUID) (domain.Bet, bool, error)) *BetRepository_ShareUserBetByBetId_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertBet provides a mock function with given fields: ctx, bet
func (_m *BetRepository) UpsertBet(ctx context.Context, bet *domain.Bet) (*domain.Bet, error) {
	ret := _m.Called(ctx, bet)

	if len(ret) == 0 {
		panic("no return value specified for UpsertBet")
	}

	var r0 *domain.Bet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Bet) (*domain.Bet, error)); ok {
		return rf(ctx, bet)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *domain.Bet) *domain.Bet); ok {
		r0 = rf(ctx, bet)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Bet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *domain.Bet) error); ok {
		r1 = rf(ctx, bet)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BetRepository_UpsertBet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertBet'
type BetRepository_UpsertBet_Call struct {
	*mock.Call
}

// UpsertBet is a helper method to define mock.On call
//   - ctx context.Context
//   - bet *domain.Bet
func (_e *BetRepository_Expecter) UpsertBet(ctx interface{}, bet interface{}) *BetRepository_UpsertBet_Call {
	return &BetRepository_UpsertBet_Call{Call: _e.mock.On("UpsertBet", ctx, bet)}
}

func (_c *BetRepository_UpsertBet_Call) Run(run func(ctx context.Context, bet *domain.Bet)) *BetRepository_UpsertBet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.Bet))
	})
	return _c
}

func (_c *BetRepository_UpsertBet_Call) Return(_a0 *domain.Bet, _a1 error) *BetRepository_UpsertBet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BetRepository_UpsertBet_Call) RunAndReturn(run func(context.Context, *domain.Bet) (*domain.Bet, error)) *BetRepository_UpsertBet_Call {
	_c.Call.Return(run)
	return _c
}

// NewBetRepository creates a new instance of BetRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBetRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *BetRepository {
	mock := &BetRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
