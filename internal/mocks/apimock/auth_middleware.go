// Code generated by mockery v2.53.3. DO NOT EDIT.

package apimock

import (
	fiber "github.com/gofiber/fiber/v2"
	mock "github.com/stretchr/testify/mock"
)

// AuthMiddleware is an autogenerated mock type for the AuthMiddleware type
type AuthMiddleware struct {
	mock.Mock
}

type AuthMiddleware_Expecter struct {
	mock *mock.Mock
}

func (_m *AuthMiddleware) EXPECT() *AuthMiddleware_Expecter {
	return &AuthMiddleware_Expecter{mock: &_m.Mock}
}

// WithAccessToken provides a mock function with given fields: c
func (_m *AuthMiddleware) WithAccessToken(c *fiber.Ctx) error {
	ret := _m.Called(c)

	if len(ret) == 0 {
		panic("no return value specified for WithAccessToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*fiber.Ctx) error); ok {
		r0 = rf(c)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AuthMiddleware_WithAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithAccessToken'
type AuthMiddleware_WithAccessToken_Call struct {
	*mock.Call
}

// WithAccessToken is a helper method to define mock.On call
//   - c *fiber.Ctx
func (_e *AuthMiddleware_Expecter) WithAccessToken(c interface{}) *AuthMiddleware_WithAccessToken_Call {
	return &AuthMiddleware_WithAccessToken_Call{Call: _e.mock.On("WithAccessToken", c)}
}

func (_c *AuthMiddleware_WithAccessToken_Call) Run(run func(c *fiber.Ctx)) *AuthMiddleware_WithAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*fiber.Ctx))
	})
	return _c
}

func (_c *AuthMiddleware_WithAccessToken_Call) Return(_a0 error) *AuthMiddleware_WithAccessToken_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *AuthMiddleware_WithAccessToken_Call) RunAndReturn(run func(*fiber.Ctx) error) *AuthMiddleware_WithAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// WithBypass provides a mock function with given fields: c
func (_m *AuthMiddleware) WithBypass(c *fiber.Ctx) error {
	ret := _m.Called(c)

	if len(ret) == 0 {
		panic("no return value specified for WithBypass")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*fiber.Ctx) error); ok {
		r0 = rf(c)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AuthMiddleware_WithBypass_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithBypass'
type AuthMiddleware_WithBypass_Call struct {
	*mock.Call
}

// WithBypass is a helper method to define mock.On call
//   - c *fiber.Ctx
func (_e *AuthMiddleware_Expecter) WithBypass(c interface{}) *AuthMiddleware_WithBypass_Call {
	return &AuthMiddleware_WithBypass_Call{Call: _e.mock.On("WithBypass", c)}
}

func (_c *AuthMiddleware_WithBypass_Call) Run(run func(c *fiber.Ctx)) *AuthMiddleware_WithBypass_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*fiber.Ctx))
	})
	return _c
}

func (_c *AuthMiddleware_WithBypass_Call) Return(_a0 error) *AuthMiddleware_WithBypass_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *AuthMiddleware_WithBypass_Call) RunAndReturn(run func(*fiber.Ctx) error) *AuthMiddleware_WithBypass_Call {
	_c.Call.Return(run)
	return _c
}

// NewAuthMiddleware creates a new instance of AuthMiddleware. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAuthMiddleware(t interface {
	mock.TestingT
	Cleanup(func())
}) *AuthMiddleware {
	mock := &AuthMiddleware{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
