// Code generated by mockery v2.53.3. DO NOT EDIT.

package grecomock

import mock "github.com/stretchr/testify/mock"

// Producer is an autogenerated mock type for the Producer type
type Producer struct {
	mock.Mock
}

type Producer_Expecter struct {
	mock *mock.Mock
}

func (_m *Producer) EXPECT() *Producer_Expecter {
	return &Producer_Expecter{mock: &_m.Mock}
}

// SendMessage provides a mock function with given fields: topic, message
func (_m *Producer) SendMessage(topic string, message interface{}) error {
	ret := _m.Called(topic, message)

	if len(ret) == 0 {
		panic("no return value specified for SendMessage")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, interface{}) error); ok {
		r0 = rf(topic, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Producer_SendMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMessage'
type Producer_SendMessage_Call struct {
	*mock.Call
}

// SendMessage is a helper method to define mock.On call
//   - topic string
//   - message interface{}
func (_e *Producer_Expecter) SendMessage(topic interface{}, message interface{}) *Producer_SendMessage_Call {
	return &Producer_SendMessage_Call{Call: _e.mock.On("SendMessage", topic, message)}
}

func (_c *Producer_SendMessage_Call) Run(run func(topic string, message interface{})) *Producer_SendMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(interface{}))
	})
	return _c
}

func (_c *Producer_SendMessage_Call) Return(_a0 error) *Producer_SendMessage_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Producer_SendMessage_Call) RunAndReturn(run func(string, interface{}) error) *Producer_SendMessage_Call {
	_c.Call.Return(run)
	return _c
}

// NewProducer creates a new instance of Producer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewProducer(t interface {
	mock.TestingT
	Cleanup(func())
}) *Producer {
	mock := &Producer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
