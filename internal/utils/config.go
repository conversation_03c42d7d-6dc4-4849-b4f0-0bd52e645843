package utils

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

type RESTServiceConfig struct {
	Address          string              `mapstructure:"address" validate:"required"`
	LogLevel         string              `mapstructure:"log_level"`
	EnableAuthBypass bool                `mapstructure:"enable_auth_bypass"`
	Postgres         PostgresConfig      `mapstructure:"postgres"`
	Keycloak         KeycloakConfig      `mapstructure:"keycloak"`
	Directus         DirectusConfig      `mapstructure:"directus"`
	Wagering         WageringConfig      `mapstructure:"wagering"`
	CMSURL           string              `mapstructure:"cms_url" validate:"required"`
	CMSGamesURL      string              `mapstructure:"cms_games_url"`
	Wallet           WalletConfig        `mapstructure:"wallet"`
	EmailService     EmailServiceConfig  `mapstructure:"email"`
	SendGridConfig   SendGridConfig      `mapstructure:"sendgrid"`
	RailsClient      RailsClient         `mapstructure:"rails_client"`
	SwaggerConfig    SwaggerConfig       `mapstructure:"swagger"`
	SlackConfig      SlackConfig         `mapstructure:"slack"`
	S3BucketConfig   S3BucketConfig      `mapstructure:"AWS"`
	OptimoveConfig   OptimoveConfig      `mapstructure:"optimove"`
	BetSimulation    BetSimulationConfig `mapstructure:"bet_simulation"`
}

type RailsClient struct {
	BaseUrl   string `mapstructure:"base_url" validate:"required"`
	SecretKey string
}
type SlackConfig struct {
	Token          string `mapstructure:"token"`
	ChannelID      string `mapstructure:"channel_id"`
	AlertURL       string `mapstructure:"alert_url"`
	BonusThreshold string
}

type SendGridConfig struct {
	SendGridAPIKey string
}

type EventsServiceConfig struct {
	Address       string              `mapstructure:"address" validate:"required"`
	LogLevel      string              `mapstructure:"log_level"`
	Postgres      PostgresConfig      `mapstructure:"postgres"`
	KafkaConsumer KafkaConsumerConfig `mapstructure:"kafka_consumer"`
	BetSimulation BetSimulationConfig `mapstructure:"bet_simulation"`
}

type GrecoEventsServiceConfig struct {
	Enabled            bool                `mapstructure:"enabled"`
	LogLevel           string              `mapstructure:"log_level"`
	Postgres           PostgresConfig      `mapstructure:"postgres"`
	KafkaConsumer      KafkaConsumerConfig `mapstructure:"kafka_consumer"`
	GrecoKafkaProducer KafkaProducerConfig `mapstructure:"greco_kafka_producer"`
}

type PostgresConfig struct {
	User     string `mapstructure:"user" validate:"required"`
	Host     string `mapstructure:"host" validate:"required"`
	Port     int    `mapstructure:"port" validate:"required"`
	Database string `mapstructure:"database" validate:"required"`
	Password string
}

type KeycloakConfig struct {
	URL          string `mapstructure:"url" validate:"required"`
	Realm        string `mapstructure:"realm" validate:"required"`
	ClientID     string `mapstructure:"client_id" validate:"required"`
	TenantID     string `mapstructure:"tenant_id" validate:"required"`
	ClientSecret string `mapstructure:"client_secret"`
}

type KafkaConsumerConfig struct {
	Brokers        string `mapstructure:"brokers" validate:"required"`
	GroupId        string `mapstructure:"group_id" validate:"required"`
	WorkQueueSize  int    `mapstructure:"work_queue_size" validate:"required"`
	SSLCertificate string
	SSLKey         string
	SSLKeyPassword string
	SSLCA          string
}

type KafkaProducerConfig struct {
	Brokers      string `mapstructure:"brokers" validate:"required"`
	SASLUsername string `mapstructure:"username" validate:"required"`
	SASLPassword string
	APIKey       string
}

type DirectusConfig struct {
	URL                 string `mapstructure:"url" validate:"required"`
	ApiKey              string `mapstructure:"api_key"`
	AuthenticationToken string `mapstructure:"authentication_token"`
}

type WageringConfig struct {
	BaseURL           string `mapstructure:"base_url" validate:"required"`
	AuthenticationURL string `mapstructure:"authentication_url" validate:"required"`
	ClientID          string `mapstructure:"client_id" validate:"required"`
	ClientSecret      string `mapstructure:"client_secret"`
}

type WalletConfig struct {
	AuthenticationURL string `mapstructure:"authentication_url" validate:"required"`
	BaseURL           string `mapstructure:"base_url" validate:"required"`
	ClientID          string `mapstructure:"client_id" validate:"required"`
	ElantilURL        string `mapstructure:"elantil_url" validate:"required"`
	ClientSecret      string `mapstructure:"client_secret"`
}

type EmailServiceConfig struct {
	AuthenticationURL string `mapstructure:"authentication_url" validate:"required"`
	BaseURL           string `mapstructure:"base_url" validate:"required"`
	Realm             string `mapstructure:"realm" validate:"required"`
	ClientID          string `mapstructure:"client_id" validate:"required"`
	ClientSecret      string
}

type SwaggerConfig struct {
	Username string
	Password string
}

type S3BucketConfig struct {
	BucketName      string `mapstructure:"bucketName"`
	Region          string `mapstructure:"Region"`
	AccessKey       string
	SecretAccessKey string
}

type OptimoveConfig struct {
	XAPIKey string `mapstructure:"key"`
}

type VipTierConfig struct {
	Name    string     `mapstructure:"name"`
	XPRange [2]float64 `mapstructure:"xp_range"`
}

type SimulatedGameConfig struct {
	ID          string `mapstructure:"id"`
	CMSGameID   int64  `mapstructure:"cms_game_id"`
	ExternalID  string `mapstructure:"external_id"`
	Name        string `mapstructure:"name"`
	Slug        string `mapstructure:"slug"`
	ThumbnailID string `mapstructure:"thumbnail_id"`
	// Game-specific multiplier configuration
	LossRate       float64   `mapstructure:"loss_rate"`       // Probability of 0x multiplier
	WinMultipliers []float64 `mapstructure:"win_multipliers"` // Possible win multipliers
	WinWeights     []float64 `mapstructure:"win_weights"`     // Probability weights for each multiplier
}

type BetSimulationConfig struct {
	Enabled               bool                  `mapstructure:"enabled"`
	NewBetIntervalRange   [2]int                `mapstructure:"new_bet_interval_range"`
	HighPayoutThreshold   float64               `mapstructure:"high_payout_threshold"`
	HighPayoutProbability float64               `mapstructure:"high_payout_probability"`
	Usernames             []string              `mapstructure:"usernames"`
	UsernamesFile         string                `mapstructure:"usernames_file"`
	VipTiersFile          string                `mapstructure:"vip_tiers_file"`
	GamesFile             string                `mapstructure:"games_file"`
	VipTiers              []VipTierConfig       // Populated from CSV
	Games                 []SimulatedGameConfig // Populated from CSV
	ActiveUserQueueSize   int                   `mapstructure:"active_user_queue_size"`
}

func LoadRESTServiceConfig(validator *validator.Validate) (*RESTServiceConfig, error) {
	env := getEnviroment()
	viper.AddConfigPath(getConfigPath(env))
	viper.SetConfigName("rest_service")
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	config := &RESTServiceConfig{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}

	if err := validator.Struct(config); err != nil {
		return nil, err
	}

	session, err := newAWSSession()
	if err != nil {
		return nil, err
	}

	if err := loadPostgresSecrets(env, session, &config.Postgres); err != nil {
		return nil, err
	}

	if err := loadKeycloakSecrets(env, session, &config.Keycloak); err != nil {
		return nil, err
	}

	if err := loadDirectusSecretKey(env, session, &config.Directus); err != nil {
		return nil, err
	}

	if err := loadWageringClientSecret(env, session, &config.Wagering); err != nil {
		return nil, err
	}

	if err := loadWalletClientSecret(env, session, &config.Wallet); err != nil {
		return nil, err
	}

	if err := loadEmailServiceClientSecret(env, session, &config.EmailService); err != nil {
		return nil, err
	}

	if err := loadSendGridSecretKey(env, session, &config.SendGridConfig); err != nil {
		return nil, err
	}

	if err := loadRailsClientConfig(env, session, &config.RailsClient); err != nil {
		return nil, err
	}

	if err := loadSwaggerCreds(env, session, &config.SwaggerConfig); err != nil {
		return nil, err
	}
	if err := loadSlackConfig(env, session, &config.SlackConfig); err != nil {
		return nil, err
	}
	if err := loadS3BucketConfig(env, session, &config.S3BucketConfig); err != nil {
		return nil, err
	}

	configDir := filepath.Dir(viper.ConfigFileUsed())
	if err := loadSlatedUsernamesFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	if err := loadVipTiersFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	if err := loadGamesFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	return config, nil
}

func LoadEventsServiceConfig(validator *validator.Validate) (*EventsServiceConfig, error) {
	env := getEnviroment()
	viper.AddConfigPath(getConfigPath(env))
	viper.SetConfigName("events_service")
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	config := &EventsServiceConfig{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}

	if err := validator.Struct(config); err != nil {
		return nil, err
	}

	session, err := newAWSSession()
	if err != nil {
		return nil, err
	}

	if err := loadPostgresSecrets(env, session, &config.Postgres); err != nil {
		return nil, err
	}

	if err := loadKafkaConsumerSecrets(env, session, &config.KafkaConsumer); err != nil {
		return nil, err
	}

	configDir := filepath.Dir(viper.ConfigFileUsed())
	if err := loadSlatedUsernamesFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	if err := loadVipTiersFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	if err := loadGamesFromCSV(configDir, &config.BetSimulation); err != nil {
		return nil, err
	}

	return config, nil
}

func LoadEventsGrecoServiceConfig(validator *validator.Validate) (*GrecoEventsServiceConfig, error) {
	env := getEnviroment()
	viper.AddConfigPath(getConfigPath(env))
	viper.SetConfigName("greco_events_service")
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	config := &GrecoEventsServiceConfig{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}

	if err := validator.Struct(config); err != nil {
		return nil, err
	}

	session, err := newAWSSession()
	if err != nil {
		return nil, err
	}

	if err := loadPostgresSecrets(env, session, &config.Postgres); err != nil {
		return nil, err
	}

	if err := loadKafkaConsumerSecrets(env, session, &config.KafkaConsumer); err != nil {
		return nil, err
	}

	if err := loadGrecoKafkaProducerSecrets(env, session, &config.GrecoKafkaProducer); err != nil {
		return nil, err
	}

	return config, nil
}

func getEnviroment() string {
	if env := os.Getenv("ENV"); env != "" {
		return env
	} else {
		return "local"
	}
}

func getConfigPath(env string) string {
	projectRoot := FindProjectRoot("config")
	return filepath.Join(projectRoot, "config", env)
}

func loadPostgresSecrets(env string, session *session.Session, config *PostgresConfig) error {
	var err error

	config.Password, err = getAWSSecret(session, env+"/community-db-password")
	if err != nil {
		return fmt.Errorf("get DB password secret: %w", err)
	}

	return nil
}

func loadKeycloakSecrets(env string, session *session.Session, config *KeycloakConfig) error {
	var err error

	if config.ClientSecret, err = getAWSSecret(session, env+"/keycloack_client_secret"); err != nil {
		return fmt.Errorf("get Keycloack client secret secret: %w", err)
	}

	return nil
}

func loadKafkaConsumerSecrets(env string, session *session.Session, config *KafkaConsumerConfig) error {
	var err error

	config.SSLCertificate, err = getAWSSecret(session, env+"/chainkafka")
	if err != nil {
		return fmt.Errorf("get Kafka certificate chain secret: %w", err)
	}

	config.SSLKey, err = getAWSSecret(session, env+"/keykafka")
	if err != nil {
		return fmt.Errorf("get Kafka SSL key secret: %w", err)
	}

	config.SSLKeyPassword, err = getAWSSecret(session, env+"/sslKeyPassword")
	if err != nil {
		return fmt.Errorf("get Kafka SSL key password secret: %w", err)
	}

	config.SSLCA, err = getAWSSecret(session, env+"/cakafka")
	if err != nil {
		return fmt.Errorf("get Kafka CA secret: %w", err)
	}

	return nil
}

func loadGrecoKafkaProducerSecrets(env string, session *session.Session, config *KafkaProducerConfig) error {
	var err error

	config.SASLPassword, err = getAWSSecret(session, env+"/grecoProducerPassword")
	if err != nil {
		return fmt.Errorf("get Greco producer password secret: %w", err)
	}

	config.APIKey, err = getAWSSecret(session, env+"/grecoProducerHMAC")
	if err != nil {
		return fmt.Errorf("get Greco producer HMAC: %w", err)
	}

	return nil
}

func loadWageringClientSecret(env string, session *session.Session, config *WageringConfig) error {
	var err error

	config.ClientSecret, err = getAWSSecret(session, env+"/wagering_client_secret")
	if err != nil {
		return fmt.Errorf("get wagering client secret: %w", err)
	}

	return nil
}

func loadDirectusSecretKey(env string, session *session.Session, config *DirectusConfig) error {
	var err error

	config.ApiKey, err = getAWSSecret(session, env+"/directus_api_key")
	if err != nil {
		return fmt.Errorf("get directus api key: %w", err)
	}

	config.AuthenticationToken, err = getAWSSecret(session, env+"/directus_authentication_token")
	if err != nil {
		return fmt.Errorf("get directus authentication token: %w", err)
	}

	return nil
}

func loadWalletClientSecret(env string, session *session.Session, config *WalletConfig) error {
	var err error

	config.ClientSecret, err = getAWSSecret(session, env+"/wallet_client_secret")
	if err != nil {
		return fmt.Errorf("get wallet client secret: %w", err)
	}

	return nil
}

func loadEmailServiceClientSecret(env string, session *session.Session, config *EmailServiceConfig) error {
	var err error

	config.ClientSecret, err = getAWSSecret(session, env+"/email_service_client_secret")
	if err != nil {
		return fmt.Errorf("get email service client secret: %w", err)
	}

	return nil
}

func loadSendGridSecretKey(env string, session *session.Session, config *SendGridConfig) error {
	var err error

	config.SendGridAPIKey, err = getAWSSecret(session, env+"/sendgrid_api_key")
	if err != nil {
		return fmt.Errorf("get sendgrid api key: %w", err)
	}

	return nil
}

func loadRailsClientConfig(env string, session *session.Session, config *RailsClient) error {
	var err error

	config.SecretKey, err = getAWSSecret(session, env+"/rails_client_secret")
	if err != nil {
		return fmt.Errorf("get rails client secret: %w", err)
	}

	return nil
}

func loadSwaggerCreds(env string, session *session.Session, config *SwaggerConfig) error {
	var err error

	config.Username, err = getAWSSecret(session, env+"/swagger_username")
	if err != nil {
		return fmt.Errorf("get swagger username: %w", err)
	}

	config.Password, err = getAWSSecret(session, env+"/swagger_password")
	if err != nil {
		return fmt.Errorf("get swagger password: %w", err)
	}

	return nil
}

func loadSlackConfig(env string, session *session.Session, config *SlackConfig) error {
	var err error
	if env != "local" {
		if config.Token == "" {
			config.Token, err = getAWSSecret(session, env+"/slack_token")
			if err != nil {
				config.Token = "*********************************************************"
			}
		}

		if config.ChannelID == "" {
			config.ChannelID, err = getAWSSecret(session, env+"/slack_channel_id")
			if err != nil {
				config.ChannelID = "C08EJ3JHNBY"
			}
		}

		config.BonusThreshold, err = getAWSSecret(session, env+"/slack_bonus_threshold")
		if err != nil {
			config.BonusThreshold = "1000"
		}
	}
	return nil
}

func loadS3BucketConfig(env string, session *session.Session, config *S3BucketConfig) error {
	var err error

	config.AccessKey, err = getAWSSecret(session, env+"/aws_access_key")
	if err != nil {
		slog.Error("failed to get AWS access key", "error", err.Error())
	}

	config.SecretAccessKey, err = getAWSSecret(session, env+"/aws_secret_access_key")
	if err != nil {
		slog.Error("failed to get AWS secret access key", "error", err.Error())
	}

	return nil
}

func loadSlatedUsernamesFromCSV(configDir string, bs *BetSimulationConfig) error {
	if bs == nil || bs.UsernamesFile == "" {
		return nil
	}

	filePath := bs.UsernamesFile
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(configDir, filePath)
	}

	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("reading usernames file %s: %w", filePath, err)
	}

	for _, line := range strings.Split(string(data), "\n") {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		bs.Usernames = append(bs.Usernames, line)
	}
	return nil
}

func loadVipTiersFromCSV(configDir string, bs *BetSimulationConfig) error {
	if bs == nil || bs.VipTiersFile == "" {
		return nil
	}

	filePath := bs.VipTiersFile
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(configDir, filePath)
	}

	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("reading VIP tiers file %s: %w", filePath, err)
	}

	lines := strings.Split(string(data), "\n")
	if len(lines) == 0 {
		return fmt.Errorf("VIP tiers file %s is empty", filePath)
	}

	// Skip header line
	for i, line := range lines[1:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Split(line, ",")
		if len(parts) != 3 {
			return fmt.Errorf("invalid VIP tier format at line %d: expected 3 columns, got %d", i+2, len(parts))
		}

		name := strings.TrimSpace(parts[0])
		xpMinStr := strings.TrimSpace(parts[1])
		xpMaxStr := strings.TrimSpace(parts[2])

		xpMin, err := strconv.ParseFloat(xpMinStr, 64)
		if err != nil {
			return fmt.Errorf("invalid xp_min value '%s' at line %d: %w", xpMinStr, i+2, err)
		}

		xpMax, err := strconv.ParseFloat(xpMaxStr, 64)
		if err != nil {
			return fmt.Errorf("invalid xp_max value '%s' at line %d: %w", xpMaxStr, i+2, err)
		}

		vipTier := VipTierConfig{
			Name:    name,
			XPRange: [2]float64{xpMin, xpMax},
		}

		bs.VipTiers = append(bs.VipTiers, vipTier)
	}

	return nil
}

func loadGamesFromCSV(configDir string, bs *BetSimulationConfig) error {
	if bs == nil || bs.GamesFile == "" {
		return nil
	}

	filePath := bs.GamesFile
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(configDir, filePath)
	}

	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("opening games file %s: %w", filePath, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("reading CSV from games file %s: %w", filePath, err)
	}

	if len(records) == 0 {
		return fmt.Errorf("games file %s is empty", filePath)
	}

	// Skip header line
	for i, record := range records[1:] {
		if len(record) != 6 && len(record) != 9 {
			return fmt.Errorf("invalid game format at line %d: expected 6 or 9 columns, got %d", i+2, len(record))
		}

		id := strings.TrimSpace(record[0])
		cmsGameIDStr := strings.TrimSpace(record[1])
		externalID := strings.TrimSpace(record[2])
		name := strings.TrimSpace(record[3])
		slug := strings.TrimSpace(record[4])
		thumbnailID := strings.TrimSpace(record[5])

		cmsGameID, err := strconv.ParseInt(cmsGameIDStr, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid cms_game_id value '%s' at line %d: %w", cmsGameIDStr, i+2, err)
		}

		game := SimulatedGameConfig{
			ID:          id,
			CMSGameID:   cmsGameID,
			ExternalID:  externalID,
			Name:        name,
			Slug:        slug,
			ThumbnailID: thumbnailID,
		}

		// Parse optional multiplier configuration (columns 7-9)
		if len(record) == 9 {
			lossRateStr := strings.TrimSpace(record[6])
			winMultipliersStr := strings.TrimSpace(record[7])
			winWeightsStr := strings.TrimSpace(record[8])

			// Parse loss rate
			if lossRateStr != "" {
				lossRate, err := strconv.ParseFloat(lossRateStr, 64)
				if err != nil {
					return fmt.Errorf("invalid loss_rate value '%s' at line %d: %w", lossRateStr, i+2, err)
				}
				game.LossRate = lossRate
			}

			// Parse win multipliers JSON array
			if winMultipliersStr != "" {
				var winMultipliers []float64
				if err := json.Unmarshal([]byte(winMultipliersStr), &winMultipliers); err != nil {
					return fmt.Errorf("invalid win_multipliers JSON '%s' at line %d: %w", winMultipliersStr, i+2, err)
				}
				game.WinMultipliers = winMultipliers
			}

			// Parse win weights JSON array
			if winWeightsStr != "" {
				var winWeights []float64
				if err := json.Unmarshal([]byte(winWeightsStr), &winWeights); err != nil {
					return fmt.Errorf("invalid win_weights JSON '%s' at line %d: %w", winWeightsStr, i+2, err)
				}
				game.WinWeights = winWeights
			}
		}

		bs.Games = append(bs.Games, game)
	}

	return nil
}
