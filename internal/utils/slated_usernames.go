package utils

import (
	"bufio"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

type SlatedUsernames struct {
	filePath  string
	once      sync.Once
	usernames map[string]struct{}
	err       error
}

func NewSlatedUsernames(filePath string) *SlatedUsernames {
	return &SlatedUsernames{filePath: filePath}
}

func (s *SlatedUsernames) Contains(username string) (bool, error) {
	s.load()
	if s.err != nil {
		return false, s.err
	}
	_, ok := s.usernames[strings.ToLower(username)]
	return ok, nil
}

func (s *SlatedUsernames) load() {
	s.once.Do(func() {
		if !filepath.IsAbs(s.filePath) {
			abs, err := filepath.Abs(s.filePath)
			if err == nil {
				s.filePath = abs
			}
		}

		s.usernames = make(map[string]struct{})

		f, err := os.Open(s.filePath)
		if err != nil {
			s.err = err
			return
		}
		defer f.Close()

		scanner := bufio.NewScanner(f)
		for scanner.Scan() {
			name := strings.TrimSpace(scanner.Text())
			if name == "" {
				continue
			}
			s.usernames[strings.ToLower(name)] = struct{}{}
		}
		if scanErr := scanner.Err(); scanErr != nil {
			s.err = scanErr
			return
		}
	})
}
