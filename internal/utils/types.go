package utils

// PointerOf is a helper function to convert a type to its pointer.
func PointerOf[T any](value T) *T {
	return &value
}

// MapSlice applies a mapping to a slice and returns the results into a new slice.
func MapSlice[S any, D any](src []S, mapper func(S) D) []D {
	if src == nil {
		return nil
	}

	dst := make([]D, 0, len(src))
	for _, elem := range src {
		dst = append(dst, mapper(elem))
	}

	return dst
}

func MapToSlice[K comparable, V any](src map[K]V) []V {
	dst := make([]V, 0, len(src))
	for _, elem := range src {
		dst = append(dst, elem)
	}

	return dst
}
