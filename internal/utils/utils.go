package utils

import (
	"math"
	"os"
	"path/filepath"
	"strings"

	"github.com/gofiber/fiber/v2"
)

const UserIDKey = "user_id" // Change from contextKey type to string
const UserNameKey = "preferred_username"
const UserEmailKey = "email"

// FilteredCMSGameIDs lists CMS game IDs that should be excluded when filter-by-game is enabled.
// Keep this list central so that repository queries and in-memory filtering remain in sync.
var FilteredCMSGameIDs = []int{1022, 1024, 1026, 1021, 3826}

// Shared utility functions
func FindProjectRoot(marker string) string {
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		// Check if the marker exists in this directory
		path := filepath.Join(dir, marker)
		if _, err := os.Stat(path); !os.IsNotExist(err) {
			return dir
		}

		// Move to the parent directory
		parentDir := filepath.Dir(dir)
		if parentDir == dir {
			// We've reached the root directory and didn't find the marker
			return ""
		}
		dir = parentDir
	}
}

func RoundFloat(val, precision float64) float64 {
	ratio := math.Pow(10, precision)
	return math.Round(val*ratio) / ratio
}

// Update the getter for Fiber specifically
func GetUserIDFromContext(c *fiber.Ctx) (string, bool) {
	userID, ok := c.Locals(UserIDKey).(string)
	return userID, ok
}

func GetUserNameFromContext(c *fiber.Ctx) (string, bool) {
	userName, ok := c.Locals(UserNameKey).(string)
	return userName, ok
}

func GetUserEmailFromContext(c *fiber.Ctx) (string, bool) {
	userEmail, ok := c.Locals("email").(string)
	return userEmail, ok
}

func NormalizeCurrency(currency string) string {
	switch currency {
	case "TRX_USDT_S2UZ":
		return "USDT"
	case "SOL_USDC_PTHX":
		return "USD Coin"
	default:
		return strings.Split(currency, "_")[0]
	}
}
