package benchs

import (
	"fmt"
	"io"
	"math/rand"
	"testing"
)

type structExample struct {
	F1, F2, F3, F4, F5, F6, F7 float64
	I1, I2, I3, I4, I5, I6, I7 int64
}

func newStructExample() structExample {
	return structExample{
		F1: rand.Float64(),
		F2: rand.Float64(),
		F3: rand.Float64(),
		F4: rand.Float64(),
		F5: rand.Float64(),
		F6: rand.Float64(),
		F7: rand.Float64(),
		I1: int64(rand.Int()),
		I2: int64(rand.Int()),
		I3: int64(rand.Int()),
		I4: int64(rand.Int()),
		I5: int64(rand.Int()),
		I6: int64(rand.Int()),
		I7: int64(rand.Int()),
	}
}

func newStructExamplePtr() *structExample {
	return &structExample{
		F1: rand.Float64(),
		F2: rand.Float64(),
		F3: rand.Float64(),
		F4: rand.Float64(),
		F5: rand.Float64(),
		F6: rand.Float64(),
		F7: rand.Float64(),
		I1: int64(rand.Int()),
		I2: int64(rand.Int()),
		I3: int64(rand.Int()),
		I4: int64(rand.Int()),
		I5: int64(rand.Int()),
		I6: int64(rand.Int()),
		I7: int64(rand.Int()),
	}
}

func BenchmarkSliceOfStructsAppend(b *testing.B) {
	s := make([]structExample, 0, b.N)

	for range b.N {
		s = append(s, newStructExample())
	}

	for _, e := range s {
		fmt.Fprintln(io.Discard, e)
	}
}

func BenchmarkSliceOfStructsAssign(b *testing.B) {
	s := make([]structExample, b.N)

	for i := range b.N {
		s[i] = newStructExample()
	}

	for _, e := range s {
		fmt.Fprintln(io.Discard, e)
	}
}

func BenchmarkSliceOfPointersAppend(b *testing.B) {
	s := make([]*structExample, 0, b.N)

	for range b.N {
		s = append(s, newStructExamplePtr())
	}

	for _, e := range s {
		fmt.Fprintln(io.Discard, e)
	}
}

func BenchmarkSliceOfPointersAssign(b *testing.B) {
	s := make([]*structExample, b.N)

	for i := range b.N {
		s[i] = newStructExamplePtr()
	}

	for _, e := range s {
		fmt.Fprintln(io.Discard, e)
	}
}
