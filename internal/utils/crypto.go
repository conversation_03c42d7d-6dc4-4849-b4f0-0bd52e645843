package utils

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"fmt"

	"github.com/golang-jwt/jwe"
	"github.com/golang-jwt/jwt"
)

func CreateEncryptedToken(data interface{}, privateKey *rsa.PrivateKey, publicKey *rsa.PublicKey) (string, error) {
	if privateKey == nil {
		return "", fmt.Errorf("privateKey is nil")
	}
	if publicKey == nil {
		return "", fmt.Errorf("publicKey is nil")
	}

	publicKeyBytes := x509.MarshalPKCS1PublicKey(publicKey)
	_, err := x509.ParsePKCS1PublicKey(publicKeyBytes)
	if err != nil {
		return "", fmt.Errorf("invalid public key format: %v", err)
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.<PERSON><PERSON>rf("error marshaling data to JSON: %v", err)
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"data": string(jsonData),
	})

	signedToken, err := token.SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("error signing token: %v", err)
	}

	encryptedToken, err := jwe.NewJWE(jwe.KeyAlgorithmRSAOAEP, publicKey, jwe.EncryptionTypeA256GCM, []byte(signedToken))
	if err != nil {
		return "", err
	}

	compact, err := encryptedToken.CompactSerialize()
	if err != nil {
		return "", err
	}

	return compact, nil
}

func DecryptToken(token string, privateKey *rsa.PrivateKey, publicKey *rsa.PublicKey) (interface{}, error) {
	if privateKey == nil {
		return nil, fmt.Errorf("privateKey is nil")
	}
	if publicKey == nil {
		return nil, fmt.Errorf("publicKey is nil")
	}

	publicKeyBytes := x509.MarshalPKCS1PublicKey(publicKey)
	_, err := x509.ParsePKCS1PublicKey(publicKeyBytes)
	if err != nil {
		return nil, fmt.Errorf("invalid public key format: %v", err)
	}

	encryptedToken, err := jwe.ParseEncrypted(token)
	if err != nil {
		return nil, fmt.Errorf("failed to parse encrypted token: %v", err)
	}

	decryptedToken, err := encryptedToken.Decrypt(privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt token: %v", err)
	}

	jwtToken, err := jwt.Parse(string(decryptedToken), func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse JWT: %v", err)
	}

	claims, ok := jwtToken.Claims.(jwt.MapClaims)
	if !ok || !jwtToken.Valid {
		return "", fmt.Errorf("invalid JWT token or failed to parse claims")
	}

	dataJson, ok := claims["data"].(string)
	if !ok {
		return "", fmt.Errorf("data claim is not a string")
	}

	return dataJson, nil
}
