package utils

import (
	"context"
	"log/slog"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
)

// Redis connection addresses for different environments.
const (
	RedisAddrLocal = "redis_local:6379"
	RedisAddrDev   = "redis.mt-dev.monkeytilt.pro:6379"
	RedisAddrUAT   = "redis.mt-uat.monkeytilt.pro:6379"
	RedisAddrProd  = "redis.mt-prod.monkeytilt.pro:6379"
)

// GetRedisAddress returns the appropriate Redis address based on the environment
// TODO: Use REDIS_ADDR when it is properly set in the environment
func GetRedisAddress() string {
	// redisAddr := os.Getenv("REDIS_ADDR")
	// if redisAddr == "" {
	// 	redisAddr = RedisAddrLocal
	// 	slog.Warn("REDIS_ADDR is not set, using default local address", "addr", redisAddr)
	// }
	// return redisAddr

	env := getEnviroment()
	switch env {
	case "staging":
		return RedisAddrDev
	case "uat":
		return RedisAddrUAT
	case "production":
		return RedisAddrProd
	default:
		return RedisAddrLocal
	}
}

// ConnectToRedis creates a Redis client with connection testing and timeout handling.
// Returns nil if connection fails - service should continue with degraded functionality.
func ConnectToRedis() *redis.Client {
	redisAddr := GetRedisAddress()

	slog.Info("Connecting to Redis", "addr", redisAddr)
	rdb := redis.NewClient(&redis.Options{Addr: redisAddr})

	// Test Redis connection with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		slog.Error("Failed to connect to Redis - continuing without Redis", "addr", redisAddr, "error", err)
		rdb.Close()
		return nil
	}

	slog.Info("Successfully connected to Redis", "addr", redisAddr)
	return rdb
}

// GetSlatedBetsKey returns Redis key for simulated bets based on ENV.
func GetSlatedBetsKey() string {
	redisPrefix := os.Getenv("REDIS_PREFIX")
	if redisPrefix == "" {
		redisPrefix = "NO_PREFIX"
	}
	return redisPrefix + "/slated-bets"
}
