package utils

import (
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	awssession "github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/secretsmanager"
)

func newAWSSession() (*awssession.Session, error) {
	var session *awssession.Session
	var err error

	switch env := getEnviroment(); env {
	case "local":
		endpoint := "http://localstack:4566"
		if os.Getenv("LOCALSTACK_ENDPOINT") != "" {
			endpoint = os.Getenv("LOCALSTACK_ENDPOINT")
		}
		session, err = awssession.NewSession(&aws.Config{
			Region:                        aws.String("us-east-1"),
			CredentialsChainVerboseErrors: aws.Bool(true),
			Endpoint:                      aws.String(endpoint),
			Credentials:                   credentials.NewStaticCredentials("AKID", "SECRET_KEY", "TOKEN"),
		})
	default:
		session, err = awssession.NewSession(&aws.Config{
			Region:                        aws.String(os.Getenv("AWS_REGION")),
			CredentialsChainVerboseErrors: aws.Bool(true),
		})
	}

	if err != nil {
		return nil, fmt.Errorf("create AWS session: %w", err)
	}

	return session, nil
}

func getAWSSecret(session *awssession.Session, name string) (string, error) {
	svc := secretsmanager.New(session)

	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(name),
	}

	result, err := svc.GetSecretValue(input)
	if err != nil {
		return "", fmt.Errorf("retrieve AWS secret: %w", err)
	}

	return *result.SecretString, nil
}
