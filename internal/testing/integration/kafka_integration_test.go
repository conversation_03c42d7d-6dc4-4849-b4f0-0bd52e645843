//go:build exclude

// TODO: Fix these tests.

package test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/mock"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/service"
)

type MockProducer struct {
	mock.Mock
}

func (p *MockProducer) ProduceMessage(topic string, message []byte) {
	p.Called(topic, message)
}

func TestKafkaConsumerIntegrationWithMockProducer(t *testing.T) {
	ctx := service.NewChannelContext()
	kc := kafka.NewKafkaConsumer(ctx)

	// Mock producer initialization
	mockProducer := new(MockProducer)

	// Subscribe to the Kafka topics you want to test
	topics := []string{"test-topics"} // Replace with your topic names
	err := kc.SubscribeTopics(topics)
	if err != nil {
		t.Fatalf("Failed to subscribe to topics: %v", err)
	}

	// Start consuming messages in a separate goroutine
	go kc.Run()

	// Allow some time for the consumer to start consuming messages
	time.Sleep(5 * time.Second)

	// Generate a unique ID for each test message
	uniqueID := generateUniqueID()

	// Prepare the test message with a unique ID
	testMessage := []byte(`{"id":` + uniqueID + `,"name":"test"}`)

	// Mock producer to produce a test message
	mockProducer.On("ProduceMessage", "test-topics", testMessage).Once().Return()

	// Simulate sending a test message to the subscribed topic
	mockProducer.ProduceMessage("test-topics", testMessage)

	// Allow time for the consumer to receive and process the message
	time.Sleep(2 * time.Second)

	// Assert that the expected method was called on the mock producer
	mockProducer.AssertExpectations(t)
}

// Function to generate a unique ID (for illustration purposes)
func generateUniqueID() string {
	return time.Now().Format("20060102150405")
}
