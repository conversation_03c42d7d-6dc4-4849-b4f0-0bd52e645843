asyncapi: '3.0.0'
info:
  title: WebSocket API
  version: '1.0.0'
channels:
  bets:
    address: /ws/v1/bets
    messages:
      bet:
        $ref: '#/components/messages/BetResponse'

components:
  messages:
    BetResponse:
      payload:
          $ref: '#/components/schemas/BetResponse'

  schemas:
    BetResponse:
      type: object
      properties:
        id:
          type: string
          description: Bet ID.
        bet_amount:
          type: number
          description: Bet amount.
        bet_type:
          type: string
          description: Bet type.
        cancel_id:
          type: string
          description: Bet external ID.
        currency:
          type: string
          description: Bet currency.
        external_id:
          type: string
          description: Bet external ID.
        ghost_mode:
          type: boolean
          description: Bet user ghost mode status.
        hidden_boolean:
          type: boolean
          description: Hidden flag.
        multiplier:
          type: number
          description: Bet multiplier.
        payout:
          type: number
          description: Bet payout.
        time:
          type: string
          description: Bet generation time.
        win_id:
          type: string
          description: Bet wind ID.
        game:
          $ref: '#/components/schemas/GameResponse'
        user:
          $ref: '#/components/schemas/UserResponse'
      required:
        - id
        - bet_amount
        - bet_type
        - currency
        - external_id
        - ghost_mode
        - hidden_boolean
        - multiplier
        - payout
        - time
        - game
        - user
    GameResponse:
      type: object
      properties:
        id:
          type: string
          description: Game ID.
        external_id:
          type: string
          description: Game external ID.
        game_data:
          type: string
          description: Game data.
        name:
          type: string
          description: Game name.
        slug:
          type: string
          description: Game slug.
      required:
        - id
        - external_id
        - game_data

    UserResponse:
      type: object
      properties:
        id:
          type: string
          description: User ID.
        config:
          type: string
          description: User config.
        external_id:
          type: string
          description: User external ID.
        first_name:
          type: string
          description: User first name.
        ghost_mode:
          type: boolean
          description: User ghost mode status.
        hide_all_stats:
          type: boolean
          description: User hide all stats status.
        hide_tournament_stats:
          type: boolean
          description: User hide tournament stats status.
        join_date:
          type: string
          description: User join date.
        last_name:
          type: string
          description: User last name.
        last_state_applied:
          type: string
          description: User last state applied date.
        profile_picture:
          type: string
          description: User profile picture.
        profile_status:
          type: string
          description: User profile status.
        total_bets:
          type: number
          description: User total bets.
        total_loses:
          type: number
          description: User total loses.
        total_wins:
          type: number
          description: User total wins.
        user_name:
          type: string
          description: User name.
        vip_status:
          type: string
          description: User VIP status.
        wagered:
          type: number
          description: User VIP status.
      required:
        - id
        - external_id
        - first_name
        - ghost_mode
        - hide_all_stats
        - hide_tournament_stats
        - join_date
        - last_name
        - profile_status
        - total_bets
        - total_loses
        - total_wins
        - user_name
        - vip_status
        - wagered
