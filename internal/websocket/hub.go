package websocket

import (
	"log/slog"
	"sync"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type WebsocketHub[M, R any] struct {
	messages  chan M
	listeners map[string]map[uint]chan R
	nextID    uint
	lock      sync.Mutex
	stopChan  chan struct{}
}

func NewWebsocketHub[M, R any](messages chan M) *WebsocketHub[M, R] {
	return &WebsocketHub[M, R]{
		messages:  messages,
		listeners: make(map[string]map[uint]chan R),
		stopChan:  make(chan struct{}),
		nextID:    0,
		lock:      sync.Mutex{},
	}
}

func (wh *WebsocketHub[M, R]) RegisterClient(ownerID string) (uint, chan R) {
	wh.lock.Lock()
	defer wh.lock.Unlock()
	wh.nextID++
	id := wh.nextID
	if wh.listeners[ownerID] == nil {
		wh.listeners[ownerID] = make(map[uint]chan R)
	}
	wh.listeners[ownerID][id] = make(chan R, 100)
	slog.Info("WebSocket client registered for VIP updates",
		slog.Uint64("client_id", uint64(id)),
		slog.String("owner_id", ownerID),
		slog.Int("total_listeners", len(wh.listeners)))
	return id, wh.listeners[ownerID][id]
}

func (wh *WebsocketHub[M, R]) UnregisterClient(ownerID string, id uint) {
	wh.lock.Lock()
	defer wh.lock.Unlock()
	if listeners, ok := wh.listeners[ownerID]; ok {
		if ch, ok := listeners[id]; ok {
			close(ch)
			delete(listeners, id)
			slog.Info("VIP WebSocket client unregistered",
				slog.Uint64("client_id", uint64(id)),
				slog.String("owner_id", ownerID))
		}
		if len(listeners) == 0 {
			delete(wh.listeners, ownerID)
			slog.Info("Removed owner from VIP listeners",
				slog.String("owner_id", ownerID))
		}
	}
}

func (wh *WebsocketHub[M, R]) Start(transformFunc func(M) (string, R)) {
	defer wh.closeAllConnections()

	for {
		select {
		case msg, ok := <-wh.messages:
			if !ok {
				slog.Info("VIP messages channel closed, stopping WebsocketHub")
				return
			}
			ownerID, response := transformFunc(msg)
			if utils.ENABLE_WEBSOCKET_DEBUG_LOGS {
				slog.Info("Received VIP update to broadcast",
					slog.String("owner_id", ownerID),
					slog.Any("response", response))
			}
			wh.broadcast(ownerID, response)

		case <-wh.stopChan:
			slog.Info("Stop signal received, stopping VIP WebsocketHub")
			return
		}
	}
}

func (wh *WebsocketHub[M, R]) broadcast(ownerID string, response R) {
	wh.lock.Lock()
	defer wh.lock.Unlock()

	listenerCount := 0
	successCount := 0
	failCount := 0

	// Broadcast to all listeners
	for id, listeners := range wh.listeners {
		for clientID, listener := range listeners {
			listenerCount++
			if id == ownerID || id == "" {
				select {
				case listener <- response:
					successCount++
					if utils.ENABLE_WEBSOCKET_DEBUG_LOGS {
						slog.Info("VIP update sent successfully",
							slog.String("target_owner_id", ownerID),
							slog.String("listener_owner_id", id),
							slog.Uint64("client_id", uint64(clientID)))
					}
				default:
					failCount++
					// Channel is full or closed, remove the listener
					close(listener)
					delete(listeners, clientID)
					slog.Error("Failed to send VIP update - removing inactive listener",
						slog.String("target_owner_id", ownerID),
						slog.String("listener_owner_id", id),
						slog.Uint64("client_id", uint64(clientID)))
				}
			}
		}
		if len(listeners) == 0 {
			delete(wh.listeners, id)
			slog.Info("Removed owner due to no active listeners",
				slog.String("owner_id", id))
		}
	}

	if utils.ENABLE_WEBSOCKET_DEBUG_LOGS {
		slog.Info("VIP update broadcast complete",
			slog.String("target_owner_id", ownerID),
			slog.Int("total_listeners", listenerCount),
			slog.Int("successful_sends", successCount),
			slog.Int("failed_sends", failCount))
	}
}

func (wh *WebsocketHub[M, R]) closeAllConnections() {
	wh.lock.Lock()
	defer wh.lock.Unlock()

	closeCount := 0
	for ownerID, listeners := range wh.listeners {
		for id, listener := range listeners {
			close(listener)
			delete(listeners, id)
			closeCount++
			slog.Info("Closed VIP listener",
				slog.Uint64("client_id", uint64(id)),
				slog.String("owner_id", ownerID))
		}
		delete(wh.listeners, ownerID)
	}

	slog.Info("All VIP connections closed",
		slog.Int("total_closed", closeCount))
}

func (wh *WebsocketHub[M, R]) Stop() {
	close(wh.stopChan)
}
