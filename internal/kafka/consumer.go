package kafka

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

const (
	defaultWorkerCount = 24
	defaultBatchSize   = 300
	defaultTimeout     = 2 * time.Second
)

const (
	TopicGameConfigsChanged   = "game-configs-changed"
	TopicOwnerBonusesChanged  = "owner-bonuses-changed"
	TopicOwnerSessionsChanged = "owner-sessions-changed"
	TopicOwnerWalletsChanged  = "owner-wallets-changed"
	TopicSessionChanged       = "session-changed"
	TopicTransactionChanged   = "transaction-changed"
)

type Consumer struct {
	consumer    *kafka.Consumer
	stop        chan struct{}
	handlersMap HandlersMap
	tasksMap    tasksMap
	wg          sync.WaitGroup
	topics      []string
	workerCount int
	batchSize   int
}

type (
	tasksMap map[string]chan task
	task     func() error
)

type HandlersMap map[string][]domain.MessageHandler

type messageBatch struct {
	messages []*kafka.Message
	topic    string
}

func NewConsumer(config *utils.KafkaConsumerConfig, handlersMap HandlersMap) (*Consumer, error) {
	slog.Info("Creating Kafka consumer",
		slog.String("brokers", config.Brokers),
		slog.String("group_id", config.GroupId),
		slog.Int("work_queue_size", config.WorkQueueSize))

	configMap := &kafka.ConfigMap{
		"bootstrap.servers":        config.Brokers,
		"group.id":                 config.GroupId,
		"auto.offset.reset":        "earliest",
		"enable.auto.offset.store": false,
		"reconnect.backoff.ms":     5000,
		"reconnect.backoff.max.ms": 300000,
		"max.poll.interval.ms":     300000,
	}

	consumer, err := kafka.NewConsumer(configMap)
	if err != nil {
		slog.Error("Failed to create Kafka consumer", slog.Any("error", err))
		return nil, fmt.Errorf("create Kafka consumer: %w", err)
	}
	slog.Info("Kafka consumer created successfully")

	topics := make([]string, 0, len(handlersMap))
	for topic := range handlersMap {
		topics = append(topics, topic)
	}
	slog.Info("Kafka consumer topics configured", slog.Any("topics", topics))

	// This is a workaround to avoid receiving a "MAXPOLL" error after some time.
	// This error causes the consumer to be removed from the group.
	// See: https://github.com/confluentinc/confluent-kafka-go/issues/344#issuecomment-1483601045
	var rebalanceCb func(*kafka.Consumer, kafka.Event) error

	rebalanceCb = func(consumer *kafka.Consumer, event kafka.Event) error {
		slog.Info("Kafka partition rebalance event", slog.String("event", event.String()))

		switch e := event.(type) {
		case kafka.RevokedPartitions:
			slog.Info("Kafka partitions revoked", slog.Any("partitions", e))
			if err := consumer.SubscribeTopics(topics, rebalanceCb); err != nil {
				slog.Error("Failed to resubscribe to topics after partition revoke", slog.Any("error", err))
				return err
			}
		case kafka.AssignedPartitions:
			slog.Info("Kafka partitions assigned", slog.Any("partitions", e))
		}

		return nil
	}

	slog.Info("Subscribing to Kafka topics", slog.Any("topics", topics))
	if err := consumer.SubscribeTopics(topics, rebalanceCb); err != nil {
		slog.Error("Failed to subscribe to Kafka topics", slog.Any("error", err), slog.Any("topics", topics))
		return nil, fmt.Errorf("subscribe to topics: %w", err)
	}
	slog.Info("Successfully subscribed to Kafka topics")

	tasksMap := make(tasksMap, len(topics))
	for _, topic := range topics {
		tasksMap[topic] = make(chan task, config.WorkQueueSize)
	}

	slog.Info("Kafka consumer initialization completed",
		slog.Int("worker_count", defaultWorkerCount),
		slog.Int("batch_size", defaultBatchSize),
		slog.Int("topic_count", len(topics)))

	return &Consumer{
		consumer:    consumer,
		stop:        make(chan struct{}),
		handlersMap: handlersMap,
		tasksMap:    tasksMap,
		topics:      topics,
		workerCount: defaultWorkerCount,
		batchSize:   defaultBatchSize,
	}, nil
}

func (c *Consumer) Start() {
	slog.Info("Starting Kafka consumer",
		slog.Int("worker_count", c.workerCount),
		slog.Int("batch_size", c.batchSize),
		slog.Any("topics", c.topics))

	batchChan := make(chan messageBatch, c.workerCount)
	for i := 0; i < c.workerCount; i++ {
		c.wg.Add(1)
		go c.startWorker(batchChan)
	}
	slog.Info("Kafka consumer workers started", slog.Int("worker_count", c.workerCount))

	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		slog.Info("Kafka consumer polling loop started")

		currentBatch := messageBatch{
			messages: make([]*kafka.Message, 0, c.batchSize),
		}

		timer := time.NewTimer(defaultTimeout)
		defer timer.Stop()

		for {
			select {
			case <-c.stop:
				if len(currentBatch.messages) > 0 {
					batchChan <- currentBatch
				}
				close(batchChan)
				return

			case <-timer.C:
				if len(currentBatch.messages) > 0 {
					batchChan <- currentBatch
					currentBatch = messageBatch{
						messages: make([]*kafka.Message, 0, c.batchSize),
					}
				}
				timer.Reset(defaultTimeout)

			default:
				ev := c.consumer.Poll(100)
				if ev == nil {
					continue
				}

				switch e := ev.(type) {
				case *kafka.Message:
					if e.TopicPartition.Topic == nil {
						slog.Error("Kafka message missing topic", slog.String("event", e.String()))
						continue
					}

					currentBatch.messages = append(currentBatch.messages, e)
					currentBatch.topic = *e.TopicPartition.Topic

					if len(currentBatch.messages) >= c.batchSize {
						slog.Debug("Kafka batch full, sending to workers",
							slog.String("topic", currentBatch.topic),
							slog.Int("batch_size", len(currentBatch.messages)))
						batchChan <- currentBatch
						currentBatch = messageBatch{
							messages: make([]*kafka.Message, 0, c.batchSize),
						}
						timer.Reset(defaultTimeout)
					}

				case kafka.Error:
					slog.Error("Kafka consumer error", slog.String("error", e.String()))
				}
			}
		}
	}()
}

func (c *Consumer) startWorker(batchChan <-chan messageBatch) {
	defer c.wg.Done()
	slog.Debug("Kafka consumer worker started")

	for batch := range batchChan {
		handlers := c.handlersMap[batch.topic]
		slog.Debug("Processing Kafka message batch",
			slog.String("topic", batch.topic),
			slog.Int("message_count", len(batch.messages)),
			slog.Int("handler_count", len(handlers)))

		// Process messages in batch
		for _, msg := range batch.messages {
			for _, handler := range handlers {
				if err := handler.HandleMessage(msg.Value); err != nil {
					slog.Error("Failed to process Kafka message",
						slog.String("topic", batch.topic),
						slog.Any("error", err))
					continue
				}
			}

			// Commit successful messages
			if _, err := c.consumer.StoreMessage(msg); err != nil {
				slog.Error("Failed to commit Kafka message",
					slog.String("topic", batch.topic),
					slog.Any("error", err))
			}
		}
		slog.Debug("Kafka message batch processed successfully",
			slog.String("topic", batch.topic),
			slog.Int("message_count", len(batch.messages)))
	}
	slog.Debug("Kafka consumer worker stopped")
}

func (c *Consumer) Stop() error {
	slog.Info("Stopping Kafka consumer")
	close(c.stop)
	c.wg.Wait()
	slog.Info("Kafka consumer workers stopped")

	if err := c.consumer.Close(); err != nil {
		slog.Error("Failed to close Kafka consumer", slog.Any("error", err))
		return err
	}
	slog.Info("Kafka consumer stopped successfully")
	return nil
}

// SetWorkerCount allows configuring the number of concurrent workers
func (c *Consumer) SetWorkerCount(count int) {
	c.workerCount = count
}

// SetBatchSize allows configuring the batch size for message processing
func (c *Consumer) SetBatchSize(size int) {
	c.batchSize = size
}

func (c *Consumer) GetHandlers() []domain.MessageHandler {
	result := make([]domain.MessageHandler, 0)
	for _, handlers := range c.handlersMap {
		result = append(result, handlers...)
	}
	return result
}
