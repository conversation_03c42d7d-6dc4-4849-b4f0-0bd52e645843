package schemas

import (
	"fmt"
	"log/slog"
	"strconv"
	// "strings"
	"time"

	"github.com/google/uuid"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type TransactionMessage Message[TransactionPayload]

func (p *TransactionPayload) ExtractCurrencySymbol() (string, *float64) {
	if len(p.Balances) == 0 {
		return "", nil
	}
	var sum float64
	for _, balance := range p.Balances {
		differenceAmount, err := strconv.ParseFloat(balance.BalanceDifferenceAmount, 64)
		if err != nil {
			slog.Error("Failed to parse BalanceDifferenceAmount", slog.String("value", balance.BalanceDifferenceAmount), slog.String("error", err.Error()))
			continue
		}
		sum += differenceAmount
	}
	amount := utils.PointerOf(sum)
	currency := p.Balances[0].BalanceCurrencyCode

	return currency, amount
}

func (p *TransactionPayload) GetWalletType() string {
	for _, wallet := range p.Balances {
		if wallet.BalanceDifferenceAmount != "0" {
			return wallet.WalletType
		}
	}

	return ""
}

type TransactionPayload struct {
	ID              uuid.UUID       `json:"id"`
	Amount          string          `json:"amount"`
	BatchExternalID string          `json:"batchExternalId"`
	CreatedOn       time.Time       `json:"createdOn"`
	CurrencyCode    string          `json:"currencyCode"`
	Data            TransactionData `json:"data"`
	OwnerID         uuid.UUID       `json:"ownerId"`
	ProductID       string          `json:"productId"`
	Status          string          `json:"status"`
	Type            string          `json:"type"`
	Balances        []Balances      `json:"balances"`
}

type Balances struct {
	BalanceAmount                    string `json:"balanceAmount"`
	BalanceBeforeAmount              string `json:"balanceBeforeAmount"`
	BalanceCurrencyCode              string `json:"balanceCurrencyCode"`
	BalanceDifferenceAmount          string `json:"balanceDifferenceAmount"`
	ConvertedBalanceAmount           string `json:"convertedBalanceAmount"`
	ConvertedBalanceBeforeAmount     string `json:"convertedBalanceBeforeAmount"`
	ConvertedBalanceCurrencyCode     string `json:"convertedBalanceCurrencyCode"`
	ConvertedBalanceDifferenceAmount string `json:"convertedBalanceDifferenceAmount"`
	WalletID                         string `json:"walletId"`
	WalletType                       string `json:"walletType"`
}

type TransactionData struct {
	CasinoData CasinoData `json:"casinoData"`
	SportsData SportsData `json:"sportsData"`
}

type CasinoData struct {
	Config CasinoConfig `json:"config"`
}

type CasinoConfig struct {
	Game CasinoGame `json:"game"`
}

type CasinoGame struct {
	GameID string `json:"gameId"`
}

type SportsData struct {
	Odds         string      `json:"odds"`
	BetType      string      `json:"betType"`
	SessionID    string      `json:"sessionId"`
	Selections   []Selection `json:"selections"`
	SettleAmount string      `json:"settleAmount"`
	Type         string      `json:"type"`
	Amount       string      `json:"amount"`
	Ratios       []Ratio     `json:"ratios"`
	Status       string      `json:"status"`
	Baggage      Baggage     `json:"baggage"`
}
type Baggage struct {
	TenantID            string `json:"tenantId"`
	TenantIntegrationID string `json:"tenantIntegrationId"`
}

type Ratio struct {
	Denominator string `json:"denominator"`
	Numerator   string `json:"numerator"`
	WalletID    string `json:"walletId"`
}

type Selection struct {
	CategoryID      string    `json:"categoryId"`
	CategoryName    string    `json:"categoryName"`
	CompetitionID   string    `json:"competitionId"`
	CompetitionName string    `json:"competitionName"`
	EventID         string    `json:"eventId"`
	EventName       string    `json:"eventName"`
	EventStartsOn   time.Time `json:"eventStartsOn"`
	IsLive          bool      `json:"isLive"`
	MarketName      string    `json:"marketName"`
	Odds            string    `json:"odds"`
	Outcome         string    `json:"outcome"`
	SportID         string    `json:"sportId"`
	SportName       string    `json:"sportName"`
}

func (p *TransactionPayload) IsBetTrans() bool {
	return p.ProductID == "casino" || p.ProductID == "sports" || p.ProductID == "poker"
}

func (p *TransactionPayload) IsBetSports() bool {
	return p.ProductID == "sports"
}

func (p *TransactionPayload) IsBetCompleted() bool {
	return p.Status == "completed"
}

func (p *TransactionPayload) IsBetStatusCompleteForVipTier() bool {
	return (p.Type == "debit" || p.Type == "credit") && p.Status == "completed"
}

func (p *TransactionPayload) WagerDebitOnly() bool {
	return (p.Type == "debit") && p.Status == "completed"
}

func (p *TransactionPayload) GetUserID() string {
	return p.OwnerID.String()
}

func (p *TransactionPayload) GetGameID() string {
	return p.Data.CasinoData.Config.Game.GameID
}

func (p *TransactionPayload) ParseGame() *domain.Game {
	return &domain.Game{ExternalID: p.GetGameID()}
}

func (p *TransactionPayload) IsBetCredit() bool {
	return p.Type == "credit"
}

func (p *TransactionPayload) GetEventName() *string {
	if len(p.Data.SportsData.Selections) > 0 {
		return &p.Data.SportsData.Selections[0].EventName
	}
	return nil
}

func (p *TransactionPayload) GetEventOdds() *float64 {
	oddsStr := p.Data.SportsData.Odds
	if oddsStr == "" {
		return nil
	}
	odds, err := strconv.ParseFloat(oddsStr, 64)
	if err != nil {
		slog.Error("Failed to parse odds", slog.String("value", oddsStr), slog.String("error", err.Error()))
		return nil
	}
	return &odds
}

func (p *TransactionPayload) IsBetZero() bool {
	amount, err := strconv.ParseFloat(p.Amount, 64)
	if err != nil {
		slog.Error("Parse amount", slog.String("error", err.Error()))
		return false
	}
	return amount == 0
}

func (p *TransactionPayload) ParseBet(gameID, userID uuid.UUID, currency string, toUSDPrice, cryptoAmount *float64) (*domain.Bet, error) {
	slog.Info("Parsing bet from transaction",
		slog.String("amount", p.Amount),
		slog.String("type", p.Type),
		slog.String("product_id", p.ProductID),
		slog.String("batch_external_id", p.BatchExternalID),
		slog.String("status", p.Status),
		slog.Time("created_on", p.CreatedOn))
	slog.Info("game id", slog.String("game_id", gameID.String()))
	slog.Info("user id", slog.String("user_id", userID.String()))

	amount, err := strconv.ParseFloat(p.Amount, 64)
	if err != nil {
		return nil, fmt.Errorf("parse amount %q: %w", p.Amount, err)
	}

	var betAmount *float64
	var winAmount *float64
	var actualBetAmount *float64
	var actualWinAmount *float64

	switch p.Type {
	case "debit":
		betAmount = utils.PointerOf(amount)
		actualBetAmount = cryptoAmount
		if toUSDPrice != nil && p.CurrencyCode != "USD" {
			if domain.FiatWallet[currency] {
				*betAmount = *cryptoAmount / *toUSDPrice
			} else {
				*betAmount = *cryptoAmount * *toUSDPrice
			}
		}
		slog.Debug("Parsed debit amount", slog.Float64("bet_amount", *betAmount))
	case "credit":
		winAmount = utils.PointerOf(amount)
		actualWinAmount = cryptoAmount

		if toUSDPrice != nil && p.CurrencyCode != "USD" {
			if domain.FiatWallet[currency] {
				*winAmount = *cryptoAmount / *toUSDPrice
			} else {
				*winAmount = *cryptoAmount * *toUSDPrice
			}
		}
		slog.Debug("Parsed credit amount", slog.Float64("win_amount", *winAmount))
	default:
		slog.Warn("Unknown transaction type", slog.String("type", p.Type))
	}

	var betType domain.BetType
	switch p.ProductID {
	case "casino":
		betType = domain.BetTypeCasino
	case "sports":
		betType = domain.BetTypeSports
	case "poker":
		betType = domain.BetTypePoker
	default:
		slog.Warn("Unknown product ID", slog.String("product_id", p.ProductID))
	}

	if currency == "" {
		currency = p.CurrencyCode
	}

	bet := &domain.Bet{
		BetAmount:       betAmount,
		ActualBetAmount: actualBetAmount,
		BetType:         betType,
		Currency:        currency,
		ConvertedTo:     p.CurrencyCode,
		ExternalID:      p.BatchExternalID,
		Payout:          winAmount,
		ActualWinAmount: actualWinAmount,
		RoundStatus:     p.Status,
		Time:            p.CreatedOn,
		Event:           p.GetEventName(),
		Odds:            p.GetEventOdds(),
		Game:            domain.Game{ID: gameID},
		User:            domain.User{ID: userID},
	}

	slog.Info("Created bet object",
		slog.Any("bet_amount", bet.BetAmount),
		slog.Any("actual_bet_amount", bet.ActualBetAmount),
		slog.String("bet_type", string(bet.BetType)),
		slog.String("currency", bet.Currency),
		slog.String("external_id", bet.ExternalID),
		slog.Any("payout", bet.Payout),
		slog.Any("actual_win_amount", bet.ActualWinAmount),
		slog.String("status", bet.RoundStatus))

	return bet, nil
}

func (p *TransactionPayload) ParseTransaction(currency string, cryptoFiatAmount, toUsdValue *float64) (*domain.Transaction, error) {

	amount, err := strconv.ParseFloat(p.Amount, 64)
	if err != nil {
		return nil, err
	}

	if toUsdValue != nil && p.CurrencyCode != "USD" {
		if domain.FiatWallet[currency] {
			amount = *cryptoFiatAmount / *toUsdValue
		} else {
			amount = *cryptoFiatAmount * *toUsdValue
		}
	}

	if currency == "" {
		currency = p.CurrencyCode
	}

	var cryptoAmount float64
	if cryptoFiatAmount != nil {
		cryptoAmount = *cryptoFiatAmount
	}

	return &domain.Transaction{
		Amount:           amount,
		CryptoFiatAmount: cryptoAmount,
		BetExternalID:    p.BatchExternalID,
		Category:         p.ProductID,
		Currency:         currency,
		ConvertedTo:      p.CurrencyCode,
		ExternalID:       p.ID.String(),
		InsertedAt:       p.CreatedOn,
		Status:           p.Status,
		Type:             p.Type,
		WalletType:       p.GetWalletType(),
	}, nil
}
