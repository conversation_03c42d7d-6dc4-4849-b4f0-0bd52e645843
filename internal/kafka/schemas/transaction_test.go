package schemas

import (
	"strconv"
	"testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

var (
	testTransBadAmount = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "1-1-1",
		BatchExternalID: "111",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "credit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "111"}},
			},
		},
	}

	testDebitCasinoTrans = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "50",
		BatchExternalID: "111",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "debit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "111"}},
			},
		},
	}

	testCreditCasinoTrans = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "50",
		BatchExternalID: "111",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "credit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "111"}},
			},
		},
	}

	testSportsTrans = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000002"),
		Amount:          "50",
		BatchExternalID: "222",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "sports",
		Status:          "completed",
		Type:            "credit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "222"}},
			},
		},
	}

	testPokerTrans = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000003"),
		Amount:          "50",
		BatchExternalID: "333",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "poker",
		Status:          "completed",
		Type:            "credit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "333"}},
			},
		},
	}

	testPaymentsTrans = TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000004"),
		Amount:          "50",
		BatchExternalID: "444",
		CreatedOn:       mustParseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "payments",
		Status:          "completed",
		Type:            "credit",
		Data: TransactionData{
			CasinoData: CasinoData{
				Config: CasinoConfig{Game: CasinoGame{GameID: "444"}},
			},
		},
	}
)

func TestIsBetCompleted(t *testing.T) {
	tests := []struct {
		name           string
		trans          *TransactionPayload
		expectedResult bool
	}{
		{
			name:           "DebitNotCompleted",
			trans:          &TransactionPayload{Type: "debit", Status: "submitted"},
			expectedResult: false,
		},
		{
			name:           "DebitCompleted",
			trans:          &TransactionPayload{Type: "debit", Status: "completed"},
			expectedResult: true, 
		},
		{
			name:           "CreditNotCompleted",
			trans:          &TransactionPayload{Type: "credit", Status: "submitted"},
			expectedResult: false,
		},
		{
			name:           "CreditCompleted",
			trans:          &TransactionPayload{Type: "credit", Status: "completed"},
			expectedResult: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.trans.IsBetCompleted()
			assert.Equal(t, test.expectedResult, result)
		})
	}
}

func TestIsBetTrans(t *testing.T) {
	tests := []struct {
		name           string
		trans          *TransactionPayload
		expectedResult bool
	}{
		{
			name:           "OtherBet",
			trans:          &TransactionPayload{ProductID: "other"},
			expectedResult: false,
		},
		{
			name:           "CasinoBet",
			trans:          &TransactionPayload{ProductID: "casino"},
			expectedResult: true,
		},
		{
			name:           "SportsBet",
			trans:          &TransactionPayload{ProductID: "sports"},
			expectedResult: true,
		},
		{
			name:           "PokerBet",
			trans:          &TransactionPayload{ProductID: "poker"},
			expectedResult: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.trans.IsBetTrans()
			assert.Equal(t, test.expectedResult, result)
		})
	}
}

func TestParseGame(t *testing.T) {
	tests := []struct {
		name         string
		trans        *TransactionPayload
		expectedGame *domain.Game
	}{
		{
			name: "Success",
			trans: &TransactionPayload{
				Data: TransactionData{
					CasinoData: CasinoData{
						Config: CasinoConfig{
							CasinoGame{GameID: "123"},
						},
					},
				},
			},
			expectedGame: &domain.Game{ExternalID: "123"},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			game := test.trans.ParseGame()
			assert.Equal(t, test.expectedGame, game)
		})
	}
}

func TestParseBet(t *testing.T) {
	tests := []struct {
		name            string
		trans           TransactionPayload
		gameID          uuid.UUID
		userID          uuid.UUID
		expectedBet     *domain.Bet
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:        "BadAmount",
			trans:       testTransBadAmount,
			expectedBet: nil,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var NumError *strconv.NumError
				require.ErrorAs(t, err, &NumError)
			},
		},
		{
			name:   "DebitCasinoBet",
			trans:  testDebitCasinoTrans,
			gameID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
			userID: uuid.MustParse("00000000-0000-0000-0000-000000000100"),
			expectedBet: &domain.Bet{
				BetAmount:       utils.PointerOf(50.0),
				ActualBetAmount: utils.PointerOf(0.0), 
				BetType:         domain.BetTypeCasino,
				Currency:        "USD", 
				ConvertedTo:     "USD", 
				ExternalID:      "111",
				Payout:          nil,
				ActualWinAmount: nil,
				RoundStatus:     "completed",
				Time:            mustParseTime("2006-01-02T15:04:05Z"),
				Game: domain.Game{
					ID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
				},
				User: domain.User{
					ID:       uuid.MustParse("00000000-0000-0000-0000-000000000100"),
					JoinDate: mustParseTime("0001-01-01T00:00:00Z"),
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:   "CreditCasinoBet",
			trans:  testCreditCasinoTrans,
			gameID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
			userID: uuid.MustParse("00000000-0000-0000-0000-000000000100"),
			expectedBet: &domain.Bet{
				BetAmount:       nil,
				ActualBetAmount: nil,
				BetType:         domain.BetTypeCasino,
				Currency:        "USD", 
				ConvertedTo:     "USD", 
				ExternalID:      "111",
				Payout:          utils.PointerOf(50.0),
				ActualWinAmount: utils.PointerOf(0.0),
				RoundStatus:     "completed",
				Time:            mustParseTime("2006-01-02T15:04:05Z"),
				Game: domain.Game{
					ID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
				},
				User: domain.User{
					ID:       uuid.MustParse("00000000-0000-0000-0000-000000000100"),
					JoinDate: mustParseTime("0001-01-01T00:00:00Z"),
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:   "SportsBet",
			trans:  testSportsTrans,
			gameID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
			userID: uuid.MustParse("00000000-0000-0000-0000-000000000100"),
			expectedBet: &domain.Bet{
				BetAmount:       nil,
				ActualBetAmount: nil,
				BetType:         domain.BetTypeSports,
				Currency:        "USD", 
				ConvertedTo:     "USD", 
				ExternalID:      "222",
				Payout:          utils.PointerOf(50.0),
				ActualWinAmount: utils.PointerOf(0.0),
				RoundStatus:     "completed",
				Time:            mustParseTime("2006-01-02T15:04:05Z"),
				Game: domain.Game{
					ID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
				},
				User: domain.User{
					ID:       uuid.MustParse("00000000-0000-0000-0000-000000000100"),
					JoinDate: mustParseTime("0001-01-01T00:00:00Z"),
				},
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:   "PokerBet",
			trans:  testPokerTrans,
			gameID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
			userID: uuid.MustParse("00000000-0000-0000-0000-000000000100"),
			expectedBet: &domain.Bet{
				BetAmount:       nil,
				ActualBetAmount: nil,
				BetType:         domain.BetTypePoker,
				Currency:        "USD", 
				ConvertedTo:     "USD", 
				ExternalID:      "333",
				Payout:          utils.PointerOf(50.0),
				ActualWinAmount: utils.PointerOf(0.0), 
				RoundStatus:     "completed",
				Time:            mustParseTime("2006-01-02T15:04:05Z"),
				Game: domain.Game{
					ID: uuid.MustParse("00000000-0000-0000-0000-000000000010"),
				},
				User: domain.User{
					ID:       uuid.MustParse("00000000-0000-0000-0000-000000000100"),
					JoinDate: mustParseTime("0001-01-01T00:00:00Z"),
				},
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			bet, err := test.trans.ParseBet(test.gameID, test.userID, "USD", nil, utils.PointerOf(0.0))
			test.expectedErrFunc(t, err)
			assert.Equal(t, test.expectedBet, bet)
		})
	}
}

func TestParseTransaction(t *testing.T) {
	tests := []struct {
		name            string
		trans           TransactionPayload
		expectedTrans   *domain.Transaction
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:          "BadAmount",
			trans:         testTransBadAmount,
			expectedTrans: nil,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var NumError *strconv.NumError
				require.ErrorAs(t, err, &NumError)
			},
		},
		{
			name:  "CasinoTrans",
			trans: testCreditCasinoTrans,
			expectedTrans: &domain.Transaction{
				Amount:        50,
				BetExternalID: "111",
				Category:      "casino",
				Currency:      "USD",
				ConvertedTo:   "USD", 
				ExternalID:    "00000000-0000-0000-0000-000000000001",
				InsertedAt:    mustParseTime("2006-01-02T15:04:05Z"),
				Status:        "completed",
				Type:          "credit",
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:  "SportsTrans",
			trans: testSportsTrans,
			expectedTrans: &domain.Transaction{
				Amount:        50,
				BetExternalID: "222",
				Category:      "sports",
				Currency:      "USD",
				ConvertedTo:   "USD",
				ExternalID:    "00000000-0000-0000-0000-000000000002",
				InsertedAt:    mustParseTime("2006-01-02T15:04:05Z"),
				Status:        "completed",
				Type:          "credit",
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:  "PokerTrans",
			trans: testPokerTrans,
			expectedTrans: &domain.Transaction{
				Amount:        50,
				BetExternalID: "333",
				Category:      "poker",
				Currency:      "USD",
				ConvertedTo:   "USD", 
				ExternalID:    "00000000-0000-0000-0000-000000000003",
				InsertedAt:    mustParseTime("2006-01-02T15:04:05Z"),
				Status:        "completed",
				Type:          "credit",
			},
			expectedErrFunc: require.NoError,
		},
		{
			name:  "PaymentsTrans",
			trans: testPaymentsTrans,
			expectedTrans: &domain.Transaction{
				Amount:        50,
				BetExternalID: "444",
				Category:      "payments",
				Currency:      "USD",
				ConvertedTo:   "USD", 
				ExternalID:    "00000000-0000-0000-0000-000000000004",
				InsertedAt:    mustParseTime("2006-01-02T15:04:05Z"),
				Status:        "completed",
				Type:          "credit",
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			trans, err := test.trans.ParseTransaction("", nil, nil)
			test.expectedErrFunc(t, err)
			assert.Equal(t, test.expectedTrans, trans)
		})
	}
}