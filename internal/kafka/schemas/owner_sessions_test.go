package schemas

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func TestParseUser(t *testing.T) {
	testOwnerSessionsBadCreatedOn := OwnerSessionsPayload{
		CreatedOn:     "2006-01-02",
		LastUpdatedOn: utils.PointerOf("2006-01-02T15:04:05Z"),
		OwnerID:       uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Profile: OwnerSessionsProfile{
			AdditionalName: "aditional-name",
			Email:          "email",
			FamilyName:     "family-name",
			GivenName:      "given-name",
		},
	}

	testOwnerSessionsBadLastUpdatedOn := OwnerSessionsPayload{
		CreatedOn:     "2006-01-02T15:04:05Z",
		LastUpdatedOn: utils.PointerOf("2006-01-02"),
		OwnerID:       uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Profile: OwnerSessionsProfile{
			AdditionalName: "aditional-name",
			Email:          "email",
			FamilyName:     "family-name",
			GivenName:      "given-name",
		},
	}

	testOwnerSessions := OwnerSessionsPayload{
		CreatedOn:     "2006-01-02T15:04:05Z",
		LastUpdatedOn: utils.PointerOf("2006-01-02T15:04:05Z"),
		OwnerID:       uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Profile: OwnerSessionsProfile{
			AdditionalName: "aditional-name",
			Email:          "email",
			FamilyName:     "family-name",
			GivenName:      "given-name",
		},
	}

	tests := []struct {
		name            string
		ownerSessions   *OwnerSessionsPayload
		expectedUser    *domain.User
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:          "BadCreatedOn",
			ownerSessions: &testOwnerSessionsBadCreatedOn,
			expectedUser:  nil,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var parseError *time.ParseError
				require.ErrorAs(t, err, &parseError)
			},
		},
		{
			name:          "BadLastUpdatedOn",
			ownerSessions: &testOwnerSessionsBadLastUpdatedOn,
			expectedUser:  nil,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var parseError *time.ParseError
				require.ErrorAs(t, err, &parseError)
			},
		},
		{
			name:          "Success",
			ownerSessions: &testOwnerSessions,
			expectedUser: &domain.User{
				Email:         "email",
				ExternalID:    "00000000-0000-0000-0000-000000000001",
				FirstName:     "given-name",
				JoinDate:      mustParseTime("2006-01-02T15:04:05Z"),
				LastName:      "family-name",
				LastUpdatedOn: utils.PointerOf(mustParseTime("2006-01-02T15:04:05Z")),
				UserName:      "aditional-name",
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			trans, err := test.ownerSessions.ParseUser()
			test.expectedErrFunc(t, err)
			assert.Equal(t, test.expectedUser, trans)
		})
	}
}
