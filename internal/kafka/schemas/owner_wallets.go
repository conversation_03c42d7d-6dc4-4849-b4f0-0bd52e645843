package schemas

import (
	"strconv"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type OwnerWalletMessage Message[OwnerWalletsPayload]

type OwnerWalletsPayload struct {
	OwnerID   string            `json:"ownerId"`
	OwnerType string            `json:"ownerType"`
	Wallets   map[string]Wallet `json:"wallets"`
}

type Wallet struct {
	ID            string    `json:"id"`
	Type          string    `json:"type"`
	BalanceAmount string    `json:"balanceAmount"`
	LastUpdatedOn time.Time `json:"lastUpdatedOn"`
	CurrencyCode  string    `json:"currencyCode"`
}

func (p *Wallet) ParseBalance(ownerID string) (*domain.Balance, error) {
	balanceAmount, err := strconv.ParseFloat(p.BalanceAmount, 64)
	if err != nil {
		return nil, err
	}

	return &domain.Balance{
		UserExternalID: ownerID,
		BalanceAmount:  balanceAmount,
		Type:           p.Type,
		LastUpdatedOn:  p.LastUpdatedOn,
		Currency:       p.CurrencyCode,
	}, nil
}
