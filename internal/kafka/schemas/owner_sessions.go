package schemas

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
)

var (
	companyUsersWhitelist []string

	//go:embed company_users_whitelist.json
	companyUsersData []byte
)

func init() {
	if err := json.Unmarshal(companyUsersData, &companyUsersWhitelist); err != nil {
		panic(fmt.Sprintf("unmarshal company users: %v", err))
	}
}

type OwnerSessionsMessage Message[OwnerSessionsPayload]

type OwnerSessionsPayload struct {
	CreatedOn     string               `json:"createdOn"`
	LastUpdatedOn *string              `json:"lastUpdatedOn"`
	OwnerID       uuid.UUID            `json:"ownerId"`
	Profile       OwnerSessionsProfile `json:"profile"`
}

type OwnerSessionsProfile struct {
	AdditionalName string `json:"username"`
	Email          string `json:"email"`
	FamilyName     string `json:"familyName"`
	GivenName      string `json:"givenName"`
}

func (p *OwnerSessionsPayload) ParseUser() (*domain.User, error) {
	createdOn, err := parseTime(p.CreatedOn)
	if err != nil {
		return nil, fmt.Errorf("parse insertion time %v for user %v: %w", p.CreatedOn, p.OwnerID, err)
	}

	user := &domain.User{
		CompanyUser: p.isCompanyUser(),
		Email:       p.Profile.Email,
		ExternalID:  p.OwnerID.String(),
		FirstName:   p.Profile.GivenName,
		JoinDate:    createdOn,
		LastName:    p.Profile.FamilyName,
		UserName:    p.Profile.AdditionalName,
	}

	if p.LastUpdatedOn != nil {
		lastUpdatedOn, err := parseTime(*p.LastUpdatedOn)
		if err != nil {
			return nil, fmt.Errorf("parse last updated on time %v for user %v: %w", p.LastUpdatedOn, p.OwnerID, err)
		}

		user.LastUpdatedOn = &lastUpdatedOn
	}

	return user, nil
}

// TODO: Check because this won't work without username
func (p *OwnerSessionsPayload) isCompanyUser() bool {
	if strings.HasSuffix(p.Profile.Email, "@monkeytilt.co") {
		return true
	}

	for _, user := range companyUsersWhitelist {
		if p.Profile.AdditionalName == user {
			return true
		}
	}

	return false
}
