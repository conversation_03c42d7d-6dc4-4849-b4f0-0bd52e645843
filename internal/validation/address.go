package validation

import (
	"crypto/sha256"
	"fmt"
	"golang.org/x/crypto/sha3"
	"math/big"
	"strings"
)

// AddressValidationConfig defines address validation configuration for different networks
type AddressValidationConfig struct {
	Network        string
	Prefixes       []string
	MinLength      int
	MaxLength      int
	Encoding       string
	ValidationType string
}

// addressValidationConfig defines address validation configurations for supported tokens by asset_uid
var addressValidationConfig = map[string]AddressValidationConfig{
	// Ethereum-based tokens
	"USDC":       {Network: "Ethereum", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"ETH":        {Network: "Ethereum", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"USDT_ERC20": {Network: "Ethereum", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"SHIB":       {Network: "Ethereum", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},

	// Binance Smart Chain tokens
	"USDC_BSC": {Network: "Binance Smart Chain", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"ETH_BSC":  {Network: "Binance Smart Chain", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"USDT_BSC": {Network: "Binance Smart Chain", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"BNB_BSC":  {Network: "Binance Smart Chain", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},

	// Bitcoin (including Taproot bc1p...)
	"BTC": {Network: "Bitcoin", Prefixes: []string{"1", "3", "bc1q", "bc1p"}, MinLength: 26, MaxLength: 62, Encoding: "Base58/Bech32/Bech32m", ValidationType: "bitcoin"},

	// Bitcoin Cash
	"BCH": {Network: "Bitcoin Cash", Prefixes: []string{"1", "3", "q", "p"}, MinLength: 26, MaxLength: 54, Encoding: "Base58Check/CashAddr", ValidationType: "bitcoincash"},

	// Litecoin
	"LTC": {Network: "Litecoin", Prefixes: []string{"L", "M", "ltc1"}, MinLength: 26, MaxLength: 62, Encoding: "Base58/Bech32", ValidationType: "litecoin"},

	// Dogecoin
	"DOGE": {Network: "Dogecoin", Prefixes: []string{"D", "9", "A"}, MinLength: 34, MaxLength: 34, Encoding: "Base58Check", ValidationType: "dogecoin"},

	// Solana tokens (Base58 encoded 32-byte addresses, typically 43-44 characters, but System Program ID is 32 chars)
	"SOL":           {Network: "Solana", Prefixes: []string{}, MinLength: 32, MaxLength: 44, Encoding: "Base58", ValidationType: "solana"},
	"SOL_USDC_PTHX": {Network: "Solana", Prefixes: []string{}, MinLength: 32, MaxLength: 44, Encoding: "Base58", ValidationType: "solana"},
	"BONK_SOL":      {Network: "Solana", Prefixes: []string{}, MinLength: 32, MaxLength: 44, Encoding: "Base58", ValidationType: "solana"},
	"$WIF_SOL":      {Network: "Solana", Prefixes: []string{}, MinLength: 32, MaxLength: 44, Encoding: "Base58", ValidationType: "solana"},
	"USDT_SOL":      {Network: "Solana", Prefixes: []string{}, MinLength: 32, MaxLength: 44, Encoding: "Base58", ValidationType: "solana"},

	// Avalanche
	"AVAX": {Network: "Avalanche C-Chain", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},

	// XRP
	"XRP": {Network: "XRP Ledger", Prefixes: []string{"r", "X"}, MinLength: 25, MaxLength: 34, Encoding: "Base58Check", ValidationType: "xrp"},

	// Polygon
	"MATIC_POLYGON":     {Network: "Polygon", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"USDT_POLYGON":      {Network: "Polygon", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"USDC_POL":          {Network: "Polygon", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"MATIC_POLYGON_POL": {Network: "Polygon", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},
	"MATIC_POLYGON_ETH": {Network: "Polygon", Prefixes: []string{"0x"}, MinLength: 42, MaxLength: 42, Encoding: "Hexadecimal", ValidationType: "ethereum"},

	// Cardano
	"ADA": {Network: "Cardano", Prefixes: []string{"addr1"}, MinLength: 100, MaxLength: 103, Encoding: "Bech32", ValidationType: "cardano"},

	// Tron
	"TRX":           {Network: "Tron", Prefixes: []string{"T"}, MinLength: 34, MaxLength: 34, Encoding: "Base58Check", ValidationType: "tron"},
	"TRX_USDT_S2UZ": {Network: "Tron", Prefixes: []string{"T"}, MinLength: 34, MaxLength: 34, Encoding: "Base58Check", ValidationType: "tron"},
}

// Base58 alphabet (Bitcoin/Solana style - excludes 0, O, I, l)
const base58Alphabet = "**********************************************************"

// Bech32 alphabet
const bech32Alphabet = "qpzry9x8gf2tvdw0s3jn54khce6mua7l"

// GetAddressValidationConfig returns the address validation configuration for a given asset UID
func GetAddressValidationConfig(assetUID string) (AddressValidationConfig, bool) {
	config, exists := addressValidationConfig[assetUID]
	return config, exists
}

// ValidateAddress validates a withdrawal address based on the asset's network requirements
func ValidateAddress(address, assetUID string) error {
	// Trim whitespace
	address = strings.TrimSpace(address)

	if address == "" {
		return fmt.Errorf("address cannot be empty")
	}

	config, exists := GetAddressValidationConfig(assetUID)
	if !exists {
		return fmt.Errorf("no address validation config found for asset: %s", assetUID)
	}

	// Basic length validation
	if len(address) < config.MinLength || len(address) > config.MaxLength {
		return fmt.Errorf("address length %d is invalid for %s (expected %d-%d characters)",
			len(address), assetUID, config.MinLength, config.MaxLength)
	}

	// Prefix validation (if required)
	if len(config.Prefixes) > 0 {
		validPrefix := false
		for _, prefix := range config.Prefixes {
			if strings.HasPrefix(address, prefix) {
				validPrefix = true
				break
			}
		}
		if !validPrefix {
			return fmt.Errorf("address must start with one of: %v for %s", config.Prefixes, assetUID)
		}
	}

	// Network-specific validation
	switch config.ValidationType {
	case "ethereum":
		return validateEthereumAddress(address)
	case "bitcoin":
		return validateBitcoinAddress(address)
	case "litecoin":
		return validateLitecoinAddress(address)
	case "dogecoin":
		return validateDogecoinAddress(address)
	case "solana":
		return validateSolanaAddress(address)
	case "xrp":
		return validateXRPAddress(address)
	case "cardano":
		return validateCardanoAddress(address)
	case "tron":
		return validateTronAddress(address)
	case "bitcoincash":
		return validateBitcoinCashAddress(address)
	default:
		return fmt.Errorf("unsupported validation type: %s", config.ValidationType)
	}
}

// validateEthereumAddress validates Ethereum-style addresses (0x followed by 40 hex characters)
func validateEthereumAddress(address string) error {
	if !strings.HasPrefix(address, "0x") {
		return fmt.Errorf("ethereum address must start with 0x")
	}

	hexPart := address[2:] // Remove 0x prefix
	if len(hexPart) != 40 {
		return fmt.Errorf("ethereum address must be 42 characters long (0x + 40 hex chars)")
	}

	// Check if all characters after 0x are valid hex
	for _, char := range hexPart {
		if !isHexChar(char) {
			return fmt.Errorf("ethereum address contains invalid hex character")
		}
	}

	// Basic checksum validation (EIP-55)
	return validateEthereumChecksum(address)
}

// validateEthereumChecksum ensures an Ethereum address uses the correct EIP-55 checksum
func validateEthereumChecksum(address string) error {
	if !strings.HasPrefix(address, "0x") || len(address) != 42 {
		return fmt.Errorf("ethereum address must start with 0x and be 42 characters long")
	}

	hexPart := address[2:]

	// If the address is all lowercase or all uppercase, skip checksum validation (per EIP-55 spec)
	if hexPart == strings.ToLower(hexPart) || hexPart == strings.ToUpper(hexPart) {
		return nil
	}

	// Check for mixed-case address correctness using EIP-55
	hasher := sha3.NewLegacyKeccak256()
	hasher.Write([]byte(strings.ToLower(hexPart)))
	hash := hasher.Sum(nil)

	for i, char := range hexPart {
		if char >= 'a' && char <= 'f' || char >= 'A' && char <= 'F' {
			byteVal := hash[i/2]
			var nibble byte
			if i%2 == 0 {
				nibble = byteVal >> 4
			} else {
				nibble = byteVal & 0x0f
			}

			if (nibble >= 8 && char >= 'a' && char <= 'f') ||
				(nibble < 8 && char >= 'A' && char <= 'F') {
				return fmt.Errorf("invalid checksum at position %d: character '%c' does not match nibble %d", i+2, char, nibble)
			}
		}
	}

	return nil
}

// validateBitcoinAddress validates Bitcoin addresses
func validateBitcoinAddress(address string) error {
	if len(address) < 26 || len(address) > 62 {
		return fmt.Errorf("bitcoin address length must be between 26 and 62 characters")
	}

	// Taproot addresses (bc1p...) - use Bech32m
	if strings.HasPrefix(address, "bc1p") {
		return validateBech32mAddress(address, "bc")
	}

	// SegWit v0 addresses (bc1q...) - use Bech32
	if strings.HasPrefix(address, "bc1q") || strings.HasPrefix(address, "bc1") {
		return validateBech32Address(address, "bc")
	}

	// Legacy and P2SH addresses (Base58Check)
	if strings.HasPrefix(address, "1") || strings.HasPrefix(address, "3") {
		return validateBase58CheckAddress(address)
	}

	return fmt.Errorf("bitcoin address must start with 1, 3, bc1q, or bc1p")
}

// validateLitecoinAddress validates Litecoin addresses
func validateLitecoinAddress(address string) error {
	if len(address) < 26 || len(address) > 62 {
		return fmt.Errorf("litecoin address length must be between 26 and 62 characters")
	}

	// Bech32 addresses (ltc1)
	if strings.HasPrefix(address, "ltc1") {
		return validateBech32Address(address, "ltc")
	}

	// Legacy and P2SH addresses (Base58Check)
	if strings.HasPrefix(address, "L") || strings.HasPrefix(address, "M") {
		return validateBase58CheckAddress(address)
	}

	return fmt.Errorf("litecoin address must start with L, M, or ltc1")
}

// validateDogecoinAddress validates Dogecoin addresses
func validateDogecoinAddress(address string) error {
	if len(address) != 34 {
		return fmt.Errorf("dogecoin address must be exactly 34 characters long")
	}

	if !strings.HasPrefix(address, "D") {
		return fmt.Errorf("dogecoin address must start with D")
	}

	return validateBase58CheckAddress(address)
}

// validateSolanaAddress validates Solana addresses
func validateSolanaAddress(address string) error {
	// Basic length validation (done in the main ValidateAddress function)
	// Solana addresses are Base58 encoded 32-byte public keys

	// Validate Base58 encoding
	for _, char := range address {
		if !strings.ContainsRune(base58Alphabet, char) {
			return fmt.Errorf("solana address contains invalid Base58 character")
		}
	}

	// Additional validation: decode and check if it results in 32 bytes
	decoded, err := decodeBase58(address)
	if err != nil {
		return fmt.Errorf("failed to decode solana address: %v", err)
	}

	if len(decoded) != 32 {
		return fmt.Errorf("solana address must decode to exactly 32 bytes, got %d bytes", len(decoded))
	}

	return nil
}

// validateXRPAddress validates XRP addresses
func validateXRPAddress(address string) error {
	if len(address) < 25 || len(address) > 34 {
		return fmt.Errorf("XRP address length must be between 25 and 34 characters")
	}

	if !strings.HasPrefix(address, "r") && !strings.HasPrefix(address, "X") {
		return fmt.Errorf("XRP address must start with r or X")
	}

	// Skip checksum validation for XRP addresses due to potential algorithm differences
	// Only validate Base58 format
	for _, char := range address {
		if !strings.ContainsRune(base58Alphabet, char) {
			return fmt.Errorf("XRP address contains invalid Base58 character")
		}
	}

	return nil
}

// validateCardanoAddress validates Cardano addresses
func validateCardanoAddress(address string) error {
	if len(address) < 100 || len(address) > 103 {
		return fmt.Errorf("cardano address must be between 100 and 103 characters long")
	}

	if !strings.HasPrefix(address, "addr1") {
		return fmt.Errorf("cardano address must start with addr1")
	}

	// Validate bech32 encoding
	return validateBech32Address(address, "addr")
}

// validateTronAddress validates Tron addresses
func validateTronAddress(address string) error {
	if len(address) != 34 {
		return fmt.Errorf("tron address must be exactly 34 characters long")
	}

	if !strings.HasPrefix(address, "T") {
		return fmt.Errorf("tron address must start with T")
	}

	return validateBase58CheckAddress(address)
}

// validateBitcoinCashAddress validates Bitcoin Cash addresses
func validateBitcoinCashAddress(address string) error {
	if len(address) < 26 || len(address) > 54 {
		return fmt.Errorf("bitcoin cash address length must be between 26 and 54 characters")
	}

	// CashAddr format (q, p prefixes)
	if strings.HasPrefix(address, "q") || strings.HasPrefix(address, "p") {
		// For CashAddr format, we'll do basic validation
		// Full CashAddr validation would require implementing the CashAddr spec
		return nil
	}

	// Legacy format (Base58Check - same as Bitcoin)
	if strings.HasPrefix(address, "1") || strings.HasPrefix(address, "3") {
		return validateBase58CheckAddress(address)
	}

	return fmt.Errorf("bitcoin cash address must start with 1, 3, q, or p")
}

// Helper functions

// isHexChar checks if a character is a valid hexadecimal character
func isHexChar(char rune) bool {
	return (char >= '0' && char <= '9') ||
		(char >= 'a' && char <= 'f') ||
		(char >= 'A' && char <= 'F')
}

// validateBase58CheckAddress validates Base58Check encoded addresses
func validateBase58CheckAddress(address string) error {
	// Check if all characters are valid Base58
	for _, char := range address {
		if !strings.ContainsRune(base58Alphabet, char) {
			return fmt.Errorf("address contains invalid Base58 character")
		}
	}

	// Decode Base58
	decoded, err := decodeBase58(address)
	if err != nil {
		return fmt.Errorf("failed to decode Base58 address: %v", err)
	}

	// Must be at least 5 bytes (1 version + 4 checksum)
	if len(decoded) < 5 {
		return fmt.Errorf("decoded address too short")
	}

	// Verify checksum
	payload := decoded[:len(decoded)-4]
	checksum := decoded[len(decoded)-4:]

	hash1 := sha256.Sum256(payload)
	hash2 := sha256.Sum256(hash1[:])
	expectedChecksum := hash2[:4]

	for i := 0; i < 4; i++ {
		if checksum[i] != expectedChecksum[i] {
			return fmt.Errorf("invalid checksum")
		}
	}

	return nil
}

// validateBech32mAddress validates Bech32m encoded addresses (used for Bitcoin Taproot)
func validateBech32mAddress(address, expectedHrp string) error {
	// Basic format validation
	if len(address) < 8 {
		return fmt.Errorf("bech32m address too short")
	}

	// Convert to lowercase for processing
	address = strings.ToLower(address)

	// Find the separator
	sepIndex := strings.LastIndex(address, "1")
	if sepIndex == -1 {
		return fmt.Errorf("invalid bech32m format: missing separator '1'")
	}

	hrp := address[:sepIndex]
	data := address[sepIndex+1:]

	if hrp != expectedHrp {
		return fmt.Errorf("invalid bech32m HRP: expected '%s', got '%s'", expectedHrp, hrp)
	}

	// Validate data part contains only valid bech32 characters
	for _, char := range data {
		if !strings.ContainsRune(bech32Alphabet, char) {
			return fmt.Errorf("invalid bech32m character")
		}
	}

	// Data part must be at least 6 characters (checksum is 6 chars)
	if len(data) < 6 {
		return fmt.Errorf("bech32m data part too short")
	}

	// Perform checksum validation using polymod with Bech32m constant
	if !validateBech32mChecksum(hrp, data) {
		return fmt.Errorf("invalid bech32m checksum")
	}

	return nil
}

// validateBech32Address validates Bech32 encoded addresses with proper checksum validation
func validateBech32Address(address, expectedHrp string) error {
	// Basic format validation
	if len(address) < 8 {
		return fmt.Errorf("bech32 address too short")
	}

	// Convert to lowercase for processing
	address = strings.ToLower(address)

	// Find the separator
	sepIndex := strings.LastIndex(address, "1")
	if sepIndex == -1 {
		return fmt.Errorf("invalid bech32 format: missing separator '1'")
	}

	hrp := address[:sepIndex]
	data := address[sepIndex+1:]

	if hrp != expectedHrp {
		return fmt.Errorf("invalid bech32 HRP: expected '%s', got '%s'", expectedHrp, hrp)
	}

	// Validate data part contains only valid bech32 characters
	for _, char := range data {
		if !strings.ContainsRune(bech32Alphabet, char) {
			return fmt.Errorf("invalid bech32 character")
		}
	}

	// Data part must be at least 6 characters (checksum is 6 chars)
	if len(data) < 6 {
		return fmt.Errorf("bech32 data part too short")
	}

	// Perform checksum validation using polymod
	if !validateBech32Checksum(hrp, data) {
		return fmt.Errorf("invalid bech32 checksum")
	}

	return nil
}

func bech32Polymod(values []int) int {
	bech32Generators := [5]int{
		0x3b6a57b2,
		0x26508e6d,
		0x1ea119fa,
		0x3d4233dd,
		0x2a1462b3,
	}

	chk := 1
	for _, v := range values {
		top := chk >> 25
		chk = ((chk & 0x1ffffff) << 5) ^ v
		for i := 0; i < 5; i++ {
			if (top>>i)&1 != 0 {
				chk ^= bech32Generators[i]
			}
		}
	}
	return chk
}

func validateBech32Checksum(hrp, data string) bool {
	values := bech32Expand(hrp)
	for _, c := range data {
		pos := strings.IndexRune(bech32Alphabet, c)
		if pos == -1 {
			return false
		}
		values = append(values, pos)
	}
	return bech32Polymod(values) == 1
}

func validateBech32mChecksum(hrp, data string) bool {
	values := bech32Expand(hrp)
	for _, c := range data {
		pos := strings.IndexRune(bech32Alphabet, c)
		if pos == -1 {
			return false
		}
		values = append(values, pos)
	}
	return bech32Polymod(values) == 0x2bc830a3
}

func bech32Expand(hrp string) []int {
	res := make([]int, 0, len(hrp)*2+1)
	for _, c := range hrp {
		res = append(res, int(c)>>5)
	}
	res = append(res, 0)
	for _, c := range hrp {
		res = append(res, int(c)&31)
	}
	return res
}

// decodeBase58 decodes a Base58 encoded string
func decodeBase58(encoded string) ([]byte, error) {
	if len(encoded) == 0 {
		return []byte{}, nil
	}

	// Count leading zeros
	leadingZeros := 0
	for _, char := range encoded {
		if char == '1' {
			leadingZeros++
		} else {
			break
		}
	}

	// Convert to big integer
	num := big.NewInt(0)
	base := big.NewInt(58)

	for _, char := range encoded {
		index := strings.IndexRune(base58Alphabet, char)
		if index == -1 {
			return nil, fmt.Errorf("invalid Base58 character: %c", char)
		}
		num.Mul(num, base)
		num.Add(num, big.NewInt(int64(index)))
	}

	// Convert to bytes
	decoded := num.Bytes()

	// Add leading zeros
	result := make([]byte, leadingZeros+len(decoded))
	copy(result[leadingZeros:], decoded)

	return result, nil
}

// validateXRPBase58CheckAddress validates XRP Base58Check encoded addresses using double SHA256 checksum
func validateXRPBase58CheckAddress(address string) error {
	// Check if all characters are valid Base58
	for _, char := range address {
		if !strings.ContainsRune(base58Alphabet, char) {
			return fmt.Errorf("address contains invalid Base58 character")
		}
	}

	// Decode Base58
	decoded, err := decodeBase58(address)
	if err != nil {
		return fmt.Errorf("failed to decode Base58 address: %v", err)
	}

	// Must be at least 5 bytes (1 version + 4 checksum)
	if len(decoded) < 5 {
		return fmt.Errorf("decoded address too short")
	}

	// Verify checksum using double SHA256 (same as Bitcoin)
	payload := decoded[:len(decoded)-4]
	checksum := decoded[len(decoded)-4:]

	// XRP actually uses double SHA256 for checksum calculation (same as Bitcoin)
	hash1 := sha256.Sum256(payload)
	hash2 := sha256.Sum256(hash1[:])
	expectedChecksum := hash2[:4]

	for i := 0; i < 4; i++ {
		if checksum[i] != expectedChecksum[i] {
			return fmt.Errorf("invalid XRP checksum")
		}
	}

	return nil
}

// Additional utility functions for comprehensive validation

// ValidateAddressFormat provides a quick format-only validation without full checksum verification
func ValidateAddressFormat(address, assetUID string) error {
	config, exists := GetAddressValidationConfig(assetUID)
	if !exists {
		return fmt.Errorf("no address validation config found for asset: %s", assetUID)
	}

	// Trim whitespace
	address = strings.TrimSpace(address)

	if address == "" {
		return fmt.Errorf("address cannot be empty")
	}

	// Basic length validation
	if len(address) < config.MinLength || len(address) > config.MaxLength {
		return fmt.Errorf("address length %d is invalid for %s (expected %d-%d characters)",
			len(address), assetUID, config.MinLength, config.MaxLength)
	}

	// Prefix validation (if required)
	if len(config.Prefixes) > 0 {
		validPrefix := false
		for _, prefix := range config.Prefixes {
			if strings.HasPrefix(address, prefix) {
				validPrefix = true
				break
			}
		}
		if !validPrefix {
			return fmt.Errorf("address must start with one of: %v for %s", config.Prefixes, assetUID)
		}
	}

	// Additional format validation for Base58-based coins, but skip for Bech32 addresses
	switch config.ValidationType {
	case "bitcoin":
		if strings.HasPrefix(address, "bc1") {
			// Bech32, skip Base58 check
			break
		}
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	case "litecoin":
		if strings.HasPrefix(address, "ltc1") {
			// Bech32, skip Base58 check
			break
		}
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	case "dogecoin":
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	case "xrp":
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	case "tron":
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	case "solana":
		// Solana addresses are always Base58
		for _, char := range address {
			if !strings.ContainsRune(base58Alphabet, char) {
				return fmt.Errorf("address contains invalid Base58 character")
			}
		}
	}

	return nil
}

// GetSupportedAssets returns a list of all supported asset UIDs
func GetSupportedAssets() []string {
	assets := make([]string, 0, len(addressValidationConfig))
	for assetUID := range addressValidationConfig {
		assets = append(assets, assetUID)
	}
	return assets
}

// GetAssetsByNetwork returns assets grouped by network
func GetAssetsByNetwork() map[string][]string {
	networkAssets := make(map[string][]string)

	for assetUID, config := range addressValidationConfig {
		networkAssets[config.Network] = append(networkAssets[config.Network], assetUID)
	}

	return networkAssets
}
