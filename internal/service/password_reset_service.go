package service

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	textTemplate "text/template"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

const (
	passwordResetTokenTTL = time.Hour // 1 hour as specified
	redisTokenPrefix      = "password_reset:"

	// Rate limiting constants
	maxResetAttempts = 5
	rateLimitTTL     = time.Hour // 1 hour rate limit window
	rateLimitPrefix  = "rate_limit:password_reset:"

	// Encryption key for password reset tokens (32 bytes for AES-256)
	// In production, this should come from environment variables or a secure key management system
	encryptionKey = "MonkeyTilt2024PasswordResetKey32" // 32 bytes exactly
)

// EmailTemplateData represents the data passed to email templates
type EmailTemplateData struct {
	ResetLink string
	Email     string
}

// PasswordResetTokenData represents the data encoded in the reset token
type PasswordResetTokenData struct {
	Token string `json:"token"`
	Email string `json:"email"`
}

// Assert interface implementation
var _ domain.PasswordResetService = (*PasswordResetService)(nil)

type PasswordResetService struct {
	httpClient *http.Client
	config     *utils.RESTServiceConfig
	redis      *redis.Client
	middleware *rest_client.RestClientMiddleware
}

func NewPasswordResetService(
	httpClient *http.Client,
	config *utils.RESTServiceConfig,
	redisClient *redis.Client,
	middleware *rest_client.RestClientMiddleware,
) *PasswordResetService {
	return &PasswordResetService{
		httpClient: httpClient,
		config:     config,
		redis:      redisClient,
		middleware: middleware,
	}
}

func (s *PasswordResetService) InitiateReset(ctx context.Context, email, baseUrl, referrer, clientIP string) error {
	// Step 1: Check rate limiting by IP address
	if err := s.checkRateLimit(ctx, clientIP); err != nil {
		slog.Warn("Password reset rate limited", "ip", clientIP, "email", email)
		return err
	}

	// Step 2: Validate user exists in Keycloak
	userID, err := s.validateUserExistsInKeycloak(ctx, email)
	if err != nil {
		return err
	}

	// Step 3: Generate UUID token
	token := uuid.New().String()

	// Step 4: Store token in Redis with TTL
	tokenKey := redisTokenPrefix + token
	tokenData := map[string]interface{}{
		"email":   email,
		"user_id": userID,
	}

	tokenDataJSON, err := json.Marshal(tokenData)
	if err != nil {
		return fmt.Errorf("failed to marshal token data: %w", err)
	}

	// Encrypt the token data
	encryptedTokenData, err := encryptTokenData(tokenDataJSON)
	if err != nil {
		return fmt.Errorf("failed to encrypt token data: %w", err)
	}

	err = s.redis.Set(ctx, tokenKey, string(encryptedTokenData), passwordResetTokenTTL).Err()
	if err != nil {
		return fmt.Errorf("failed to store reset token: %w", err)
	}

	// Step 5: Send reset email
	err = s.sendResetEmail(email, token, baseUrl, referrer)
	if err != nil {
		// Clean up token if email fails
		s.redis.Del(ctx, tokenKey)
		return fmt.Errorf("failed to send reset email: %w", err)
	}

	slog.Info("Password reset initiated successfully", "email", email, "user_id", userID)
	return nil
}

func (s *PasswordResetService) ValidateToken(ctx context.Context, encryptedToken string) (bool, string, error) {
	slog.Info("Password reset token validation started", "encryptedToken", encryptedToken[:16]+"...")

	// Decode and decrypt the token
	tokenData, err := decodePasswordResetToken(encryptedToken)
	if err != nil {
		slog.Warn("Failed to decode password reset token", "error", err)
		return false, "", nil // Invalid token format, but don't error - just return invalid
	}

	// Get token data from Redis using the decrypted token
	storedEmail, _, err := s.getTokenData(ctx, tokenData.Token)
	if err != nil {
		if err == domain.ErrPasswordResetTokenNotFound {
			slog.Warn("Password reset token not found in Redis", "email", tokenData.Email, "token", tokenData.Token[:8]+"...")
			return false, "", nil // Token not found, but don't error - just return invalid
		}
		slog.Error("Password reset token validation error", "email", tokenData.Email, "token", tokenData.Token[:8]+"...", "error", err)
		return false, "", err
	}

	// Verify the email matches between URL token and Redis data
	if storedEmail != tokenData.Email {
		slog.Warn("Password reset token email mismatch",
			"urlEmail", tokenData.Email,
			"storedEmail", storedEmail,
			"token", tokenData.Token[:8]+"...")
		return false, "", nil
	}

	slog.Info("Password reset token validation successful", "email", tokenData.Email, "token", tokenData.Token[:8]+"...")
	return true, tokenData.Email, nil
}

func (s *PasswordResetService) CompleteReset(ctx context.Context, encryptedToken, encodedPassword string) error {
	// Step 1: Decode and decrypt the token to get email and token
	tokenData, err := decodePasswordResetToken(encryptedToken)
	if err != nil {
		slog.Warn("Failed to decode password reset token", "error", err)
		return domain.ErrPasswordResetTokenInvalid
	}

	// Step 2: Get token data (email and user ID) from Redis
	storedEmail, userID, err := s.getTokenData(ctx, tokenData.Token)
	if err != nil {
		if err == domain.ErrPasswordResetTokenNotFound {
			return domain.ErrPasswordResetTokenNotFound
		}
		return err
	}

	// Step 3: Verify the email matches between URL token and Redis data
	if storedEmail != tokenData.Email {
		slog.Warn("Password reset completion email mismatch",
			"urlEmail", tokenData.Email,
			"storedEmail", storedEmail,
			"token", tokenData.Token[:8]+"...")
		return domain.ErrPasswordResetTokenInvalid
	}

	// Step 4: Base64 decode the password (frontend encodes it to handle special characters)
	decodedPassword, err := base64.StdEncoding.DecodeString(encodedPassword)
	if err != nil {
		slog.Warn("Failed to decode base64 password", "error", err)
		return fmt.Errorf("invalid password format")
	}
	newPassword := string(decodedPassword)

	// DEBUG: Log password details (remove this after debugging)
	slog.Info("Password reset debug info",
		"email", tokenData.Email,
		"encodedPasswordLength", len(encodedPassword),
		"decodedPasswordLength", len(newPassword),
		"encodedPassword", encodedPassword[:min(10, len(encodedPassword))]+"...", // First 10 chars only
		"decodedPassword", newPassword[:min(10, len(newPassword))]+"...") // First 10 chars only

	// Step 5: Update password in Keycloak via admin API
	if err := s.updatePasswordInKeycloak(ctx, userID, newPassword); err != nil {
		slog.Error("Keycloak password update failed",
			"email", tokenData.Email,
			"userID", userID,
			"error", err,
			"passwordLength", len(newPassword))
		return err // Return the error as-is, including any Keycloak validation errors
	}

	// Step 6: Delete token from Redis (cleanup)
	if err := s.deleteTokenFromRedis(ctx, tokenData.Token); err != nil {
		slog.Warn("Failed to delete reset token from Redis", "token", tokenData.Token[:8]+"...", "error", err)
		// Don't fail the request if cleanup fails
	}

	slog.Info("Password reset completed successfully",
		"email", tokenData.Email,
		"userID", userID,
		"token", tokenData.Token[:8]+"...")

	return nil
}

// validateUserExistsInKeycloak checks if user exists in Keycloak and returns user ID
func (s *PasswordResetService) validateUserExistsInKeycloak(ctx context.Context, email string) (string, error) {
	// Get admin token for Keycloak API
	token, err := s.getKeycloakAdminToken(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get Keycloak admin token: %w", err)
	}

	// Query Keycloak for user by email
	apiURL := fmt.Sprintf("%s/admin/realms/%s/users?email=%s",
		s.config.Keycloak.URL,
		s.config.Keycloak.Realm,
		url.QueryEscape(email))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to query Keycloak: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("Keycloak API error: %d", resp.StatusCode)
	}

	var users []struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&users); err != nil {
		return "", fmt.Errorf("failed to decode Keycloak response: %w", err)
	}

	if len(users) == 0 {
		return "", domain.ErrUserNotFoundInKeycloak
	}

	// DEBUG: Log the user ID we found (remove this after debugging)
	slog.Info("Found user in Keycloak",
		"email", email,
		"userID", users[0].ID,
		"userCount", len(users))

	return users[0].ID, nil
}

// generateResetToken creates a cryptographically secure random token
func (s *PasswordResetService) generateResetToken() string {
	return uuid.New().String()
}

// storeTokenInRedis stores the token with email as value and TTL
func (s *PasswordResetService) storeTokenInRedis(ctx context.Context, token, email string) error {
	if s.redis == nil {
		return fmt.Errorf("Redis client not available")
	}

	key := redisTokenPrefix + token
	return s.redis.Set(ctx, key, email, passwordResetTokenTTL).Err()
}

// getEmailFromToken retrieves email associated with token from Redis
func (s *PasswordResetService) getEmailFromToken(ctx context.Context, token string) (string, error) {
	if s.redis == nil {
		return "", fmt.Errorf("Redis client not available")
	}

	key := redisTokenPrefix + token
	encryptedTokenDataJSON, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", domain.ErrPasswordResetTokenNotFound
		}
		return "", err
	}

	// Decrypt the token data
	decryptedTokenData, err := decryptTokenData([]byte(encryptedTokenDataJSON))
	if err != nil {
		return "", fmt.Errorf("failed to decrypt token data: %w", err)
	}

	// Parse the JSON to extract the email
	var tokenData map[string]interface{}
	if err := json.Unmarshal(decryptedTokenData, &tokenData); err != nil {
		return "", fmt.Errorf("failed to parse token data: %w", err)
	}

	// Extract email from the JSON data
	email, ok := tokenData["email"].(string)
	if !ok {
		return "", fmt.Errorf("email not found in token data")
	}

	return email, nil
}

// getTokenData retrieves both email and user ID associated with token from Redis
func (s *PasswordResetService) getTokenData(ctx context.Context, token string) (email, userID string, err error) {
	if s.redis == nil {
		return "", "", fmt.Errorf("Redis client not available")
	}

	key := redisTokenPrefix + token
	encryptedTokenDataJSON, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", "", domain.ErrPasswordResetTokenNotFound
		}
		return "", "", err
	}

	// Decrypt the token data
	decryptedTokenData, err := decryptTokenData([]byte(encryptedTokenDataJSON))
	if err != nil {
		return "", "", fmt.Errorf("failed to decrypt token data: %w", err)
	}

	// Parse the JSON to extract email and user ID
	var tokenData map[string]interface{}
	if err := json.Unmarshal(decryptedTokenData, &tokenData); err != nil {
		return "", "", fmt.Errorf("failed to parse token data: %w", err)
	}

	// Extract email from the JSON data
	email, ok := tokenData["email"].(string)
	if !ok {
		return "", "", fmt.Errorf("email not found in token data")
	}

	// Extract user ID from the JSON data
	userID, ok = tokenData["user_id"].(string)
	if !ok {
		return "", "", fmt.Errorf("user_id not found in token data")
	}

	return email, userID, nil
}

// deleteTokenFromRedis removes token from Redis
func (s *PasswordResetService) deleteTokenFromRedis(ctx context.Context, token string) error {
	if s.redis == nil {
		return fmt.Errorf("Redis client not available")
	}

	key := redisTokenPrefix + token
	return s.redis.Del(ctx, key).Err()
}

// sendResetEmail sends password reset email via SendGrid using templates
func (s *PasswordResetService) sendResetEmail(email, token, baseUrl, referrer string) error {
	// Determine base URL: use explicit baseUrl, fallback to referrer, then default
	baseURL := s.determineBaseURL(baseUrl, referrer)

	// Create token data structure
	tokenData := PasswordResetTokenData{
		Token: token,
		Email: email,
	}

	// Marshal to JSON
	tokenJSON, err := json.Marshal(tokenData)
	if err != nil {
		return fmt.Errorf("failed to marshal token data: %w", err)
	}

	// Encrypt the JSON data
	encryptedData, err := encryptTokenData(tokenJSON)
	if err != nil {
		return fmt.Errorf("failed to encrypt token data: %w", err)
	}

	// Base64 encode (URL-safe)
	encodedToken := base64.URLEncoding.EncodeToString(encryptedData)

	// Create reset link with single encoded token parameter
	resetLink := fmt.Sprintf("%s/?auth=reset&token=%s", baseURL, url.QueryEscape(encodedToken))

	// Prepare template data
	templateData := EmailTemplateData{
		ResetLink: resetLink,
		Email:     email,
	}

	// Load and execute HTML template
	htmlContent, err := s.executeHTMLTemplate(templateData)
	if err != nil {
		return fmt.Errorf("failed to generate HTML email: %w", err)
	}

	// Load and execute plain text template
	textContent, err := s.executeTextTemplate(templateData)
	if err != nil {
		return fmt.Errorf("failed to generate text email: %w", err)
	}

	// Create SendGrid email
	from := mail.NewEmail("Monkey Tilt", "<EMAIL>")
	to := mail.NewEmail("", email)
	subject := "Reset Your Monkey Tilt Password"

	message := mail.NewSingleEmail(from, subject, to, textContent, htmlContent)

	client := sendgrid.NewSendClient(s.config.SendGridConfig.SendGridAPIKey)
	response, err := client.Send(message)
	if err != nil {
		return fmt.Errorf("failed to send email via SendGrid: %w", err)
	}

	if response.StatusCode >= 400 {
		return fmt.Errorf("SendGrid API error: %d %s", response.StatusCode, response.Body)
	}

	slog.Info("Password reset email sent successfully",
		"email", email,
		"statusCode", response.StatusCode)

	return nil
}

// executeHTMLTemplate loads and executes the HTML email template
func (s *PasswordResetService) executeHTMLTemplate(data EmailTemplateData) (string, error) {
	templatePath := filepath.Join("templates", "email", "password_reset.html")

	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		return "", fmt.Errorf("failed to parse HTML template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute HTML template: %w", err)
	}

	return buf.String(), nil
}

// executeTextTemplate loads and executes the plain text email template
func (s *PasswordResetService) executeTextTemplate(data EmailTemplateData) (string, error) {
	templatePath := filepath.Join("templates", "email", "password_reset.txt")

	tmpl, err := textTemplate.ParseFiles(templatePath)
	if err != nil {
		return "", fmt.Errorf("failed to parse text template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute text template: %w", err)
	}

	return buf.String(), nil
}

// determineBaseURL determines the base URL for password reset links
// Priority: explicit baseUrl > referrer header > default fallback
func (s *PasswordResetService) determineBaseURL(baseUrl, referrer string) string {
	// First priority: explicit baseUrl from request body
	if baseUrl != "" {
		// Ensure it has proper scheme
		if strings.HasPrefix(baseUrl, "http://") || strings.HasPrefix(baseUrl, "https://") {
			return strings.TrimSuffix(baseUrl, "/") // remove trailing slash
		}
		// Add https if no scheme provided
		return "https://" + strings.TrimSuffix(baseUrl, "/")
	}

	// Second priority: referrer header
	if referrer != "" {
		parsedURL, err := url.Parse(referrer)
		if err == nil {
			return fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)
		}
	}

	// Final fallback
	return "https://monkeytilt.com"
}

// getKeycloakAdminToken gets admin token for Keycloak API calls using existing middleware
func (s *PasswordResetService) getKeycloakAdminToken(ctx context.Context) (string, error) {
	// Use the existing middleware that already handles email service authentication
	// This uses the main keycloak instance, not keycloak-bo
	return s.middleware.MiddlewareForRestClient("email")
}

// updatePasswordInKeycloak updates user password via Keycloak admin API
func (s *PasswordResetService) updatePasswordInKeycloak(ctx context.Context, userID, newPassword string) error {
	// Get admin token
	token, err := s.getKeycloakAdminToken(ctx)
	if err != nil {
		return err
	}

	// Update password via Keycloak admin API
	apiURL := fmt.Sprintf("%s/admin/realms/%s/users/%s/reset-password",
		s.config.Keycloak.URL,
		s.config.Keycloak.Realm,
		userID)

	passwordData := map[string]interface{}{
		"type":      "password",
		"value":     newPassword,
		"temporary": false,
	}

	jsonData, err := json.Marshal(passwordData)
	if err != nil {
		return err
	}

	// DEBUG: Log what we're sending to Keycloak (remove this after debugging)
	slog.Info("Sending password update to Keycloak",
		"userID", userID,
		"passwordLength", len(newPassword),
		"jsonPayload", string(jsonData))

	req, err := http.NewRequestWithContext(ctx, http.MethodPut, apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)

		// DEBUG: Log the full Keycloak response (remove this after debugging)
		slog.Error("Keycloak password update failed",
			"statusCode", resp.StatusCode,
			"responseBody", string(body),
			"userID", userID,
			"passwordLength", len(newPassword))

		// Parse Keycloak error response to extract password validation errors
		if resp.StatusCode == http.StatusBadRequest {
			var keycloakError map[string]interface{}
			if json.Unmarshal(body, &keycloakError) == nil {
				if errorMsg, ok := keycloakError["errorMessage"].(string); ok {
					// Return password validation error with Keycloak's message
					return domain.NewPasswordValidationError(errorMsg)
				}
				if error_desc, ok := keycloakError["error_description"].(string); ok {
					return domain.NewPasswordValidationError(error_desc)
				}
			}
		}

		return fmt.Errorf("Keycloak password update error: %d %s", resp.StatusCode, string(body))
	}

	return nil
}

// checkRateLimit checks and increments the rate limit for an IP address
func (s *PasswordResetService) checkRateLimit(ctx context.Context, clientIP string) error {
	rateLimitKey := rateLimitPrefix + clientIP

	// Get current attempt count
	currentCount, err := s.redis.Get(ctx, rateLimitKey).Int()
	if err != nil && err != redis.Nil {
		// Log error but don't fail the request - fail open for Redis issues
		slog.Error("Failed to check rate limit", "error", err, "ip", clientIP)
		return nil
	}

	// Check if rate limit exceeded
	if currentCount >= maxResetAttempts {
		return domain.ErrPasswordResetRateLimited
	}

	// Increment counter
	if err == redis.Nil {
		// First attempt - set counter to 1 with TTL
		err = s.redis.Set(ctx, rateLimitKey, 1, rateLimitTTL).Err()
	} else {
		// Increment existing counter (keep original TTL)
		err = s.redis.Incr(ctx, rateLimitKey).Err()
	}

	if err != nil {
		// Log error but don't fail the request - fail open for Redis issues
		slog.Error("Failed to increment rate limit", "error", err, "ip", clientIP)
		return nil
	}

	slog.Debug("Rate limit check passed", "ip", clientIP, "attempts", currentCount+1)
	return nil
}

// decodePasswordResetToken decodes and decrypts a base64 encoded password reset token
func decodePasswordResetToken(encodedToken string) (*PasswordResetTokenData, error) {
	// URL decode first (in case it was URL encoded)
	decodedURL, err := url.QueryUnescape(encodedToken)
	if err != nil {
		return nil, fmt.Errorf("failed to URL decode token: %w", err)
	}

	// Base64 decode
	encryptedData, err := base64.URLEncoding.DecodeString(decodedURL)
	if err != nil {
		return nil, fmt.Errorf("failed to base64 decode token: %w", err)
	}

	// Decrypt the data
	decryptedData, err := decryptTokenData(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt token data: %w", err)
	}

	// Unmarshal JSON
	var tokenData PasswordResetTokenData
	if err := json.Unmarshal(decryptedData, &tokenData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal token data: %w", err)
	}

	return &tokenData, nil
}

// encryptTokenData encrypts the password reset token data using AES-GCM
func encryptTokenData(data []byte) ([]byte, error) {
	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt and authenticate
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// decryptTokenData decrypts the password reset token data using AES-GCM
func decryptTokenData(encryptedData []byte) ([]byte, error) {
	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Extract nonce and ciphertext
	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]

	// Decrypt and verify
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

// min returns the smaller of two integers (helper for debug logging)
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
