package service

import (
	// "context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type ElantilWageringClientService struct {
	elantil domain.ElantilWageringClient
}

func NewUserWageringService(elantil domain.ElantilWageringClient) *ElantilWageringClientService {
	return &ElantilWageringClientService{elantil: elantil}
}

func (u *ElantilWageringClientService) UpsertPlayerActivityTagsByUserExternalID(userExternalID string, categoryKey string, keyToSet string, value string) error {
	return u.elantil.UpsertPlayerActivityTagsByUserExternalID(userExternalID, categoryKey, keyToSet, value)
}

func (u *ElantilWageringClientService) GetPlayerActivityTagsByUserExternalID(userExternalID string) (interface{}, error) {
	return u.elantil.GetPlayerActivityTagsByUserExternalID(userExternalID)
}

func (u *ElantilWageringClientService) SendVerificationEmail(userExternalID string) error {
	return u.elantil.SendVerificationEmail(userExternalID)
}

func (u *ElantilWageringClientService) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName string) (bool, error) {
	return u.elantil.CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName)
}

func (u *ElantilWageringClientService) UpdatePassword(email string) error {
	return u.elantil.UpdatePassword(email)
}

func (u *ElantilWageringClientService) UpsertPlayerRefferals(userExternalID string, referralId string, username string) error {
	return u.elantil.UpsertPlayerRefferals(userExternalID, referralId, username)
}
