package service

import (
	"context"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type BonusQueueService struct {
	bonus          chan domain.BonusQueueItem
	userBonusQueue domain.UserBonusQueue
}

func NewBonusQueueService(userBonusQueue domain.UserBonusQueue) *BonusQueueService {
	queue := &BonusQueueService{
		bonus:          make(chan domain.BonusQueueItem),
		userBonusQueue: userBonusQueue,
	}

	go queue.StartBonusConsumer() // todo: if not handled in goroutine, it blocks the main thread
	return queue
}

func (q *BonusQueueService) StartBonusConsumer() {
	for bonus := range q.bonus {
		slog.Info("processing bonus from queue",
			"bonusID", bonus.B.ID,
			"queueLength", len(q.bonus))
		err := q.userBonusQueue.ProcessBonus(bonus)

		if err != nil {
			slog.Error("Error processing bonus",
				"bonusID", bonus.B.ID,
				"error", err)
		} else {
			slog.Info("Successfully processed bonus",
				"bonusID", bonus.B.ID)
		}
	}
}

func (q *BonusQueueService) AddBonusToQueue(ctx context.Context, token string, b domain.UserBonus) {
	slog.Info("received bonus to process", "bonusID", b.ID, "externalID", b.ExternalID)
	go func() {
		q.bonus <- domain.BonusQueueItem{
			B:     b,
			Token: token,
			Ctx:   ctx,
		}
	}()
}
