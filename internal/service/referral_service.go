package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type ReferralService struct {
	referralRepo domain.ReferralRepository
}

func NewReferralService(referralRepo domain.ReferralRepository) *ReferralService {
	return &ReferralService{referralRepo: referralRepo}
}

func (s *ReferralService) CreateUserReferralCampaign(ctx context.Context, userID string, username string, campaignName string) error {
	return s.referralRepo.CreateUserReferralCampaign(ctx, userID, username, campaignName)
}

func (s *ReferralService) GetUserReferralCampaign(ctx context.Context, userID string, campaignName string) ([]domain.UserCampaignResponse, int, error) {
	return s.referralRepo.GetUserReferralCampaign(ctx, userID, campaignName)
}

func (s *ReferralService) AddUserToTheListOfRefferedUsers(ctx context.Context, referralCode string, userID string, username string) error {
	return s.referralRepo.AddUserToTheListOfRefferedUsers(ctx, referralCode, userID, username)
}

func (s *ReferralService) CheckIfUserAlreadyUsedTheCode(ctx context.Context, referralCode string, userId string) (bool, error) {
	return s.referralRepo.CheckIfUserAlreadyUsedTheCode(ctx, referralCode, userId)
}

func (s *ReferralService) CalculateUserCommissions(ctx context.Context, userID string) error {
	return s.referralRepo.CalculateUserCommissions(ctx, userID)
}

func (s *ReferralService) ClaimReward(ctx context.Context, token string, userExternalID string) (float64, error) {
	return s.referralRepo.ClaimReward(ctx, token, userExternalID)
}

func (s *ReferralService) GetCommissionHistory(ctx context.Context, userID string, offset, limit int) (domain.CommissionHistoryResponse, error) {
	return s.referralRepo.GetCommissionHistory(ctx, userID, offset, limit)
}

func (s *ReferralService) GetDetailedCommissionInfo(ctx context.Context, parentUserId string) (domain.UserCommissionInfo, error) {
	return s.referralRepo.GetDetailedCommissionInfo(ctx, parentUserId)
}

func (s *ReferralService) GetParentUserIDByReferralCode(ctx context.Context, referralCode string) (string, error) {
	return s.referralRepo.GetParentUserIDByReferralCode(ctx, referralCode)
}

func (s *ReferralService) UpdateUserCommisionPercentage(ctx context.Context, userId string, campaignName string, commissionPercentage string, referredUsers []domain.CampaignReferredUsers) error {
	return s.referralRepo.UpdateUserCommisionPercentage(ctx, userId, campaignName, commissionPercentage, referredUsers)
}

func (s *ReferralService) IsUserInAnyReferralCampaign(ctx context.Context, userID string) (bool, string, error) {
	return s.referralRepo.IsUserInAnyReferralCampaign(ctx, userID)
}

func (s *ReferralService) CreateDefaultCampaignInDirectus(ctx context.Context, userId, username string) error {
	return s.referralRepo.CreateDefaultCampaignInDirectus(ctx, userId, username)
}

func (s *ReferralService) DeleteCampaigns(ctx context.Context, userID string, campaignName string) error {
	return s.referralRepo.DeleteCampaigns(ctx, userID, campaignName)
}

func (s *ReferralService) GetCampaignStats(ctx context.Context, userID string, campaignName string) (domain.CampaignStats, error) {
	return s.referralRepo.GetCampaignStats(ctx, userID, campaignName)
}

func (s *ReferralService) CalculateAllUsersCommissions(ctx context.Context) error {
	return s.referralRepo.CalculateAllUsersCommissions(ctx)
}

func (s *ReferralService) UpdateUserDefaultCommissionPercentage(userId string, defaultCommissionPercentage string) error {
	return s.referralRepo.UpdateUserDefaultCommissionPercentage(userId, defaultCommissionPercentage)
}

func (s *ReferralService) ManuallyUpdateReferralOfUser(userIdToUpdate string, userNameToUpdate string, newParentId string) error {
	return s.referralRepo.ManuallyUpdateReferralOfUser(userIdToUpdate, userNameToUpdate, newParentId)
}

func (s *ReferralService) CreateAdminCampaign(ctx context.Context, referralCode string, rewardAmount float64, codeUsageLimit int) error {
	return s.referralRepo.CreateAdminCampaign(ctx, referralCode, rewardAmount, codeUsageLimit)
}