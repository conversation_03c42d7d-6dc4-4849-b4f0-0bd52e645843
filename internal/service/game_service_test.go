package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

func TestGameService_LoadAll(t *testing.T) {
	ctx := context.Background()

	gameRepository := domainmock.NewGameRepository(t)
	gameProvider := domainmock.NewGameProvider(t)

	gameRepository.EXPECT().UpsertAllGamesIfNotInitialized(ctx, mock.Anything).Return(nil).Once()

	gameService, err := NewGameService(gameRepository, gameProvider)
	require.NoError(t, err)

	games := []domain.Game{}
	gameProvider.EXPECT().GetAllGames(ctx).Return(games, nil).Once()
	gameRepository.EXPECT().UpsertGames(ctx, games).Return(nil).Once()

	err = gameService.UpsertAllGames(ctx)
	require.NoError(t, err)
}
