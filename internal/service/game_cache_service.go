package service

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/robfig/cron/v3"
)

type CacheService struct {
	config *utils.RESTServiceConfig
}

func NewCacheService(
	config *utils.RESTServiceConfig,
) *CacheService {
	return &CacheService{
		config: config,
	}
}

var apiURLs = []string{
	"lobbies/2?fields=groups.groups_id.id,groups.groups_id.identifier,groups.groups_id.icon,groups.groups_id.games.siteGames_id.game.id,groups.groups_id.translations.value,groups.groups_id.games.siteGames_id.game.code,groups.groups_id.games.siteGames_id.status,groups.groups_id.games.siteGames_id.game.name,groups.groups_id.games.siteGames_id.game.label,groups.groups_id.games.siteGames_id.game.provider.id,groups.groups_id.games.siteGames_id.game.provider.countryTargetType,groups.groups_id.games.siteGames_id.game.provider.countries_id.alpha2,groups.groups_id.games.siteGames_id.thumbnail&deep[groups][groups_id][games][_limit]=20&deep[groups][groups_id][games][_filter][siteGames_id][status][_eq]=published&deep[groups][groups_id][translations][_filter][languages_id][code][_eq]=en-ca",
	"groups/?fields=id,identifier,games.siteGames_id.game.id,games.siteGames_id.status,games.siteGames_id.thumbnail,games.siteGames_id.game.name,games.siteGames_id.game.label,games.siteGames_id.game.launchType,games.siteGames_id.game.provider.name,games.siteGames_id.game.launchType,games.siteGames_id.game.provider.countryTargetType,games.siteGames_id.game.launchType,games.siteGames_id.game.provider.countries_id.alpha2,games.siteGames_id.game.code,count(games)&deep[games][_filter][siteGames_id][status][_eq]=published",
	"siteGames?fields=thumbnail,status,game.name,game.label,game.id,game.temp_EMRTP,game.provider.name,game.temp_EMRTP,game.provider.countryTargetType,game.temp_EMRTP,game.provider.countries_id.alpha2,game.code&meta=filter_count&filter[status][_eq]=published&filter[game][provider][id][_eq]=1&offset=0&limit=42",
}

func (c *CacheService) CacheGamesResponse() {
	loc, err := time.LoadLocation("Europe/Paris")
	if err != nil {
		slog.Error("Failed to load timezone", "error", err)
		return
	}

	scheduler := cron.New(cron.WithLocation(loc))

	_, err = scheduler.AddFunc("0 */5 * * *", func() {
		c.UploadAPIResponseToS3()
	})
	if err != nil {
		slog.Error("Failed to add cron job", "error", err)
		return
	}

	c.UploadAPIResponseToS3()

	scheduler.Start()
}

func (c *CacheService) UploadAPIResponseToS3() {
	for i, apiURL := range apiURLs {
		log := func(format string, a ...interface{}) {
			fmt.Printf("[API #%d] %s\n", i+1, fmt.Sprintf(format, a...))
		}

		resp, err := http.Get(c.config.CMSGamesURL + apiURL)
		if err != nil {
			log("Failed to fetch: %v", err)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log("Failed to fetch: %s", resp.Status)
			continue
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log("Failed to read response: %v", err)
			continue
		}

		var jsonData map[string]interface{}
		if err := json.Unmarshal(body, &jsonData); err != nil {
			log("Failed to parse JSON: %v", err)
			continue
		}

		formatted, _ := json.MarshalIndent(jsonData, "", "  ")
		// Extract the first part of the URL before any '/' or '?' to use as filename
		urlPath := apiURL
		if idx := strings.Index(urlPath, "/"); idx != -1 {
			urlPath = urlPath[:idx]
		} else if idx := strings.Index(urlPath, "?"); idx != -1 {
			urlPath = urlPath[:idx]
		}
		filename := fmt.Sprintf("%s.json", urlPath)
		if err := os.WriteFile(filename, formatted, 0644); err != nil {
			log("Failed to write file: %v", err)
			continue
		}
		log("Saved to file: %s", filename)

		s3filepath := filepath.Join("games", filename)
		if err := c.uploadToS3(filename, c.config.S3BucketConfig.BucketName, s3filepath); err != nil {
			log("Failed to upload to S3: %v", err)
		} else {
			log("Uploaded to S3: %s", s3filepath)
		}

		if err := os.Remove(filename); err != nil {
			log("Failed to delete file: %v", err)
		} else {
			log("Deleted local file")
		}
	}

	fmt.Println("All APIs processed.")
}

func (c *CacheService) uploadToS3(filename, bucket, s3filepath string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("open file: %v", err)
	}
	defer file.Close()

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(c.config.S3BucketConfig.Region),
		Credentials: credentials.NewStaticCredentials(
			c.config.S3BucketConfig.AccessKey,
			c.config.S3BucketConfig.SecretAccessKey,
			"",
		),
	})
	if err != nil {
		return fmt.Errorf("create AWS session: %v", err)
	}

	s3Client := s3.New(sess)

	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(s3filepath),
		Body:   file,
	})
	return err
}
