package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type SettingsService struct {
	settingsRepo domain.SettingsRepository
}

func NewSettingsService(settingsRepo domain.SettingsRepository) *SettingsService {
	return &SettingsService{settingsRepo: settingsRepo}
}

func (s *SettingsService) CreateSettings(ctx context.Context, userID string, settings domain.UserSettingsPayload) (domain.SettingsResponse, error) {
	return s.settingsRepo.CreateSettings(ctx, userID, settings)
}

func (s *SettingsService) GetSettings(userID string) (domain.SettingsResponse, error) {
	return s.settingsRepo.GetSettings(userID)
}

func (s *SettingsService) UpdateSettings(userID string, settings domain.UserSettings) error {
	return s.settingsRepo.UpdateSettings(userID, settings)
}

func (s *SettingsService) DeleteSettings(userID string, key string) error {
	return s.settingsRepo.DeleteSettings(userID, key)
}