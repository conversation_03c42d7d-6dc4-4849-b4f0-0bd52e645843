package service

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type PriceStore struct {
	prices                map[string]domain.Price
	mu                    sync.RWMutex
	elantilWageringClient domain.ElantilWageringClient
}

func NewPriceStore(elantilWageringClient domain.ElantilWageringClient) *PriceStore {
	return &PriceStore{
		prices:                make(map[string]domain.Price),
		elantilWageringClient: elantilWageringClient,
	}
}

func (ps *PriceStore) Update(price domain.Price) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	ps.prices[price.Name] = price
}

func (ps *PriceStore) Get(currency string) (domain.Price, bool) {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	price, exists := ps.prices[currency]
	return price, exists
}

func (ps *PriceStore) StartFiatPriceUpdater() {
	go ps.FiatPriceUpdater(context.Background(), ps)
}

func (ps *PriceStore) FiatPriceUpdater(ctx context.Context, store *PriceStore) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	ps.UpdatePriceStore(ctx, store)

	for {
		select {
		case <-ticker.C:
			ps.UpdatePriceStore(ctx, store)
		case <-ctx.Done():
			return
		}
	}
}

func (ps *PriceStore) UpdatePriceStore(ctx context.Context, store *PriceStore) {
	fetchCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	response, err := ps.elantilWageringClient.GetConversionRates(fetchCtx, "USD")
	if err != nil {
		fmt.Printf("Error fetching conversion rates: %v", err)
		return
	}

	for _, price := range response.Data.FiatPrices {
		store.Update(price)
	}
	for _, price := range response.Data.CryptoPrices {
		store.Update(price)
	}
}

func (ps *PriceStore) GetPriceValue(currencyCode string) *float64 {
	price, exists := ps.Get(currencyCode)
	if !exists {
		return nil
	}
	parsed, _ := strconv.ParseFloat(price.Price, 64)
	return &parsed
}
