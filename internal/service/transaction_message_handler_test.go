package service

import (
	"context"
	"strconv"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

func TestTransactionMessageHandler_HandleTransaction(t *testing.T) {
	ctx := context.Background()

	testTransBadAmount := schemas.TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "1-1-1",
		BatchExternalID: "123",
		CreatedOn:       *parseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "credit",
		Data: schemas.TransactionData{
			CasinoData: schemas.CasinoData{
				Config: schemas.CasinoConfig{Game: schemas.CasinoGame{GameID: "456"}},
			},
		},
	}

	testTrans := schemas.TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "50",
		BatchExternalID: "123",
		CreatedOn:       *parseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "credit",
		Data: schemas.TransactionData{
			CasinoData: schemas.CasinoData{
				Config: schemas.CasinoConfig{Game: schemas.CasinoGame{GameID: "456"}},
			},
		},
	}

	tests := []struct {
		name                     string
		getTransactionRepository func(t *testing.T) *domainmock.TransactionRepository
		getUserRepository        func(t *testing.T) *domainmock.UserRepository
		trans                    *schemas.TransactionPayload
		expectedErrFunc          require.ErrorAssertionFunc
	}{
		{
			name: "ParseTransactionError",
			getTransactionRepository: func(t *testing.T) *domainmock.TransactionRepository {
				return domainmock.NewTransactionRepository(t)
			},
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				return domainmock.NewUserRepository(t)
			},
			trans: &testTransBadAmount,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var numError *strconv.NumError
				require.ErrorAs(t, err, &numError)
			},
		},
		{
			name: "GetUserByExternalIDError",
			getTransactionRepository: func(t *testing.T) *domainmock.TransactionRepository {
				return domainmock.NewTransactionRepository(t)
			},
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repo := domainmock.NewUserRepository(t)
				repo.EXPECT().
					GetUserByExternalID(mock.Anything, mock.Anything).
					Return(nil, errTest).
					Once()
				return repo
			},
			trans: &testTrans,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, errTest)
			},
		},
		{
			name: "UpsertTransactionError",
			getTransactionRepository: func(t *testing.T) *domainmock.TransactionRepository {
				repo := domainmock.NewTransactionRepository(t)
				repo.EXPECT().
					UpsertTransaction(mock.Anything, mock.Anything).
					Return(nil, errTest).
					Once()
				return repo
			},
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repo := domainmock.NewUserRepository(t)
				repo.EXPECT().
					GetUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.User{}, nil).
					Once()
				return repo
			},
			trans: &testTrans,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, errTest)
			},
		},
		{
			name: "Success",
			getTransactionRepository: func(t *testing.T) *domainmock.TransactionRepository {
				repo := domainmock.NewTransactionRepository(t)
				repo.EXPECT().
					UpsertTransaction(mock.Anything, mock.Anything).
					Return(&domain.Transaction{}, nil).
					Once()
				return repo
			},
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repo := domainmock.NewUserRepository(t)
				repo.EXPECT().
					GetUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.User{}, nil).
					Once()
				return repo
			},
			trans:           &testTrans,
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Create a proper PriceStore instead of passing nil
			priceStore := &PriceStore{
				prices: make(map[string]domain.Price),
			}
			priceStore.prices["USD"] = domain.Price{
				Name:  "USD",
				Price: "1.0",
			}

			transHandler := NewTransactionMessageHandler(
				test.getTransactionRepository(t),
				test.getUserRepository(t),
				nil,
				nil,
				priceStore, // Use the created priceStore instead of nil
			)

			err := transHandler.handleTransaction(ctx, test.trans)
			test.expectedErrFunc(t, err)
		})
	}
}

func parseTime(s string) *time.Time {
	parsedTime, err := time.Parse(time.RFC3339, s)
	if err != nil {
		panic(err)
	}
	return &parsedTime
}