package service

import (
	"errors"
	"io"
	"log"

	"github.com/google/uuid"
)

func init() {
	log.SetOutput(io.Discard)
}

var (
	errTest = errors.New("test error")

	gameID1     = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	userID1     = uuid.MustParse("00000000-0000-0000-0000-000000000010")
	betID1      = uuid.MustParse("00000000-0000-0000-0000-000000000100")
	betID2      = uuid.MustParse("00000000-0000-0000-0000-000000000200")
	sessionsID1 = uuid.MustParse("00000000-0000-0000-0000-000000001000")
	boostID1    = uuid.MustParse("00000000-0000-0000-0000-000000010000")
)
