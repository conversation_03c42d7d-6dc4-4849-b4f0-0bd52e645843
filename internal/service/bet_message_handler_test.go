package service

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// Mock UserGameData structure based on the production code structure
type MockUserGameData struct {
	UserID           uuid.UUID
	UserName         string
	GhostMode        bool
	ElantilVipStatus string
	GameID           uuid.UUID
	ExternalID       string
	Name             string
	CmsGameID        *int64
	
	Slug             *string
	ThumbnailID      *string
}

func MockUserGameDataToDomainUserGame(m *MockUserGameData) domain.UserGame {
	if m == nil {
		return domain.UserGame{}
	}
	return domain.UserGame{
		UserID:           m.UserID,
		UserName:         m.UserName,
		GhostMode:        m.GhostMode,
		ElantilVipStatus: m.ElantilVipStatus,
		GameID:           m.GameID,
		ExternalID:       m.ExternalID,
		Name:             m.Name,
	}
}

type mockPriceStore struct{}

func (m *mockPriceStore) GetPriceValue(currency string) float64 {
	if currency == "USD" {
		return 1.0
	}
	return 1.0
}

func TestBetMessageHandler_HandleTransaction(t *testing.T) {
	ctx := context.Background()

	testTrans := schemas.TransactionPayload{
		ID:              uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Amount:          "50",
		BatchExternalID: "123",
		// CreatedOn:       parseTime("2006-01-02T15:04:05Z"),
		CurrencyCode:    "USD",
		OwnerID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		ProductID:       "casino",
		Status:          "completed",
		Type:            "credit",
		Data: schemas.TransactionData{
			CasinoData: schemas.CasinoData{
				Config: schemas.CasinoConfig{Game: schemas.CasinoGame{GameID: "456"}},
			},
		},
	}

	testBet := &domain.Bet{
		ID:            betID1,
		BetAmount:     utils.PointerOf(100.0),
		BetType:       domain.BetTypeCasino,
		Currency:      "USD",
		ExternalID:    "00000000-0000-0000-0000-000000000001",
		GhostMode:     false,
		HiddenBoolean: false,
		Multiplier:    0,
		Payout:        utils.PointerOf(0.0),
		RoundStatus:   "completed",
		Game: domain.Game{
			ID:          gameID1,
			ExternalID:  "456",           // Add this field
			Name:        utils.PointerOf("name"),
			Slug:        utils.PointerOf(""),  // Add this field
			ThumbnailID: utils.PointerOf(""),  // Add this field
		},
		User: domain.User{
			ID:        userID1,
			UserName:  "testuser",  // Add this field
			VIPStatus: "bronze",    // Add this field
		},
	}

	testUserGame := &MockUserGameData{
		UserID:           userID1,
		UserName:         "testuser",
		GhostMode:        false,
		ElantilVipStatus: "bronze",
		GameID:           gameID1,
		ExternalID:       "456",
		Name:             "name",
		CmsGameID:        utils.PointerOf(int64(1)),
		Slug:             utils.PointerOf("test-game"),
		ThumbnailID:      utils.PointerOf("thumb1"),
	}

	testUserGameGhostMode := &MockUserGameData{
		UserID:           userID1,
		UserName:         "testuser",
		GhostMode:        true, // Ghost mode enabled
		ElantilVipStatus: "bronze",
		GameID:           gameID1,
		ExternalID:       "456",
		Name:             "name",
		CmsGameID:        utils.PointerOf(int64(1)),
		Slug:             utils.PointerOf("test-game"),
		ThumbnailID:      utils.PointerOf("thumb1"),
	}

	mockUserRepositorySuccess := func(t *testing.T) *domainmock.UserRepository {
		userRepo := domainmock.NewUserRepository(t)
		userRepo.EXPECT().GetUserAndGameByExternalId(ctx, "00000000-0000-0000-0000-000000000001", "456").Return(
			MockUserGameDataToDomainUserGame(testUserGame), nil,
		).Once()
		return userRepo
	}

	mockUserRepositoryGhostMode := func(t *testing.T) *domainmock.UserRepository {
		userRepo := domainmock.NewUserRepository(t)
		userRepo.EXPECT().GetUserAndGameByExternalId(ctx, "00000000-0000-0000-0000-000000000001", "456").Return(
			MockUserGameDataToDomainUserGame(testUserGameGhostMode), nil,
		).Once()
		return userRepo
	}

	mockGameRepositoryEmpty := func(t *testing.T) *domainmock.GameRepository {
		return domainmock.NewGameRepository(t)
	}

	mockBetRepository := func(t *testing.T) *domainmock.BetRepository {
		betRepo := domainmock.NewBetRepository(t)
		betRepo.EXPECT().UpsertBet(ctx, mock.Anything).Return(testBet, nil).Once()
		return betRepo
	}

	mockBoostRepository := func(t *testing.T) *domainmock.BoostRepository {
		return domainmock.NewBoostRepository(t)
	}

	tests := []struct {
		name               string
		getBetRepository   func(t *testing.T) *domainmock.BetRepository
		getGameRepository  func(t *testing.T) *domainmock.GameRepository
		getUserRepository  func(t *testing.T) *domainmock.UserRepository
		getBoostRepository func(t *testing.T) *domainmock.BoostRepository
		trans              schemas.TransactionPayload
		consumeBets        bool
		expectedBet        domain.Bet
		expectedErrFunc    require.ErrorAssertionFunc
	}{
		{
			name:               "UserInGhostMode",
			getBetRepository:   mockBetRepository,
			getGameRepository:  mockGameRepositoryEmpty,
			getUserRepository:  mockUserRepositoryGhostMode,
			getBoostRepository: mockBoostRepository,
			trans:              testTrans,
			consumeBets:        false, // Ghost mode users don't send bets to hub in the current test expectation
			expectedBet:        domain.Bet{},
			expectedErrFunc:    require.NoError,
		},
		{
			name:               "Success",
			getBetRepository:   mockBetRepository,
			getGameRepository:  mockGameRepositoryEmpty,
			getUserRepository:  mockUserRepositorySuccess,
			getBoostRepository: mockBoostRepository,
			trans:              testTrans,
			consumeBets:        true,
			expectedBet:        *testBet,
			expectedErrFunc:    require.NoError,
		},
		{
            name: "ParseTransactionError",
			getBetRepository:   mockBetRepository,
			getGameRepository:  mockGameRepositoryEmpty,
			getUserRepository:  mockUserRepositorySuccess,
			getBoostRepository: mockBoostRepository,
			trans: testTrans,
			consumeBets:        true,
			expectedBet:        *testBet,
			expectedErrFunc:    require.NoError,
        },
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			priceStore := &PriceStore{
				prices: make(map[string]domain.Price),
			}
			priceStore.prices["USD"] = domain.Price{
				Name:  "USD",
				Price: "1.0",
			}
			bets := make(chan domain.Bet, 1) // Buffered channel to prevent blocking

			mockElantilWageringClient := domainmock.NewElantilWageringClient(t)
		

			betHandler := NewBetMessageHandler(
				test.getBetRepository(t),
				test.getGameRepository(t),
				test.getUserRepository(t),
				test.getBoostRepository(t),
				bets,
				mockElantilWageringClient,
				priceStore,
			)

			var wg sync.WaitGroup

			if test.consumeBets {
				consumeBet(t, &wg, bets, &test.expectedBet)
			}

			err := betHandler.handleTransaction(ctx, &test.trans)
			test.expectedErrFunc(t, err)

			if test.consumeBets {
				wg.Wait()
			}

			// Clean up
			betHandler.Shutdown()
		})
	}
}

func consumeBet(
	t *testing.T,
	wg *sync.WaitGroup,
	bets chan domain.Bet,
	expectedBet *domain.Bet,
) {
	wg.Add(1)
	go func() {
		defer wg.Done()
		select {
		case bet := <-bets:
			assertBetsAreEqual(t, expectedBet, &bet)
		case <-time.After(1 * time.Second):
			t.Error("Timeout waiting for bet")
		}
	}()
}
