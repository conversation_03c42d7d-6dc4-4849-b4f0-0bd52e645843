package service

import (
	"context"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
)

type UserBonusService struct {
	restClient          *rest_client.DirectusCMSClient
	userBonusRepository *postgres.UserBonusRepository
}

func NewUserBonusService(restClient *rest_client.DirectusCMSClient, userBonusRepository *postgres.UserBonusRepository) *UserBonusService {
	if restClient == nil {
		slog.Error("restClient is nil in NewUserBonusService")
	}
	if userBonusRepository == nil {
		slog.Error("userBonusRepository is nil in NewUserBonusService")
	}
	return &UserBonusService{
		restClient:          restClient,
		userBonusRepository: userBonusRepository,
	}
}

func (s *UserBonusService) CreateUserBonusInDatabase(ctx context.Context, data domain.UserBonus) error {
	return s.userBonusRepository.CreateUserBonusInDatabase(ctx, data)
}

func (s *UserBonusService) AssignBonusByType(ctx context.Context, bonusType, startDate, endDate string) error {
	return s.userBonusRepository.AssignBonusByType(ctx, bonusType, startDate, endDate)
}

func (s *UserBonusService) GetUserBonusesByExternalID(ctx context.Context, externalID string) ([]domain.UserBonus, error) {
	return s.userBonusRepository.GetUserBonusesByExternalID(ctx, externalID)
}

func (s *UserBonusService) UpdateUserBonusStatus(ctx context.Context, externalBonusId int, externalID string, status string) error {
	return s.userBonusRepository.UpdateUserBonusStatus(ctx, externalBonusId, externalID, status)
}

func (s *UserBonusService) DeleteExpiredBonuses(ctx context.Context) error {
	return s.userBonusRepository.DeleteExpiredBonuses(ctx)
}

func (s *UserBonusService) GetClaimedBonusesOfUserByExternalID(ctx context.Context, externalID string) ([]domain.UserBonus, error) {
	return s.userBonusRepository.GetClaimedBonusesOfUserByExternalID(ctx, externalID)
}

func (s *UserBonusService) ActivateUserBonusesByExternalIds(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int) error {
	return s.userBonusRepository.ActivateUserBonusesByExternalIds(ctx, userExternalIDs, category, status, rewardAmount, bonusExternalId)
}

func (s *UserBonusService) AssignSpecialBonus(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string) error {
	return s.userBonusRepository.AssignSpecialBonus(ctx, userId, username, category, rewardAmount, bonusExternalId, reason, note)
}

func (s *UserBonusService) CreateReloadBonuses(ctx context.Context, requestData []domain.ReloadBonusRequest) error {
	return s.userBonusRepository.CreateReloadBonuses(ctx, requestData)
}

func (s *UserBonusService) GetLevelUpBonusOfUser(userId string) ([]domain.UserBonus, error) {
	return s.userBonusRepository.GetLevelUpBonusOfUser(userId)
}

func (s *UserBonusService) UpdateModalPopupClosed(ctx context.Context, userId string) error {
	return s.userBonusRepository.UpdateModalPopupClosed(ctx, userId)
}

func (s *UserBonusService) ClaimAllBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	return s.userBonusRepository.ClaimAllBonusAndUpdateWallet(ctx, externalId, token)
}

func (s *UserBonusService) ClaimTiltBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error {
	return s.userBonusRepository.ClaimTiltBonusAndUpdateWallet(ctx, externalId, token)
}

func (s *UserBonusService) SingleClaimUpdate(ctx context.Context, token string, externalID string, bonusConfigID int) error {
	return s.userBonusRepository.SingleClaimUpdate(ctx, token, externalID, bonusConfigID)
}
