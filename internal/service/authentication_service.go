package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/karlseguin/ccache/v3"
)

type KeyCloakClient struct {
	httpClient   *http.Client
	config       *utils.RESTServiceConfig
	sessionCache *ccache.Cache[struct{}]
}

func NewKeyCloakClient(config *utils.RESTServiceConfig, httpClient *http.Client) *KeyCloakClient {
	return &KeyCloakClient{
		httpClient:   httpClient,
		config:       config,
		sessionCache: ccache.New(ccache.Configure[struct{}]()),
	}
}

func (k *KeyCloakClient) ValidateAccessToken(ctx context.Context, accessToken string) (domain.ValidateTokenResponse, error) {
	cacheKey := getTokenCacheKey(accessToken)
	if item := k.sessionCache.GetWithoutPromote(cacheKey); item != nil && !item.Expired() {
		resp, err := k.validateToken(ctx, accessToken)
		if err != nil {
			return domain.ValidateTokenResponse{}, err
		}
		return resp, nil
	}
	resp, err := k.validateToken(ctx, accessToken)
	if err != nil {
		return domain.ValidateTokenResponse{}, err
	}
	k.sessionCache.Set(cacheKey, struct{}{}, 5*time.Minute)
	return resp, nil
}

func (k *KeyCloakClient) validateToken(ctx context.Context, accessToken string) (domain.ValidateTokenResponse, error) {
	url := fmt.Sprintf("%s/realms/%s/protocol/openid-connect/userinfo", k.config.Keycloak.URL, k.config.Keycloak.Realm)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return domain.ValidateTokenResponse{}, fmt.Errorf("create request: %w", err)
	}
	req.Header.Set("Authorization", fmt.Sprint(accessToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-Id", k.config.Keycloak.TenantID)

	resp, err := k.httpClient.Do(req)
	if err != nil {
		return domain.ValidateTokenResponse{}, fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()
	switch resp.StatusCode {
	case http.StatusOK:
		// Continue
	case http.StatusUnauthorized:
		return domain.ValidateTokenResponse{}, fmt.Errorf("invalid token")
	default:
		return domain.ValidateTokenResponse{}, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var userInfo domain.ValidateTokenResponse

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return domain.ValidateTokenResponse{}, fmt.Errorf("read response body: %w", err)
	}
	if err := json.Unmarshal(body, &userInfo); err != nil {
		slog.Error("error decoding response", "error", err)
		return domain.ValidateTokenResponse{}, fmt.Errorf("decode response: %w", err)
	}

	if userInfo.UserID == "" {
		return domain.ValidateTokenResponse{}, fmt.Errorf("invalid token: empty subject")
	}
	return userInfo, nil
}

func getTokenCacheKey(accessToken string) string {
	return "access_token_" + accessToken
}

func (k *KeyCloakClient) StopCache() {
	k.sessionCache.Stop()
}
