package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type RailsClientService struct {
	RailsClient domain.RailsClient
}

func NewRailsClientService(railsClient domain.RailsClient) *RailsClientService {
	return &RailsClientService{
		RailsClient: railsClient,
	}
}

func (r *RailsClientService) CreateDepositAddressOfUser(ctx context.Context, token string) error {
	return r.RailsClient.CreateDepositAddressOfUser(ctx, token)
}

func (r *RailsClientService) GetUserDepositAddresses(ctx context.Context, token string) ([]domain.TokenAddress, error) {
	return r.RailsClient.GetUserDepositAddresses(ctx, token)
}

func (r *RailsClientService) CreateWithdrawalRequest(ctx context.Context, withdrawalRequest domain.CreateWithdrawalRequest) error {
	return r.RailsClient.CreateWithdrawalRequest(ctx, withdrawalRequest)
}
