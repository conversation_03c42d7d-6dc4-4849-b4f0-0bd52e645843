package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
)

type BonusService struct {
	restClient            *rest_client.DirectusCMSClient
	bonusRepository       *postgres.BonusRepository
	elantilWageringClient *rest_client.ElantilWageringClient
}

func NewBonusService(restClient *rest_client.DirectusCMSClient, bonusRepository *postgres.BonusRepository, elantilWageringClient *rest_client.ElantilWageringClient) *BonusService {
	return &BonusService{
		restClient:            restClient,
		bonusRepository:       bonusRepository,
		elantilWageringClient: elantilWageringClient,
	}
}

func (s *BonusService) CreateBonusConfig(ctx context.Context) error {
	return s.bonusRepository.CreateBonusConfig(ctx)
}

func (s *BonusService) UpdateVipTiersConfig() error {
	return s.restClient.UpdateVipTiersConfig()
}