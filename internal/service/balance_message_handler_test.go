package service

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
)

func TestBalanceMessageHandler_HandleMessage(t *testing.T) {
	tests := []struct {
		name            string
		input           schemas.OwnerWalletMessage
		expectedBalance domain.Balance
		expectError     bool
		expectUpdate    bool // Added to clarify when we expect an update
	}{
		{
			name: "Valid message with main wallet",
			input: schemas.OwnerWalletMessage{
				Payload: schemas.MessagePayload[schemas.OwnerWalletsPayload]{
					Current: schemas.OwnerWalletsPayload{
						OwnerID: "user123",
						Wallets: map[string]schemas.Wallet{
							"main": {
								ID:            "wallet123",
								Type:          "main",
								CurrencyCode:  "USD",
								BalanceAmount: "100.50",
								LastUpdatedOn: time.Now(),
							},
						},
					},
				},
			},
			expectedBalance: domain.Balance{
				UserExternalID: "user123",
				BalanceAmount:  100.50,
				Currency:       "USD",
				Type:           "main",
			},
			expectError:  false,
			expectUpdate: true,
		},
		{
			name: "Message without main wallet but with bonus wallet",
			input: schemas.OwnerWalletMessage{
				Payload: schemas.MessagePayload[schemas.OwnerWalletsPayload]{
					Current: schemas.OwnerWalletsPayload{
						OwnerID: "user123",
						Wallets: map[string]schemas.Wallet{
							"bonus": {
								ID:            "wallet456",
								Type:          "bonus",
								CurrencyCode:  "EUR",
								BalanceAmount: "50.25",
								LastUpdatedOn: time.Now(),
							},
						},
					},
				},
			},
			expectedBalance: domain.Balance{
				UserExternalID: "user123",
				BalanceAmount:  50.25,
				Currency:       "EUR",
				Type:           "bonus",
			},
			expectError:  false,
			expectUpdate: true, // Fixed: bonus wallets should trigger updates
		},
		{
			name: "Message with unsupported wallet type",
			input: schemas.OwnerWalletMessage{
				Payload: schemas.MessagePayload[schemas.OwnerWalletsPayload]{
					Current: schemas.OwnerWalletsPayload{
						OwnerID: "user123",
						Wallets: map[string]schemas.Wallet{
							"unsupported": {
								ID:            "wallet789",
								Type:          "unsupported",
								CurrencyCode:  "GBP",
								BalanceAmount: "75.00",
								LastUpdatedOn: time.Now(),
							},
						},
					},
				},
			},
			expectError:  false,
			expectUpdate: false, // No update expected for unsupported wallet types
		},
		{
			name: "Invalid balance amount",
			input: schemas.OwnerWalletMessage{
				Payload: schemas.MessagePayload[schemas.OwnerWalletsPayload]{
					Current: schemas.OwnerWalletsPayload{
						OwnerID: "user123",
						Wallets: map[string]schemas.Wallet{
							"main": {
								ID:            "wallet789",
								Type:          "main",
								CurrencyCode:  "GBP",
								BalanceAmount: "invalid",
								LastUpdatedOn: time.Now(),
							},
						},
					},
				},
			},
			expectError:  true,
			expectUpdate: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hubBalances := make(chan domain.Balance, 1)
			handler := NewBalanceMessageHandler(hubBalances)

			message, err := json.Marshal(tt.input)
			require.NoError(t, err)

			err = handler.HandleMessage(message)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				
				if tt.expectUpdate {
					select {
					case receivedBalance := <-hubBalances:
						assert.Equal(t, tt.expectedBalance.UserExternalID, receivedBalance.UserExternalID)
						assert.Equal(t, tt.expectedBalance.BalanceAmount, receivedBalance.BalanceAmount)
						assert.Equal(t, tt.expectedBalance.Type, receivedBalance.Type)
						assert.Equal(t, tt.expectedBalance.Currency, receivedBalance.Currency)
					default:
						t.Error("Expected to receive a balance update, but got none")
					}
				} else {
					select {
					case <-hubBalances:
						t.Error("Received unexpected balance update")
					default:
						// This is the expected behavior when no update should occur
					}
				}
			}
		})
	}
}