package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
)

// Assert interface implementation.
var _ domain.UserConfigAssetsService = (*UserConfigAssetsService)(nil)

type UserConfigAssetsService struct {
	userConfigUserRepository domain.UserConfigAssetRepository
}

func NewUserConfigAssetsService(userConfigUserRepository domain.UserConfigAssetRepository) *UserConfigAssetsService {
	return &UserConfigAssetsService{userConfigUserRepository: userConfigUserRepository}
}

func (s *UserConfigAssetsService) GetAssets(ctx context.Context) ([]domain.UserConfigAsset, error) {
	return s.userConfigUserRepository.GetAssets(ctx)
}

func (s *UserConfigAssetsService) GetAssetsTypes(ctx context.Context) ([]string, error) {
	return s.userConfigUserRepository.GetAssetsTypes(ctx)
}

func (s *UserConfigAssetsService) GetAssetsSubTypes(ctx context.Context) ([]string, error) {
	return s.userConfigUserRepository.GetAssetsSubTypes(ctx)
}

func (s *UserConfigAssetsService) GetAsset(ctx context.Context, id uuid.UUID) (*domain.UserConfigAsset, error) {
	return s.userConfigUserRepository.GetAsset(ctx, id)
}
