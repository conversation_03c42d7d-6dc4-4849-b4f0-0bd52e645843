package service

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/google/uuid"
)

type VIPStatusHandler struct {
	userRepository  domain.UserRepository
	vip_tier        domain.VIPTiers
	directusClient  domain.DirectusCMSClient
	betRepo         domain.BetRepository
	elantilWagering domain.ElantilWageringClient
}

func NewVIPStatusHandler(userRepository domain.UserRepository, vip_tier domain.VIPTiers, directusClient domain.DirectusCMSClient, betRepo domain.BetRepository, elantilWagering domain.ElantilWageringClient) *VIPStatusHandler {
	return &VIPStatusHandler{
		userRepository:  userRepository,
		vip_tier:        vip_tier,
		directusClient:  directusClient,
		betRepo:         betRepo,
		elantilWagering: elantilWagering,
	}
}

func (h *VIPStatusHandler) ProcessTierUpgrades() error {
	endTime := time.Now()
	startTime := endTime.Add(-1 * time.Minute)
	var userIDs []string
	err := h.userRepository.DB().
		Table("bets").
		Select("DISTINCT users.external_id").
		Joins("JOIN users ON users.id = bets.user_id").
		Where("bets.created_at BETWEEN ? AND ?", startTime, endTime).
		Where("bets.round_status = ?", "completed").
		Where("bets.bet_type IN ?", []string{"CASINO", "SPORTS"}).
		Pluck("users.external_id", &userIDs).Error

	if err != nil {
		slog.Error("Failed to fetch users with bets", "error", err)
		return err
	}

	if err != nil {
		slog.Error("Failed to fetch users with bets", "error", err)
		return err
	}

	for _, userID := range userIDs {
		user, err := h.userRepository.GetUserByExternalID(context.Background(), userID)
		if err != nil {
			slog.Error("Failed to get user", "user_id", userID, "error", err)
			continue
		}

		username := user.UserName
		currentTier, err := h.userRepository.GetCurrentVipStatus(context.Background(), userID)
		if err != nil {
			slog.Error("Failed to get user status", "user_id", userID, "error", err)
			continue
		}

		xp, _, err := h.userRepository.GetUserWageringAndVIPStatus(context.Background(), userID)
		if err != nil {
			slog.Error("Failed to get user XPs", "user_id", userID, "error", err)
			continue
		}
		newTier := h.vip_tier.MatchVIPTier(xp)
		if currentTier != newTier {
			note := fmt.Sprintf("VIP Upgrade Bonus to %s", newTier)
			db := h.userRepository.DB()
			query := db.Unscoped().
				Where("external_id = ? AND note = ? AND category = 'level-up'", userID, note)
			var existingBonus domain.UserBonus
			err = query.First(&existingBonus).Error
			if err != nil {
				slog.Error("Database query error or no record found",
					"error", err,
					"is_not_found", err == domain.ErrRecordNotFound)
			}

			if err == nil {
				continue
			}
			bonusReason := fmt.Sprintf("VIP Upgrade Bonus for the tier upgrade from %s to %s", currentTier, newTier)
			hasBonus, err := h.directusClient.CheckExistingTierUpgradeBonus(userID, note)
			if err != nil {
				slog.Error("Failed to check Directus bonus", "user_id", userID, "error", err)
				continue
			}
			if hasBonus {
				slog.Info("Bonus exists in Directus", "user_id", userID)
				continue
			}
			bonusAmount, _ := h.vip_tier.GetVIPTierUpgradeBonus(currentTier, newTier)
			directusBonus := domain.UserBonusInDirectus{
				ExternalID:   userID,
				RewardAmount: bonusAmount,
				Category:     "level-up",
				Username:     username,
				BonusStatus:  "active",
				ExpiresOn:    time.Now().AddDate(0, 0, 30),
				Reason:       bonusReason,
				Note:         note,
			}

			bonusID, err := h.directusClient.CreateUserBonus(directusBonus)
			if err != nil {
				slog.Error("Failed to create Directus bonus", "user_id", userID, "error", err)
				continue
			}
			localBonus := domain.VipUpgradeUserBonus{
				ID:              uuid.New(),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
				ExternalID:      userID,
				RewardAmount:    bonusAmount,
				Category:        "level-up",
				Username:        username,
				BonusConfigID:   7,
				ExpiresOn:       time.Now().AddDate(0, 0, 30),
				Reason:          bonusReason,
				BonusExternalID: bonusID,
				BonusStatus:     "active",
				Eligible:        true,
				Availed:         false,
				UserVipStatus:   newTier,
				Note:            note,
			}

			if err := h.userRepository.DB().Create(&localBonus).Error; err != nil {
				slog.Error("Failed to create local bonus", "user_id", userID, "error", err)
				continue
			}
			slog.Info("Created tier upgrade bonus in database", "user_id", userID, "from_tier", currentTier, "to_tier", newTier, "bonus_amount", bonusAmount)
		}
	}

	return nil
}

// Start the cron job
func StartTierUpgradeProcessor(handler *VIPStatusHandler) {
	ticker := time.NewTicker(1 * time.Minute)
	go func() {
		for range ticker.C {
			if err := handler.ProcessTierUpgrades(); err != nil {
				slog.Error("Failed to process tier upgrades", "error", err)
			}
		}
	}()
}
