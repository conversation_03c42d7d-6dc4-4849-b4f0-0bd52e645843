package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*TransactionMessageHandler)(nil)

type TransactionMessageHandler struct {
	transRepository         domain.TransactionRepository
	userRepository          domain.UserRepository
	bonusTemplateRepository domain.BonusTemplateRepository
	wageringClient          domain.ElantilWageringClient
	priceStore              *PriceStore
}

func NewTransactionMessageHandler(
	transRepository domain.TransactionRepository,
	userRepository domain.UserRepository,
	bonusTemplateRepository domain.BonusTemplateRepository,
	wageringClient domain.ElantilWageringClient,
	priceStore *PriceStore,
) *TransactionMessageHandler {
	return &TransactionMessageHandler{
		transRepository:         transRepository,
		userRepository:          userRepository,
		bonusTemplateRepository: bonusTemplateRepository,
		wageringClient:          wageringClient,
		priceStore:              priceStore,
	}
}

func (h *TransactionMessageHandler) HandleMessage(message []byte) error {
	var value schemas.TransactionMessage
	if err := json.Unmarshal(message, &value); err != nil {
		return err
	}

	slog.Debug("Succesfully decoded transaction", slog.Any("message", value))
	return h.handleTransaction(context.Background(), &value.Payload.Current)
}

func (h *TransactionMessageHandler) handleTransaction(
	ctx context.Context,
	message *schemas.TransactionPayload,
) error {
	symbol, cryptoAmount := message.ExtractCurrencySymbol()
	toUSDValue := h.priceStore.GetPriceValue(symbol)

	trans, err := message.ParseTransaction(symbol, cryptoAmount, toUSDValue)
	if err != nil {
		return fmt.Errorf("parse transaction '%v' for user %v: %w", message.ID, message.OwnerID, err)
	}

	user, err := h.userRepository.GetUserByExternalID(ctx, message.OwnerID.String())
	if err != nil {
		return fmt.Errorf("get user by external ID for transaction '%v': %w", message.ID, err)
	}

	trans.User = user

	if _, err := h.transRepository.UpsertTransaction(ctx, trans); err != nil {
		return err
	}

	go func() {
		if trans.Category == "payments" && trans.Type == "credit" && trans.Status == "completed" {
			bonusTemplate, err := h.bonusTemplateRepository.GetUserBonusTemplates(ctx, user.UserName)
			if err != nil {
				slog.Error("Failed to get user bonus templates", "error", err)
				return
			}

			if bonusTemplate != nil && len(bonusTemplate.BonusTemplates) > 0 && bonusTemplate.BonusTemplates[0].Status == "pending" {
				firstTransaction, err := h.transRepository.HasOnlyOneCreditTransaction(ctx, user.ID)
				if err != nil {
					slog.Error("Failed to get first credit transaction", "error", err)
					return
				}

				if firstTransaction {
					err := h.wageringClient.AssignBonusTemplate(ctx, bonusTemplate.BonusTemplates[0].OfferCode, user.ExternalID, "profiles", "")
					if err != nil {
						slog.Error("Failed to assign bonus template", "error", err)
						return
					}

					err = h.wageringClient.UpsertPlayerActivityTagsByUserExternalID(user.ExternalID, "player_activity", "wb_bonus", "true")
					if err != nil {
						slog.Error("Failed to upsert player activity tags", "error", err)
						return
					}

					updateBonusTemplate := domain.UserBonusTemplate{
						UserName:  user.UserName,
						OfferCode: bonusTemplate.BonusTemplates[0].OfferCode,
						Status:    "claimed",
					}

					if err := h.bonusTemplateRepository.UpdateBonusTemplate(ctx, &updateBonusTemplate); err != nil {
						slog.Error("Failed to update bonus template", "error", err)
						return
					}
				}
			}
		}
	}()
	return nil
}
