package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/karlseguin/ccache/v3"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

const (
	// One month
	usersEmailCacheItemTTL = 28 * 7 * 24 * time.Hour
)

// Assert interface implementation.
var _ domain.UserService = (*UserService)(nil)

// Service layer for users
type UserService struct {
	userRepository domain.UserRepository
	cache          *ccache.Cache[string]
}

func NewUserService(userRepository domain.UserRepository) *UserService {
	return &UserService{
		userRepository: userRepository,
		cache:          ccache.New(ccache.Configure[string]()),
	}
}

func (s *UserService) StopCache() {
	s.cache.Stop()
}

// Get user by UserName
func (s *UserService) GetUserByUserName(ctx context.Context, userName string) (*domain.User, error) {
	user, err := s.userRepository.GetUserByUserName(ctx, userName)
	if err != nil {
		return nil, fmt.Errorf("find user by name '%v': %w", userName, err)
	}

	return user, nil
}

// Get user by id
func (s *UserService) GetUserByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	user, err := s.userRepository.GetUserByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("find user by ID '%v': %w", id, err)
	}

	return user, nil
}

// Get Ranked Users by params
func (s *UserService) GetRankedUsers(ctx context.Context, params *domain.GetUserParams) (*domain.RankedUsers, error) {
	return s.userRepository.GetRankedUsers(ctx, params)
}

// Get Ranked Userss by UserExternalID params
func (s *UserService) GetRankedUsersByExternalID(
	ctx context.Context,
	id string,
	retrieveAmount int,
	orderBy string,
) ([]domain.RankedUser, error) {
	return s.userRepository.GetRankedUsersByExternalID(ctx, id, retrieveAmount, orderBy)
}

// Get user by external user id
func (s *UserService) GetUserByExternalID(ctx context.Context, externalUserID string) (*domain.User, error) {
	return s.userRepository.GetUserByExternalID(ctx, externalUserID)
}

// Get user and game by external user id and game id
func (s *UserService) GetUserAndGameByExternalId(ctx context.Context, userId, gameId string) (domain.UserGame, error) {
	return s.userRepository.GetUserAndGameByExternalId(ctx, userId, gameId)
}

// Get user by external user id
func (s *UserService) GetRankedUserByExternalID(ctx context.Context, externalUserID string) (*domain.RankedUser, error) {
	return s.userRepository.GetRankedUserByExternalID(ctx, externalUserID)
}

// update user by id
func (s *UserService) UpdateUser(ctx context.Context, user *domain.User) error {
	if err := s.userRepository.UpdateUserByID(ctx, user); err != nil {
		return fmt.Errorf("update user: %w", err)
	}

	return nil
}

func (s *UserService) SaveRegisteredEmail(ctx context.Context, email string, emailMarketing bool) error {
	return s.userRepository.SaveRegisteredEmail(ctx, email, emailMarketing)
}

func (s *UserService) GetRegisteredEmail(ctx context.Context, email string, marketing bool) (*domain.RegisteredEmail, error) {
	registeredEmail, err := s.userRepository.GetRegisteredEmail(ctx, email)

	if err != nil && !errors.Is(err, domain.ErrResourceNotFound) {
		return nil, err
	}

	err = s.userRepository.SaveRegisteredEmail(ctx, email, marketing)
	if err != nil {
		return nil, err
	}

	return registeredEmail, err
}

func (s *UserService) GetWinnerOfTheMonth(ctx context.Context) ([]domain.WinnerDetails, error) {
	return s.userRepository.GetWinnerOfTheMonth(ctx)
}

func (s *UserService) UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error) {
	return s.userRepository.UpdateUserXP(ctx, userID, xpToAdd)
}

func (s *UserService) GetUsersXPAndUpdateInElantil(ctx context.Context) error {
	return s.userRepository.GetUsersXPAndUpdateInElantil(ctx)
}

func (s *UserService) UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode, hideStats *bool) error {
	return s.userRepository.UpdateUserPreferences(ctx, userExternalID, ghostMode, hideStats)
}

func (s *UserService) UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error {
	return s.userRepository.UpdateUserMultiCurrency(ctx, userID, multiCurrency)
}

func (s *UserService) IsUserNameUnique(ctx context.Context, userName string) (bool, error) {
	exists, err := s.userRepository.IsUserNameUnique(ctx, userName)
	if err != nil {
		return false, fmt.Errorf("check if username '%v' is unique: %w", userName, err)
	}

	return exists, nil
}