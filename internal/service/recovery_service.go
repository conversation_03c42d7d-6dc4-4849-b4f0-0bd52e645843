package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strconv"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

type RecoveryService struct {
	db                 *gorm.DB
	wageringClient     domain.ElantilWageringClient
	betHandler         BetMessageHandler
	transactionHandler TransactionMessageHandler
	userRepository     domain.UserRepository
}

func NewRecoveryService(
	db *gorm.DB,
	wageringClient domain.ElantilWageringClient,
	betHandler BetMessageHandler,
	transactionHandler TransactionMessageHandler,
	userRepository domain.UserRepository,
) *RecoveryService {
	return &RecoveryService{
		db:                 db,
		wageringClient:     wageringClient,
		betHandler:         betHandler,
		transactionHandler: transactionHand<PERSON>,
		userRepository:     userRepository,
	}
}

func (s *RecoveryService) RecoverUsersTransactions(ctx context.Context) error {
	var allUsers []domain.UserRecovery

	query := `
	SELECT 
	    u.user_name, 
	    u.external_id, 
	    SUM(t.amount) AS total_amount
	FROM transactions t
	JOIN users u ON t.user_id = u.id
	WHERE t.type = 'debit'
	  AND t.category IN ('casino', 'sports', 'poker')
	  AND t.status = 'completed'
	  AND t.created_at >= NOW() - INTERVAL '24 hours'
	GROUP BY u.user_name, u.external_id;
	`

	if err := s.db.Raw(query).Scan(&allUsers).Error; err != nil {
		return fmt.Errorf("failed to query recover users: %w", err)
	}

	var allUserIDs []string
	for _, user := range allUsers {
		allUserIDs = append(allUserIDs, user.ExternalId)
	}

	usersWagering, err := s.wageringClient.BatchGetWageringSummary(ctx, allUserIDs)
	if err != nil {
		slog.Error("Failed to get wagering summary", "error", err)
	}

	var usersToRecover []string

	for _, user := range allUsers {
		fmt.Printf("User: %+v\n", user)
		userWagered, _ := strconv.ParseFloat(usersWagering[user.ExternalId].BetSum, 64)
		fmt.Printf("User wagered: %f\n", userWagered)
		wagerDifference := userWagered - user.TotalAmount
		fmt.Printf("Wager difference: %f\n", wagerDifference)

		if wagerDifference > 100 {
			usersToRecover = append(usersToRecover, user.ExternalId)
		}
	}

	fmt.Printf("Found %d recover users\n", len(usersToRecover))

	count := 0

	go func() {
		for _, recoverUser := range usersToRecover {
			fmt.Printf("User Info: %+v\n", recoverUser)

			transactions, err := s.wageringClient.GetUserTransactions(ctx, recoverUser, 1, 10000)
			if err != nil {
				slog.Error("Failed to get user transactions", "error", err, "externalID", recoverUser)
				return
			}

			for _, transaction := range transactions.Transaction {
				schemaPayload := s.ConvertToSchemaPayload(transaction)
				fmt.Printf("Schema Payload: %+v\n", schemaPayload)

				msg := schemas.Message[schemas.TransactionPayload]{
					Payload: schemas.MessagePayload[schemas.TransactionPayload]{
						Current: schemaPayload,
					},
				}

				msgBytes, err := json.Marshal(msg)
				if err != nil {
					slog.Error("Failed to marshal first transaction", "error", err, "transactionId", transaction.ID)
					return
				}

				if err := s.handleMessage(schemaPayload, msgBytes); err != nil {
					slog.Error("Failed to handle first transaction message", "error", err, "transactionId", transaction.ID)
					return
				}
			}

			count++

			fmt.Printf("Processed %d users\n", count)
			fmt.Printf("Recovered user transactions %s\n", recoverUser)
		}
		fmt.Println("Background recovery process completed")
	}()

	fmt.Println("Recovered transactions for all users")
	return nil
}

func (r *RecoveryService) ConvertToSchemaPayload(transaction domain.ElantilTransactions) schemas.TransactionPayload {
	return schemas.TransactionPayload{
		ID:              uuid.MustParse(transaction.ID),
		Amount:          transaction.Attributes.Amount,
		BatchExternalID: transaction.Attributes.BatchExternalID,
		CreatedOn:       transaction.Attributes.CreatedOn,
		CurrencyCode:    transaction.Attributes.CurrencyCode,
		Data: schemas.TransactionData{
			CasinoData: schemas.CasinoData{
				Config: schemas.CasinoConfig{
					Game: schemas.CasinoGame{
						GameID: transaction.Attributes.Data.CasinoData.Config.Game.GameID,
					},
				},
			},
		},
		OwnerID:   uuid.MustParse(transaction.Attributes.OwnerID),
		ProductID: transaction.Attributes.ProductID,
		Status:    transaction.Attributes.Status,
		Type:      transaction.Attributes.Type,
	}
}

func (s *RecoveryService) handleMessage(payload schemas.TransactionPayload, msgBytes []byte) error {
	if payload.IsBetTrans() {
		if err := s.betHandler.HandleRecoveryMessage(msgBytes); err != nil {
			slog.Error("Failed to handle bet message",
				"error", err,
				"transactionId", payload.ID)
			return err
		}
	}

	if err := s.transactionHandler.HandleMessage(msgBytes); err != nil {
		slog.Error("Failed to handle transaction message",
			"error", err,
			"transactionId", payload.ID)
		return err
	}

	return nil
}

func (s *RecoveryService) ScheduleWageringCron() {
	loc, err := time.LoadLocation("Asia/Tashkent") // GMT+5
	if err != nil {
		slog.Error("Failed to load timezone", "error", err)
	}

	scheduler := cron.New(cron.WithLocation(loc))

	_, err = scheduler.AddFunc("0 14 * * *", func() {
		if err := s.RecoverUsersTransactions(context.Background()); err != nil {
			slog.Error("Failed to recover users transactions", "error", err)
		}
	})

	if err != nil {
		slog.Error("Failed to schedule recovery job", "error", err)
	}
	
	scheduler.Start()
}
