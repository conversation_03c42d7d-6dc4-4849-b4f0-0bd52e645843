package service

// import (
// 	// "context"
// 	"context"
// 	"encoding/json"
// 	"testing"
// 	"time"

// 	"github.com/google/uuid"
// 	"github.com/stretchr/testify/require"

// 	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
// 	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
// 	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
// )

// func TestVIPStatusHandler_HandleMessage(t *testing.T) {
//     ctx := context.Background()
//     testUserID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

//     testTrans := schemas.TransactionPayload{
//         ID:           uuid.New(),
//         Amount:       "100",
//         CreatedOn:    time.Now(),
//         CurrencyCode: "USD",
//         OwnerID:      testUserID,
//         Status:       "completed",
//         Type:         "bet",
//     }

//     mockUserRepository := func(t *testing.T) *domainmock.UserRepository {
//         userRepo := domainmock.NewUserRepository(t)
//         userRepo.EXPECT().GetUserEarningAndSpending(ctx, testUserID.String()).
//             Return(float64(9000), float64(0), float64(0), nil).
//             Once()
//         userRepo.EXPECT().GetUserEarningAndSpending(ctx, testUserID.String()).
//             Return(float64(11000), float64(0), float64(0), nil).
//             Once()
//         userRepo.EXPECT().UpdateUserElantilVIPStatus(testUserID.String(), "Bronze").
//             Return(nil).
//             Once()
//         return userRepo
//     }

//     mockUserRepositoryNoChange := func(t *testing.T) *domainmock.UserRepository {
//         userRepo := domainmock.NewUserRepository(t)
//         userRepo.EXPECT().GetUserEarningAndSpending(ctx, testUserID.String()).
//             Return(float64(5000), float64(0), float64(0), nil).
//             Once()
//         userRepo.EXPECT().GetUserEarningAndSpending(ctx, testUserID.String()).
//             Return(float64(6000), float64(0), float64(0), nil).
//             Once()
//         return userRepo
//     }

//     tests := []struct {
//         name            string
//         getUserRepo     func(*testing.T) *domainmock.UserRepository
//         trans          schemas.TransactionPayload
//         expectVIPUpdate bool
//         expectedError   require.ErrorAssertionFunc
//     }{
//         {
//             name:            "VIP Status Changes",
//             getUserRepo:     mockUserRepository,
//             trans:          testTrans,
//             expectVIPUpdate: true,
//             expectedError:   require.NoError,
//         },
//         {
//             name:            "No VIP Status Change",
//             getUserRepo:     mockUserRepositoryNoChange,
//             trans:          testTrans,
//             expectVIPUpdate: false,
//             expectedError:   require.NoError,
//         },
//     }

//     for _, tt := range tests {
//         t.Run(tt.name, func(t *testing.T) {
//             vipUpdates := make(chan domain.VIPStatusUpdate, 1)
//             userRepo := tt.getUserRepo(t)

//             handler := NewVIPStatusHandler(userRepo, VIPTiers, vipUpdates)

//             message := schemas.TransactionMessage{
//                 Payload: schemas.MessagePayload[schemas.TransactionPayload]{
//                     Current: tt.trans,
//                 },
//             }

//             messageBytes, err := json.Marshal(message)
//             require.NoError(t, err)

//             err = handler.HandleMessage(messageBytes)
//             tt.expectedError(t, err)

//             if tt.expectVIPUpdate {
//                 select {
//                 case update := <-vipUpdates:
//                     require.Equal(t, tt.trans.OwnerID.String(), update.UserID)
//                     require.Equal(t, "Baboon", update.OldVIPTier)
//                     require.Equal(t, "Bronze", update.NewVIPTier)
//                 case <-time.After(100 * time.Millisecond):
//                     t.Error("Expected VIP update wasn't received")
//                 }
//             }
//         })
//     }
// }
