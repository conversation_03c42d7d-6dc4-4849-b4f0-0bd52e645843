package service

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func TestUserMessageHandler_HandleOwnerSessions(t *testing.T) {
	ctx := context.Background()

	testOwnerSessionsBadCreatedOn := schemas.OwnerSessionsPayload{
		CreatedOn:     "2006-01-02",
		LastUpdatedOn: utils.PointerOf("2006-01-02T15:04:05Z"),
		OwnerID:       uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Profile: schemas.OwnerSessionsProfile{
			AdditionalName: "name",
			Email:          "email",
			FamilyName:     "family-name",
			GivenName:      "given-name",
		},
	}

	testOwnerSessions := schemas.OwnerSessionsPayload{
		CreatedOn:     "2006-01-02T15:04:05Z",
		LastUpdatedOn: utils.PointerOf("2006-01-02T15:04:05Z"),
		OwnerID:       uuid.MustParse("00000000-0000-0000-0000-000000000001"),
		Profile: schemas.OwnerSessionsProfile{
			AdditionalName: "name",
			Email:          "email",
			FamilyName:     "family-name",
			GivenName:      "given-name",
		},
	}

	tests := []struct {
		name              string
		getUserRepository func(t *testing.T) *domainmock.UserRepository
		ownerSessions     *schemas.OwnerSessionsPayload
		expectedErrFunc   require.ErrorAssertionFunc
	}{
		{
			name: "ParseUserError",
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				return domainmock.NewUserRepository(t)
			},
			ownerSessions: &testOwnerSessionsBadCreatedOn,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				var parseError *time.ParseError
				require.ErrorAs(t, err, &parseError)
			},
		},
		{
			name: "UpserUserError",
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repo := domainmock.NewUserRepository(t)
				repo.EXPECT().UpsertUser(mock.Anything, mock.Anything).Return(nil, errTest).Once()
				return repo
			},
			ownerSessions: &testOwnerSessions,
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, errTest)
			},
		},
		{
			name: "Success",
			getUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repo := domainmock.NewUserRepository(t)
				repo.EXPECT().UpsertUser(mock.Anything, mock.Anything).Return(&domain.User{}, nil).Once()
				return repo
			},
			ownerSessions:   &testOwnerSessions,
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			userHandler := NewUserMessageHandler(test.getUserRepository(t))

			err := userHandler.handleOwnerSessions(ctx, test.ownerSessions)
			test.expectedErrFunc(t, err)
		})
	}
}
