package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type BonusTemplateService struct {
	bonusTemplateRepo domain.BonusTemplateRepository
}

func NewBonusTemplateService(bonusTemplateRepo domain.BonusTemplateRepository) *BonusTemplateService {
	return &BonusTemplateService{bonusTemplateRepo: bonusTemplateRepo}
}

func (s *BonusTemplateService) CreateBonusTemplate(ctx context.Context, bonusTemplate *domain.CreateBonusTemplateRequest) error {
	return s.bonusTemplateRepo.CreateBonusTemplate(ctx, bonusTemplate)
}

func (s *BonusTemplateService) GetUserBonusTemplates(ctx context.Context, username string) (*domain.BonusTemplateResponse, error) {
	return s.bonusTemplateRepo.GetUserBonusTemplates(ctx, username)
}

func (s *BonusTemplateService) UpdateBonusTemplate(ctx context.Context, bonusTemplate *domain.UserBonusTemplate) error {
	return s.bonusTemplateRepo.UpdateBonusTemplate(ctx, bonusTemplate)
}

func (s *BonusTemplateService) DeleteBonusTemplate(ctx context.Context, username string, offerCode string) error {
	return s.bonusTemplateRepo.DeleteBonusTemplate(ctx, username, offerCode)
}
