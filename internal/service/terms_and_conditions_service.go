package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type CMSTermsAndConditionsService struct {
	cmsTermAndConditionrepo domain.CMSTermsAndConditionsRepository
}

func NewCMSTermsAndConditionsService(cmsTermAndConditionrepo domain.CMSTermsAndConditionsRepository) *CMSTermsAndConditionsService {
	return &CMSTermsAndConditionsService{cmsTermAndConditionrepo: cmsTermAndConditionrepo}
}

func (s *CMSTermsAndConditionsService) CreateandUpdateCMSTermsAndConditions(ctx context.Context, contentTemplate *domain.AddCMSTermsAndConditions) error {
	return s.cmsTermAndConditionrepo.CreateandUpdateCMSTermsAndConditions(ctx, contentTemplate)
}

func (s *CMSTermsAndConditionsService) GetCMSTermsAndConditions(ctx context.Context, category string) (*domain.AddCMSTermsAndConditions, error) {
	return s.cmsTermAndConditionrepo.GetCMSTermsAndConditions(ctx, category)
}

func (s *CMSTermsAndConditionsService) DeleteCMSTermsAndConditions(ctx context.Context, category string) error {
	return s.cmsTermAndConditionrepo.DeleteCMSTermsAndConditions(ctx, category)
}

func (s *CMSTermsAndConditionsService) GetSportsBookCMSTermsAndConditions(ctx context.Context) ([]*domain.AddCMSTermsAndConditions, error) {
	return s.cmsTermAndConditionrepo.GetSportsBookCMSTermsAndConditions(ctx)
}
