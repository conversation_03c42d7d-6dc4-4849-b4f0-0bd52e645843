package service

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

func Test(t *testing.T) {
	tests := []struct {
		name                        string
		newVIPUserBalanceRepository func(*testing.T) *domainmock.VIPUserBalanceRepository
		newVIPTiersService          func(*testing.T) *domainmock.VIPTiersService
		newUserRepository           func(*testing.T) *domainmock.UserRepository
		bumpUser                    domain.BumpUserVIPStatus
		expectedErrFunc             require.ErrorAssertionFunc
	}{
		{
			name: "EqualTiers",
			newVIPUserBalanceRepository: func(t *testing.T) *domainmock.VIPUserBalanceRepository {
				return domainmock.NewVIPUserBalanceRepository(t)
			},
			newVIPTiersService: func(t *testing.T) *domainmock.VIPTiersService {
				return domainmock.NewVIPTiersService(t)
			},
			newUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repository := domainmock.NewUserRepository(t)

				repository.EXPECT().
					GetRankedUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.RankedUser{User: domain.User{VIPStatus: "tier1", TotalCoins: 600}}, nil).
					Once()

				return repository
			},
			bumpUser: domain.BumpUserVIPStatus{
				UserExternalID:  "id1",
				VIPStatusToBump: "tier1",
				AddBase:         false,
			},
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, domain.ErrInvalidVIPUserToBumpTo)
			},
		},
		{
			name: "TotalCoinsGreaterThanThreshold",
			newVIPUserBalanceRepository: func(t *testing.T) *domainmock.VIPUserBalanceRepository {
				return domainmock.NewVIPUserBalanceRepository(t)
			},
			newVIPTiersService: func(t *testing.T) *domainmock.VIPTiersService {
				service := domainmock.NewVIPTiersService(t)

				service.EXPECT().
					GetVIPTierByName(mock.Anything).
					Return(&domain.VIPTiersThreshold{Tier: "tier2", Threshold: 999}, true).
					Once()

				return service
			},
			newUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repository := domainmock.NewUserRepository(t)

				repository.EXPECT().
					GetRankedUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.RankedUser{User: domain.User{VIPStatus: "tier1", TotalCoins: 1000}}, nil).
					Once()

				return repository
			},
			bumpUser: domain.BumpUserVIPStatus{
				UserExternalID:  "id1",
				VIPStatusToBump: "tier2",
				AddBase:         false,
			},
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, domain.ErrInvalidVIPUserToBumpTo)
			},
		},
		{
			name: "TotalCoinsEqualsThreshold",
			newVIPUserBalanceRepository: func(t *testing.T) *domainmock.VIPUserBalanceRepository {
				return domainmock.NewVIPUserBalanceRepository(t)
			},
			newVIPTiersService: func(t *testing.T) *domainmock.VIPTiersService {
				service := domainmock.NewVIPTiersService(t)

				service.EXPECT().
					GetVIPTierByName(mock.Anything).
					Return(&domain.VIPTiersThreshold{Tier: "tier2", Threshold: 1000}, true).
					Once()

				return service
			},
			newUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repository := domainmock.NewUserRepository(t)

				repository.EXPECT().
					GetRankedUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.RankedUser{User: domain.User{VIPStatus: "tier1", TotalCoins: 1000}}, nil).
					Once()

				return repository
			},
			bumpUser: domain.BumpUserVIPStatus{
				UserExternalID:  "id1",
				VIPStatusToBump: "tier2",
				AddBase:         false,
			},
			expectedErrFunc: func(t require.TestingT, err error, _ ...any) {
				require.ErrorIs(t, err, domain.ErrInvalidVIPUserToBumpTo)
			},
		},
		{
			name: "Success",
			newVIPUserBalanceRepository: func(t *testing.T) *domainmock.VIPUserBalanceRepository {
				repository := domainmock.NewVIPUserBalanceRepository(t)

				repository.EXPECT().
					CreateVIPUserBalance(mock.Anything, mock.Anything).
					Run(func(_ context.Context, user *domain.VIPUserBalance) {
						expectedUser := &domain.VIPUserBalance{User: domain.User{ExternalID: "id1"}, Coins: 400}
						require.True(t, cmp.Equal(expectedUser, user), cmp.Diff(expectedUser, user))
					}).
					Return(&domain.VIPUserBalance{}, nil).
					Once()

				return repository
			},
			newVIPTiersService: func(t *testing.T) *domainmock.VIPTiersService {
				service := domainmock.NewVIPTiersService(t)

				service.EXPECT().
					GetVIPTierByName(mock.Anything).
					Return(&domain.VIPTiersThreshold{Tier: "tier2", Threshold: 1000}, true).
					Once()

				return service
			},
			newUserRepository: func(t *testing.T) *domainmock.UserRepository {
				repository := domainmock.NewUserRepository(t)

				repository.EXPECT().
					GetRankedUserByExternalID(mock.Anything, mock.Anything).
					Return(&domain.RankedUser{User: domain.User{VIPStatus: "tier1", TotalCoins: 600}}, nil).
					Once()

				return repository
			},
			bumpUser: domain.BumpUserVIPStatus{
				UserExternalID:  "id1",
				VIPStatusToBump: "tier2",
				AddBase:         false,
			},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			vipUserBalanceService := NewVIPUserBalanceService(
				test.newVIPUserBalanceRepository(t),
				test.newVIPTiersService(t),
				test.newUserRepository(t),
			)

			err := vipUserBalanceService.BumpUser(context.Background(), test.bumpUser)
			test.expectedErrFunc(t, err)
		})
	}
}
