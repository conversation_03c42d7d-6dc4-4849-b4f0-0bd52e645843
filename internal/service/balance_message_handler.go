package service

import (
	"context"
	"encoding/json"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*BalanceMessageHandler)(nil)

type BalanceMessageHandler struct {
	hubBalances chan<- domain.Balance
}

func NewBalanceMessageHandler(hubBalances chan<- domain.Balance) *BalanceMessageHandler {
	return &BalanceMessageHandler{hubBalances: hubBalances}
}

func (h *BalanceMessageHandler) HandleMessage(message []byte) error {
	var value schemas.OwnerWalletMessage
	if err := json.Unmarshal(message, &value); err != nil {
		return err
	}

	slog.Debug("Succesfully decoded balance", slog.Any("message", value))
	return h.handleTransactionMessage(context.Background(), &value.Payload.Current)
}

func (h *BalanceMessageHandler) handleTransactionMessage(
	ctx context.Context,
	message *schemas.OwnerWalletsPayload,
) error {
	for _, wallet := range message.Wallets {
		switch wallet.Type {
		case "main", "bonus", "bonus-locked":
			balance, err := wallet.ParseBalance(message.OwnerID)
			if err != nil {
				return err
			}
			h.hubBalances <- *balance
		}
	}
	return nil
}
