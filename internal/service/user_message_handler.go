package service

import (
	"context"
	"encoding/json"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*UserMessageHandler)(nil)

type UserMessageHandler struct {
	userRepository domain.UserRepository
}

func NewUserMessageHandler(
	userRepository domain.UserRepository,
) *UserMessageHandler {
	return &UserMessageHandler{
		userRepository: userRepository,
	}
}

func (h *UserMessageHandler) HandleMessage(message []byte) error {
	var value schemas.OwnerSessionsMessage
	if err := json.Unmarshal(message, &value); err != nil {
		return err
	}
	slog.Debug("Succesfully decoded owner sessions", slog.Any("message", value))
	return h.handleOwnerSessions(context.Background(), &value.Payload.Current)
}

func (h *UserMessageHandler) handleOwnerSessions(
	ctx context.Context,
	message *schemas.OwnerSessionsPayload,
) error {
	user, err := message.ParseUser()
	if err != nil {
		return err
	}

	_, err = h.userRepository.UpsertUser(ctx, user)
	return err
}
