package service

import (
	"context"
	"encoding/json"
	"log/slog"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// Assert interface implementation.
var _ domain.BetService = (*BetService)(nil)

// Service layer for bets
type BetService struct {
	betRepository     domain.BetRepository
	gameRepository    domain.GameRepository
	userRepository    domain.UserRepository
	boostRepository   domain.BoostRepository
	redis             *redis.Client
	privateToPublicID map[int64]uint
	stopChan          chan struct{}
}

func NewBetService(
	betRepository domain.BetRepository,
	gameRepository domain.GameRepository,
	userRepository domain.UserRepository,
	boostRepository domain.BoostRepository,
	redisClient *redis.Client,
) *BetService {
	return &BetService{
		betRepository:     betRepository,
		gameRepository:    gameRepository,
		userRepository:    userRepository,
		boostRepository:   boostRepository,
		redis:             redisClient,
		privateToPublicID: make(map[int64]uint),
	}
}

// getSlatedBetsFromRedis retrieves all slated bets from Redis with timeout protection
func (s *BetService) getSlatedBetsFromRedis(ctx context.Context) ([]domain.Bet, error) {
	if s.redis == nil {
		return nil, nil
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	vals, err := s.redis.ZRange(ctx, utils.GetSlatedBetsKey(), 0, -1).Result()
	if err != nil {
		if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
			slog.Error("redis fetch slated bets", "error", err)
		}
		return nil, err
	}

	bets := make([]domain.Bet, 0, len(vals))
	for _, val := range vals {
		var bet domain.Bet
		if json.Unmarshal([]byte(val), &bet) != nil {
			continue
		}
		bets = append(bets, bet)
	}

	if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
		slog.Debug("Retrieved slated bets from Redis", "count", len(bets))
	}
	return bets, nil
}

// Queries the bets table for all the bets and merges with simulated bets
func (s *BetService) GetBets(ctx context.Context, params *domain.GetBetsParams) (*domain.Bets, error) {
	realBets, err := s.betRepository.GetBets(ctx, params)
	if err != nil {
		slog.Error("Failed to get real bets from repository", "error", err)
		return nil, err
	}

	// Get slated bets from Redis
	slatedBets, err := s.getSlatedBetsFromRedis(ctx)
	if err != nil {
		if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
			slog.Warn("Failed to get slated bets from Redis, continuing with real bets only", "error", err)
		}
		return realBets, nil
	}

	sims := make([]domain.Bet, 0, len(slatedBets))
	for _, b := range slatedBets {
		if s.shouldIncludeSimulatedBet(&b, params) {
			sims = append(sims, b)
		}
	}

	merged := s.mergeBets(realBets.Items, sims, params)

	if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
		slog.Info("Retrieved bets with slated bet merging",
			"realBetsCount", len(realBets.Items),
			"slatedBetsCount", len(sims),
			"totalMergedBets", len(merged),
			"userFilter", params.UserExternalID)
	}

	return &domain.Bets{Items: merged, Paging: realBets.Paging}, nil
}

// Queries the bets table by bet id for bet details => Public
func (s *BetService) GetBetByID(ctx context.Context, id uuid.UUID) (*domain.Bet, error) {
	// First try to get from real bets
	bet, err := s.betRepository.GetBetByID(ctx, id)
	if err == nil {
		return bet, nil
	}

	// If not found, try to locate a slated bet with this ID in Redis
	if err == domain.ErrResourceNotFound {
		slatedBets, redisErr := s.getSlatedBetsFromRedis(ctx)
		if redisErr != nil {
			if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
				slog.Warn("Failed to get slated bets from Redis for bet lookup", "error", redisErr)
			}
			return nil, err
		}

		if slatedBets != nil {
			for _, b := range slatedBets {
				if b.ID == id {
					if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
						slog.Debug("Found slated bet by ID",
							"betId", id,
							"user", b.User.UserName,
							"game", *b.Game.Name,
							"betAmount", *b.BetAmount)
					}
					return &b, nil
				}
			}
		}
		if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
			slog.Debug("Slated bet not found by ID", "betId", id)
		}
	}

	return nil, err
}

// shouldIncludeSimulatedBet applies the same filters as the database query to simulated bets
func (s *BetService) shouldIncludeSimulatedBet(bet *domain.Bet, params *domain.GetBetsParams) bool {
	return (params.UserExternalID == nil || bet.User.ExternalID == *params.UserExternalID) &&
		(params.IncludeBetsWithoutGame || (bet.Game.Name != nil && *bet.Game.Name != "")) &&
		(params.PayoutOver == nil || bet.Payout == nil || *bet.Payout > *params.PayoutOver) &&
		(params.FilterBetsByGame == nil || !*params.FilterBetsByGame || !s.isGameFiltered(bet.Game.CMSGameID)) &&
		(params.BetType == nil || string(bet.BetType) == *params.BetType)
}

// isGameFiltered checks if a game CMS ID is in the filtered list
func (s *BetService) isGameFiltered(cmsGameID int64) bool {
	for _, filteredID := range utils.FilteredCMSGameIDs {
		if cmsGameID == int64(filteredID) {
			return true
		}
	}
	return false
}

// mergeBets combines real and simulated bets, maintaining proper ordering and deduplicating by bet ID
func (s *BetService) mergeBets(realBets []domain.Bet, simulatedBets []domain.Bet, params *domain.GetBetsParams) []domain.Bet {
	// Use a map to deduplicate bets by ID
	betMap := make(map[uuid.UUID]domain.Bet)

	// Add real bets first (they take precedence over simulated bets with same ID)
	for _, bet := range realBets {
		betMap[bet.ID] = bet
	}

	// Add simulated bets only if they don't already exist
	for _, bet := range simulatedBets {
		if _, exists := betMap[bet.ID]; !exists {
			betMap[bet.ID] = bet
		}
	}

	// Convert map back to slice
	allBets := make([]domain.Bet, 0, len(betMap))
	for _, bet := range betMap {
		allBets = append(allBets, bet)
	}

	// Sort by time (default order)
	sort.Slice(allBets, func(i, j int) bool {
		if params != nil && params.Order == domain.OrderDirAsc {
			return allBets[i].Time.Before(allBets[j].Time)
		}
		return allBets[i].Time.After(allBets[j].Time) // desc is default
	})

	// Apply page size limit
	if params.PageSize > 0 && len(allBets) > params.PageSize {
		allBets = allBets[:params.PageSize]
	}

	return allBets
}

func (s *BetService) GetBetByExternalID(ctx context.Context, externalID string) (*domain.Bet, error) {
	return s.betRepository.GetBetByExternalID(ctx, externalID)
}

// Queries the bets table for all the bets by user id => Private
func (s *BetService) GetBetsByUserID(ctx context.Context, params *domain.GetBetsByUserIDParams) (*domain.UserBets, error) {
	return s.betRepository.GetBetsByUserID(ctx, params)
}

func (s *BetService) ExportBets(ctx context.Context, params *domain.ExportBetsParams) ([]domain.Bet, error) {
	return s.betRepository.ExportBets(ctx, params)
}

func (s *BetService) GetLatestBetsByUserId(ctx context.Context, externalID string) ([]domain.Bet, error) {
	return s.betRepository.GetLatestBetsByUserId(ctx, externalID)
}

func (s *BetService) ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (domain.Bet, bool, error) {
	return s.betRepository.ShareUserBetByBetId(ctx, externalID, betID)
}

func (s *BetService) GetWageredAmountByTypeForXDays(ctx context.Context, userID string, days int) (float64, error) {
	return s.betRepository.GetWageredAmountByTypeForXDays(ctx, userID, days)
}
