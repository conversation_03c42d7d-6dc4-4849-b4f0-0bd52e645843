package service

import (
	"context"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestUserService_GetUserByID(t *testing.T) {
	ctx := context.Background()

	mockUserRepository := func(user *domain.User, err error) *domainmock.UserRepository {
		repo := domainmock.NewUserRepository(t)
		repo.EXPECT().GetUserByID(ctx, mock.Anything).Return(user, err).Once()
		return repo
	}

	tests := []struct {
		name              string
		repository        *domainmock.UserRepository
		userID            uuid.UUID
		expectedUser      *domain.User
		expectedErrorFunc require.ErrorAssertionFunc
	}{
		{
			name:              "Success",
			repository:        mockUserRepository(&domain.User{ExternalID: "user_id", UserName: "user_name"}, nil),
			userID:            userID1,
			expectedUser:      &domain.User{ExternalID: "user_id", UserName: "user_name"},
			expectedErrorFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			service := &UserService{userRepository: test.repository}
			user, err := service.GetUserByID(ctx, test.userID)
			require.Equal(t, test.expectedUser, user)
			test.expectedErrorFunc(t, err)
		})
	}
}

func TestUserService_GetUserByExternalUserID(t *testing.T) {
	ctx := context.Background()

	mockUserRepository := func(user *domain.User, err error) *domainmock.UserRepository {
		repo := domainmock.NewUserRepository(t)
		repo.EXPECT().GetUserByExternalID(ctx, mock.Anything).Return(user, err).Once()
		return repo
	}

	tests := []struct {
		name              string
		repository        *domainmock.UserRepository
		externalUserID    string
		expectedUser      *domain.User
		expectedErrorFunc require.ErrorAssertionFunc
	}{
		{
			name:              "Success",
			repository:        mockUserRepository(&domain.User{ExternalID: "user_id", UserName: "user_name"}, nil),
			externalUserID:    "id_1",
			expectedUser:      &domain.User{ExternalID: "user_id", UserName: "user_name"},
			expectedErrorFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			service := &UserService{userRepository: test.repository}
			user, err := service.GetUserByExternalID(context.Background(), test.externalUserID)
			require.Equal(t, test.expectedUser, user)
			test.expectedErrorFunc(t, err)
		})
	}
}

func TestUserService_GetUserByName(t *testing.T) {
	ctx := context.Background()

	mockUserRepository := func(userName string, user *domain.User, err error) *domainmock.UserRepository {
		repo := domainmock.NewUserRepository(t)
		repo.EXPECT().GetUserByUserName(ctx, userName).Return(user, err).Once()
		return repo
	}

	tests := []struct {
		name              string
		repository        *domainmock.UserRepository
		userName          string
		expectedUser      *domain.User
		expectedErrorFunc require.ErrorAssertionFunc
	}{
		{
			name:              "Success",
			repository:        mockUserRepository("name_1", &domain.User{ExternalID: "user_id", UserName: "user_name"}, nil),
			userName:          "name_1",
			expectedUser:      &domain.User{ExternalID: "user_id", UserName: "user_name"},
			expectedErrorFunc: require.NoError,
		},
		{
			name:              "Fail Not Found",
			repository:        mockUserRepository("name_10", nil, errTest),
			userName:          "name_10",
			expectedUser:      nil,
			expectedErrorFunc: require.Error,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			service := &UserService{userRepository: test.repository}
			user, err := service.GetUserByUserName(ctx, test.userName)
			require.Equal(t, test.expectedUser, user)
			test.expectedErrorFunc(t, err)
		})
	}
}

func TestMockUserService_UpdateUser(t *testing.T) {
	ctx := context.Background()

	mockUserRepository := func(err error) *domainmock.UserRepository {
		repo := domainmock.NewUserRepository(t)
		repo.EXPECT().UpdateUserByID(ctx, mock.Anything).Return(err).Once()
		return repo
	}

	tests := []struct {
		name              string
		repository        *domainmock.UserRepository
		user              *domain.User
		expectedErrorFunc require.ErrorAssertionFunc
	}{
		{
			name:              "Success",
			repository:        mockUserRepository(nil),
			user:              &domain.User{ID: userID1, UserName: "user_name"},
			expectedErrorFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			service := &UserService{userRepository: test.repository}
			err := service.UpdateUser(ctx, test.user)
			test.expectedErrorFunc(t, err)
		})
	}
}

// func TestMockUserService_SetUserRanksBasedOnWageringAmount(t *testing.T) {
// 	ctx := context.Background()

// 	mockUserRepository := func(rankedUsers *domain.RankedUsers, err error) *domainmock.UserRepository {
// 		repo := domainmock.NewUserRepository(t)
// 		repo.EXPECT().SetUserRanksBasedOnWageringAmount(ctx, mock.Anything).Return(rankedUsers, err).Once()
// 		return repo
// 	}

// 	tests := []struct {
// 		name              string
// 		repository        *domainmock.UserRepository
// 		params            *domain.GetUserParams
// 		expectedRanked    *domain.RankedUsers
// 		expectedErrorFunc require.ErrorAssertionFunc
// 	}{
// 		{
// 			name:              "Success",
// 			repository:        mockUserRepository(&domain.RankedUsers{User: []domain.RankedUser{{User: domain.User{ID: userID1, UserName: "user_name"}}}}, nil),
// 			params:            &domain.GetUserParams{UserExternalID: userID1.String()},
// 			expectedRanked:    &domain.RankedUsers{User: []domain.RankedUser{{User: domain.User{ID: userID1, UserName: "user_name"}}}},
// 			expectedErrorFunc: require.NoError,
// 		},
// 	}

// 	for _, test := range tests {
// 		t.Run(test.name, func(t *testing.T) {
// 			service := &UserService{userRepository: test.repository}
// 			ranked, err := service.SetUserRanksBasedOnWageringAmount(ctx, test.params)
// 			require.Equal(t, test.expectedRanked, ranked)
// 			test.expectedErrorFunc(t, err)
// 		})
// 	}
// }
