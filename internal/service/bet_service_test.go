package service

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

var default_order_by = "time"

func TestBetService_GetBetByID(t *testing.T) {
	ctx := context.Background()

	mockBetRepository := func(id uuid.UUID, bet *domain.Bet, err error) *domainmock.BetRepository {
		betRepo := domainmock.NewBetRepository(t)
		betRepo.EXPECT().GetBetByID(ctx, id).Return(bet, err).Once()
		return betRepo
	}

	tests := []struct {
		name            string
		id              uuid.UUID
		betRepository   *domainmock.BetRepository
		expectedBet     *domain.Bet
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Error",
			id:              betID1,
			betRepository:   mockBetRepository(betID1, nil, errTest),
			expectedBet:     nil,
			expectedErrFunc: require.Error,
		},
		{
			name:            "Success",
			id:              betID1,
			betRepository:   mockBetRepository(betID1, &domain.Bet{ExternalID: "1"}, nil),
			expectedBet:     &domain.Bet{ExternalID: "1"},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			betService := &BetService{betRepository: test.betRepository}
			bet, err := betService.GetBetByID(ctx, test.id)
			assert.Equal(t, test.expectedBet, bet)
			test.expectedErrFunc(t, err)
		})
	}
}

func TestBetService_GetBetsByUserID(t *testing.T) {
	ctx := context.Background()

	mockBetRepository := func(ctx context.Context, bets *domain.UserBets, err error) *domainmock.BetRepository {
		betRepo := domainmock.NewBetRepository(t)
		betRepo.EXPECT().GetBetsByUserID(ctx, mock.Anything).Return(bets, err).Once()
		return betRepo
	}

	tests := []struct {
		name            string
		betRepository   *domainmock.BetRepository
		params          *domain.GetBetsByUserIDParams
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Error",
			params:          &domain.GetBetsByUserIDParams{},
			betRepository:   mockBetRepository(ctx, nil, errTest),
			expectedBets:    nil,
			expectedErrFunc: require.Error,
		},
		{
			name:            "Success",
			params:          &domain.GetBetsByUserIDParams{},
			betRepository:   mockBetRepository(ctx, &domain.UserBets{}, nil),
			expectedBets:    &domain.Bets{},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			betService := &BetService{betRepository: test.betRepository}
			bet, err := betService.GetBetsByUserID(ctx, test.params)
			assert.Equal(t, test.expectedBets, bet)
			test.expectedErrFunc(t, err)
		})
	}
}

func TestBetService_GetBets(t *testing.T) {
	ctx := context.Background()

	mockBetRepository := func(ctx context.Context, bets *domain.Bets, err error) *domainmock.BetRepository {
		betRepo := domainmock.NewBetRepository(t)
		betRepo.EXPECT().GetBets(ctx, mock.Anything).Return(bets, err).Once()
		return betRepo
	}

	tests := []struct {
		name            string
		params          *domain.GetBetsParams
		betRepository   *domainmock.BetRepository
		expectedBets    *domain.Bets
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name:            "Error",
			params:          &domain.GetBetsParams{},
			betRepository:   mockBetRepository(ctx, nil, errTest),
			expectedBets:    nil,
			expectedErrFunc: require.Error,
		},
		{
			name:            "Success",
			params:          &domain.GetBetsParams{},
			betRepository:   mockBetRepository(ctx, &domain.Bets{}, nil),
			expectedBets:    &domain.Bets{},
			expectedErrFunc: require.NoError,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			betService := &BetService{betRepository: test.betRepository}
			bet, err := betService.GetBets(ctx, test.params)
			assert.Equal(t, test.expectedBets, bet)
			test.expectedErrFunc(t, err)
		})
	}
}

func assertBetsAreEqual(t *testing.T, expected, actual *domain.Bet) {
	opts := []cmp.Option{
		cmpopts.IgnoreFields(domain.Bet{}, "Time"),
		cmpopts.IgnoreFields(domain.User{}, "JoinDate"),
	}

	assert.True(t,
		cmp.Equal(expected, actual, opts...),
		cmp.Diff(expected, actual, opts...),
	)
}
