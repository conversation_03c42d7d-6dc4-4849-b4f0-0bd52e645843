package service

import (
	_ "embed"
	"encoding/json"
	"fmt"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

var (
	VIPTiers domain.VIPTiers
	//go:embed vip_tiers_updated.json
	vipTiersData []byte
)

func init() {
	if err := json.Unmarshal(vipTiersData, &VIPTiers); err != nil {
		panic(fmt.Sprintf("unmarshal VIP tiers: %v", err))
	}
}

type VIPTierService struct{}

func NewVIPTierService() VIPTierService {
	return VIPTierService{}
}

func (s VIPTierService) GetVIPTierByName(name string) (*domain.VIPTiersThreshold, bool) {
	for _, tier := range VIPTiers {
		if tier.Tier == name {
			return &tier, true
		}
	}
	return nil, false
}
