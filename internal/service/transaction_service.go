package service

import (
	"context"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.TransactionService = (*TransactionService)(nil)

type TransactionService struct {
	transRepository domain.TransactionRepository
	railsClient     domain.RailsClient
}

func NewTransactionService(transRepository domain.TransactionRepository, railsClient domain.RailsClient) *TransactionService {
	return &TransactionService{
		transRepository: transRepository,
		railsClient:     railsClient,
	}
}

func (s *TransactionService) GetTransactions(
	ctx context.Context,
	params *domain.GetTransactionParams,
) (*domain.Transactions, error) {
	transactions, err := s.transRepository.GetTransactions(ctx, params)
	if err != nil {
		return nil, err
	}

	// Add transaction explorer links for crypto transactions
	if err := s.addTransactionExplorerLinks(ctx, transactions.Items, params.UserExternalID); err != nil {
		slog.Error("Failed to add transaction explorer links", "error", err)
		// Continue processing even if explorer links fail
	}

	return transactions, nil
}

func (s *TransactionService) ExportTransactions(
	ctx context.Context,
	params *domain.ExportTransactionsParams,
) ([]domain.Transaction, error) {
	return s.transRepository.ExportTransactions(ctx, params)
}

func (s *TransactionService) GetTransactionByType(
	ctx context.Context,
	transType string,
	category string,
	currency string,
	userId string,
	offset int,
	limit int,
) ([]domain.Transaction, error) {
	return s.transRepository.GetTransactionByType(ctx, transType, category, currency, userId, offset, limit)
}

func (s *TransactionService) GetTransactionExplorerLinks(ctx context.Context, userID string) (map[string]string, error) {
	return s.railsClient.GetTransactionExplorerLink(ctx, userID)
}

// addTransactionExplorerLinks efficiently adds explorer links by batching requests per user
func (s *TransactionService) addTransactionExplorerLinks(ctx context.Context, transactions []domain.Transaction, userID string) error {
	// Only process crypto transactions with external IDs
	cryptoTransactions := make([]*domain.Transaction, 0)
	for i := range transactions {
		transaction := &transactions[i]
		if isCryptoCurrency(transaction.Currency) && transaction.ExternalID != "" {
			cryptoTransactions = append(cryptoTransactions, transaction)
		}
	}

	if len(cryptoTransactions) == 0 {
		return nil
	}

	explorerLinks, err := s.railsClient.GetTransactionExplorerLink(ctx, userID)
	if err != nil {
		slog.Error("Failed to get explorer links for user", "user_id", userID, "error", err)
		return err
	}

	for _, transaction := range cryptoTransactions {
		if explorerLink, exists := explorerLinks[transaction.ExternalID]; exists {
			transaction.TransactionExplorerLink = &explorerLink
		} else {
			slog.Debug("No explorer link found for transaction hash",
				"transaction_id", transaction.ID,
				"elantil_tx_id", transaction.ExternalID)
		}
	}

	return nil
}

// isCryptoCurrency checks if a currency is NOT a known fiat currency
func isCryptoCurrency(currency string) bool {
	// Common fiat currencies - if it's not in this list, assume it's crypto
	fiatCurrencies := map[string]struct{}{
		"USD": {}, "EUR": {}, "GBP": {}, "JPY": {}, "CAD": {},
		"AUD": {}, "CHF": {}, "CNY": {}, "INR": {}, "BRL": {},
		"KRW": {}, "MXN": {}, "SGD": {}, "HKD": {}, "NZD": {},
		"SEK": {}, "NOK": {}, "DKK": {}, "PLN": {}, "CZK": {},
		"TRY": {}, "RUB": {}, "ZAR": {}, "THB": {}, "MYR": {},
	}

	_, isFiat := fiatCurrencies[currency]
	return !isFiat
}
