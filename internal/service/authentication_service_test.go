package service

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

var (
	testAccessToken = "test_access_token"
)

func TestKeyCloakClient_ValidateAccessToken(t *testing.T) {
	tests := []struct {
		name           string
		accessToken    string
		mockReturn     domain.ValidateTokenResponse
		mockError      error
		expectResponse domain.ValidateTokenResponse
		expectError    bool
	}{
		{
			name:        "ValidToken",
			accessToken: "valid_token",
			mockReturn: domain.ValidateTokenResponse{
				UserID: "user123",
			},
			mockError: nil,
			expectResponse: domain.ValidateTokenResponse{
				UserID: "user123",
			},
			expectError: false,
		},
		{
			name:        "InvalidToken",
			accessToken: "invalid_token",
			mockReturn:  domain.ValidateTokenResponse{},
			mockError:   errors.New("invalid token"),
			expectResponse: domain.ValidateTokenResponse{},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := domainmock.NewAuthenticationService(t)

			// Fix the mock expectation to return the correct type
			mockService.On("ValidateAccessToken", mock.Anything, tt.accessToken).Return(tt.mockReturn, tt.mockError)

			// Call with context and accessToken
			response, err := mockService.ValidateAccessToken(context.Background(), tt.accessToken)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectResponse, response)

			mockService.AssertExpectations(t)
		})
	}
}

func TestKeyCloakClient_ValidateToken(t *testing.T) {
	tests := []struct {
		name               string
		serverResponseCode int
		expectedErr        string
	}{
		{
			name:               "ValidToken",
			serverResponseCode: http.StatusOK,
			expectedErr:        "",
		},
		{
			name:               "InvalidToken",
			serverResponseCode: http.StatusUnauthorized,
			expectedErr:        "invalid token",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, testAccessToken, r.Header.Get("Authorization"))
				assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
				assert.Equal(t, "test-tenant", r.Header.Get("X-Tenant-Id"))
				w.WriteHeader(test.serverResponseCode)

				// Add the following lines here to return a valid JSON response
				if test.serverResponseCode == http.StatusOK {
					w.Write([]byte(`{"sub": "some-user-id"}`))
				} else {
					w.Write([]byte(`{"active": false}`))
				}
			}))
			defer server.Close()

			config := &utils.RESTServiceConfig{
				Keycloak: utils.KeycloakConfig{
					URL:      server.URL,
					Realm:    "test-realm",
					TenantID: "test-tenant",
				},
			}
			client := NewKeyCloakClient(config, &http.Client{})

			_, err := client.validateToken(context.Background(), testAccessToken)

			if test.expectedErr == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, test.expectedErr)
			}
		})
	}
}