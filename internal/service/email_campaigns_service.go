package service

import (
	"context"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
)

type EmailCampaignsService struct {
	emailCampaignsRepo domain.EmailCampaignsRepository
	optimoveClient     rest_client.OptimoveClient
}

func NewEmailCampaignsService(emailcampaignsRepo domain.EmailCampaignsRepository, optimoveClient rest_client.OptimoveClient) *EmailCampaignsService {
	return &EmailCampaignsService{emailCampaignsRepo: emailcampaignsRepo, optimoveClient: optimoveClient}
}

func (s *EmailCampaignsService) CreateEmailCampaigns(ctx context.Context, contentTemplate *domain.OptimoveWebhookPayload) error {
	customerDetails, err := s.optimoveClient.GetCustomerDetailsByChannel(ctx, contentTemplate.CampaignID, contentTemplate.ChannelID, "email")
	if err != nil {
		return err
	}
	return s.emailCampaignsRepo.CreateEmailCampaigns(ctx, &customerDetails)
}

func (s *EmailCampaignsService) GetEmailCampaigns(ctx context.Context, email string) (*domain.EmailCampaignsResponse, error) {
	return s.emailCampaignsRepo.GetEmailCampaigns(ctx, email)
}