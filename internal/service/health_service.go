package service

import (
	"fmt"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/slack-go/slack"
)

// Assert interface implementation.
var _ domain.HealthService = (*HealthService)(nil)

type HealthService struct {
	SlackClient *SlackClient
	config      *utils.RESTServiceConfig
}
type SlackConfig struct {
	Token     string
	ChannelID string
}

type SlackClient struct {
	Client *slack.Client
	Config SlackConfig
}

func NewSlackClient(config SlackConfig) (*SlackClient, error) {
	if config.Token == "" {
		return nil, fmt.Errorf("slack token is required")
	}
	if config.ChannelID == "" {
		return nil, fmt.Errorf("channel ID is required")
	}

	return &SlackClient{
		Client: slack.New(config.Token),
		Config: config,
	}, nil
}

func NewHealthService(config *utils.RESTServiceConfig) (*HealthService, error) {

	slackConfig := SlackConfig{
		Token:     config.SlackConfig.Token,
		ChannelID: config.SlackConfig.ChannelID}
	if slackConfig.Token == "" || slackConfig.ChannelID == "" {
		return nil, fmt.Errorf("slack token or channel id is required")
	}

	slackClient, err := NewSlackClient(slackConfig)
	if err != nil {
		return &HealthService{}, err
	}
	return &HealthService{
		SlackClient: slackClient,
	}, nil
}

func (h *HealthService) SendMessageCMSAlert(BonusType string, ErrorMsg string) error {

	message := fmt.Sprintf(
		"\n🚨 *Alert* 🚨\n\n"+
			"*Bonus Type:* %v\n"+
			"*Error Message:* %v\n\n",
		BonusType, ErrorMsg)

	_, _, err := h.SlackClient.Client.PostMessage(
		h.SlackClient.Config.ChannelID,
		slack.MsgOptionText(message, false),
	)
	if err != nil {
		return fmt.Errorf("error sending JSON to Slack: %v", err)
	}
	return nil
}
