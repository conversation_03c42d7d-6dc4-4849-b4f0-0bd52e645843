package service

import (
	"context"
	"errors"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.BoostService = (*BoostService)(nil)

// Service layer for boost
type BoostService struct {
	boostRepository domain.BoostRepository
	userService     domain.UserService
}

func NewBoostService(boostRepository domain.BoostRepository, userService domain.UserService) *BoostService {
	return &BoostService{
		boostRepository: boostRepository,
		userService:     userService,
	}
}

func (b *BoostService) CreateBoost(
	ctx context.Context,
	boost *domain.Boost,
	usersExternalID []string,
) (*domain.BoostCreationOut, error) {
	var boostErrors []domain.CreateBoostError
	var boosts []domain.Boost
	for _, extID := range usersExternalID {
		user, err := b.userService.GetUserByExternalID(ctx, extID)
		if err != nil {
			if errors.Is(err, domain.ErrResourceNotFound) {
				boostErrors = append(boostErrors, domain.CreateBoostError{
					UserID: extID,
					Error:  domain.ErrResourceNotFound,
				})
				continue
			}
			return nil, err
		}

		overlaped, err := b.boostRepository.GetOverlapingBoostByUserID(
			ctx,
			extID,
			boost.BonusStartsAt,
			boost.BonusFinishesAt,
		)
		if err != nil && !errors.Is(err, domain.ErrResourceNotFound) {
			return nil, err
		}

		if overlaped != nil {
			boostErrors = append(boostErrors, domain.CreateBoostError{
				UserID: extID,
				Error:  domain.ErrOverlapingDates,
			})
			continue
		}

		boosts = append(boosts, domain.Boost{
			BonusStartsAt:      boost.BonusStartsAt,
			BonusFinishesAt:    boost.BonusFinishesAt,
			BoostDurationHours: boost.BoostDurationHours,
			Multiplier:         boost.Multiplier,
			User:               *user,
		})
	}

	if err := b.boostRepository.CreateBoosts(ctx, boosts); err != nil {
		return nil, err
	}

	return &domain.BoostCreationOut{
		CreatedCount: len(boosts),
		Errors:       boostErrors,
	}, nil
}

// Update Boost
func (b *BoostService) UpdateBoosts(ctx context.Context, u domain.Boost) error {
	return b.boostRepository.UpdateBoosts(ctx, u)
}

// Get Valid Boost By User ID
func (b *BoostService) GetAvailableBoostByUserID(
	ctx context.Context,
	userID string,
	targetTime time.Time,
	includeStarted bool,
) (*domain.Boost, error) {
	boost, err := b.boostRepository.GetAvailableBoostByUserID(ctx, userID, targetTime, includeStarted)
	if err != nil {
		return nil, err
	}

	if boost.BoostStartedAt != nil && boost.BoostStartedAt.Add(
		time.Duration(boost.BoostDurationHours*int(time.Hour)),
	).Before(time.Now().UTC()) {
		return nil, domain.ErrResourceGone
	}

	rankedUser, err := b.userService.GetRankedUserByExternalID(ctx, boost.User.ExternalID)
	if err != nil {
		return nil, err
	}

	boost.RankedUser = rankedUser
	return boost, nil
}
