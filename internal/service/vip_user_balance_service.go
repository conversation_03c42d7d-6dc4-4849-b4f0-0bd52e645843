package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.VIPUserBalanceService = (*VIPUserBalanceService)(nil)

type VIPUserBalanceService struct {
	userRepository           domain.UserRepository
	vipTiersService          domain.VIPTiersService
	vipUserBalanceRepository domain.VIPUserBalanceRepository
}

func NewVIPUserBalanceService(
	vipUserBalanceRepository domain.VIPUserBalanceRepository,
	vipTiersService domain.VIPTiersService,
	userRepository domain.UserRepository,
) *VIPUserBalanceService {
	return &VIPUserBalanceService{
		vipUserBalanceRepository: vipUserBalanceRepository,
		vipTiersService:          vipTiersService,
		userRepository:           userRepository,
	}
}

func (s *VIPUserBalanceService) CreateVIPUserBalance(
	ctx context.Context,
	balance *domain.VIPUserBalance,
) (*domain.VIPUserBalance, error) {
	return s.vipUserBalanceRepository.CreateVIPUserBalance(ctx, balance)
}

func (s *VIPUserBalanceService) BumpUser(ctx context.Context, bumpRequest domain.BumpUserVIPStatus) error {
	rankedUser, err := s.userRepository.GetRankedUserByExternalID(ctx, bumpRequest.UserExternalID)
	if err != nil {
		return err
	}

	if rankedUser.VIPStatus == bumpRequest.VIPStatusToBump {
		return domain.ErrInvalidVIPUserToBumpTo
	}

	vipTier, ok := s.vipTiersService.GetVIPTierByName(bumpRequest.VIPStatusToBump)
	if !ok {
		return domain.ErrInvalidVipUserToBump
	}

	if rankedUser.TotalCoins >= float64(vipTier.Threshold) {
		return domain.ErrInvalidVIPUserToBumpTo
	}

	coinsToAdd := float64(vipTier.Threshold)
	if !bumpRequest.AddBase {
		coinsToAdd = coinsToAdd - rankedUser.TotalCoins
	}

	vipUserBalance := domain.VIPUserBalance{
		User:   domain.User{ExternalID: bumpRequest.UserExternalID},
		Coins:  coinsToAdd,
		Source: bumpRequest.Source,
	}

	if _, err := s.CreateVIPUserBalance(ctx, &vipUserBalance); err != nil {
		return err
	}

	return nil
}
