package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type BonusDropService struct {
	bonusDropRepository domain.BonusDropRepository
	betRepository        domain.BetRepository
	wageringClient      domain.ElantilWageringClient
	directusClient      domain.DirectusCMSClient
}

func NewBonusDropService(bonusDropRepository domain.BonusDropRepository, betRepository domain.BetRepository, wageringClient domain.ElantilWageringClient, directusClient domain.DirectusCMSClient) *BonusDropService {
	return &BonusDropService{
		bonusDropRepository: bonusDropRepository,
		betRepository:       betRepository,
		wageringClient:      wageringClient,
		directusClient:      directusClient,
	}
}

func (s *BonusDropService) UpsertBonusDrop(ctx context.Context, bonusDrop *domain.BonusDrop) error {
	return s.bonusDropRepository.UpsertBonusDrop(ctx, bonusDrop)
}

func (s *BonusDropService) RedeemBonusDrop(ctx context.Context, userId, username, bonusCode, token string) (domain.RedeemBonusResponse, error) {
	return s.bonusDropRepository.RedeemBonusDrop(ctx, userId, username, bonusCode, token)
}