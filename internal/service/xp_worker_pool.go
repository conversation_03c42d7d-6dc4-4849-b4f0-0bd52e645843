package service

import (
	"context"
	"log/slog"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)
type XPUpdateJob struct {
	UserExternalID string
	BetAmount      *float64
	IsSportsBet    bool
}
type BatchedXPUpdate struct {
	UserExternalID string
	TotalXP        float64
	LastUpdate     time.Time
}
type XPWorkerPool struct {
	jobQueue         chan XPUpdateJob
	workerCount      int
	userRepo         domain.UserRepository
	wg               sync.WaitGroup
	stopChan         chan struct{}
	bufferMutex      sync.Mutex
	overflowBuffer   []XPUpdateJob
	overflowSignal   chan struct{}
	batchMutex       sync.Mutex
	batchedUpdates   map[string]*BatchedXPUpdate
	batchTicker      *time.Ticker
	batchInterval    time.Duration
}

func NewXPWorkerPool(workerCount int, queueSize int, userRepo domain.UserRepository) *XPWorkerPool {
	pool := &XPWorkerPool{
		jobQueue:        make(chan XPUpdate<PERSON><PERSON>, queueSize),
		workerCount:     workerCount,
		userRepo:        userRepo,
		stop<PERSON>han:        make(chan struct{}),
		overflowBuffer:  make([]XPUpdateJob, 0, 1000),
		overflowSignal:  make(chan struct{}, 1),
		batchedUpdates:  make(map[string]*BatchedXPUpdate),
		batchInterval:   30 * time.Second, // Flush batches every 30 seconds
	}
	
	return pool
}

func (p *XPWorkerPool) Start() {
	for i := 0; i < p.workerCount; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}
	p.wg.Add(1)
	go p.processOverflowBuffer()

	p.batchTicker = time.NewTicker(p.batchInterval)
	p.wg.Add(1)
	go p.batchProcessor()
	
	slog.Info("XP worker pool started", 
		"workers", p.workerCount, 
		"queue_size", cap(p.jobQueue),
		"batch_interval", p.batchInterval)
}

func (p *XPWorkerPool) Stop() {
	close(p.stopChan)	
	if p.batchTicker != nil {
		p.batchTicker.Stop()
	}
	p.flushAllBatches()
	p.drainOverflowBuffer()
	
	p.wg.Wait()
	close(p.jobQueue)
	slog.Info("XP worker pool stopped")
}

func (p *XPWorkerPool) EnqueueXPUpdate(userExternalID string, betAmount *float64, isSportsBet bool) {
	job := XPUpdateJob{
		UserExternalID: userExternalID,
		BetAmount:      betAmount,
		IsSportsBet:    isSportsBet,
	}
	select {
	case p.jobQueue <- job:
		return
	default:
		// Channel is full, add to overflow buffer
		p.bufferMutex.Lock()
		p.overflowBuffer = append(p.overflowBuffer, job)
		bufferSize := len(p.overflowBuffer)
		p.bufferMutex.Unlock()
		
		slog.Warn("XP update queue full, added to overflow buffer", 
			"user_id", userExternalID,
			"buffer_size", bufferSize)
			
		// Signal overflow processor (non-blocking)
		select {
		case p.overflowSignal <- struct{}{}:
		default:
			// Signal already sent, that's fine
		}
	}
}

func (p *XPWorkerPool) processOverflowBuffer() {
	defer p.wg.Done()
	
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-p.stopChan:
			return
			
		case <-p.overflowSignal:
			p.moveFromBufferToQueue(10)
			
		case <-ticker.C:
			p.moveFromBufferToQueue(10)
		}
	}
}

func (p *XPWorkerPool) moveFromBufferToQueue(batchSize int) {
	p.bufferMutex.Lock()
	defer p.bufferMutex.Unlock()
	
	if len(p.overflowBuffer) == 0 {
		return
	}
	
	slog.Info("Processing overflow buffer", 
		"buffer_size", len(p.overflowBuffer),
		"batch_size", batchSize)
	moveCount := 0
	for i := 0; i < batchSize && len(p.overflowBuffer) > 0; i++ {
		select {
		case p.jobQueue <- p.overflowBuffer[0]:
			p.overflowBuffer = p.overflowBuffer[1:]
			moveCount++
		default:
			// Queue still full, stop trying for now
			break
		}
	}
	
	if moveCount > 0 {
		slog.Info("Moved items from overflow buffer to queue", 
			"moved_count", moveCount,
			"remaining", len(p.overflowBuffer))
	}
	if len(p.overflowBuffer) > 0 {
		select {
		case p.overflowSignal <- struct{}{}:
		default:
			// Signal already sent, that's fine
		}
	}
}

func (p *XPWorkerPool) drainOverflowBuffer() {
	p.bufferMutex.Lock()
	bufferItems := p.overflowBuffer
	p.overflowBuffer = nil
	p.bufferMutex.Unlock()
	
	if len(bufferItems) == 0 {
		return
	}
	
	slog.Info("Draining overflow buffer during shutdown", 
		"items_count", len(bufferItems))
	userBatches := make(map[string]float64)
	
	for _, job := range bufferItems {
		xpToAdd := *job.BetAmount
		if job.IsSportsBet {
			xpToAdd = xpToAdd * 3
		}
		
		userBatches[job.UserExternalID] += xpToAdd
	}
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	for userID, xpAmount := range userBatches {
		_, err := p.userRepo.UpdateUserXP(ctx, userID, xpAmount)
		if err != nil {
			slog.Error("Failed to update user XP during shutdown drain",
				"user_id", userID,
				"xp_to_add", xpAmount,
				"error", err)
		}
	}
	
	slog.Info("Completed draining overflow buffer", 
		"processed_users", len(userBatches),
		"total_updates", len(bufferItems))
}

func (p *XPWorkerPool) batchProcessor() {
	defer p.wg.Done()
	
	for {
		select {
		case <-p.stopChan:
			return
			
		case <-p.batchTicker.C:
			p.flushBatches(false) // false = don't flush empty batches
		}
	}
}

func (p *XPWorkerPool) flushBatches(flushAll bool) {
	p.batchMutex.Lock()
	if len(p.batchedUpdates) == 0 {
		p.batchMutex.Unlock()
		return
	}
	
	batchesToProcess := make([]*BatchedXPUpdate, 0, len(p.batchedUpdates))
	for userID, batch := range p.batchedUpdates {
		batchesToProcess = append(batchesToProcess, batch)
		delete(p.batchedUpdates, userID)
	}
	
	p.batchMutex.Unlock()
	if len(batchesToProcess) == 0 {
		return
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	slog.Info("Flushing XP update batches", "batch_count", len(batchesToProcess))
	
	for _, batch := range batchesToProcess {
		if batch.TotalXP <= 0 {
			continue
		}
		
		_, err := p.userRepo.UpdateUserXP(ctx, batch.UserExternalID, batch.TotalXP)
		if err != nil {
			slog.Error("Failed to update user XP in batch",
				"user_id", batch.UserExternalID,
				"xp_to_add", batch.TotalXP,
				"error", err)
		}
	}
}

func (p *XPWorkerPool) flushAllBatches() {
	slog.Info("Flushing all batched XP updates during shutdown")
	p.flushBatches(true)
}

func (p *XPWorkerPool) addToBatch(job XPUpdateJob) {
	xpToAdd := *job.BetAmount
	if job.IsSportsBet {
		xpToAdd = xpToAdd * 3
	}
	
	p.batchMutex.Lock()
	defer p.batchMutex.Unlock()

	batch, exists := p.batchedUpdates[job.UserExternalID]
	if !exists {
		batch = &BatchedXPUpdate{
			UserExternalID: job.UserExternalID,
			TotalXP:        0,
			LastUpdate:     time.Now(),
		}
		p.batchedUpdates[job.UserExternalID] = batch
	}
	batch.TotalXP += xpToAdd
	batch.LastUpdate = time.Now()
}

func (p *XPWorkerPool) worker(id int) {
	defer p.wg.Done()	
	for {
		select {
		case <-p.stopChan:
			return
			
		case job, ok := <-p.jobQueue:
			if !ok {
				return
			}
			p.addToBatch(job)
		}
	}
}