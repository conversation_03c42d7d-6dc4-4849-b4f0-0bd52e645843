package service

import (
	"context"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.GameService = (*GameService)(nil)

type GameService struct {
	gameRepository domain.GameRepository
	gamesProvider  domain.GameProvider
}

func NewGameService(
	gameRepository domain.GameRepository,
	gamesProvider domain.GameProvider,
) (*GameService, error) {
	if err := gameRepository.UpsertAllGamesIfNotInitialized(context.Background(), gamesProvider.GetAllGames); err != nil {
		return nil, err
	}

	return &GameService{
		gameRepository: gameRepository,
		gamesProvider:  gamesProvider,
	}, nil
}

func (s *GameService) UpsertAllGames(ctx context.Context) error {
	games, err := s.gamesProvider.GetAllGames(ctx)
	if err != nil {
		return err
	}

	return s.gameRepository.UpsertGames(ctx, games)
}
