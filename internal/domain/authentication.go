package domain

import (
	"context"
)

type ValidateTokenResponse struct {
	UserID   string `json:"sub"`
	UserName string `json:"preferred_username"`
	Email    string `json:"email"`
}

// Password Reset Domain Types
type PasswordResetInitiateRequest struct {
	Email     string `json:"email" validate:"required,email"`
	BaseUrl   string `json:"baseUrl,omitempty"`             // Optional: explicit base URL for reset link
	IPAddress string `json:"ipAddress" validate:"required"` // Required: client IP address for rate limiting
}

type PasswordResetInitiateResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type PasswordResetValidateRequest struct {
	Token string `json:"token" validate:"required"`
}

type PasswordResetValidateResponse struct {
	Success bool   `json:"success"`
	Valid   bool   `json:"valid"`
	Email   string `json:"email,omitempty"` // Email extracted from token for frontend prefill
	Message string `json:"message"`
}

type PasswordResetCompleteRequest struct {
	Token    string `json:"token" validate:"required"`
	Password string `json:"password" validate:"required"` // Base64 encoded password to handle special characters
}

type PasswordResetCompleteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// Password Reset Service Interface
type PasswordResetService interface {
	InitiateReset(ctx context.Context, email, baseUrl, referrer, clientIP string) error
	ValidateToken(ctx context.Context, encryptedToken string) (valid bool, email string, err error)
	CompleteReset(ctx context.Context, encryptedToken, newPassword string) error
}

type AuthenticationService interface {
	ValidateAccessToken(ctx context.Context, accessToken string) (ValidateTokenResponse, error)
}
