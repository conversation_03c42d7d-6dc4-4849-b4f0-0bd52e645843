package domain

// PagedItems represents the paginated result items.
type PagedItems[T any] struct {
	Items  []T
	Paging Paging
}

type TransactionPagedItems[T any] struct {
	Items   []T
	Paging  Paging
	Details Details
}

type PagedItemsWithoutTotalCount[T any] struct {
	Items  []T
	Paging PagingWithoutTotalCount
}

type UserPagedItems[T any] struct {
	Items   []T
	Paging  Paging
	Details Details
}

// Paging represents the paginated results information.
type Paging struct {
	TotalCount  int64
	CurrentPage int
	PageSize    int
}

type PagingWithoutTotalCount struct {
	CurrentPage int
	PageSize    int
}

type Details struct {
	TotalCredit float64
	TotalDebit  float64
}
