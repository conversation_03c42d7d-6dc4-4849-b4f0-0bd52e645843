package domain

import (
	"context"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

type BetSimulatorService interface {
	StartSimulation(ctx context.Context, config SimulationConfig) error
	StopSimulation() error
	GetSimulationStatus() SimulationStatus
	GenerateSimulatedBet(ctx context.Context, timestamp time.Time, forceHighPayout bool) (*Bet, error)
}

type SimulationConfig struct {
	Enabled               bool
	NewBetIntervalRange   [2]int
	HighPayoutThreshold   float64
	HighPayoutProbability float64
	UsernameList          []string
	VipTiers              []utils.VipTierConfig
	Games                 []SimulatedGameData
	ActiveUserQueueSize   int
}

type SimulationStatus struct {
	IsActive                bool
	TotalSimulatedBets      int
	HighPayoutBetsGenerated int
	LastBetGenerated        time.Time
	CurrentInterval         time.Duration
}

type SimulatedGameData struct {
	ID          uuid.UUID
	CMSGameID   int64
	ExternalID  string
	Name        string
	Slug        string
	ThumbnailID string
	// Game-specific multiplier configuration
	LossRate       float64   // Probability of 0x multiplier (default: 0.6)
	WinMultipliers []float64 // Possible win multipliers [1.2, 1.5, 2.0, 3.0, 5.0]
	WinWeights     []float64 // Probability weights for each multiplier [0.4, 0.3, 0.2, 0.08, 0.02]
}

type SimulatedUserData struct {
	ID           uuid.UUID
	UserName     string
	VIPStatus    string
	JoinDate     time.Time
	GhostMode    bool
	HideAllStats bool
	TotalBets    int
	TotalWins    int
	TotalLoses   int
	Wagered      float64
	WinRate      float64
	XP           float64
	TotalCoins   float64
}

type SessionState struct {
	CurrentUser      *SimulatedUserData
	CurrentGame      *SimulatedGameData
	SessionBetsLeft  int
	BaseBetAmount    float64
	IsAutoplay       bool
	SessionStartTime time.Time
}

type BetSimulationData struct {
	Games []SimulatedGameData
	Users []SimulatedUserData
}
