package domain

import (
	"errors"
	"fmt"
)

var (
	ErrResourceNotFound                = errors.New("resource not found")
	ErrOverlapingDates                 = errors.New("boost overlaped by an existing one")
	ErrInGhostMode                     = errors.New("resource is in ghost mode")
	ErrInvalidParameter                = errors.New("invalid parameter")
	ErrInvalidAssetType                = errors.New("invalid asset type")
	ErrInvalidAssetSubType             = errors.New("invalid asset sub type")
	ErrInvalidAssetID                  = errors.New("invalid asset ID")
	ErrInvalidVIPUserToBumpTo          = errors.New("invalid vip user to bump to")
	ErrInvalidVipUserToBump            = errors.New("invalid vip user to bump")
	ErrResourceGone                    = errors.New("resource gone")
	ErrUserAlreadyHasReferralCampaign  = errors.New("user already has a referral campaign")
	ErrUserAlreadyInReferredList       = errors.New("user already is a part of another referral campaign")
	ErrUserReferringItself             = errors.New("user cannot refer itself")
	ErrRecordNotFound                  = errors.New("record not found")
	ErrDeleleCampaignWithReferredUsers = errors.New("cannot delete campaign with referrals")
	ErrNoBonusToClaim                  = errors.New("no bonus to claim")
	ErrDuplicateRecord                 = errors.New("duplicate record")
	ErrCodeLimitReached                = errors.New("code limit reached")
	ErrCodeUsed                        = errors.New("code already used")
	ErrBetNotAssociatedWithUser        = errors.New("bet not associated with user")
	ErrBonusTemplateAssigned           = errors.New("bonus template already assigned to user")
	ErrCreditTransactionNotFound       = errors.New("first credit transaction not found")
	ErrBonusTemplateNotFound           = errors.New("bonus template not found")
	ErrBonusDropLimitReached           = errors.New("all codes have been claimed stay tuned for the next release")
	ErrBonusDropExpiredOrNotActive     = errors.New("code expired or inactive")
	ErrBonusDropAlreadyClaimed         = errors.New("you have already claimed this bonus")
	ErrWageringRequirementsNotMet      = errors.New("you do not meet the wagering requirement to redeem this code")
	ErrBonusDropNotFound               = errors.New("invalid bonus code")
	ErrUserAlreadyHasLocalWallet       = errors.New("user already has a local wallet")
	ErrUserCountryNotFound             = errors.New("user country not found")
	ErrUserNotEligibleForLocalWallet   = errors.New("user not eligible for local wallet")
	ErrInsufficientWalletBalance       = errors.New("insufficient wallet balance")
	ErrWalletNotFound                  = errors.New("wallet not found")
	ErrUsernameAlreadyExists           = errors.New("username already exists")
	ErrPasswordResetTokenNotFound      = errors.New("password reset token not found")
	ErrPasswordResetTokenExpired       = errors.New("password reset token expired")
	ErrPasswordResetTokenInvalid       = errors.New("password reset token invalid")
	ErrPasswordResetRateLimited        = errors.New("too many password reset attempts, please try again in 1 hour")
	ErrUserNotFoundInKeycloak          = errors.New("user not found in Keycloak")
)

// PasswordValidationError represents a password validation error with details from Keycloak
type PasswordValidationError struct {
	Message string
}

func (e *PasswordValidationError) Error() string {
	return e.Message
}

// NewPasswordValidationError creates a new password validation error
func NewPasswordValidationError(message string) *PasswordValidationError {
	return &PasswordValidationError{Message: message}
}

// RailsClientError represents errors from Rails client operations
type RailsClientError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error codes for Rails client operations
const (
	// Deposit Address Errors
	ErrInvalidDepositAddress     = "INVALID_DEPOSIT_ADDRESS"
	ErrDepositAddressValidation  = "DEPOSIT_ADDRESS_VALIDATION_FAILED"
	ErrDepositAddressUnsupported = "DEPOSIT_ADDRESS_UNSUPPORTED_TOKEN"

	// Withdrawal Errors
	ErrInvalidWithdrawalAddress = "INVALID_WITHDRAWAL_ADDRESS"
	ErrWithdrawalValidation     = "WITHDRAWAL_VALIDATION_FAILED"
	ErrInsufficientBalance      = "INSUFFICIENT_BALANCE"
	ErrWithdrawalLimitExceeded  = "WITHDRAWAL_LIMIT_EXCEEDED"
	ErrWithdrawalUnsupported    = "WITHDRAWAL_UNSUPPORTED_TOKEN"

	// General Rails Errors
	ErrRailsServiceUnavailable = "RAILS_SERVICE_UNAVAILABLE"
	ErrRailsTimeout            = "RAILS_TIMEOUT"
	ErrRailsInternalError      = "RAILS_INTERNAL_ERROR"
)

// Error messages mapping
var ErrorMessages = map[string]string{
	ErrInvalidDepositAddress:     "Invalid deposit address format",
	ErrDepositAddressValidation:  "Deposit address validation failed",
	ErrDepositAddressUnsupported: "Unsupported token for deposit address",
	ErrInvalidWithdrawalAddress:  "Invalid withdrawal address format",
	ErrWithdrawalValidation:      "Withdrawal validation failed",
	ErrInsufficientBalance:       "Insufficient balance for withdrawal",
	ErrWithdrawalLimitExceeded:   "Withdrawal limit exceeded",
	ErrWithdrawalUnsupported:     "Unsupported token for withdrawal",
	ErrRailsServiceUnavailable:   "Service temporarily unavailable",
	ErrRailsTimeout:              "Request timeout",
	ErrRailsInternalError:        "Internal service error",
}

// NewRailsClientError creates a new Rails client error
func NewRailsClientError(code, details string) *RailsClientError {
	message, exists := ErrorMessages[code]
	if !exists {
		message = "Unknown error occurred"
	}

	return &RailsClientError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// Error implements the error interface
func (e *RailsClientError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}
