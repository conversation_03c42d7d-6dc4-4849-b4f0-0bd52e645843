package domain

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
)

type UserBonusRepository interface {
	CreateUserBonusInDatabase(ctx context.Context, data UserBonus) error
	AssignBonusByType(ctx context.Context, bonusType, startDate, endDate string) error
	GetUserBonusesByExternalID(ctx context.Context, externalID string) ([]UserBonus, error)
	UpdateUserBonusStatus(ctx context.Context, externalBonusId int, externalID string, status string) error
	DeleteExpiredBonuses(ctx context.Context) error
	ActivateUserBonusesByExternalIds(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int) error
	AssignSpecialBonus(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string) error
	CreateReloadBonuses(ctx context.Context, requestData []ReloadBonusRequest) error
	GetLevelUpBonusOfUser(userId string) ([]UserBonus, error)
	UpdateModalPopupClosed(ctx context.Context, userId string) error
	ClaimAllBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error
	ClaimTiltBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error
	SingleClaimUpdate(ctx context.Context, token string, externalID string, bonusConfigID int) error
	CreateCampaignBonusForUser(ctx context.Context, externalID string, rewardAmount float64, reason string, note string) error
}

type UserBonusService interface {
	AssignBonusByType(ctx context.Context, bonusType, startDate, endDate string) error
	GetUserBonusesByExternalID(ctx context.Context, externalID string) ([]UserBonus, error)
	UpdateUserBonusStatus(ctx context.Context, externalBonusId int, externalID string, status string) error
	DeleteExpiredBonuses(ctx context.Context) error
	ActivateUserBonusesByExternalIds(ctx context.Context, userExternalIDs string, category string, status string, rewardAmount float64, bonusExternalId int) error
	AssignSpecialBonus(ctx context.Context, userId string, username string, category string, rewardAmount float64, bonusExternalId int, reason string, note string) error
	CreateReloadBonuses(ctx context.Context, requestData []ReloadBonusRequest) error
	GetLevelUpBonusOfUser(userId string) ([]UserBonus, error)
	UpdateModalPopupClosed(ctx context.Context, userId string) error
	ClaimAllBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error
	ClaimTiltBonusAndUpdateWallet(ctx context.Context, externalId string, token string) error
	SingleClaimUpdate(ctx context.Context, token string, externalID string, bonusConfigID int) error
}

type BonusQueueService interface {
	StartBonusConsumer()
	AddBonusToQueue(ctx context.Context, token string, b UserBonus)
}

type UserBonusQueue interface {
	ProcessBonus(b BonusQueueItem) error
}

type UserBonusRequest struct {
	ExternalID      string        `json:"external_id"`
	BonusExternalID int           `json:"id"`
	BonusConfigID   int           `json:"bonus_config_id"`
	Username        string        `json:"user_name"`
	Category        string        `json:"category"`
	ExpiresOn       time.Time     `json:"expires_on"`
	Eligible        bool          `json:"eligible"`
	Availed         bool          `json:"availed"`
	BonusStatus     string        `json:"bonus_status"`
	UserVipStatus   string        `json:"user_vip_status"`
	RewardAmount    float64       `json:"reward_amount"`
	ReloadBonuses   []ReloadBonus `json:"reloadBonuses"`
	Reason          string        `json:"reason"`
	Note            string        `json:"note"`
	ModalClosed     bool          `json:"modal_closed" gorm:"column:modal_closed"`
	Type            string        `json:"type" gorm:"column:type"`
	TimeRange       string        `json:"time_range"`
}

type UserBonus struct {
	ID              uuid.UUID     `json:"id"`
	ExternalID      string        `json:"external_id,omitempty"`
	BonusExternalID int           `json:"bonus_external_id,omitempty" mapstructure:"id"`
	BonusConfigID   int           `json:"bonus_config_id,omitempty"`
	Username        string        `json:"user_name,omitempty"`
	Category        string        `json:"category,omitempty"`
	ExpiresOn       time.Time     `json:"expires_on,omitempty"`
	Eligible        bool          `json:"eligible,omitempty"`
	Availed         bool          `json:"availed,omitempty"`
	BonusStatus     string        `json:"bonus_status,omitempty"`
	UserVipStatus   string        `json:"user_vip_status,omitempty"`
	RewardAmount    float64       `json:"reward_amount,omitempty"`
	ClaimAttempt    bool          `json:"claim_attempt,omitempty"`
	ClaimSuccess    bool          `json:"claim_success,omitempty"`
	ReloadBonuses   []ReloadBonus `json:"reloadBonuses,omitempty" gorm:"-"`
	Reason          string        `json:"reason,omitempty"`
	Note            string        `json:"note,omitempty"`
	ModalClosed     bool          `json:"modal_closed" gorm:"column:modal_closed"`
	Type            string        `json:"type" gorm:"column:type"`
	TimeRange       string        `json:"time_range"`
}
type ReloadBonus struct {
	Amount      float64   `json:"amount"`
	AvailableOn time.Time `json:"availableOn"`
	ExpiresOn   time.Time `json:"expiresOn"`
	Claimed     bool      `json:"claimed"`
}

type VipUpgradeUserBonus struct {
	ID              uuid.UUID     `json:"id" gorm:"column:id"`
	CreatedAt       time.Time     `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt       time.Time     `json:"updated_at" gorm:"column:updated_at"`
	ExternalID      string        `json:"external_id" gorm:"column:external_id"`
	BonusConfigID   int           `json:"bonus_config_id" gorm:"column:bonus_config_id"`
	BonusExternalID int           `json:"bonus_external_id" gorm:"column:bonus_external_id"`
	Username        string        `json:"user_name" gorm:"column:user_name"`
	Category        string        `json:"category" gorm:"column:category"`
	ExpiresOn       time.Time     `json:"expires_on" gorm:"column:expires_on"`
	Eligible        bool          `json:"eligible" gorm:"column:eligible"`
	Availed         bool          `json:"availed" gorm:"column:availed"`
	BonusStatus     string        `json:"bonus_status" gorm:"column:bonus_status"`
	UserVipStatus   string        `json:"user_vip_status" gorm:"column:user_vip_status"`
	RewardAmount    float64       `json:"reward_amount" gorm:"column:reward_amount"`
	ClaimAttempt    bool          `json:"claim_attempt" gorm:"column:claim_attempt"`
	ClaimSuccess    bool          `json:"claim_success" gorm:"column:claim_success"`
	ReloadBonuses   []ReloadBonus `json:"reloadBonuses" gorm:"type:jsonb;column:reload_bonuses"`
	Reason          string        `json:"reason" gorm:"column:reason"`
	Note            string        `json:"note" gorm:"column:note"`
	ModalClosed     bool          `json:"modal_closed" gorm:"column:modal_closed"`
}

func (VipUpgradeUserBonus) TableName() string {
	return "user_bonus"
}

type ReloadBonusRequest struct {
	ID            int                      `json:"id"`
	Status        string                   `json:"status"`
	UserID        string                   `json:"userId"`
	Username      string                   `json:"username"`
	ExpiresOn     string                   `json:"expiresOn"` 
	RewardAmount  string                   `json:"rewardAmount"`
	Category      string                   `json:"category"`
	ReloadBonuses []ReloadBonusRequestItem `json:"reloadBonuses"`
	Reason        string                   `json:"reason,omitempty"`
	Note          string                   `json:"note,omitempty"`
	Type          string                   `json:"type"`
}

type ReloadBonusRequestItem struct {
	Amount      float64 `json:"amount"`
	ExpiresOn   string  `json:"expiresOn"`   
	AvailableOn string  `json:"availableOn"` 
	Claimed     bool    `json:"claimed"`
}

type UserBonusInDirectus struct {
	ExternalID           string        `json:"userId"`
	Username             string        `json:"username"`
	Category             string        `json:"category"`
	BonusStatus          string        `json:"status"`
	ExpiresOn            time.Time     `json:"expiresOn"`
	RewardAmount         float64       `json:"rewardAmount"`
	ReloadBonuses        []ReloadBonus `json:"reloadBonuses,omitempty"`
	SplitOnDays          int           `json:"splitOnDays,omitempty"`
	Reason               string        `json:"reason,omitempty"`
	Note                 string        `json:"note,omitempty"`
	Type                 string        `json:"type"`
	CasinoWageringAmount string        `json:"casinoBetSum"`
	CasinoLossAmount     string        `json:"casinoLossSum"`
	SportsWageringAmount string        `json:"sportsBetSum"`
	SportsLossAmount     string        `json:"sportsLossSum"`
	TotalLossbackSum     string        `json:"totalLossbackSum"`
	TotalNGRSum          string        `json:"totalNGRSum"`
	RtpRate              string        `json:"rtpRate"`
	TimeRange            string        `json:"timeRange"`
}
type ReloadBonusDTO struct {
	Amount      float64   `json:"amount"`
	AvailableOn string    `json:"availableOn"`
	ExpiresOn   time.Time `json:"expiresOn"`
	Claimed     bool      `json:"claimed"`
}

type BonusWalletUpdateRequst struct {
	ID           uuid.UUID `json:"id"`
	ExternalID   string    `json:"user_id"`
	RewardAmount float64   `json:"reward_amount"`
	Category     string    `json:"category"`
	Reason       string    `json:"reason"`
	Note         string    `json:"note"`
	Token        string    `json:"token"`
}

type BonusQueue struct {
	Bonus chan BonusQueueItem
	Mut   sync.Mutex
}

type BonusQueueItem struct {
	B     UserBonus
	Token string
	Ctx   context.Context
}
