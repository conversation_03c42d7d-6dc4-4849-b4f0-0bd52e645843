package domain

import (
	"context"
	"time"

	"github.com/google/uuid"
)

type BetService interface {
	GetBets(ctx context.Context, params *GetBetsParams) (*Bets, error)
	GetBetByID(ctx context.Context, id uuid.UUID) (*Bet, error)
	GetBetByExternalID(ctx context.Context, externalID string) (*Bet, error)
	GetBetsByUserID(ctx context.Context, params *GetBetsByUserIDParams) (*UserBets, error)
	ExportBets(ctx context.Context, params *ExportBetsParams) ([]Bet, error)
	GetLatestBetsByUserId(ctx context.Context, externalID string) ([]Bet, error)
	ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (Bet, bool, error)
}

type BetRepository interface {
	UpsertBet(ctx context.Context, bet *Bet) (*Bet, error)
	GetBets(ctx context.Context, params *GetBetsParams) (*Bets, error)
	GetBetByID(ctx context.Context, id uuid.UUID) (*Bet, error)
	GetBetsByUserID(ctx context.Context, params *GetBetsByUserIDParams) (*UserBets, error)
	ExportBets(ctx context.Context, params *ExportBetsParams) ([]Bet, error)
	GetBetByExternalID(ctx context.Context, externalID string) (*Bet, error)
	GetLatestBetsByUserId(ctx context.Context, externalID string) ([]Bet, error)
	ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (Bet, bool, error)
	GetWageredAmountByTypeForXDays(ctx context.Context, userID string, days int) (float64, error)
}

type GetBetsParams struct {
	OrderParams
	PagingParams
	UserExternalID         *string
	IncludeBetsWithoutGame bool
	PayoutOver             *float64
	FilterBetsByGame       *bool
	BetType                *string
}

type GetBetsByUserIDParams struct {
	OrderParams
	PagingParams
	UserID                 uuid.UUID
	IncludeBetsWithoutGame bool
	FilterBetsByGame       bool
	From                   *time.Time
	To                     *time.Time
	Result                 *string
	Type                   *string
	Currency               []string
	Status                 *string
}

type ExportBetsParams struct {
	ExportParams
	UserExternalID string
}

type Bets PagedItemsWithoutTotalCount[Bet]
type UserBets UserPagedItems[Bet]

type Bet struct {
	ID              uuid.UUID
	BetAmount       *float64
	ActualBetAmount *float64
	BetType         BetType
	CoinsMultiplier float64
	Currency        string
	ConvertedTo     string
	ExternalID      string
	GhostMode       bool
	HiddenBoolean   bool
	Multiplier      float64
	Payout          *float64
	ActualWinAmount *float64
	RoundStatus     string
	Time            time.Time
	Event           *string
	Odds            *float64
	Game            Game
	User            User
}

type BetType string

const (
	BetTypeCasino BetType = "CASINO"
	BetTypeSports BetType = "SPORTS"
	BetTypePoker  BetType = "POKER"
)
