package domain

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type CampaignCMSResponse struct {
	Data []struct {
		Campaigns []struct {
			Code          string `json:"code"`
			Name          string `json:"name"`
			ReferredUsers []struct {
				UserID   string `json:"userId"`
				Username string `json:"username"`
			} `json:"referredUsers"`
		} `json:"campaigns"`
		ParentID string `json:"parentId"`
		UserID   string `json:"userId"`
		Username string `json:"username"`
	} `json:"data"`
}
type ReferralCampaign struct {
	UserID                      string      `json:"userId"`
	Username                    string      `json:"username"`
	ParentID                    string      `json:"parentId"`
	DefaultCommissionPercentage float64     `json:"defaultCommissionPercentage"`
	Campaigns                   []Campaigns `json:"campaigns"`
}

type UserCampaignResponse struct {
	CampaignsName       string  `json:"name"`
	ReferralCode        string  `json:"code"`
	ComissionPercentage float64 `json:"commissionPercentage"`
}
type Campaigns struct {
	CampaignName         string         `json:"name"`
	ReferralCode         string         `json:"code"`
	CommissionPercentage float64        `json:"commissionPercentage"`
	ReferredUsers        []ReferredUser `json:"referredUsers"`
}
type ReferredUser struct {
	UserID                   string    `json:"userId"`
	Username                 string    `json:"username"`
	CreatedAt                time.Time `json:"created_at"`
	LastCasinoWageringAmount float64   `json:"lastCasinoWageringAmount"`
	LastSportsWageringAmount float64   `json:"lastSportsWageringAmount"`
	LastCommission           float64   `json:"lastCommission"`
	TotalWagered             float64   `json:"totalWagered"`
	TotalCommission          float64   `json:"totalCommission"`
}
type WageringSummary struct {
	UserID        string  `gorm:"column:external_id"`
	CasinoWagered float64 `gorm:"column:casino_wagered"`
	SportsWagered float64 `gorm:"column:sports_wagered"`
	TotalWagered  float64 `gorm:"column:total_wagered"`
}

type UserCommissionInfo struct {
	UserID                   string                   `json:"userId"`
	TotalCommission          float64                  `json:"totalCommission"`
	ClaimedCommission        float64                  `json:"claimedCommission"`
	ClaimableCommission      float64                  `json:"claimableCommission"`
	Campaigns                []CampaignCommission     `json:"campaigns"`
	TotalReferrals           int                      `json:"totalReferrals"`
	TotalWageredAllCampaigns float64                  `json:"totalWageredAllCampaigns"`
	TopCampaign              string                   `json:"topCampaign"`
	AverageReferrals         float64                  `json:"averageReferrals"`
	ReferredUsers            []ReferredUserCommission `json:"referredUsers" gorm:"-"`
	LastCalculationTime      time.Time                `json:"lastCalculationTime"`
	TotalCampaigns           int                      `json:"totalCampaigns"`
}
type CampaignCommission struct {
	CampaignName         string                   `json:"campaignName"`
	RefferalCode         string                   `json:"refferalCode"`
	TotalCommission      float64                  `json:"totalCommission"`
	ReferralCount        int                      `json:"referralCount"`
	CommissionPercentage float64                  `json:"commissionPercentage"`
	TopReferredUser      string                   `json:"topReferredUser,omitempty"`
	ReferredUsers        []ReferredUserCommission `json:"referredUsers"`
	TotalWagered         float64                  `json:"totalWagered"`
	CampaignCreatedAt    time.Time                `json:"campaignCreatedAt"`
	LastCalculationTime  time.Time                `json:"lastCalculationTime"`
	TotalCampaigns       int                      `json:"totalCampaigns"`
}

type ReferredUserCommission struct {
	UserID          string    `json:"userId,omitempty"`
	TotalCommission float64   `json:"totalCommission"`
	CasinoWagered   string    `json:"casinoWagered"`
	SportsWagered   string    `json:"sportsWagered"`
	TotalWagered    float64   `json:"totalWagered,"`
	Deposits        string    `json:"deposits"`
	NetEarnings     string    `json:"netEarnings"`
	Username        string    `json:"username"`
	CampaignName    string    `json:"campaignName"`
	CreatedAt       time.Time `json:"createdAt"`
}

type CommissionDetails struct {
	TotalCommission     float64
	ClaimedCommission   float64
	ClaimableCommission float64
}

type BonusCommissionUpdate struct {
	UserID                      string     `json:"userId"`
	Campaigns                   []Campaign `json:"campaigns"`
	DefaultCommissionPercentage string     `json:"defaultCommissionPercentage"`
}

type Campaign struct {
	Name                 string                  `json:"name"`
	CommissionPercentage interface{}             `json:"commissionPercentage"`
	ReferredUsers        []CampaignReferredUsers `json:"referredUsers"`
}

type CampaignReferredUsers struct {
	UserID   string `json:"userId"`
	Username string `json:"username"`
}

type CampaignDeleteError struct {
	CampaignNames []string
	Message       string
}

type CommissionHistoryResponse struct {
	CommissionHistory []CommissionHistory `json:"commissionHistory"`
	Offset            int                 `json:"offset"`
	Limit             int                 `json:"limit"`
	Total             int64               `json:"total"`
}

type CommissionHistory struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"createdAt"`
	Currency      string    `json:"currency"`
	ClaimedAmount float64   `json:"claimedAmount"`
}

func (c *CampaignDeleteError) Error() string {
	return fmt.Sprintf("%s: %v", c.Message, c.CampaignNames)
}

type CampaignStats struct {
	TotalCommission     float64   `json:"totalCommission"`
	ClaimedCommission   float64   `json:"claimedCommission"`
	ClaimableCommission float64   `json:"claimableCommission"`
	TotalWagered        float64   `json:"totalWagered"`
	CampaignCreatedAt   time.Time `json:"campaignCreatedAt"`
	TotalReferrals      int       `json:"totalReferrals"`
	TotalDeposits       float64   `json:"totalDeposits"`
}

type ReferralRepository interface {
	CreateUserReferralCampaign(ctx context.Context, userID string, username string, campaignName string) error
	GetUserReferralCampaign(ctx context.Context, userID string, campaignName string) ([]UserCampaignResponse, int, error)
	AddUserToTheListOfRefferedUsers(ctx context.Context, referralCode string, userID string, username string) error
	CheckIfUserAlreadyUsedTheCode(ctx context.Context, referralCode string, userId string) (bool, error)
	CalculateUserCommissions(ctx context.Context, userID string) error
	ClaimReward(ctx context.Context, token string, userExternalID string) (float64, error)
	GetCommissionHistory(ctx context.Context, userID string, offset, limit int) (CommissionHistoryResponse, error)
	GetDetailedCommissionInfo(ctx context.Context, parentUserId string) (UserCommissionInfo, error)
	GetParentUserIDByReferralCode(ctx context.Context, referralCode string) (string, error)
	UpdateUserCommisionPercentage(ctx context.Context, userId string, campaignName string, commissionPercentage string, referredUsers []CampaignReferredUsers) error
	IsUserInAnyReferralCampaign(ctx context.Context, userID string) (bool, string, error)
	CreateDefaultCampaignInDirectus(ctx context.Context, userId, username string) error
	GetCampaignStats(ctx context.Context, userID string, campaignName string) (CampaignStats, error)
	DeleteCampaigns(ctx context.Context, userID string, campaignName string) error
	DetectIfUserIsNotReferringItself(ctx context.Context, referralCode string, userID string) (bool, error)
	CalculateAllUsersCommissions(ctx context.Context) error
	UpdateUserDefaultCommissionPercentage(userId string, defaultCommissionPercentage string) error
	ManuallyUpdateReferralOfUser(userIdToUpdate string, userNameToUpdate string, newParentId string) error
	CreateAdminCampaign(ctx context.Context, referralCode string, rewardAmount float64, codeUsageLimit int) error
	IsReferralCodeValid(ctx context.Context, referralCode string) (bool, error)
}
type ReferralService interface {
	CreateUserReferralCampaign(ctx context.Context, userID string, username string, campaignName string) error
	GetUserReferralCampaign(ctx context.Context, userID string, campaignName string) ([]UserCampaignResponse, int, error)
	AddUserToTheListOfRefferedUsers(ctx context.Context, referralCode string, userID string, username string) error
	CheckIfUserAlreadyUsedTheCode(ctx context.Context, referralCode string, userId string) (bool, error)
	CalculateUserCommissions(ctx context.Context, userID string) error
	ClaimReward(ctx context.Context, token string, userExternalID string) (float64, error)
	GetCommissionHistory(ctx context.Context, userID string, offset, limit int) (CommissionHistoryResponse, error)
	GetDetailedCommissionInfo(ctx context.Context, parentUserId string) (UserCommissionInfo, error)
	GetParentUserIDByReferralCode(ctx context.Context, referralCode string) (string, error)
	UpdateUserCommisionPercentage(ctx context.Context, userId string, campaignName string, commissionPercentage string, referredUsers []CampaignReferredUsers) error
	IsUserInAnyReferralCampaign(ctx context.Context, userID string) (bool, string, error)
	CreateDefaultCampaignInDirectus(ctx context.Context, userId, username string) error
	DeleteCampaigns(ctx context.Context, userID string, campaignName string) error
	GetCampaignStats(ctx context.Context, userID string, campaignName string) (CampaignStats, error)
	CalculateAllUsersCommissions(ctx context.Context) error
	UpdateUserDefaultCommissionPercentage(userId string, defaultCommissionPercentage string) error
	ManuallyUpdateReferralOfUser(userIdToUpdate string, userNameToUpdate string, newParentId string) error
	CreateAdminCampaign(ctx context.Context, referralCode string, rewardAmount float64, codeUsageLimit int) error
}
