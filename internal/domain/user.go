package domain

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type UserService interface {
	GetUserByID(ctx context.Context, id uuid.UUID) (*User, error)
	GetUserByExternalID(ctx context.Context, id string) (*User, error)
	GetRankedUserByExternalID(ictx context.Context, d string) (*RankedUser, error)
	GetUserAndGameByExternalId(ctx context.Context, userId, gameId string) (UserGame, error)
	GetUserByUserName(ctx context.Context, userName string) (*User, error)
	GetRankedUsers(ctx context.Context, params *GetUserParams) (*RankedUsers, error)
	GetRankedUsersByExternalID(ctx context.Context, id string, retrieveAmount int, orderBy string) ([]RankedUser, error)
	GetRegisteredEmail(ctx context.Context, email string, emailMarketing bool) (*RegisteredEmail, error)
	SaveRegisteredEmail(ctx context.Context, email string, emailMarketing bool) error
	UpdateUser(ctx context.Context, user *User) error
	GetWinnerOfTheMonth(ctx context.Context) ([]WinnerDetails, error)
	UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error)
	GetUsersXPAndUpdateInElantil(ctx context.Context) error
	UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode, hideStats *bool) error
	UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error
	IsUserNameUnique(ctx context.Context, userName string) (bool, error)
}

type UserRepository interface {
	UpsertUser(ctx context.Context, user *User) (*User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*User, error)
	GetUserByUserName(ctx context.Context, userName string) (*User, error)
	GetUserByExternalID(ctx context.Context, id string) (*User, error)
	GetRankedUserByExternalID(ctx context.Context, id string) (*RankedUser, error)
	GetUserAndGameByExternalId(ctx context.Context, userId, gameId string) (UserGame, error)
	GetUsersByExternalIDs(ctx context.Context, externalIDs []string) ([]GetAllUsersResponse, error)
	GetRankedUsers(ctx context.Context, params *GetUserParams) (*RankedUsers, error)
	GetRankedUsersByExternalID(ctx context.Context, id string, retrieveAmount int, orderBy string) ([]RankedUser, error)
	SaveRegisteredEmail(ctx context.Context, email string, emailMarketing bool) error
	GetRegisteredEmail(ctx context.Context, email string) (*RegisteredEmail, error)
	UpdateUserByID(ctx context.Context, user *User) error
	GetWinnerOfTheMonth(ctx context.Context) ([]WinnerDetails, error)
	StreamAllUsers(ctx context.Context, userChan chan<- GetAllUsersResponse, needsVipStatus bool) error
	UpdateUserElantilVIPStatus(externalID string, vipStatus string) error
	AssignSpecialBonusToUserOnTierUpgrade(ctx context.Context, userExternalID string, bonusAmount float64, newVipStatus string) error
	BeginTransaction(ctx context.Context) (*gorm.DB, error)
	GetCurrentVipStatus(ctx context.Context, userExternalID string) (string, error)
	GetUserWageringAndVIPStatus(ctx context.Context, userExternalID string) (float64, string, error)
	DB() *gorm.DB
	UpdateUpsertUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error)
	UpdateUserXP(ctx context.Context, userID string, xpToAdd float64) (float64, error)
	GetAllUsersWithPagination(ctx context.Context, limit, offset int) ([]GetUsersResponseForXP, error)
	GetUsersXPAndUpdateInElantil(ctx context.Context) error
	UpdateUserPreferences(ctx context.Context, userExternalID string, ghostMode, hideStats *bool) error
	GetUserAndGameInfo(ctx context.Context, userID, gameID string) (UserGameDTO, error)
	UpdateUserEmailVerificationStatus(ctx context.Context, username string, verified bool) error
	UpdateUserMultiCurrency(ctx context.Context, userID string, multiCurrency bool) error
	IsUserNameUnique(ctx context.Context, userName string) (bool, error)
}

type RegisteredEmail struct {
	Email          string
	EmailMarketing bool
	Registered     bool
}

type RankedUser struct {
	User
	Rank                int
	RemainingToNextRank float64
}

type UserClaimedCoins struct {
	ID         uuid.UUID
	RankedUser RankedUser
	Coins      float64
	Source     string
}

type User struct {
	ID                  uuid.UUID
	ClaimedCoins        float64
	CompanyUser         bool
	Email               string
	ExternalID          string
	FactorSID           *string
	FirstName           string
	GhostMode           bool
	HideAllStats        bool
	HideTournamentStats bool
	JoinDate            time.Time
	LastName            string
	LastUpdatedOn       *time.Time
	ProfileStatus       string
	UserName            string
	VIPStatus           string
	ElantilVIpStatus    string
	UserAssets          []UserAsset
	Verified            bool
	MultiCurrency       bool
	// Stats
	NumberOfLosses      int
	NumberOfWins        int
	TotalBets           int
	TotalCoins          float64
	Wagered             float64
	LifeTimeBetSummary  float64
	LifeTimeLossSummary float64
	XP                  float64
}

type UserGame struct {
	UserID           uuid.UUID `gorm:"column:id"`
	UserName         string    `gorm:"column:user_name"`
	ElantilVipStatus string    `gorm:"column:elantil_vip_status"`
	GhostMode        bool      `gorm:"column:ghost_mode"`
	GameID           uuid.UUID `gorm:"column:id"`
	CmsGameID        int64     `gorm:"column:cms_game_id"`
	ExternalID       string    `gorm:"column:external_id"`
	Name             string    `gorm:"column:name"`
	Slug             string    `gorm:"column:slug"`
	ThumbnailID      string    `gorm:"column:thumbnail_id"`
}

type UserGameDTO struct {
	// User fields
	UserID           uuid.UUID `gorm:"column:user_id"`
	UserExternalID   string    `gorm:"column:user_external_id"`
	UserName         string    `gorm:"column:user_name"`
	ElantilVIPStatus string    `gorm:"column:elantil_vip_status"`
	GhostMode        bool      `gorm:"column:ghost_mode"`

	// Game fields
	GameID         uuid.UUID `gorm:"column:game_id"`
	GameExternalID string    `gorm:"column:game_external_id"`
	GameName       string    `gorm:"column:game_name"`
	GameSlug       string    `gorm:"column:game_slug"`
	ThumbnailID    string    `gorm:"column:thumbnail_id"`
	CMSGameID      int64     `gorm:"column:cms_game_id"`
}

type UserAsset struct {
	ID              uuid.UUID
	User            User
	Type            string
	UserConfigAsset UserConfigAsset
}

type GetUserParams struct {
	OrderParams
	PagingParams
	UserExternalID string
	RetrieveAmount int
}

type Users PagedItems[User]

type RankedUsers PagedItems[RankedUser]
type GetAllUsersResponse struct {
	ID               uuid.UUID
	ExternalID       string
	UserName         string
	ElantilVIpStatus string
	WageredAmount    float64
}

type WinnerDetails struct {
	UserID           string  `json:"user_id" gorm:"column:user_id"`
	UserName         string  `json:"user_name" gorm:"column:user_name"`
	ElantilVIPStatus string  `json:"vip_status" gorm:"column:elantil_vip_status"`
	WinAmount        float64 `json:"win_amount" gorm:"column:win_amount"`
	BetAmount        float64 `json:"bet_amount" gorm:"column:bet_amount"`
	PayoutAmount     float64 `json:"payout" gorm:"column:payout"`
	GameName         string  `json:"game_name" gorm:"column:game_name"`
	CMSGameID        string  `json:"cms_game_id" gorm:"column:cms_game_id"`
	ThumbnailID      string  `json:"thumbnail_id" gorm:"column:thumbnail_id"`
	Month            string  `json:"month" gorm:"column:month"`
	WinTimestamp     string  `json:"win_timestamp" gorm:"column:win_timestamp"`
}

type GetUsersResponseForXP struct {
	ExternalID string  `gorm:"column:external_id"`
	XP         float64 `gorm:"column:xp"`
}

type UserPreferences struct {
	GhostMode    bool `json:"ghost_mode" gorm:"-"`
	HideAllStats bool `json:"hide_all_stats" gorm:"-"`
}

type UserRecovery struct {
	Username    string  `json:"user_name"`
	ExternalId  string  `json:"external_id"`
	TotalAmount float64 `json:"total_amount"`
}
