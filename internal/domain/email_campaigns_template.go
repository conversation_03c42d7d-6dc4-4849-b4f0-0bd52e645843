package domain

import (
	"context"
	"time"
)

type GetUserInformation []GetUserInformationElement

type GetUserInformationElement struct {
	CustomerID         string    `json:"CustomerID"`
	TemplateID         int64     `json:"TemplateID"`
	ScheduledTime      string `json:"ScheduledTime"`
	CustomerAttributes []string  `json:"CustomerAttributes"`
}

type EmailCampaignsResponse struct {
	CustomerID    string    `json:"customerID"`
	TemplateID    int       `json:"templateID"`
	ScheduledTime time.Time `json:"scheduledTime"`
	Email         string    `json:"email"`
}

type CustomerAttributeField struct {
	Email string `json:"email"`
}

type OptimoveWebhookPayload struct {
	EventTypeID int32  `json:"EventTypeID"`
	TimeStamp   string `json:"TimeStamp"`
	CampaignID  int32  `json:"CampaignID"`
	ChannelID   int32  `json:"ChannelID"`
}

type EmailCampaignsRepository interface {
	CreateEmailCampaigns(ctx context.Context, contentTemplate *GetUserInformation) error
	GetEmailCampaigns(ctx context.Context, email string) (*EmailCampaignsResponse, error)
}

type EmailCampaignsService interface {
	CreateEmailCampaigns(ctx context.Context, contentTemplate *OptimoveWebhookPayload) error
	GetEmailCampaigns(ctx context.Context, email string) (*EmailCampaignsResponse, error)
}
