package domain

import (
	"context"

	"github.com/google/uuid"
)

type GameService interface {
	UpsertAllGames(ctx context.Context) error
}

type GameRepository interface {
	CreateGame(ctx context.Context, game *Game) (*Game, error)
	UpsertGames(ctx context.Context, games []Game) error
	UpsertAllGamesIfNotInitialized(ctx context.Context, getAllGames func(context.Context) ([]Game, error)) error
	GetGameByGameID(ctx context.Context, id uuid.UUID) (*Game, error)
	GetGameByExternalID(ctx context.Context, id string) (*Game, error)
	GetGameByVendorGameID(ctx context.Context, id string) (*Game, error)
}

type GameProvider interface {
	GetAllGames(ctx context.Context) ([]Game, error)
}

type Game struct {
	ID           uuid.UUID
	CMSGameID    int64
	ExternalID   string
	Name         *string
	Slug         *string
	ThumbnailID  *string
	VendorGameID string
}
