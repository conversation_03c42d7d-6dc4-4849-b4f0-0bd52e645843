package domain

import (
	"context"

	"github.com/google/uuid"
)

type VIPUserBalanceService interface {
	CreateVIPUserBalance(ctx context.Context, user *VIPUserBalance) (*VIPUserBalance, error)
	BumpUser(ctx context.Context, bumpRequest BumpUserVIPStatus) error
}

type VIPUserBalanceRepository interface {
	CreateVIPUserBalance(ctx context.Context, user *VIPUserBalance) (*VIPUserBalance, error)
}

type VIPUserBalance struct {
	ID           uuid.UUID
	Handle       float64
	Coins        float64
	ClaimedCoins float64
	BetsWon      int
	TotalBets    int
	Source       string
	User         User
}

type BumpUserVIPStatus struct {
	UserExternalID  string `json:"user_external_id" validate:"required"`
	VIPStatusToBump string `json:"vip_status_to_bump" validate:"required"`
	AddBase         bool   `json:"add_base"`
	Source          string `json:"source" validate:"required"`
}
