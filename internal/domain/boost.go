package domain

import (
	"context"
	"time"

	"github.com/google/uuid"
)

type BoostService interface {
	CreateBoost(
		ctx context.Context,
		boost *Boost,
		usersExternalID []string,
	) (*BoostCreationOut, error)

	GetAvailableBoostByUserID(
		ctx context.Context,
		userID string,
		targetTime time.Time,
		includeStarted bool,
	) (*Boost, error)

	UpdateBoosts(ctx context.Context, boost Boost) error
}

type BoostRepository interface {
	CreateBoosts(ctx context.Context, boost []Boost) error

	GetAvailableBoostByUserID(
		ctx context.Context,
		userID string,
		targetTime time.Time,
		includeStarted bool,
	) (*Boost, error)

	GetActiveBoostByUserID(
		ctx context.Context,
		userID string,
		targetTime time.Time,
	) (*Boost, error)

	// This method is intended to return overlapping boosts, if there are no overlaping records for boost it returns nil, domain.ErrResourceNotFound
	GetOverlapingBoostByUserID(
		ctx context.Context,
		userID string,
		startTime, finishTime time.Time,
	) (*Boost, error)

	UpdateBoosts(ctx context.Context, boost Boost) error
}

type Boost struct {
	ID                 uuid.UUID
	BonusStartsAt      time.Time
	BonusFinishesAt    time.Time
	BoostDurationHours int
	BoostStartedAt     *time.Time
	Multiplier         float64
	User               User
	RankedUser         *RankedUser
}

type BoostCreationOut struct {
	CreatedCount int
	Errors       []CreateBoostError
}

type CreateBoostError struct {
	UserID string
	Error  error
}
