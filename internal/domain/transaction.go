package domain

import (
	"context"
	"time"

	"github.com/google/uuid"
)

type TransactionRepository interface {
	UpsertTransaction(ctx context.Context, trans *Transaction) (*Transaction, error)
	GetTransactions(ctx context.Context, params *GetTransactionParams) (*Transactions, error)
	ExportTransactions(ctx context.Context, params *ExportTransactionsParams) ([]Transaction, error)
	GetTransactionByType(ctx context.Context, transType string, category string, currency string, userId string, offset int, limit int) ([]Transaction, error)
	GetFirstCreditTransaction(ctx context.Context, userId string) (float64, error)
	HasOnlyOneCreditTransaction(ctx context.Context, userId uuid.UUID) (bool, error)
	GetWageringForInstantBonuses(ctx context.Context, category string) (map[string]BatchWageringData, string, error)
}

type TransactionService interface {
	GetTransactions(ctx context.Context, params *GetTransactionParams) (*Transactions, error)
	ExportTransactions(ctx context.Context, params *ExportTransactionsParams) ([]Transaction, error)
	GetTransactionByType(ctx context.Context, transType string, category string, currency string, userId string, offset int, limit int) ([]Transaction, error)
	GetTransactionExplorerLinks(ctx context.Context, userID string) (map[string]string, error)
}

type Transactions TransactionPagedItems[Transaction]

type TransactionResult struct {
	Transaction []ElantilTransactions `json:"data"`
	Meta        Meta                  `json:"meta"`
}

type ElantilTransactions struct {
	ID         string                `json:"id"`
	Attributes TransactionAttributes `json:"attributes"`
}

type Meta struct {
	Total int64 `json:"total"`
}

type TransactionAttributes struct {
	OwnerID         string          `json:"ownerId"`
	Amount          string          `json:"amount"`
	BatchExternalID string          `json:"batchExternalId"`
	CurrencyCode    string          `json:"currencyCode"`
	Data            TransactionData `json:"data"`
	CreatedOn       time.Time       `json:"createdOn"`
	ProductID       string          `json:"productId"`
	Status          string          `json:"status"`
	Type            string          `json:"type"`
}
type TransactionData struct {
	CasinoData CasinoData `json:"casinoData"`
}

type CasinoData struct {
	Config CasinoConfig `json:"config"`
}

type CasinoConfig struct {
	Game CasinoGame `json:"game"`
}

type CasinoGame struct {
	GameID string `json:"gameId"`
}

type Transaction struct {
	ID                      uuid.UUID
	Amount                  float64
	CryptoFiatAmount        float64
	BetExternalID           string
	Category                string
	Currency                string
	ConvertedTo             string
	ExternalID              string
	InsertedAt              time.Time
	Status                  string
	Type                    string
	WalletType              string
	TransactionExplorerLink *string
	User                    *User
}

type GetTransactionParams struct {
	OrderParams
	PagingParams
	UserExternalID string
	Types          []string
	Categories     []string
	Status         []string
	From           *time.Time
	To             *time.Time
	Currency       []string
}

type ExportTransactionsParams struct {
	ExportParams
	UserExternalID string
	Types          []string
	Categories     []string
}
