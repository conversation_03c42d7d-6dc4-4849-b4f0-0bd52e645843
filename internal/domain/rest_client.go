package domain

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

type DirectusCMSClient interface {
	UpdateVipTiersConfig() error
	GetBonusConfigurationFromCMS() (BonusConfigResponse, error)
	CreateUserBonus(data UserBonusInDirectus) (int, error)
	ClaimBonus(externalID string, category string, id int) (bool, error)
	DeleteExpiredBonuses() error
	ClaimMultipleBonuses(userExternalID string, categories []string) (bool, error)
	DeleteAllBonuses() error
	SetSpecialBonusExpiryDate(category string, expiryDate time.Time, bonusId int) error
	CreateFirstCampaignOfUser(data ReferralCampaign) error
	CreateCampaignInDirectus(referralCampaign ReferralCampaign) error
	UpsertReferredUsersInDirectus(userID string, campaignCode string, newReferredUsers []ReferredUser) error
	CreateDefaultCampaign(ctx context.Context, userID string, campaignName string, code string, username string, parentId string) error
	UpdateParentIdInDirectus(ctx context.Context, userId string, parentId string) error
	ClaimReloadBonus(externalID string, availableOn time.Time) (bool, error)
	CheckExistingTierUpgradeBonus(externalID string, reason string) (bool, error)
	UnclaimBonusIfWalletUpdateFails(externalID string, category string, id int) (bool, error)
	GetAllCampaignsFromCMS() (*CampaignCMSResponse, error)
	GetAllCampaignsOfUserFromCMS(userID string) (*CampaignCMSResponse, error)
	CreateUserBonusesBatch(data []UserBonusInDirectus) ([]int, error)
	UpdateBonusDropCountAndUsers(ctx context.Context, bonusCode string, dropCount int, users RedemptionUser) error
	UpdateBonusDropStatus(ctx context.Context, bonusCode string, status string) error
	RollbackBonusDropCountAndRedemption(ctx context.Context, bonusCode, userId string, dropCount int) error
}

type ElantilWageringClient interface {
	ValidateToken(ctx context.Context, token string) (bool, error)
	UpdateUserWallet(ctx context.Context, token string, data UserWalletUpdateRequest) (UserWalletUpdateResponse, error)
	UpsertPlayerActivityTagsByUserExternalID(userExternalID string, categoryKey string, keyToSet string, value string) error
	GetPlayerActivityTagsByUserExternalID(userExternalID string) (interface{}, error)
	SendVerificationEmail(userExternalID string) error
	CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName string) (bool, error)
	UpdatePassword(email string) error
	UpsertPlayerRefferals(userExternalID string, referralId string, username string) error
	GetBatchWageringSummary(ctx context.Context, externalIDs []string, wageringType *string, duration string, startDate, endDate string) (map[string]BatchWageringData, string, error)
	BatchGetWageringSummary(ctx context.Context, userIDs []string) (map[string]BatchWageringData, error)
	GetUserInformationFromJWTToken(token string) (string, string, error)
	SyncPlayerReferralsFromDB(userExternalID string, dbReferrals []ReferredUser) error
	GetUserIdsAndBatchWageringSummary(ctx context.Context, wageringType string, duration string, startDate, endDate string) (map[string]BatchWageringData, string, error)
	GetUserTransactions(ctx context.Context, userID string, page, size int64) (TransactionResult, error)
	AssignBonusTemplate(ctx context.Context, templateKey string, ownerId string, ownerType string, userToken string) error
	ForfeitBonus(ctx context.Context, bonusId, token string) error
	GetOwnerBonusesByUserId(ctx context.Context, userId, token string) (*OwnerBonusResponse, error)
	GetConversionRates(ctx context.Context, fiatCurrency string) (ConversionResponse, error)
	GetUserProfile(ctx context.Context, userExternalID string, token string) (GetUserProfileResponse, error)
	CompleteSocialProfile(ctx context.Context, userID, email string, completeProfile SocialProfileCompletionRequest, accessToken string) error
	GetUserWallet(ctx context.Context, userExternalID string, token string) (GetUserWalletResponse, error)
	CreateUserLocalWallet(ctx context.Context, userID string, token string) (GetUserWalletResponse, error)
	UpdateUserProfile(ctx context.Context, userExternalID string, token string, data UserProfileUpdateRequest) error
	CheckUserWalletBalance(ctx context.Context, userExternalID, currency string, withdrawalAmount string) error
}

type SlackAlertClient interface {
	SendSlackNotification(UserBonus []UserBonus) error
}

type RailsClient interface {
	CreateDepositAddressOfUser(ctx context.Context, userID string) error
	GetUserDepositAddresses(ctx context.Context, userID string) ([]TokenAddress, error)
	CreateWithdrawalRequest(ctx context.Context, request CreateWithdrawalRequest) error
	GetTransactionExplorerLink(ctx context.Context, userID string) (map[string]string, error)
}

type RailsClientService interface {
	CreateDepositAddressOfUser(ctx context.Context, userID string) error
	GetUserDepositAddresses(ctx context.Context, userID string) ([]TokenAddress, error)
	CreateWithdrawalRequest(ctx context.Context, request CreateWithdrawalRequest) error
}

type UserInfo struct {
	UserID   string
	Username string
}

type WageringUpdate struct {
	UserExternalID string
	WageredAmount  float64
	Timestamp      time.Time
}
type BatchWageringData struct {
	BetSum     string
	LossSum    string
	WinSum     string
	NGRSum     string
	ExpenseSum string
	RtpRate    string
}

type WageringDataResponse struct {
	Data []WageringData `json:"data"`
}

type WageringResponse struct {
	UserID     string `json:"user_id"`
	RmBetSum   string `json:"rm_bet_sum"`
	LossSum    string `json:"loss_sum"`
	WinSum     string `json:"rm_win_sum"`
	NGRSum     string `json:"ngr_sum"`
	ExpenseSum string `json:"expense_sum"`
}

type UpsertTagRequest struct {
	UserExternalID string `json:"userExternalID"`
	CategoryKey    string `json:"categoryKey"`
	Key            string `json:"key"`
}

type WageringData struct {
	UserID       string `json:"v_user_daily_kpis.user_id"`
	RecordedData string `json:"v_user_daily_kpis.rec_on,omitempty"`
	BetSum       string `json:"v_user_daily_kpis.rm_bet_sum"`
	LossSum      string `json:"v_user_daily_kpis.loss_sum"`
	WinSum       string `json:"v_user_daily_kpis.rm_win_sum"`
	NGRSum       string `json:"v_user_daily_kpis.ngr_sum"`
	ExpenseSum   string `json:"v_user_daily_kpis.expense_sum"`
	RtpRate      string `json:"v_user_daily_kpis.rtp_rate"`
}

type BonusWageringData struct {
	UserID               string
	CasinoBetSum         string
	CasinoLossSum        string
	CasinoWinSum         string
	CasinoRewardAmount   string
	SportBetSum          string
	SportLossSum         string
	SportWinSum          string
	SportsRewardAmount   string
	TotalLossSum         string
	TotalNgrSum          string
	LossbackRewardAmount string
}
type UserWalletUpdateRequest struct {
	CurrencyCode string `json:"currencyCode"`
	Amount       string `json:"amount"`
	Reason       string `json:"reason"`
	ProductId    string `json:"productId"`
	Category     string `json:"category"`
}

type UserWalletUpdateResponse struct {
	TransactionId string `json:"transactionId"`
	Status        string `json:"status"`
	Reason        string `json:"reason"`
}
type GetAddressesResponse struct {
	AssetUID          string `json:"asset_uid"`
	NetworkName       string `json:"network_name"`
	TokenTicker       string `json:"token_ticker"`
	NetworkTicker     string `json:"network_ticker"`
	DepositAddress    string `json:"deposit_address"`
	LegacyAddress     string `json:"legacy_address,omitempty"`
	Tag               string `json:"tag,omitempty"`
	EnterpriseAddress string `json:"enterprise_address,omitempty"`
	// Community-calculated fields - omitted from frontend requests
	MinWithdrawal decimal.Decimal `json:"min_withdrawal,omitempty"`
	WithdrawalFee decimal.Decimal `json:"withdrawal_fee,omitempty"`
}

type CreateWithdrawalRequest struct {
	UserID            string `json:"user_id"`
	AssetUID          string `json:"asset_uid"`
	Amount            string `json:"amount"`
	WithdrawalAddress string `json:"withdrawal_address"`
	DestinationTag    string `json:"destination_tag"`
	IdempotencyKey    string `json:"idempotency_key"`
	// Community-calculated fields - omitted from frontend requests
	WithdrawalFee  decimal.Decimal `json:"withdrawal_fee,omitempty"`
	AmountAfterFee decimal.Decimal `json:"amount_after_fee,omitempty"`
}

type TokenAddress struct {
	Token        string                 `json:"token"`
	NetworkData  []GetAddressesResponse `json:"network_data"`
	DisplayOrder int                    `json:"display_order"`
}

type ElantilConversionResponse struct {
	Data []ConversionData `json:"data"`
}

type ConversionData struct {
	ID         string               `json:"id"`
	Attributes ConversionAttributes `json:"attributes"`
}

type ConversionAttributes struct {
	SourceCurrencyCode string `json:"sourceCurrencyCode"`
	TargetCurrencyCode string `json:"targetCurrencyCode"`
	Numerator          string `json:"numerator"`
	Denominator        string `json:"denominator"`
}

type ConversionResponse struct {
	Data         ResponseData `json:"data"`
	FiatCurrency string       `json:"fiatCurrency"`
}

type ResponseData struct {
	CryptoPrices []Price `json:"cryptoPrices"`
	FiatPrices   []Price `json:"fiatPrices"`
}

type Price struct {
	Name  string `json:"name"`
	Price string `json:"price"`
}

type GetUserProfileResponse struct {
	ID                       string      `json:"id"`
	Username                 string      `json:"username"`
	Email                    string      `json:"email"`
	GivenName                string      `json:"givenName"`
	FamilyName               string      `json:"familyName"`
	AdditionalName           string      `json:"additionalName"`
	MobilePrefix             string      `json:"mobilePrefix"`
	MobileNumber             string      `json:"mobileNumber"`
	AddressStreet            string      `json:"addressStreet"`
	AddressLocality          string      `json:"addressLocality"`
	AddressRegion            string      `json:"addressRegion"`
	AddressPostalCode        string      `json:"addressPostalCode"`
	AddressCountryAlpha2Code string      `json:"addressCountryAlpha2Code"`
	Identifiers              Identifiers `json:"identifiers"`
	BornOn                   string      `json:"bornOn"`
	Marketing                Marketing   `json:"marketing"`
	Consents                 Consents    `json:"consents"`
}

type Consents struct {
	IsEmailAllowed bool `json:"isEmailAllowed"`
}

type Identifiers struct {
}

type Marketing struct {
	BTag string `json:"bTag"`
}

type GetUserWalletResponse struct {
	IsNotFound bool              `json:"isNotFound"`
	Wallets    map[string]Wallet `json:"wallets"`
	Aggregates Aggregates        `json:"aggregates"`
}

type Aggregates struct {
	Wagers       Wagers       `json:"wagers"`
	CasinoWagers Wagers       `json:"casino-wagers"`
	SportsWagers SportsWagers `json:"sports-wagers"`
}

type Wagers struct {
	Counter            int64                        `json:"counter"`
	CurrencyAggregates map[string]CurrencyAggregate `json:"currencyAggregates"`
	LastUpdatedOn      time.Time                    `json:"lastUpdatedOn"`
}

type CurrencyAggregate struct {
	Counter       int64     `json:"counter"`
	Amount        string    `json:"amount"`
	Markers       Markers   `json:"markers"`
	LastUpdatedOn time.Time `json:"lastUpdatedOn"`
}

type Markers struct {
}

type SportsWagers struct {
	Counter            int64              `json:"counter"`
	CurrencyAggregates CurrencyAggregates `json:"currencyAggregates"`
	LastUpdatedOn      time.Time          `json:"lastUpdatedOn"`
}

type CurrencyAggregates struct {
	Usd CurrencyAggregate `json:"USD"`
}

type Wallet struct {
	ID                 string        `json:"id"`
	Type               Type          `json:"type"`
	CurrencyCode       string        `json:"currencyCode"`
	BalanceAmount      string        `json:"balanceAmount"`
	MinBalanceAmount   string        `json:"minBalanceAmount"`
	MaxBalanceAmount   *string       `json:"maxBalanceAmount"`
	CreatedOn          time.Time     `json:"createdOn"`
	LastUpdatedOn      time.Time     `json:"lastUpdatedOn"`
	OrderNumber        int64         `json:"orderNumber"`
	Data               interface{}   `json:"data"`
	Conditions         []interface{} `json:"conditions"`
	Source             *Source       `json:"source"`
	IsAlreadyProcessed bool          `json:"isAlreadyProcessed"`
	IsLocked           bool          `json:"isLocked"`
	LockIDS            []interface{} `json:"lockIds"`
}

type Source struct {
	ID           ID        `json:"id"`
	ActionID     ActionID  `json:"actionId"`
	ExternalID   *string   `json:"externalId"`
	CreatedOn    time.Time `json:"createdOn"`
	ParentSource *Source   `json:"parentSource"`
}

type ActionID string

const (
	Registration    ActionID = "registration"
	CryptoAuthorize ActionID = "cryptoauthorize"
	Assign          ActionID = "assign"
)

type ID string

const (
	Wallets    ID = "wallets"
	Payments   ID = "payments"
	Promotions ID = "promotions"
)

type Type string

const (
	Main      Type = "main"
	Promotion Type = "promotion"
)

var CountryToCurrencyCode = map[string]string{
	"DE": "EUR",
	"JP": "JPY",
	"CA": "CAD",
	"NZ": "NZD",
}

var FiatWallet = map[string]bool{
	"EUR": true,
	"NZD": true,
	"JPY": true,
	"CAD": true,
}

type UserProfileUpdateRequest struct {
	Username                 string  `json:"username"`
	Email                    string  `json:"email"`
	GivenName                string  `json:"givenName"`
	FamilyName               string  `json:"familyName"`
	AdditionalName           string  `json:"additionalName"`
	MobilePrefix             *string `json:"mobilePrefix"`
	MobileNumber             *string `json:"mobileNumber"`
	AddressStreet            *string `json:"addressStreet"`
	AddressLocality          *string `json:"addressLocality"`
	AddressRegion            string  `json:"addressRegion"`
	AddressPostalCode        string  `json:"addressPostalCode"`
	AddressCountryAlpha2Code string  `json:"addressCountryAlpha2Code"`
	Identifiers              any     `json:"identifiers"`
	BornOn                   string  `json:"bornOn"`
}

type SocialProfileCompletionRequest struct {
	Username     string `json:"username"`
	IslegalAge   bool   `json:"isLegalAge"`
	ReferralCode string `json:"referralCode"`
}

func (c *ElantilConversionResponse) FilterByFiatCurrency(fiatCurrency string) ConversionResponse {
	cryptoPrices := []Price{}
	fiatPrices := []Price{}

	for _, data := range c.Data {
		attr := data.Attributes

		if attr.TargetCurrencyCode == fiatCurrency && attr.SourceCurrencyCode != fiatCurrency &&
			attr.SourceCurrencyCode != "USD" && attr.SourceCurrencyCode != "EUR" && attr.SourceCurrencyCode != "JPY" && attr.SourceCurrencyCode != "NZD" && attr.SourceCurrencyCode != "CAD" {
			cryptoPrices = append(cryptoPrices, Price{
				Name:  attr.SourceCurrencyCode,
				Price: attr.Numerator,
			})
		}

		if attr.SourceCurrencyCode == "USD" && (attr.TargetCurrencyCode == "USD" || attr.TargetCurrencyCode == "EUR" || attr.TargetCurrencyCode == "JPY" || attr.TargetCurrencyCode == "NZD" || attr.TargetCurrencyCode == "CAD") {

			fiatPrices = append(fiatPrices, Price{
				Name:  attr.TargetCurrencyCode,
				Price: attr.Numerator,
			})
		}
	}

	return ConversionResponse{
		Data: ResponseData{
			CryptoPrices: cryptoPrices,
			FiatPrices:   fiatPrices,
		},
		FiatCurrency: fiatCurrency,
	}
}
