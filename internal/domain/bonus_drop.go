package domain

import (
	"context"
	"time"
)

type RedemptionUser struct {
	BonusDropCode string `json:"bonus_drop_code"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
	Amount        int    `json:"amount"`
	Status        string `json:"status"`
}

type BonusDrop struct {
	Title           string     `json:"title"`
	StartDate       time.Time  `json:"start_date"`
	EndDate         *time.Time `json:"end_date"`
	Code            string     `json:"code"`
	TotalDropLimit  int        `json:"total_drop_limit"`
	MaxParticipants int        `json:"max_participants"`
	CodeValue       int        `json:"code_value"`
	WagerRequired   float64    `json:"wager_required"`
	WagerDays       int        `json:"wager_days"`
	Status          string     `json:"status"`
}

type RedeemBonusResponse struct {
	BonusDropCode string `json:"bonus_drop_code"`
	Amount        int    `json:"amount"`
}

type BonusDropRepository interface {
	UpsertBonusDrop(ctx context.Context, bonusDrop *BonusDrop) error
	RedeemBonusDrop(ctx context.Context, userId, username, bonusCode, token string) (RedeemBonusResponse, error)
}

type BonusDropService interface {
	UpsertBonusDrop(ctx context.Context, bonusDrop *BonusDrop) error
	RedeemBonusDrop(ctx context.Context, userId, username, bonusCode, token string) (RedeemBonusResponse, error)
}
