package domain

import (
	"context"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

type BonusService interface {
	CreateBonusConfig(ctx context.Context) error
	UpdateVipTiersConfig() error
}

type BonusRepository interface {
	CreateBonusConfig(ctx context.Context) error
	CalculateSpecificBonusRewardAmount(ctx context.Context, wager, loss, ngr string, RtpRate float64, catagory string, bonusId int) (float64, error)
	GetBonusConfigByType(ctx context.Context, bonusType string) (BonusConfig, error)
}
type BonusConfig struct {
	ID                   uuid.UUID      `json:"id" gorm:"column:id"`
	ExternalID           int            `json:"external_id" gorm:"column:external_id"`
	Status               string         `json:"status" gorm:"column:status"`
	Category             string         `json:"category" gorm:"column:category"`
	DaysToExpiry         int            `json:"daysToExpiry" gorm:"column:expiry_duration"`
	UserTiers            pq.StringArray `json:"userTiers" gorm:"column:vip_tiers"`
	TheoMarginPercentage float64        `json:"theoMarginPercentage" gorm:"column:theoretical_margin_percentage"`
	LossbackPercentage   float64        `json:"lossbackPercentage" gorm:"column:loss_back_percentage"`
}

type BonusConfigResponse struct {
	Data []BonusConfigDirectus `json:"data"`
}
type BonusConfigDirectus struct {
	ExternalID                  int      `json:"id"`
	Category                    string   `json:"category"`
	ExpiryDuration              int      `json:"daysToExpiry"`
	Status                      string   `json:"status"`
	VipTiers                    []string `json:"userTiers"`
	TheoreticalMarginPercentage string   `json:"theoMarginPercentage"`
	LossBackPercentage          string   `json:"lossbackPercentage"`
}

type OwnerBonusResponse struct {
	Data []OwnerBonusData `json:"data"`
}

type OwnerBonusData struct {
	Type       string     `json:"type"`
	ID         string     `json:"id"`
	Attributes Attributes `json:"attributes"`
}

type Attributes struct {
	OwnerID                    string           `json:"ownerId"`
	OwnerType                  string           `json:"ownerType"`
	BonusTemplateID            string           `json:"bonusTemplateId"`
	BonusTemplateKey           string           `json:"bonusTemplateKey"`
	BonusTemplateName          string           `json:"bonusTemplateName"`
	CurrencyCode               string           `json:"currencyCode"`
	Strategy                   string           `json:"strategy"`
	IsAutoClaimable            bool             `json:"isAutoClaimable"`
	ClaimableUntil             *string          `json:"claimableUntil"`
	ExpiredConfig              interface{}      `json:"expiredConfig"`
	WageringRequirementAmount  string           `json:"wageringRequirementAmount"`
	BonusConversionStrategy    string           `json:"bonusConversionStrategy"`
	BonusAmount                string           `json:"bonusAmount"`
	LockedAmount               string           `json:"lockedAmount"`
	IsSticky                   bool             `json:"isSticky"`
	WageredAmount              string           `json:"wageredAmount"`
	BalanceAmount              string           `json:"balanceAmount"`
	CreatedOn                  string           `json:"createdOn"`
	LastUpdatedOn              string           `json:"lastUpdatedOn"`
	Status                     string           `json:"status"`
	UsageConditions            []UsageCondition `json:"usageConditions"`
	ExpiresOn                  *string          `json:"expiresOn"`
	LockedWalletID             *string          `json:"lockedWalletId"`
	MaxConversionAmount        string           `json:"maxConversionAmount"`
	AutoForfeitedBalanceAmount string           `json:"autoForfeitedBalanceAmount"`
	ProgressPercentage         float64          `json:"progressPercentage"`
}

type UsageCondition struct {
	Item Item `json:"item"`
}

type Item struct {
	Games    []AllowedGames `json:"games"`
	Products []Product      `json:"products"`
}

type AllowedGames struct {
	GameID string `json:"game_id"`
}

type Product struct {
	ProductID string `json:"product_id"`
}
