package domain

import (
	"context"

	"github.com/google/uuid"
)

type UserConfigAssetsService interface {
	GetAssets(ctx context.Context) ([]UserConfigAsset, error)
	GetAsset(ctx context.Context, id uuid.UUID) (*UserConfigAsset, error)
	GetAssetsTypes(ctx context.Context) ([]string, error)
	GetAssetsSubTypes(ctx context.Context) ([]string, error)
}

type UserConfigAssetRepository interface {
	GetAssets(ctx context.Context) ([]UserConfigAsset, error)
	GetAsset(ctx context.Context, id uuid.UUID) (*UserConfigAsset, error)
	GetAssetsTypes(ctx context.Context) ([]string, error)
	GetAssetsSubTypes(ctx context.Context) ([]string, error)
}

type UserConfigAsset struct {
	ID      uuid.UUID
	Type    string
	SubType string
	Key     string
	Value   string
}
