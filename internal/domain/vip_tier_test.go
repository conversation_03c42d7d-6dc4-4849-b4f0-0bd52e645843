package domain

import (
	"testing"

	"github.com/stretchr/testify/require"
)

var testTiers = VIPTiers{
	{Tier: "tier1", Threshold: 10},
	{Tier: "tier2", Threshold: 20},
	{Tier: "tier3", Threshold: 30},
}

func TestVIPTiers_MatchVIPTier(t *testing.T) {
	tests := []struct {
		name         string
		totalWagered float64
		expectedTier string
	}{
		{
			name:         "Zero",
			totalWagered: 0.0,
			expectedTier: "",
		},
		{
			name:         "Match",
			totalWagered: 11.0,
			expectedTier: "tier1",
		},
		{
			name:         "MatchWithDecimals",
			totalWagered: 14.5,
			expectedTier: "tier1",
		},
		{
			name:         "MatchWithDecimalsTier2",
			totalWagered: 24.5,
			expectedTier: "tier2",
		},
		{
			name:         "MatchExactTier1",
			totalWagered: 10,
			expectedTier: "tier1",
		},
		{
			name:         "MatchExactTier2",
			totalWagered: 20,
			expectedTier: "tier2",
		},
		{
			name:         "MatchExactTier3",
			totalWagered: 30,
			expectedTier: "tier3",
		},
		{
			name:         "HighestTier",
			totalWagered: 31.0,
			expectedTier: "tier3",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tier := testTiers.MatchVIPTier(test.totalWagered)
			require.Equal(t, test.expectedTier, tier)
		})
	}
}

func TestVIPTiers_GetCoinsForNextRank(t *testing.T) {
	tests := []struct {
		name          string
		totalCoins    float64
		expectedCoins float64
	}{
		{
			name:          "Zero",
			totalCoins:    0.0,
			expectedCoins: 10.0,
		},
		{
			name:          "Match",
			totalCoins:    11.0,
			expectedCoins: 9.0,
		},
		{
			name:          "MatchExact",
			totalCoins:    20.0,
			expectedCoins: 10.0,
		},
		{
			name:          "MatchWithDecimals",
			totalCoins:    14.5,
			expectedCoins: 5.5,
		},
		{
			name:          "InHighestTier",
			totalCoins:    31.0,
			expectedCoins: 0.0,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tier := testTiers.GetAmountsForNextRank(test.totalCoins)
			require.Equal(t, test.expectedCoins, tier)
		})
	}
}
