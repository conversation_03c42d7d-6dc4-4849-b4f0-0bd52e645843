package domain

import "context"

type UserBonusTemplate struct {
	UserName      string `json:"user_name"`
	OfferCode     string `json:"offer_code"`
	OfferName     string `json:"offer_name"`
	OwnerBonusID  string `json:"owner_bonus_id"`
	IsModalClosed bool   `json:"is_modal_closed"`
	Status        string `json:"status"`
}

type CreateBonusTemplateRequest struct {
	UserName  string `json:"user_name"`
	OfferCode string `json:"offer_code"`
	OfferName string `json:"offer_name"`
	Status    string `json:"status"`
}

type BonusTemplateResponse struct {
	BonusTemplates []UserBonusTemplate `json:"bonus_templates"`
}

type BonusTemplateRepository interface {
	CreateBonusTemplate(ctx context.Context, bonusTemplate *CreateBonusTemplateRequest) error
	GetUserBonusTemplates(ctx context.Context, username string) (*BonusTemplateResponse, error)
	UpdateBonusTemplate(ctx context.Context, bonusTemplate *UserBonusTemplate) error
	DeleteBonusTemplate(ctx context.Context, offerCode, username string) error
}

type BonusTemplateService interface {
	CreateBonusTemplate(ctx context.Context, bonusTemplate *CreateBonusTemplateRequest) error
	GetUserBonusTemplates(ctx context.Context, username string) (*BonusTemplateResponse, error)
	UpdateBonusTemplate(ctx context.Context, bonusTemplate *UserBonusTemplate) error
	DeleteBonusTemplate(ctx context.Context, offerCode, username string) error
}
