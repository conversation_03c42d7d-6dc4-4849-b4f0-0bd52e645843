package domain

import (
	"log/slog"
	"time"
)

type VIPTiersService interface {
	GetVIPTierByName(name string) (*VIPTiersThreshold, bool)
}

type VIPTiers []VIPTiersThreshold

type VIPTiersThreshold struct {
	Tier                string  `json:"tier"`
	Threshold           int64   `json:"threshold"`
	Description         string  `json:"description"`
	LevelUpRewardAmount float64 `json:"level_up_reward_amount"`
}

type VIPStatusUpdate struct {
	UserID      string
	OldVIPTier  string
	NewVIPTier  string
	BonusAmount float64
	UpdatedAt   time.Time
}

func (s VIPTiers) MatchVIPTier(totalWagered float64) string {
	if len(s) == 0 {
		return ""
	}

	vipTier := s[0].Tier

	for _, tier := range s {
		if totalWagered < float64(tier.Threshold) {
			break
		}
		vipTier = tier.Tier
	}
	return vipTier
}

func (s VIPTiers) GetAmountsForNextRank(totalWagered float64) float64 {
	wageredAmountForNextRank := 0.0

	for i, tier := range s {
		if totalWagered < float64(tier.Threshold) {
			wageredAmountForNextRank = float64(s[i].Threshold) - totalWagered
			break
		}
	}

	return wageredAmountForNextRank
}

func (s VIPTiers) GetVIPTierUpgradeBonus(oldTier, newTier string) (float64, bool) {
	var oldTierIndex, newTierIndex int
	var found bool
	for i, tier := range s {
		if tier.Tier == oldTier {
			oldTierIndex = i
			found = true
		}
		if tier.Tier == newTier {
			newTierIndex = i
			found = true
		}
	}

	if !found {
		slog.Warn("Tier not found in configuration",
			slog.String("old_tier", oldTier),
			slog.String("new_tier", newTier))
		return 0, false
	}

	if newTierIndex <= oldTierIndex {
		return 0, false
	}

	newTierConfig := s[newTierIndex]
	return newTierConfig.LevelUpRewardAmount, true
}
