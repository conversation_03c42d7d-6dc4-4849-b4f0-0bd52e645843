package domain

import "context"

type AddCMSTermsAndConditions struct {
	Category string `json:"category"`
	Content  string `json:"content,omitempty"`
	Status   string `json:"status"`
	Version  string `json:"version"`
	Type     string `json:"type"`
}

type CMSTermsAndConditionsRepository interface {
	CreateandUpdateCMSTermsAndConditions(ctx context.Context, contentTemplate *AddCMSTermsAndConditions) error
	GetCMSTermsAndConditions(ctx context.Context, category string) (*AddCMSTermsAndConditions, error)
	DeleteCMSTermsAndConditions(ctx context.Context, category string) error
	GetSportsBookCMSTermsAndConditions(ctx context.Context) ([]*AddCMSTermsAndConditions, error)
}

type CMSTermsAndConditionsService interface {
	CreateandUpdateCMSTermsAndConditions(ctx context.Context, contentTemplate *AddCMSTermsAndConditions) error
	GetCMSTermsAndConditions(ctx context.Context, category string) (*AddCMSTermsAndConditions, error)
	DeleteCMSTermsAndConditions(ctx context.Context, category string) error
	GetSportsBookCMSTermsAndConditions(ctx context.Context) ([]*AddCMSTermsAndConditions, error)
}
