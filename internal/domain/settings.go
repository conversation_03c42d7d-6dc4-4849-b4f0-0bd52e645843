package domain

import (
	"context"
)

type SettingsResponse struct {
	Settings UserSettings `json:"settings"`
}

type UserSettings struct {
	FirstLogin              *bool  `json:"first_login"`
	PreferredCryptoCurrency string `json:"preferred_crypto_currency"`
	PreferredFiatCurrency   string `json:"preferred_fiat_currency"`
	ShowInFiat              *bool  `json:"show_in_fiat"`
	HideZeroBalances        *bool  `json:"hide_zero_balances"`
	WalletModal             *bool  `json:"wallet_modal"`
}

type UserSettingsPayload struct {
	FirstLogin              *bool  `json:"first_login,omitempty"`
	PreferredCryptoCurrency string `json:"preferred_crypto_currency,omitempty"`
	PreferredFiatCurrency   string `json:"preferred_fiat_currency,omitempty"`
	ShowInFiat              *bool  `json:"show_in_fiat,omitempty"`
	HideZeroBalances        *bool  `json:"hide_zero_balances,omitempty"`
	WalletModal             *bool  `json:"wallet_modal,omitempty"`
}

type SettingsRepository interface {
	CreateSettings(ctx context.Context, userID string, settings UserSettingsPayload) (SettingsResponse, error)
	GetSettings(userID string) (SettingsResponse, error)
	UpdateSettings(userID string, settings UserSettings) error
	DeleteSettings(userID string, key string) error
}

type SettingsService interface {
	CreateSettings(ctx context.Context, userID string, settings UserSettingsPayload) (SettingsResponse, error)
	GetSettings(userID string) (SettingsResponse, error)
	UpdateSettings(userID string, settings UserSettings) error
	DeleteSettings(userID string, key string) error
}
