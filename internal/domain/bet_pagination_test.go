package domain

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestPagingWithoutTotalCount tests the new pagination structure
func TestPagingWithoutTotalCount(t *testing.T) {
	paging := PagingWithoutTotalCount{
		CurrentPage: 2,
		PageSize:    10,
	}

	assert.Equal(t, 2, paging.CurrentPage)
	assert.Equal(t, 10, paging.PageSize)

	// Verify that TotalCount field doesn't exist
	_, hasTotalCount := interface{}(paging).(struct{ TotalCount int64 })
	assert.False(t, hasTotalCount, "PagingWithoutTotalCount should not have TotalCount field")
}
func TestPagedItemsWithoutTotalCount(t *testing.T) {
	items := []Bet{
		{ExternalID: "bet1"},
		{ExternalID: "bet2"},
	}

	paging := PagingWithoutTotalCount{
		CurrentPage: 1,
		PageSize:    10,
	}

	pagedItems := PagedItemsWithoutTotalCount[Bet]{
		Items:  items,
		Paging: paging,
	}

	assert.Len(t, pagedItems.Items, 2)
	assert.Equal(t, "bet1", pagedItems.Items[0].ExternalID)
	assert.Equal(t, "bet2", pagedItems.Items[1].ExternalID)
	assert.Equal(t, 1, pagedItems.Paging.CurrentPage)
	assert.Equal(t, 10, pagedItems.Paging.PageSize)
}

// TestBetsStructure tests that Bets now uses PagingWithoutTotalCount
func TestBetsStructure(t *testing.T) {
	bets := Bets{
		Items: []Bet{
			{ExternalID: "test1"},
			{ExternalID: "test2"},
		},
		Paging: PagingWithoutTotalCount{
			CurrentPage: 1,
			PageSize:    20,
		},
	}

	// Verify the structure
	assert.Len(t, bets.Items, 2)
	assert.IsType(t, PagingWithoutTotalCount{}, bets.Paging)
	assert.Equal(t, 1, bets.Paging.CurrentPage)
	assert.Equal(t, 20, bets.Paging.PageSize)

	// Verify that Bets is actually a type alias for PagedItemsWithoutTotalCount[Bet]
	// by converting explicitly
	pagedItems := PagedItemsWithoutTotalCount[Bet](bets)
	assert.Equal(t, bets.Items, pagedItems.Items)
	assert.Equal(t, bets.Paging, pagedItems.Paging)
}

// TestBothPagingTypesExist tests that both old and new paging types exist for backward compatibility during transition
func TestBothPagingTypesExist(t *testing.T) {
	// Test old paging structure still exists
	oldPaging := Paging{
		TotalCount:  100,
		CurrentPage: 1,
		PageSize:    10,
	}
	assert.Equal(t, int64(100), oldPaging.TotalCount)
	assert.Equal(t, 1, oldPaging.CurrentPage)
	assert.Equal(t, 10, oldPaging.PageSize)

	// Test new paging structure
	newPaging := PagingWithoutTotalCount{
		CurrentPage: 1,
		PageSize:    10,
	}
	assert.Equal(t, 1, newPaging.CurrentPage)
	assert.Equal(t, 10, newPaging.PageSize)

	// Verify they are different types
	assert.IsType(t, Paging{}, oldPaging)
	assert.IsType(t, PagingWithoutTotalCount{}, newPaging)
	assert.NotEqual(t, oldPaging, newPaging)
}

// TestPerformanceOptimizationConcept tests the concept behind the optimization
func TestPerformanceOptimizationConcept(t *testing.T) {
	// The optimization removes the need to COUNT(*) rows for total count
	// This test verifies that the new structure doesn't include total count

	bets := Bets{
		Items: []Bet{{ExternalID: "bet1"}},
		Paging: PagingWithoutTotalCount{
			CurrentPage: 1,
			PageSize:    10,
		},
	}

	// Verify no total count information is available
	pagingStruct := bets.Paging

	// Use reflection-like approach to ensure TotalCount doesn't exist
	switch v := interface{}(pagingStruct).(type) {
	case struct {
		TotalCount  int64
		CurrentPage int
		PageSize    int
	}:
		t.Error("Should not have TotalCount field")
	case struct {
		CurrentPage int
		PageSize    int
	}:
		// This is expected - only CurrentPage and PageSize
		assert.Equal(t, 1, v.CurrentPage)
		assert.Equal(t, 10, v.PageSize)
	default:
		// Check if it matches our expected type
		assert.IsType(t, PagingWithoutTotalCount{}, v)
	}
}
