package greco

type GrecoBetMessage struct {
	ActivityID        string                 `json:"activity_id"`
	RoundID           string                 `json:"round_id"`
	Type              string                 `json:"type"`
	Timestamp         string                 `json:"timestamp"`
	UserID            string                 `json:"user_id"`
	Currency          string                 `json:"currency"`
	GameID            string                 `json:"game_id"`
	GameName          string                 `json:"game_name"`
	VendorID          string                 `json:"vendor_id"`
	VendorName        string                 `json:"vendor_name"`
	Amount            float64                `json:"amount"`
	WagerAmount       float64                `json:"wager_amount"`
	BonusWagerAmount  float64                `json:"bonus_wager_amount"`
	LockedWagerAmount float64                `json:"locked_wager_amount"`
	BalanceAfter      float64                `json:"balance_after"`
	BalanceBefore     float64                `json:"balance_before"`
	Origin            string                 `json:"origin"`
	GameType          string                 `json:"game_type"`
	ExchangeRate      int                    `json:"exchange_rate"`
	IsRoundEnd        bool                   `json:"is_round_end"`
	Status            string                 `json:"status"`
	UserBonusID       string                 `json:"user_bonus_id"`
	Meta              map[string]interface{} `json:"meta"`
}

type GrecoBonusMessage struct {
	BonusID                string  `json:"bonus_id"`
	Amount                 float64 `json:"amount"`
	BonusTurnedReal        float64 `json:"bonus_turned_real"`
	Currency               string  `json:"currency"`
	LockedAmount           float64 `json:"locked_amount"`
	Origin                 string  `json:"origin"`
	Product                string  `json:"product"`
	RequiredWageringAmount float64 `json:"required_wagering_amount"`
	Status                 string  `json:"status"`
	Timestamp              string  `json:"timestamp"`
	Type                   string  `json:"type"`
	UserBonusID            string  `json:"user_bonus_id"`
	UserID                 string  `json:"user_id"`
}
