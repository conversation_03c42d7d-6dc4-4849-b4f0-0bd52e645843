package greco

import (
	"errors"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*BonusMessageHandler)(nil)

type BonusMessageHandler struct{}

func NewBonusMessageHandler() *BonusMessageHandler {
	return &BonusMessageHandler{}
}

func (h *BonusMessageHandler) HandleMessage(message []byte) error {
	return errors.New("not implemented")
}
