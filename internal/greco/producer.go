package greco

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

var _ Producer = (*KafkaProducer)(nil)

type Producer interface {
	SendMessage(topic string, message interface{}) error
}

type KafkaProducer struct {
	producer     *kafka.Producer
	deliveryChan chan kafka.Event
	apiKey       string
	stopChan     chan struct{}
}

func NewKafkaProducer(kafkaConfig *utils.KafkaProducerConfig) (*KafkaProducer, error) {
	p, err := kafka.NewProducer(&kafka.ConfigMap{
		"bootstrap.servers":  kafkaConfig.Brokers,
		"sasl.mechanism":     "SCRAM-SHA-512",
		"security.protocol":  "SASL_SSL",
		"sasl.username":      kafkaConfig.SASLUsername,
		"sasl.password":      kafkaConfig.SASLPassword,
		"partitioner":        "consistent_random",
		"message.timeout.ms": 60000,
	})
	if err != nil {
		return nil, err
	}

	return &KafkaProducer{
		producer:     p,
		apiKey:       kafkaConfig.APIKey,
		deliveryChan: make(chan kafka.Event),
	}, nil
}

func (kp *KafkaProducer) SendMessage(topic string, message interface{}) error {
	messageJSON, err := json.Marshal(message)
	if err != nil {
		return err
	}

	hmacValue := generateHMAC(messageJSON, kp.apiKey)

	messageType := ""
	var key []byte
	switch m := message.(type) {
	case GrecoBetMessage:
		messageType = "CASINO"
		key = []byte(fmt.Sprintf("%s-%s", m.UserID, "CASINO"))
	case GrecoBonusMessage:
		messageType = "BONUS"
		key = []byte(fmt.Sprintf("%s-%s", m.UserID, "BONUS"))
	}

	kafkaHeaders := []kafka.Header{
		{Key: "type", Value: []byte(messageType)},
		{Key: "HMAC", Value: []byte(hmacValue)},
	}

	kafkaMessage := &kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Key:            key,
		Value:          messageJSON,
		Headers:        kafkaHeaders,
	}

	err = kp.producer.Produce(kafkaMessage, kp.deliveryChan)
	if err != nil {
		return err
	}

	return nil
}

func generateHMAC(message []byte, apiKey string) string {
	h := hmac.New(sha256.New, []byte(apiKey))
	h.Write(message)
	return hex.EncodeToString(h.Sum(nil))
}

func (kp *KafkaProducer) Run() {
	go kp.persistenceThread()
}

func (kp *KafkaProducer) Stop() {
	kp.stopChan <- struct{}{}
}

func (kp *KafkaProducer) persistenceThread() {
	for {
		select {
		case event := <-kp.deliveryChan:
			switch ev := event.(type) {
			case *kafka.Message:
				if ev.TopicPartition.Error != nil {
					slog.Error(fmt.Sprintf("Failed to deliver message: %v\n", ev.TopicPartition))
				} else {
					slog.Info(fmt.Sprintf("Successfully produced record to topic %s partition [%d] @ offset %v\n",
						*ev.TopicPartition.Topic, ev.TopicPartition.Partition, ev.TopicPartition.Offset))
				}
			}
		case <-kp.stopChan:
			slog.Info("Stopping kafka producer service")
			for kp.producer.Flush(10000) > 0 {
				slog.Info("Still waiting to flush outstanding messages")
			}
			kp.producer.Close()
			return
		}
	}
}
