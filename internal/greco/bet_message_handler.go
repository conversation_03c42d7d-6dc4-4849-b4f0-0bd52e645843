package greco

import (
	"errors"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*BetMessageHandler)(nil)

type BetMessageHandler struct{}

func NewBetMessageHandler() *BetMessageHandler {
	return &BetMessageHandler{}
}

func (h *BetMessageHandler) HandleMessage(message []byte) error {
	return errors.New("not implemented")
}
