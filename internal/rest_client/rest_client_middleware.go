package rest_client

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"

	"net/http"
	"net/url"
	"strings"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type RestClientMiddleware struct {
	Config *utils.RESTServiceConfig
}

func NewRestClientMiddleware(Config *utils.RESTServiceConfig) *RestClientMiddleware {
	return &RestClientMiddleware{Config: Config}
}

func (c *RestClientMiddleware) MiddlewareForRestClient(authenticationType string) (string, error) {
	var authenticationURL, client_id, client_secret string

	switch authenticationType {
	case "wagering":
		authenticationURL = c.Config.Wagering.AuthenticationURL
		client_id = c.Config.Wagering.ClientID
		client_secret = c.Config.Wagering.ClientSecret
	case "wallet":
		authenticationURL = c.Config.Wallet.AuthenticationURL
		client_id = c.Config.Wallet.ClientID
		client_secret = c.Config.Wallet.ClientSecret
	case "email":
		authenticationURL = c.Config.EmailService.AuthenticationURL
		client_id = c.Config.EmailService.ClientID
		client_secret = c.Config.EmailService.ClientSecret
	default:
		return "", fmt.Errorf("unknown authentication type: %s", authenticationType)
	}
	token, err := getAuthenticationToken(authenticationURL, client_id, client_secret)
	if err != nil {
		slog.Error("Error getting authentication token", "error", err)
		return "", fmt.Errorf("error getting authentication token: %w", err)
	}
	return token, nil

}

func getAuthenticationToken(authenticationURL, client_id, client_secret string) (string, error) {
	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("scope", "openid")
	data.Set("client_id", client_id)
	data.Set("client_secret", client_secret)

	req, err := http.NewRequest(http.MethodPost, authenticationURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	httpClient := &http.Client{}
	resp, err := httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	var tokenResponse struct {
		AccessToken string `json:"access_token"`
	}

	if err := json.Unmarshal(bodyBytes, &tokenResponse); err != nil {
		return "", fmt.Errorf("error decoding response: %w", err)
	}
	return tokenResponse.AccessToken, nil
}
