package rest_client

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	restclientmock "github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/rest_clientmock"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type elantilGamesClientSuite struct {
	suite.Suite
	emClient   *ElantilGamesClient
	httpClient *restclientmock.HTTPClient
}

func (s *elantilGamesClientSuite) SetupSuite() {
	s.httpClient = restclientmock.NewHTTPClient(s.T())
}

func (s *elantilGamesClientSuite) SetupTest() {
	s.emClient = NewElantilGamesClient(s.httpClient, "https://foobar.com")
}

func (s *elantilGamesClientSuite) TestGetAllGames_Success() {
	expectedGames := []domain.Game{
		{
			CMSGameID:    1,
			Name:         utils.PointerOf("Game 1"),
			ExternalID:   "code1",
			VendorGameID: "1",
			Slug:         utils.PointerOf("game1"),
			ThumbnailID:  utils.PointerOf(""),
		},
		{
			CMSGameID:    2,
			Name:         utils.PointerOf("Game 2"),
			ExternalID:   "code2",
			VendorGameID: "1",
			Slug:         utils.PointerOf("game2"),
			ThumbnailID:  utils.PointerOf(""),
		},
	}

	data := `{
	"data": [
		{
		"thumbnail": "",
		"game": {
			"id": 1,
			"status": "active",
			"name": "Game 1",
			"provider": 1,
			"temp_EMGameType": "",
			"code": "code1",
			"temp_EMGameSlug": "game1",
			"temp_EMGameCode": ""
		}
		},
		{
		"thumbnail": "",
		"game": {
			"id": 2,
			"status": "active",
			"name": "Game 2",
			"provider": 1,
			"temp_EMGameType": "",
			"code": "code2",
			"temp_EMGameSlug": "game2",
			"temp_EMGameCode": ""
		}
		}
	]
	}`

	s.httpClient.EXPECT().
		Do(mock.Anything).
		Return(&http.Response{
			StatusCode: http.StatusOK,
			Body:       makeBodyReader([]byte(data)),
		}, nil).
		Once()

	games, err := s.emClient.GetAllGames(context.Background())

	s.Require().NoError(err)
	s.Require().Equal(expectedGames, games)
}

func (s *elantilGamesClientSuite) TestGetAllGames_HTTPError() {
	s.httpClient.EXPECT().
		Do(mock.Anything).
		Return(&http.Response{
			StatusCode: http.StatusInternalServerError,
			Body:       makeBodyReader(nil),
		}, nil).
		Once()

	games, err := s.emClient.GetAllGames(context.Background())

	s.Require().Error(err)
	s.Require().Nil(games)
}

func (s *elantilGamesClientSuite) TestGetAllGames_DecodeError() {
	s.httpClient.EXPECT().
		Do(mock.Anything).
		Return(&http.Response{
			StatusCode: http.StatusOK,
			Body:       makeBodyReader([]byte(`{"invalid": "json}`)),
		}, nil).
		Once()

	games, err := s.emClient.GetAllGames(context.Background())

	s.Require().Error(err)
	s.Require().Nil(games)
}

func TestElantilGamesClientSuite(t *testing.T) {
	suite.Run(t, new(elantilGamesClientSuite))
}

func makeBodyReader(body []byte) io.ReadCloser {
	return io.NopCloser(bytes.NewReader(body))
}
