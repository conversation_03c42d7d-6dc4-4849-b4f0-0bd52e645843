package rest_client

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type OptimoveClient struct {
	HTTPClient HTTPClient
	Config     *utils.RESTServiceConfig
}

func NewOptimoveClient(client HTTPClient, config *utils.RESTServiceConfig) *OptimoveClient {
	return &OptimoveClient{
		HTTPClient: client,
		Config:     config,
	}
}

func (c *OptimoveClient) GetCustomerDetailsByChannel(ctx context.Context, campaignID int32, channelID int32, customerAttribute string) (domain.GetUserInformation, error) {
	
	baseUrl := "https://api5.optimove.net/Customers/GetCustomerSendDetailsByChannel"
	reqUrl := fmt.Sprintf("%s?CampaignID=%d&ChannelID=%d&CustomerAttribute=%s", 
		baseUrl, 
		campaignID, 
		channelID, 
		url.QueryEscape(customerAttribute))

	slog.Info("Sending request to get customer details", "url", reqUrl)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, reqUrl, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return domain.GetUserInformation{}, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("X-API-Key", c.Config.OptimoveConfig.XAPIKey)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making the request", "error", err)
		return domain.GetUserInformation{}, fmt.Errorf("error making request: %w", err)
    }
	  defer resp.Body.Close()

	  respBody, _ := io.ReadAll(resp.Body)
	  if resp.StatusCode != http.StatusOK {
		slog.Error("Unexpected status code while getting the user details", "status_code", resp.StatusCode, "body", string(respBody))
		return domain.GetUserInformation{}, fmt.Errorf("unexpected status code for retrieving user details: %d", resp.StatusCode)
    }

	var userdetailsResponse domain.GetUserInformation
	if err := json.Unmarshal(respBody, &userdetailsResponse); err != nil {
		slog.Error("Error unmarshaling response", "error", err)
		return domain.GetUserInformation{}, fmt.Errorf("error unmarshaling response: %w", err)
	}
	return userdetailsResponse, nil
}

	

