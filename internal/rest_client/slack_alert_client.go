package rest_client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type SlackAlertClient struct {
	HTTPClient HTTPClient
	Config     *utils.RESTServiceConfig
}

func NewSlackAlertClient(httpClient HTTPClient, config *utils.RESTServiceConfig) *SlackAlertClient {
	return &SlackAlertClient{
		HTTPClient: httpClient,
		Config:     config,
	}
}

func (c *SlackAlertClient) SendSlackNotification(userBonus []domain.UserBonus) error {
	bonusThreshold, _ := strconv.ParseFloat(c.Config.SlackConfig.BonusThreshold, 64)
	for _, bonus := range userBonus {
		if bonus.RewardAmount > bonusThreshold {
			details := fmt.Sprintf("User %s (ID: %s) has received a bonus of %.2f for %s bonus, exceeding the threshold of %.2f.", bonus.Username, bonus.ExternalID, bonus.RewardAmount, bonus.Category, bonusThreshold)

			alertData := map[string]string{
				"type":    "alert",
				"message": "Bonus threshold exceeded",
				"details": details,
				"channel": "C08NBQNSEKV",
			}

			jsonData, err := json.Marshal(alertData)
			if err != nil {
				slog.Error("Error creating alert JSON:", "error", err)
				return err
			}

			err = c.SendAlert(jsonData)
			if err != nil {
				slog.Error("Error sending alert:", "error", err)
				return err
			}
			slog.Info("High bonus alert sent successfully")
		}
	}
	return nil
}

func (c *SlackAlertClient) SendAlert(payload []byte) error {
	req, err := http.NewRequest("POST", c.Config.SlackConfig.AlertURL+"/send-alert", bytes.NewBuffer(payload))
	if err != nil {
		slog.Error("error creating alert request", "error", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("error sending alert request", "error", err)
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		slog.Error("alert request failed", "status_code", resp.StatusCode)
		return err
	}

	return nil
}
