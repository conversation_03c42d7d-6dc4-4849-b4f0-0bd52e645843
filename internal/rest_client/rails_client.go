package rest_client

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/validation"
	"github.com/shopspring/decimal"
)

// WithdrawalConfig defines withdrawal configuration for a token
type WithdrawalConfig struct {
	MinAmount     decimal.Decimal
	WithdrawalFee decimal.Decimal
}

// withdrawalConfig defines withdrawal configurations for supported tokens by asset_uid
var withdrawalConfig = map[string]WithdrawalConfig{
	// Bitcoin
	"BTC": {MinAmount: decimal.RequireFromString("0.00035"), WithdrawalFee: decimal.RequireFromString("0.00006")},

	// Ethereum
	"ETH":     {MinAmount: decimal.RequireFromString("0.005"), WithdrawalFee: decimal.RequireFromString("0.0004")},
	"ETH_BSC": {MinAmount: decimal.RequireFromString("0.005"), WithdrawalFee: decimal.RequireFromString("0.0004")},

	// USDC
	"USDC":     {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},
	"USDC_BSC": {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("1.0")},
	"USDC_POL": {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},

	// USDT
	"USDT_ERC20":    {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},
	"USDT_BSC":      {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},
	"TRX_USDT_S2UZ": {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},
	"USDT_POLYGON":  {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("1.0")},
	"USDT_SOL":      {MinAmount: decimal.RequireFromString("10"), WithdrawalFee: decimal.RequireFromString("2.5")},

	// Matic
	"MATIC_POLYGON_POL": {MinAmount: decimal.RequireFromString("50"), WithdrawalFee: decimal.RequireFromString("4.0")},
	"MATIC_POLYGON_ETH": {MinAmount: decimal.RequireFromString("50"), WithdrawalFee: decimal.RequireFromString("4.0")},

	// Other tokens
	"LTC":     {MinAmount: decimal.RequireFromString("0.06"), WithdrawalFee: decimal.RequireFromString("0.001")},
	"SHIB":    {MinAmount: decimal.RequireFromString("600000"), WithdrawalFee: decimal.RequireFromString("125000")},
	"BNB_BSC": {MinAmount: decimal.RequireFromString("0.015"), WithdrawalFee: decimal.RequireFromString("0.0002")},
	"XRP":     {MinAmount: decimal.RequireFromString("5"), WithdrawalFee: decimal.RequireFromString("0.01")},
	"TRX":     {MinAmount: decimal.RequireFromString("35"), WithdrawalFee: decimal.RequireFromString("1")},
	"DOGE":    {MinAmount: decimal.RequireFromString("30"), WithdrawalFee: decimal.RequireFromString("1.5")},
	"SOL":     {MinAmount: decimal.RequireFromString("0.1"), WithdrawalFee: decimal.RequireFromString("0.01")},
	"BCH":     {MinAmount: decimal.RequireFromString("0.01"), WithdrawalFee: decimal.RequireFromString("0.002")},
}

// GetWithdrawalConfig returns the withdrawal configuration for a given asset UID
func GetWithdrawalConfig(assetUID string) (WithdrawalConfig, bool) {
	config, exists := withdrawalConfig[assetUID]
	return config, exists
}

// GetAllWithdrawalConfigs returns a copy of all withdrawal configurations
func GetAllWithdrawalConfigs() map[string]WithdrawalConfig {
	// Return a copy to prevent modification of the original map
	configsCopy := make(map[string]WithdrawalConfig, len(withdrawalConfig))
	for k, v := range withdrawalConfig {
		configsCopy[k] = v
	}
	return configsCopy
}

// tokenPopularityOrder defines the order of tokens by popularity (lower index = higher popularity)
var tokenPopularityOrder = map[string]int{
	// Top stablecoins and majors
	"USD Coin":     0,
	"Tether":       1,
	"Bitcoin":      2,
	"Ethereum":     3,
	"Solana":       4,
	"Tron":         5,
	"Binance Coin": 6,
	"Dogecoin":     7,
	"Ripple":       8,
	"Litecoin":     9,
	"Shiba Inu":    10,
}

// getTokenPopularityOrder returns the popularity order for a token (lower number = higher popularity)
func getTokenPopularityOrder(token string) int {
	if order, exists := tokenPopularityOrder[token]; exists {
		return order
	}
	// Unknown tokens go to the end
	return math.MaxInt
}

type RailsClient struct {
	httpClient *http.Client
	config     *utils.RESTServiceConfig
	wagering   domain.ElantilWageringClient
}

func NewRailsClient(httpClient *http.Client, config *utils.RESTServiceConfig, wagering domain.ElantilWageringClient) *RailsClient {
	return &RailsClient{
		httpClient: httpClient,
		config:     config,
		wagering:   wagering,
	}
}

func (r *RailsClient) CreateDepositAddressOfUser(ctx context.Context, userID string) error {

	reqBody := map[string]interface{}{
		"player_id": userID,
	}
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		slog.Error("Failed to marshal request body", "error", err)
		return domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}
	secretKey := []byte(r.config.RailsClient.SecretKey)
	h := hmac.New(sha512.New, secretKey)
	h.Write(jsonBody)
	hash := hex.EncodeToString(h.Sum(nil))

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/v1/users", r.config.RailsClient.BaseUrl), bytes.NewBuffer(jsonBody))
	slog.Info("Request", "url", fmt.Sprintf("%s/v1/users", r.config.RailsClient.BaseUrl))
	if err != nil {
		slog.Error("Failed to create request", "error", err)
		return domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Digest", fmt.Sprintf("SHA512=%s", hash))

	resp, err := r.httpClient.Do(req)
	if err != nil {
		slog.Error("Failed to make request", "error", err)
		return domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, err.Error())
	}
	defer closeResponseBody(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("Failed to read response body", "error", err)
		return domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != 409 && resp.StatusCode != 201 {
		// Try to parse Rails error response
		var railsError struct {
			Error   string `json:"error"`
			Message string `json:"message"`
			Code    string `json:"code"`
		}
		if err := json.Unmarshal(body, &railsError); err == nil {
			// Map Rails error codes to our error codes
			var errorCode string
			switch railsError.Code {
			case "INVALID_ADDRESS":
				errorCode = domain.ErrInvalidDepositAddress
			case "UNSUPPORTED_TOKEN":
				errorCode = domain.ErrDepositAddressUnsupported
			default:
				errorCode = domain.ErrDepositAddressValidation
			}
			return domain.NewRailsClientError(errorCode, railsError.Message)
		}

		slog.Error("Request failed",
			"status", resp.StatusCode,
			"body", string(body),
			"headers", resp.Header)
		return domain.NewRailsClientError(domain.ErrRailsInternalError, fmt.Sprintf("request failed with status %d: %s", resp.StatusCode, string(body)))
	}

	slog.Info("Deposit address created", "status", resp.StatusCode)
	return nil
}

func (r *RailsClient) GetUserDepositAddresses(ctx context.Context, userID string) ([]domain.TokenAddress, error) {
	h := sha512.New()
	h.Write([]byte(""))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("%s/v1/addresses/%s", r.config.RailsClient.BaseUrl, userID), nil)
	fullURL := fmt.Sprintf("%s/v1/addresses/%s", r.config.RailsClient.BaseUrl, userID)
	slog.Info("Making Rails client request", "method", "GET", "url", fullURL, "base_url", r.config.RailsClient.BaseUrl, "userID", userID)
	if err != nil {
		slog.Error("Failed to create request", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}
	req.Header.Set("Digest", fmt.Sprintf("SHA512=%s", signature))

	resp, err := r.httpClient.Do(req)
	if err != nil {
		slog.Error("Failed to make request", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, err.Error())
	}
	defer closeResponseBody(resp.Body)

	// Log the raw response body from crypto rails service
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("Failed to read response body", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}
	slog.Debug("Rails service response", "status", resp.StatusCode, "body_length", len(body))

	if resp.StatusCode != http.StatusOK {
		var errorResp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}
		// Try to parse error response
		if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.Message != "" {
			slog.Error("Request failed with error message",
				"status", resp.StatusCode,
				"code", errorResp.Code,
				"message", errorResp.Message)
			return nil, domain.NewRailsClientError(domain.ErrDepositAddressValidation, errorResp.Message)
		}
		// Fallback error handling
		slog.Error("Request failed",
			"status", resp.StatusCode,
			"body", string(body),
			"headers", resp.Header)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, fmt.Sprintf("request failed with status %d: %s", resp.StatusCode, string(body)))
	}

	var addresses []domain.TokenAddress
	if err := json.Unmarshal(body, &addresses); err != nil {
		slog.Error("Failed to decode response body", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	// Add withdrawal configs to addresses
	addressesWithConfig, err := r.addWithdrawalConfigToAddresses(addresses)
	if err != nil {
		slog.Error("Failed to add withdrawal configs", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	return addressesWithConfig, nil
}

// addWithdrawalConfigToAddresses adds withdrawal configuration to each address and filters out unsupported tokens
func (r *RailsClient) addWithdrawalConfigToAddresses(addresses []domain.TokenAddress) ([]domain.TokenAddress, error) {
	var filteredAddresses []domain.TokenAddress

	for _, address := range addresses {

		address.DisplayOrder = getTokenPopularityOrder(address.Token)

		var validNetworkData []domain.GetAddressesResponse
		for _, networkData := range address.NetworkData {
			slog.Debug("Processing network data",
				"assetUID", networkData.AssetUID,
				"network_ticker", networkData.NetworkTicker,
				"token_ticker", networkData.TokenTicker,
			)
			assetUID := networkData.AssetUID
			if config, exists := GetWithdrawalConfig(assetUID); exists {
				networkData.MinWithdrawal = config.MinAmount
				networkData.WithdrawalFee = config.WithdrawalFee
				slog.Debug("Added withdrawal config",
					"assetUID", assetUID,
					"min_withdrawal", config.MinAmount,
					"withdrawal_fee", config.WithdrawalFee,
				)
				validNetworkData = append(validNetworkData, networkData)
			} else {
				slog.Warn("No withdrawal configuration found for asset, filtering out", "assetUID", assetUID)
			}
		}

		// Only include addresses that have at least one valid network data
		if len(validNetworkData) > 0 {
			address.NetworkData = validNetworkData
			slog.Debug("Address included in response", "token", address.Token, "network_count", len(validNetworkData))
			filteredAddresses = append(filteredAddresses, address)
		} else {
			slog.Warn("Address excluded (no valid network data)", "token", address.Token)
		}
	}

	slog.Info("Processed deposit addresses", "total_addresses", len(addresses), "filtered_addresses", len(filteredAddresses))
	return filteredAddresses, nil
}

// CreateWithdrawalRequest submits a withdrawal request to the crypto rails service
// This function handles the complete withdrawal flow including validation, request preparation,
// HTTP communication, and response processing with extensive logging for debugging
func (r *RailsClient) CreateWithdrawalRequest(ctx context.Context, request domain.CreateWithdrawalRequest) error {
	// Initialize timing and unique request tracking for debugging purposes
	start := time.Now()
	requestID := fmt.Sprintf("withdrawal_%s_%d", request.UserID, time.Now().UnixNano())

	// Log the initial withdrawal request with all key parameters
	slog.Info("Starting withdrawal request",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"requestedAmount", request.Amount,
		"address", request.WithdrawalAddress,
		"baseURL", r.config.RailsClient.BaseUrl)

	// Phase 1: Validate the withdrawal request
	// This includes checking asset support, address validation, amount validation,
	// minimum amount checks, and user balance verification
	withdrawalAmount, withdrawalFee, err := r.validateWithdrawalRequest(ctx, request)
	if err != nil {
		slog.Error("Withdrawal request validation failed",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"error", err,
			"duration_ms", time.Since(start).Milliseconds())
		return err
	}

	// Phase 2: Prepare request data with calculated fees
	// The withdrawal fee and amount after fee are calculated server-side
	// to ensure consistency and prevent client manipulation
	request.WithdrawalFee = withdrawalFee
	request.AmountAfterFee = withdrawalAmount.Sub(withdrawalFee)

	slog.Info("Withdrawal request validation completed",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"withdrawalAmount", withdrawalAmount,
		"withdrawalFee", withdrawalFee,
		"amountAfterFee", request.AmountAfterFee,
		"validation_duration_ms", time.Since(start).Milliseconds())

	// Phase 3: Serialize the request body to JSON
	// This converts the Go struct to JSON format for the HTTP request body
	jsonBody, err := json.Marshal(request)
	if err != nil {
		slog.Error("Failed to marshal withdrawal request body",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"error", err,
			"duration_ms", time.Since(start).Milliseconds())
		return domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	// Log the prepared JSON body for debugging (contains sensitive data - use debug level)
	slog.Debug("Withdrawal request JSON body prepared",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"bodyLength", len(jsonBody),
		"body", string(jsonBody))

	// Phase 4: Generate authentication signature
	// Create HMAC-SHA512 signature for request authentication to crypto rails
	secretKey := []byte(r.config.RailsClient.SecretKey)
	h := hmac.New(sha512.New, secretKey)
	h.Write(jsonBody)
	hash := hex.EncodeToString(h.Sum(nil))

	// Phase 5: Prepare HTTP request with authentication headers
	fullURL := fmt.Sprintf("%s/v1/withdrawals", r.config.RailsClient.BaseUrl)
	slog.Info("Creating withdrawal HTTP request",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"method", "POST",
		"url", fullURL,
		"digestHash", hash[:16]+"...")

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fullURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		slog.Error("Failed to create withdrawal HTTP request",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"error", err,
			"duration_ms", time.Since(start).Milliseconds())
		return domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}

	// Set required headers for crypto rails API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Digest", fmt.Sprintf("SHA512=%s", hash))

	slog.Info("Withdrawal request headers set",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"contentType", req.Header.Get("Content-Type"),
		"hasDigest", req.Header.Get("Digest") != "",
		"userAgent", req.Header.Get("User-Agent"))

	httpStart := time.Now()
	slog.Info("Sending withdrawal request to crypto rails",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"url", fullURL,
		"preparation_duration_ms", httpStart.Sub(start).Milliseconds())

	resp, err := r.httpClient.Do(req)
	httpDuration := time.Since(httpStart)

	if err != nil {
		slog.Error("Withdrawal HTTP request failed",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"error", err,
			"http_duration_ms", httpDuration.Milliseconds(),
			"total_duration_ms", time.Since(start).Milliseconds(),
			"url", fullURL)
		return domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, err.Error())
	}

	defer closeResponseBody(resp.Body)

	slog.Info("Withdrawal response received from crypto rails",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"statusCode", resp.StatusCode,
		"status", resp.Status,
		"contentLength", resp.ContentLength,
		"contentType", resp.Header.Get("Content-Type"),
		"http_duration_ms", httpDuration.Milliseconds())

	// Read response body for logging
	body, bodyReadErr := io.ReadAll(resp.Body)
	if bodyReadErr != nil {
		slog.Error("Failed to read withdrawal response body",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"statusCode", resp.StatusCode,
			"error", bodyReadErr,
			"duration_ms", time.Since(start).Milliseconds())
		return domain.NewRailsClientError(domain.ErrRailsInternalError, bodyReadErr.Error())
	}

	slog.Info("Withdrawal response body read",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"statusCode", resp.StatusCode,
		"bodyLength", len(body),
		"bodyPreview", func() string {
			if len(body) > 500 {
				return string(body)[:500]
			}
			return string(body)
		}(),
		"responseHeaders", resp.Header)

	if resp.StatusCode != http.StatusOK {
		slog.Error("Withdrawal request returned non-OK status",
			"requestID", requestID,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"statusCode", resp.StatusCode,
			"status", resp.Status,
			"responseBody", string(body),
			"responseHeaders", resp.Header,
			"duration_ms", time.Since(start).Milliseconds())

		// Try to parse error response
		var errorResp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}
		if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.Message != "" {
			slog.Error("Withdrawal request failed with structured error",
				"requestID", requestID,
				"status", resp.StatusCode,
				"errorCode", errorResp.Code,
				"errorMessage", errorResp.Message,
				"userID", request.UserID,
				"asset", request.AssetUID,
				"duration_ms", time.Since(start).Milliseconds())
			return domain.NewRailsClientError(domain.ErrWithdrawalValidation, errorResp.Message)
		} else if err != nil {
			slog.Warn("Failed to parse withdrawal error response as structured JSON",
				"requestID", requestID,
				"userID", request.UserID,
				"asset", request.AssetUID,
				"parseError", err,
				"rawBody", string(body))
		}

		// Fallback error handling
		slog.Error("Withdrawal request failed with unstructured error",
			"requestID", requestID,
			"status", resp.StatusCode,
			"body", string(body),
			"headers", resp.Header,
			"userID", request.UserID,
			"asset", request.AssetUID,
			"duration_ms", time.Since(start).Milliseconds())
		return domain.NewRailsClientError(domain.ErrRailsInternalError, fmt.Sprintf("request failed with status %d: %s", resp.StatusCode, string(body)))
	}

	slog.Info("Withdrawal request completed successfully",
		"requestID", requestID,
		"userID", request.UserID,
		"asset", request.AssetUID,
		"statusCode", resp.StatusCode,
		"responseBodyLength", len(body),
		"responseBody", string(body),
		"total_duration_ms", time.Since(start).Milliseconds(),
		"http_duration_ms", httpDuration.Milliseconds())

	return nil
}

// validateWithdrawalRequest validates the withdrawal request and returns the withdrawal amount and the fee
func (r *RailsClient) validateWithdrawalRequest(ctx context.Context, request domain.CreateWithdrawalRequest) (decimal.Decimal, decimal.Decimal, error) {
	// Check if the asset is supported
	config, exists := GetWithdrawalConfig(request.AssetUID)
	if !exists {
		slog.Error("Unsupported asset for withdrawal", "asset", request.AssetUID)
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalUnsupported, fmt.Sprintf("unsupported asset: %s", request.AssetUID))
	}

	// Validate withdrawal address
	if err := validation.ValidateAddress(request.WithdrawalAddress, request.AssetUID); err != nil {
		slog.Error("Invalid withdrawal address",
			"asset", request.AssetUID,
			"address", request.WithdrawalAddress,
			"error", err)
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrInvalidWithdrawalAddress, err.Error())
	}

	// Parse the withdrawal amount with input sanitization
	withdrawalAmount, err := decimal.NewFromString(strings.TrimSpace(request.Amount))
	if err != nil {
		slog.Error("Invalid withdrawal amount", "amount", request.Amount, "error", err)
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalValidation, fmt.Sprintf("invalid withdrawal amount: %s", request.Amount))
	}

	// Ensure calculated fields are not provided in the request
	if !request.WithdrawalFee.IsZero() || !request.AmountAfterFee.IsZero() {
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalValidation, "withdrawal_fee and amount_after_fee are calculated by server")
	}

	// Check for zero or negative amounts
	if withdrawalAmount.LessThanOrEqual(decimal.Zero) {
		slog.Error("Invalid withdrawal amount", "amount", withdrawalAmount.String())
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalValidation, "withdrawal amount must be greater than 0")
	}

	// Check minimum withdrawal amount
	if withdrawalAmount.LessThan(config.MinAmount) {
		slog.Error("Withdrawal amount below minimum",
			"asset", request.AssetUID,
			"amount", withdrawalAmount.String(),
			"minimum", config.MinAmount.String())
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalValidation, fmt.Sprintf("withdrawal amount %s %s is below minimum of %s %s",
			withdrawalAmount.String(), request.AssetUID, config.MinAmount.String(), request.AssetUID))
	}

	// Calculate the amount after fee and ensure it's meaningful
	amountAfterFee := withdrawalAmount.Sub(config.WithdrawalFee)
	if amountAfterFee.LessThanOrEqual(decimal.Zero) {
		slog.Error("Withdrawal amount is not positive after fee deduction",
			"asset", request.AssetUID,
			"amount", withdrawalAmount.String(),
			"fee", config.WithdrawalFee.String(),
			"amountAfterFee", amountAfterFee.String())
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrWithdrawalValidation, "withdrawal amount is not positive after fee deduction")
	}

	// Check the user wallet balance for the total amount needed (user's balance is debited the full amount)
	err = r.wagering.CheckUserWalletBalance(ctx, request.UserID, request.AssetUID, withdrawalAmount.String())
	if err != nil {
		slog.Error("Insufficient balance for withdrawal",
			"userID", request.UserID,
			"asset", request.AssetUID,
			"requestedAmount", withdrawalAmount.String(),
			"amountAfterFee", amountAfterFee.String(),
			"fee", config.WithdrawalFee.String(),
			"error", err)
		return decimal.Zero, decimal.Zero, domain.NewRailsClientError(domain.ErrInsufficientBalance, fmt.Sprintf("insufficient balance: need %s %s for withdrawal", withdrawalAmount.String(), request.AssetUID))
	}

	slog.Info("Withdrawal request validated",
		"userID", request.UserID,
		"asset", request.AssetUID,
		"requestedAmount", withdrawalAmount.String(),
		"fee", config.WithdrawalFee.String(),
		"amountAfterFee", amountAfterFee.String())

	return withdrawalAmount, config.WithdrawalFee, nil
}

func (r *RailsClient) GetTransactionExplorerLink(ctx context.Context, userID string) (map[string]string, error) {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		slog.Info("Crypto rails explorer call completed",
			"userID", userID,
			"duration_ms", duration.Milliseconds(),
			"duration", duration.String())
	}()

	h := sha512.New()
	h.Write([]byte(""))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("%s/v1/transactions/explorer/%s", r.config.RailsClient.BaseUrl, userID), nil)
	fullURL := fmt.Sprintf("%s/v1/transactions/explorer/%s", r.config.RailsClient.BaseUrl, userID)
	slog.Info("Making Rails client request", "method", "GET", "url", fullURL, "base_url", r.config.RailsClient.BaseUrl, "userID", userID)
	if err != nil {
		slog.Error("Failed to create request", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}
	req.Header.Set("Digest", fmt.Sprintf("SHA512=%s", signature))

	resp, err := r.httpClient.Do(req)
	if err != nil {
		slog.Error("Failed to make request", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, err.Error())
	}
	defer closeResponseBody(resp.Body)

	// Log the raw response body from crypto rails service
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("Failed to read response body", "error", err)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, err.Error())
	}
	slog.Debug("Rails service response", "status", resp.StatusCode, "body_length", len(body))

	if resp.StatusCode != http.StatusOK {
		var errorResp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}
		// Try to parse error response
		if err := json.Unmarshal(body, &errorResp); err == nil && errorResp.Message != "" {
			slog.Error("Request failed with error message",
				"status", resp.StatusCode,
				"code", errorResp.Code,
				"message", errorResp.Message)
			return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, errorResp.Message)
		}
		// Fallback error handling
		slog.Error("Request failed",
			"status", resp.StatusCode,
			"body", string(body),
			"headers", resp.Header)
		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, fmt.Sprintf("request failed with status %d: %s", resp.StatusCode, string(body)))
	}

	var response map[string]string
	if err := json.Unmarshal(body, &response); err != nil {
		slog.Error("Failed to decode response body as map[string]string", "error", err)

		// Try to parse as a wrapped response with the transaction_explorers field
		var wrappedResponse struct {
			TransactionExplorers map[string]string `json:"transaction_explorers"`
		}
		if err := json.Unmarshal(body, &wrappedResponse); err == nil && wrappedResponse.TransactionExplorers != nil {
			slog.Info("Found transaction_explorers response structure", "userID", userID, "explorer_links_count", len(wrappedResponse.TransactionExplorers))
			return wrappedResponse.TransactionExplorers, nil
		} else if err != nil {
			slog.Debug("Failed to parse as transaction_explorers structure", "error", err)
		}

		// Try to parse as a different structure - maybe it's wrapped in a data field
		var dataResponse struct {
			Data map[string]string `json:"data"`
		}
		if err := json.Unmarshal(body, &dataResponse); err == nil && dataResponse.Data != nil {
			slog.Info("Found wrapped response structure", "userID", userID, "explorer_links_count", len(dataResponse.Data))
			return dataResponse.Data, nil
		} else if err != nil {
			slog.Debug("Failed to parse as data structure", "error", err)
		}

		// Try to parse as an array of objects
		var arrayResponse []map[string]interface{}
		if err := json.Unmarshal(body, &arrayResponse); err == nil {
			// Convert the array to map if possible
			result := make(map[string]string)
			for _, item := range arrayResponse {
				if key, ok := item["asset"].(string); ok {
					if value, ok := item["explorer_url"].(string); ok {
						result[key] = value
					}
				}
			}
			if len(result) > 0 {
				slog.Info("Converted array response to map", "userID", userID, "explorer_links_count", len(result))
				return result, nil
			}
		} else {
			slog.Debug("Failed to parse as array structure", "error", err)
		}

		return nil, domain.NewRailsClientError(domain.ErrRailsInternalError, "failed to parse response in any expected format")
	}

	slog.Info("Transaction explorer links retrieved", "userID", userID, "explorer_links_count", len(response))
	return response, nil
}

func closeResponseBody(body io.ReadCloser) {
	if err := body.Close(); err != nil {
		slog.Error("Failed to close response body", "error", err)
	}
}
