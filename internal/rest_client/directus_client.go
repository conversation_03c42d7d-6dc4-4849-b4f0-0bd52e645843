package rest_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type VIPTiersResponse struct {
	Data []VIPTier `json:"data"`
}
type VIPTier struct {
	Tier           string `json:"tier"`
	ThresholdValue string `json:"threshold"`
	Description    string `json:"description"`
}
type DirectusCMSClient struct {
	httpClient HTTPClient
	config     *utils.RESTServiceConfig
}

func NewDirectusCmsClient(client HTTPClient, config *utils.RESTServiceConfig) *DirectusCMSClient {
	return &DirectusCMSClient{
		httpClient: client,
		config:     config,
	}
}

func (c *DirectusCMSClient) UpdateVipTiersConfig() error {
	directus_url := fmt.Sprintf(`%s/vipTiers`, c.config.Directus.URL)
	slog.Info("sending request to Directus CMS to update VIP tiers", slog.String("url", directus_url))

	req, err := http.NewRequest(http.MethodGet, directus_url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	apiKey := fmt.Sprintf("Bearer %s", c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", apiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Error("error sending request to Directus CMS", "error", err)
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", res.StatusCode)
	}

	var response VIPTiersResponse
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get current working directory: %w", err)
	}

	// Construct the path to the internal/service directory
	dirPath := filepath.Join(currentDir, "internal", "service")

	// Ensure the directory exists
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	fileName := filepath.Join(dirPath, "vip_tiers_updated.json")

	f, err := os.Create(fileName)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer f.Close()

	encoder := json.NewEncoder(f)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(response); err != nil {
		return fmt.Errorf("failed to write to file: %w", err)
	}
	slog.Info("VIP tiers config updated successfully", "file", fileName)
	return nil
}

func (c *DirectusCMSClient) GetBonusConfigurationFromCMS() (domain.BonusConfigResponse, error) {
	var data domain.BonusConfigResponse
	directus_url := fmt.Sprintf(`%s/bonuses`, c.config.Directus.URL)
	slog.Info("sending request to Directus CMS to get bonus configuration", "url", directus_url)

	req, err := http.NewRequest(http.MethodGet, directus_url, nil)
	if err != nil {
		return data, err
	}

	apiKey := fmt.Sprintf("Bearer %s", c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", apiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		return data, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return data, fmt.Errorf("unexpected status code: %d", res.StatusCode)
	}

	var response domain.BonusConfigResponse
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return data, err
	}
	return response, nil
}

func (c *DirectusCMSClient) CreateUserBonus(data domain.UserBonusInDirectus) (int, error) {

	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Creating user bonus at URL: %s", "directusURL ", directusURL)

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return 0, fmt.Errorf("error marshalling user bonus data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return 0, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return 0, fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		return 0, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	var response struct {
		Data struct {
			ID int `json:"id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(respData, &response); err != nil {
		slog.Error("Error parsing response", "error", err)
		return 0, fmt.Errorf("error parsing response: %w", err)
	}

	return response.Data.ID, nil

}

func (c *DirectusCMSClient) ClaimBonus(externalID string, category string, id int) (bool, error) {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)

	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"_and": []map[string]interface{}{
					{
						"id": map[string]int{
							"_eq": id,
						},
					},
					{
						"userId": map[string]string{
							"_eq": externalID,
						},
					},
					{
						"category": map[string]interface{}{
							"_eq": category,
						},
					},
				},
			},
		},
		"data": map[string]string{
			"status": "claimed",
		},
	}

	slog.Info("Claiming bonus at URL: %s", "directusURL ", directusURL)
	slog.Info("Query data: ", "data", updateData)

	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return false, fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return false, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return false, fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return false, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}
	return true, nil
}

func (c *DirectusCMSClient) DeleteExpiredBonuses() error {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Deleting expired bonuses at: %s", "url", directusURL)
	now := time.Now()
	slog.Info("Current time", "time", now.Format(time.RFC3339))
	query := `
	{
			"query": {
				"filter": {
					"_and": [
						{
							"expiresOn": {
								"_lte": "` + now.Format(time.RFC3339) + `"
							}
						},
						{
							"status": {
								"_eq": "active"
							}
						}
					]
			}
		}
	}`

	req, err := http.NewRequest(http.MethodDelete, directusURL, bytes.NewBuffer([]byte(query)))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")
	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	return nil
}

func (c *DirectusCMSClient) DeleteBonusAfterClaimed() error {
	directusCMSUrl := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Deleting claimed bonuses at: %s", "url", directusCMSUrl)

	query := `
	{
		"query": {
			"filter": {
				"status": {
					"_eq": "claimed"
					}
				},
			}
		}`

	req, err := http.NewRequest(http.MethodDelete, directusCMSUrl, bytes.NewBuffer([]byte(query)))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")
	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()
	return nil
}

func (c *DirectusCMSClient) ClaimMultipleBonuses(userExternalID string, categories []string) (bool, error) {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Claiming multiple bonuses at: %s", "url", directusURL)

	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"_and": []map[string]interface{}{
					{
						"userId": map[string]string{
							"_eq": userExternalID,
						},
					},
					{
						"category": map[string]interface{}{
							"_in": categories,
						},
					},
				},
			},
		},
		"data": map[string]interface{}{
			"status": "claimed",
		},
	}
	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return false, fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return false, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return false, fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return false, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	var response struct {
		Data json.RawMessage `json:"data"`
	}
	if err := json.Unmarshal(respData, &response); err != nil {
		return false, fmt.Errorf("error parsing response: %w", err)
	}
	var dataArray []interface{}
	err = json.Unmarshal(response.Data, &dataArray)
	if err == nil {
		return len(dataArray) > 0, nil
	}

	var dataObject struct {
		UpdatedFields int `json:"updated_fields"`
	}
	err = json.Unmarshal(response.Data, &dataObject)
	if err != nil {
		return false, fmt.Errorf("unexpected response format: %s", string(respData))
	}

	return dataObject.UpdatedFields > 0, nil
}

func (c *DirectusCMSClient) DeleteAllBonuses() error {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Deleting all bonuses at: %s", "url", directusURL)

	query := `
	{
		"query": {
			"limit": -1
}
	}
	`

	req, err := http.NewRequest(http.MethodDelete, directusURL, bytes.NewBuffer([]byte(query)))
	if err != nil {
		slog.Error("error creating HTTP request", "error", err)
		return err
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Error("error sending HTTP request", "error", err)
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != 204 {
		slog.Error("unexpected status code", "status", res.StatusCode)
		return err
	}

	slog.Info("All bonuses deleted successfully")

	return nil
}

func (c *DirectusCMSClient) SetSpecialBonusExpiryDate(category string, expiryDate time.Time, bonusId int) error {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Setting special bonus expiry date at: %s", "url", directusURL)

	query := `
	{
		"query": {
			"filter": {
				"id": {
					"_eq": ` + fmt.Sprintf("%d", bonusId) + `
				}
			}
		},
		"data": {
			"expiresOn": "` + expiryDate.Format(time.RFC3339) + `"
		}
	}
	`

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer([]byte(query)))
	if err != nil {
		slog.Error("error creating HTTP request", "error", err)
		return err
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Error("error sending HTTP request", "error", err)
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != 204 {
		slog.Error("unexpected status code", "status", res.StatusCode)
		return err
	}
	slog.Info("Bonus expiry date set successfully")

	return nil
}

func (c *DirectusCMSClient) UpsertReferredUsersInDirectus(userID string, referralCode string, newReferredUsers []domain.ReferredUser) error {
	baseURL := fmt.Sprintf(`%s/userReferrals`, c.config.Directus.URL)
	params := url.Values{}
	params.Add("filter[userId][_eq]", userID)
	checkURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	slog.Info("Check URL", "checkURL", checkURL)

	req, err := http.NewRequest(http.MethodGet, checkURL, nil)
	if err != nil {
		slog.Info("Error creating HTTP request to check existing user", "error", err)
		return fmt.Errorf("error creating HTTP request to check existing user: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request to check existing user: %w", err)
	}
	defer res.Body.Close()

	var checkResponse struct {
		Data []struct {
			ID                          json.Number     `json:"id"`
			Campaigns                   json.RawMessage `json:"campaigns"`
			DefaultCommissionPercentage string          `json:"defaultCommissionPercentage"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&checkResponse); err != nil {
		return fmt.Errorf("error decoding check response: %w", err)
	}

	if len(checkResponse.Data) == 0 {
		return fmt.Errorf("user not found: %s", userID)
	}

	var existingID string
	var campaigns []map[string]interface{}
	campaignFound := false

	// Iterate through all entries in the response
	for _, entry := range checkResponse.Data {
		var entryCampaigns []map[string]interface{}
		if err := json.Unmarshal(entry.Campaigns, &entryCampaigns); err != nil {
			slog.Info("Error parsing campaigns for entry", "error", err, "entryID", entry.ID)
			continue
		}

		// Check if the referral code exists in this entry's campaigns
		for i, campaign := range entryCampaigns {
			if campaign["code"] == referralCode {
				campaignFound = true
				existingID = entry.ID.String()
				campaigns = entryCampaigns

				// Update referred users
				existingReferredUsers, ok := campaign["referredUsers"].([]interface{})
				if !ok {
					existingReferredUsers = []interface{}{}
				}
				for _, newUser := range newReferredUsers {
					existingReferredUsers = append(existingReferredUsers, map[string]interface{}{
						"userId":   newUser.UserID,
						"username": newUser.Username,
					})
				}
				campaigns[i]["referredUsers"] = existingReferredUsers
				break
			}
		}

		if campaignFound {
			break
		}
	}

	if !campaignFound {
		return fmt.Errorf("campaign with referral code not found: %s", referralCode)
	}

	// Prepare the request body for updating
	requestBody := map[string]interface{}{
		"campaigns": campaigns,
	}
	dataBytes, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("error marshalling referral campaign data: %w", err)
	}

	updateURL := fmt.Sprintf("%s/%s", baseURL, existingID)
	req, err = http.NewRequest(http.MethodPatch, updateURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err = c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		bodyBytes, _ := io.ReadAll(res.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(bodyBytes))
	}

	slog.Info("Successfully updated referred users in Directus")
	return nil
}

func (c *DirectusCMSClient) CreateCampaignInDirectus(referralCampaign domain.ReferralCampaign) error {
	baseURL := fmt.Sprintf(`%s/userReferrals`, c.config.Directus.URL)

	checkURL := fmt.Sprintf("%s?filter[userId][_eq]=%s", baseURL, referralCampaign.UserID)
	req, err := http.NewRequest(http.MethodGet, checkURL, nil)
	if err != nil {
		slog.Info("Error creating HTTP request to check existing user", "error", err)
		return fmt.Errorf("error creating HTTP request to check existing user: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Info("Error sending HTTP request to check existing user", "error", err)
		return fmt.Errorf("error sending HTTP request to check existing user: %w", err)
	}
	defer res.Body.Close()

	var checkResponse struct {
		Data []struct {
			Campaigns                   json.RawMessage `json:"campaigns"`
			DefaultCommissionPercentage string          `json:"defaultCommissionPercentage"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&checkResponse); err != nil {
		return fmt.Errorf("error decoding check response: %w", err)
	}

	// Prepare the new campaign data
	newCampaign := referralCampaign.Campaigns[0]
	campaignData := map[string]interface{}{
		"code":                 newCampaign.ReferralCode,
		"name":                 newCampaign.CampaignName,
		"commissionPercentage": fmt.Sprintf("%.2f", newCampaign.CommissionPercentage),
		"referredUsers":        []interface{}{},
	}

	var method string
	var requestBody map[string]interface{}

	if len(checkResponse.Data) == 0 {
		// No existing record, create new
		slog.Info("No existing record found, create new")
		method = http.MethodPost
		requestBody = map[string]interface{}{
			"userId":                      referralCampaign.UserID,
			"username":                    referralCampaign.Username,
			"parentId":                    referralCampaign.ParentID,
			"campaigns":                   []interface{}{campaignData},
			"defaultCommissionPercentage": 10.0,
		}
	} else {
		// Existing record found, update
		slog.Info("Existing record found, update")
		method = http.MethodPatch
		var existingCampaigns []interface{}
		if err := json.Unmarshal(checkResponse.Data[0].Campaigns, &existingCampaigns); err != nil {
			slog.Info("Error parsing existing campaigns", "error", err)
			return fmt.Errorf("error parsing existing campaigns: %w", err)
		}
		existingCampaigns = append(existingCampaigns, campaignData)
		requestBody = map[string]interface{}{
			"query": map[string]interface{}{
				"filter": map[string]interface{}{
					"userId": map[string]string{
						"_eq": referralCampaign.UserID,
					},
				},
			},
			"data": map[string]interface{}{
				"campaigns": existingCampaigns,
			},
		}
	}

	dataBytes, err := json.Marshal(requestBody)
	if err != nil {
		slog.Info("Error marshalling referral campaign data", "error", err)
		return fmt.Errorf("error marshalling referral campaign data: %w", err)
	}
	slog.Info("Data bytes to send the request", "dataBytes", string(dataBytes))

	// Create the request
	req, err = http.NewRequest(method, baseURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		slog.Info("Error creating HTTP request", "error", err)
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	// Log full request details
	slog.Info("Sending request",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
		"body", string(dataBytes))

	// Send the request
	res, err = c.httpClient.Do(req)
	if err != nil {
		slog.Info("Error sending HTTP request", "error", err)
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	// Log response details
	bodyBytes, _ := io.ReadAll(res.Body)
	slog.Info("Received response",
		"statusCode", res.StatusCode,
		"body", string(bodyBytes))

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(bodyBytes))
	}

	slog.Info("Referral campaign created/updated successfully")
	return nil
}

func (c *DirectusCMSClient) CreateFirstCampaignOfUser(referralCampaign domain.ReferralCampaign) error {
	baseURL := fmt.Sprintf(`%s/userReferrals`, c.config.Directus.URL)

	// Check for existing user referrals
	checkURL := fmt.Sprintf("%s?filter[userId][_eq]=%s", baseURL, referralCampaign.UserID)
	req, err := http.NewRequest(http.MethodGet, checkURL, nil)
	if err != nil {
		slog.Info("Error creating HTTP request to check existing user", "error", err)
		return fmt.Errorf("error creating HTTP request to check existing user: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Info("Error sending HTTP request to check existing user", "error", err)
		return fmt.Errorf("error sending HTTP request to check existing user: %w", err)
	}
	defer res.Body.Close()

	var checkResponse struct {
		Data []struct {
			DefaultCommissionPercentage string          `json:"defaultCommissionPercentage"`
			Campaigns                   json.RawMessage `json:"campaigns"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&checkResponse); err != nil {
		return fmt.Errorf("error decoding check response: %w", err)
	}

	// If there are existing records, check for default campaign
	if len(checkResponse.Data) > 0 {
		var existingCampaigns []map[string]interface{}
		if err := json.Unmarshal(checkResponse.Data[0].Campaigns, &existingCampaigns); err != nil {
			slog.Info("Error parsing existing campaigns", "error", err)
			return fmt.Errorf("error parsing existing campaigns: %w", err)
		}

		// Check if default campaign exists
		for _, campaign := range existingCampaigns {
			if campaign["name"] == "default" {
				slog.Info("Default campaign already exists, skipping creation")
				return nil
			}
		}
	}

	// Prepare the new campaign data
	newCampaign := referralCampaign.Campaigns[0]
	var commissionPercentage float64
	if len(checkResponse.Data) == 0 {
		// This is the first campaign for the user
		commissionPercentage = 10.0
	} else {
		commissionPercentage, err = strconv.ParseFloat(checkResponse.Data[0].DefaultCommissionPercentage, 64)
		if err != nil {
			slog.Info("Error parsing default commission percentage", "error", err)
			return fmt.Errorf("error parsing default commission percentage: %w", err)
		}
		if commissionPercentage == 0 {
			newCampaign.CommissionPercentage = 0
		} else {
			newCampaign.CommissionPercentage = commissionPercentage
		}
	}
	campaignData := map[string]interface{}{
		"code":                 newCampaign.ReferralCode,
		"name":                 newCampaign.CampaignName,
		"commissionPercentage": fmt.Sprintf("%.2f", newCampaign.CommissionPercentage),
		"referredUsers":        []interface{}{},
	}

	var method string
	var requestBody map[string]interface{}

	if len(checkResponse.Data) == 0 {
		// No existing record, create new
		method = http.MethodPost
		requestBody = map[string]interface{}{
			"userId":                      referralCampaign.UserID,
			"username":                    referralCampaign.Username,
			"parentId":                    referralCampaign.ParentID,
			"campaigns":                   []interface{}{campaignData},
			"defaultCommissionPercentage": 10.0,
		}
	} else {
		// Existing record found, update campaigns array
		var existingCampaigns []interface{}
		if err := json.Unmarshal(checkResponse.Data[0].Campaigns, &existingCampaigns); err != nil {
			return fmt.Errorf("error parsing existing campaigns: %w", err)
		}

		existingCampaigns = append(existingCampaigns, campaignData)
		method = http.MethodPatch
		requestBody = map[string]interface{}{
			"campaigns": existingCampaigns,
		}
	}

	dataBytes, err := json.Marshal(requestBody)
	if err != nil {
		slog.Info("Error marshalling referral campaign data", "error", err)
		return fmt.Errorf("error marshalling referral campaign data: %w", err)
	}

	// Create the request
	req, err = http.NewRequest(method, baseURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		slog.Info("Error creating HTTP request", "error", err)
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	res, err = c.httpClient.Do(req)
	if err != nil {
		slog.Info("Error sending HTTP request", "error", err)
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		bodyBytes, _ := io.ReadAll(res.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(bodyBytes))
	}

	slog.Info("Referral campaign created/updated successfully")
	return nil
}

func (c *DirectusCMSClient) GetAllCampaignsFromCMS() (*domain.CampaignCMSResponse, error) {
	var getAllCampaigns func(int64) (*domain.CampaignCMSResponse, error)
	const limit int64 = 5000

	getAllCampaigns = func(offset int64) (*domain.CampaignCMSResponse, error) {
		baseURL := fmt.Sprintf(`%s/userReferrals?offset=%d&limit=%d`, c.config.Directus.URL, offset, limit)
		req, err := http.NewRequest(http.MethodGet, baseURL, nil)
		if err != nil {
			return nil, fmt.Errorf("error creating HTTP request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
		req.Header.Set("Content-Type", "application/json")

		res, err := c.httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("error sending HTTP request: %w", err)
		}
		defer res.Body.Close()

		if res.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("unexpected status code: %d", res.StatusCode)
		}

		var response *domain.CampaignCMSResponse
		if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
			return nil, fmt.Errorf("error decoding response: %w", err)
		}

		if len(response.Data) == int(limit) {
			nextResponse, err := getAllCampaigns(offset + limit)
			if err != nil {
				return nil, err
			}
			response.Data = append(response.Data, nextResponse.Data...)
		}

		return response, nil
	}

	return getAllCampaigns(0)
}

func (c *DirectusCMSClient) GetAllCampaignsOfUserFromCMS(userID string) (*domain.CampaignCMSResponse, error) {
	baseURL := fmt.Sprintf(`%s/userReferrals?filter[userId][_eq]=%s`, c.config.Directus.URL, userID)
	req, err := http.NewRequest(http.MethodGet, baseURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", res.StatusCode)
	}

	var response *domain.CampaignCMSResponse
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("error decoding response: %w", err)
	}

	return response, nil
}

func (c *DirectusCMSClient) CreateDefaultCampaign(ctx context.Context, userID string, campaignName string, code string, username string, parentId string) error {
	baseURL := fmt.Sprintf(`%s/userReferrals`, c.config.Directus.URL)

	checkURL := fmt.Sprintf("%s?filter[userId][_eq]=%s", baseURL, userID)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, checkURL, nil)
	if err != nil {
		slog.Info("Error creating HTTP request to check existing user", "error", err)
		return fmt.Errorf("error creating HTTP request to check existing user: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		slog.Info("Error sending HTTP request to check existing user", "error", err)
		return fmt.Errorf("error sending HTTP request to check existing user: %w", err)
	}
	defer res.Body.Close()

	var checkResponse struct {
		Data []struct {
			DefaultCommissionPercentage string          `json:"defaultCommissionPercentage"`
			Campaigns                   json.RawMessage `json:"campaigns"`
		} `json:"data"`
	}
	if err := json.NewDecoder(res.Body).Decode(&checkResponse); err != nil {
		return fmt.Errorf("error decoding check response: %w", err)
	}

	// check if campaignName = default and userId = userID
	if len(checkResponse.Data) == 0 {
		// No existing record, create new
		slog.Info("No existing record found, create new")
		campaignData := map[string]interface{}{
			"code":                 code,
			"name":                 campaignName,
			"commissionPercentage": "10",
			"referredUsers":        []interface{}{},
		}

		requestBody := map[string]interface{}{
			"userId":    userID,
			"username":  username,
			"parentId":  parentId,
			"campaigns": []interface{}{campaignData},
		}

		dataBytes, err := json.Marshal(requestBody)
		if err != nil {
			slog.Info("Error marshalling referral campaign data", "error", err)
			return fmt.Errorf("error marshalling referral campaign data: %w", err)
		}

		// Create the request
		req, err = http.NewRequest(http.MethodPost, baseURL, bytes.NewBuffer(dataBytes))
		if err != nil {
			slog.Info("Error creating HTTP request", "error", err)
			return fmt.Errorf("error creating HTTP request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
		req.Header.Set("Content-Type", "application/json")

		// Log full request details
		slog.Info("Sending request",
			"method", req.Method,
			"url", req.URL.String(),
			"headers", req.Header,
			"body", string(dataBytes))

		// Send the request
		res, err = c.httpClient.Do(req)
		if err != nil {
			slog.Info("Error sending HTTP request", "error", err)
			return fmt.Errorf("error sending HTTP request: %w", err)
		}

		defer res.Body.Close()

		// Log response details
		bodyBytes, _ := io.ReadAll(res.Body)
		slog.Info("Received response",
			"statusCode", res.StatusCode,
			"body", string(bodyBytes))

		if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
			slog.Info("Campaign exists", "statusCode", res.StatusCode)
		}

		slog.Info("Referral campaign created/updated successfully")
		return nil
	}

	return nil
}

func (c *DirectusCMSClient) UpdateParentIdInDirectus(ctx context.Context, userId string, parentId string) error {
	baseURL := fmt.Sprintf(`%s/userReferrals`, c.config.Directus.URL)
	slog.Info("Updating parent ID in Directus", "userId", userId, "parentId", parentId)
	slog.Info("Base URL", "baseURL", baseURL)

	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"_and": []map[string]interface{}{
					{
						"userId": map[string]string{
							"_eq": userId,
						},
					},
				},
			},
		},
		"data": map[string]interface{}{
			"parentId": parentId,
		},
	}

	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, baseURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		slog.Info("Unexpected status code", "statusCode", res.StatusCode)
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}
	slog.Info("Parent ID updated successfully")
	return nil
}

func (c *DirectusCMSClient) ClaimReloadBonus(externalID string, availableOn time.Time) (bool, error) {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)
	slog.Info("Claiming reload bonus at URL: %s", "directusURL ", directusURL)

	// First, get the current bonus data
	getCurrentURL := fmt.Sprintf(`%s/userBonuses?filter[userId][_eq]=%s&filter[category][_eq]=reload`,
		c.config.Directus.URL,
		externalID)

	req, err := http.NewRequest(http.MethodGet, getCurrentURL, nil)
	if err != nil {
		return false, fmt.Errorf("error creating GET request: %w", err)
	}
	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)

	res, err := c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error getting current bonus: %w", err)
	}
	defer res.Body.Close()

	// Define the structure with flexible types
	var currentData struct {
		Data []struct {
			ID            int             `json:"id"`
			ReloadBonuses json.RawMessage `json:"reloadBonuses"`
		} `json:"data"`
	}

	if err := json.NewDecoder(res.Body).Decode(&currentData); err != nil {
		return false, fmt.Errorf("error decoding current data: %w", err)
	}

	if len(currentData.Data) == 0 {
		return false, fmt.Errorf("no bonus found for user %s", externalID)
	}

	// Find the correct bonus record
	var targetBonus struct {
		ID            int
		ReloadBonuses interface{}
	}

	for _, bonus := range currentData.Data {
		// Try to unmarshal as array first
		var arrayFormat []map[string]interface{}
		if err := json.Unmarshal(bonus.ReloadBonuses, &arrayFormat); err == nil {
			// Found matching bonus with array format
			for _, rb := range arrayFormat {
				bonusAvailableOn, parseErr := time.Parse(time.RFC3339, rb["availableOn"].(string))
				if parseErr == nil && bonusAvailableOn.Equal(availableOn) {
					targetBonus.ID = bonus.ID
					targetBonus.ReloadBonuses = arrayFormat
					break
				}
			}
		} else {
			// Try object format
			var objectFormat map[string]interface{}
			if err := json.Unmarshal(bonus.ReloadBonuses, &objectFormat); err == nil {
				if items, ok := objectFormat["items"].(map[string]interface{}); ok {
					if availableOnStr, ok := items["availableOn"].(string); ok {
						bonusAvailableOn, parseErr := time.Parse(time.RFC3339, availableOnStr)
						if parseErr == nil && bonusAvailableOn.Equal(availableOn) {
							targetBonus.ID = bonus.ID
							targetBonus.ReloadBonuses = objectFormat
							break
						}
					}
				}
			}
		}
	}

	if targetBonus.ID == 0 {
		return false, fmt.Errorf("no matching bonus found for availableOn: %s", availableOn)
	}

	// Prepare update data based on format
	var updateData map[string]interface{}
	if arrayBonus, ok := targetBonus.ReloadBonuses.([]map[string]interface{}); ok {
		// Handle array format
		for i, rb := range arrayBonus {
			bonusAvailableOn, err := time.Parse(time.RFC3339, rb["availableOn"].(string))
			if err == nil && bonusAvailableOn.Equal(availableOn) {
				arrayBonus[i]["claimed"] = true
			}
		}
		updateData = map[string]interface{}{
			"reloadBonuses": arrayBonus,
		}
	} else if objectBonus, ok := targetBonus.ReloadBonuses.(map[string]interface{}); ok {
		// Handle object format
		if items, ok := objectBonus["items"].(map[string]interface{}); ok {
			items["claimed"] = true
			updateData = map[string]interface{}{
				"reloadBonuses": objectBonus,
			}
		}
	}

	// Make the update request
	patchURL := fmt.Sprintf(`%s/userBonuses/%d`, c.config.Directus.URL, targetBonus.ID)
	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return false, fmt.Errorf("error marshalling update data: %w", err)
	}

	req, err = http.NewRequest(http.MethodPatch, patchURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return false, fmt.Errorf("error creating PATCH request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err = c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return false, fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return false, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	return true, nil
}

func (c *DirectusCMSClient) CheckExistingTierUpgradeBonus(externalID string, reason string) (bool, error) {
	baseURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)

	// Build query parameters
	queryParams := url.Values{}
	queryParams.Add("filter[_and][0][userId][_eq]", externalID)
	queryParams.Add("filter[_and][1][category][_eq]", "level-up")
	queryParams.Add("filter[_and][2][note][_eq]", reason)

	fullURL := baseURL + "?" + queryParams.Encode()
	slog.Info("Checking existing tier upgrade bonus", "url", fullURL)

	req, err := http.NewRequest(http.MethodGet, fullURL, nil)
	if err != nil {
		return false, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(res.Body)
		return false, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(body))
	}

	var response struct {
		Data []interface{} `json:"data"`
	}

	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return false, fmt.Errorf("error decoding response: %w", err)
	}

	return len(response.Data) > 0, nil
}

func (c *DirectusCMSClient) UnclaimBonusIfWalletUpdateFails(externalID string, category string, id int) (bool, error) {
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)

	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"_and": []map[string]interface{}{
					{
						"id": map[string]int{
							"_eq": id,
						},
					},
					{
						"userId": map[string]string{
							"_eq": externalID,
						},
					},
					{
						"category": map[string]interface{}{
							"_eq": category,
						},
					},
				},
			},
		},
		"data": map[string]string{
			"status": "active",
		},
	}

	slog.Info("Claiming bonus at URL: %s", "directusURL ", directusURL)
	slog.Info("Query data: ", "data", updateData)

	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return false, fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return false, fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return false, fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return false, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}
	return true, nil
}

func (c *DirectusCMSClient) CreateUserBonusesBatch(data []domain.UserBonusInDirectus) ([]int, error) {
	const batchSize = 100
	directusURL := fmt.Sprintf(`%s/userBonuses`, c.config.Directus.URL)

	// Process in batches
	var allIDs []int
	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}

		batch := data[i:end]
		slog.Info("Processing batch of bonuses",
			"batchSize", len(batch),
			"startIndex", i,
			"endIndex", end,
			"totalRecords", len(data))

		// Marshal the current batch
		dataBytes, err := json.Marshal(batch)
		if err != nil {
			return allIDs, fmt.Errorf("error marshalling batch user bonus data: %w", err)
		}

		req, err := http.NewRequest(http.MethodPost, directusURL, bytes.NewBuffer(dataBytes))
		if err != nil {
			return allIDs, fmt.Errorf("error creating HTTP request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
		req.Header.Set("Content-Type", "application/json")

		res, err := c.httpClient.Do(req)
		if err != nil {
			return allIDs, fmt.Errorf("error sending HTTP request: %w", err)
		}
		defer res.Body.Close()

		respData, err := io.ReadAll(res.Body)
		if err != nil {
			return allIDs, fmt.Errorf("error reading response body: %w", err)
		}

		if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
			slog.Error("Directus API error",
				"statusCode", res.StatusCode,
				"response", string(respData))
			return allIDs, fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
		}

		var response struct {
			Data []struct {
				ID int `json:"id"`
			} `json:"data"`
		}

		if err := json.Unmarshal(respData, &response); err != nil {
			slog.Error("Error parsing response",
				"error", err,
				"responseBody", string(respData))
			return allIDs, fmt.Errorf("error parsing response: %w", err)
		}

		// Collect IDs from this batch
		for _, item := range response.Data {
			allIDs = append(allIDs, item.ID)
		}

		slog.Info("Successfully processed batch",
			"batchSize", len(batch),
			"totalProcessed", len(allIDs))
	}

	slog.Info("Completed processing all bonuses",
		"totalBonuses", len(allIDs))

	return allIDs, nil
}

func (c *DirectusCMSClient) CreateBonusDropRedemption(ctx context.Context, redemption domain.RedemptionUser) error {
	directusURL := fmt.Sprintf(`%s/bonusDropRedemption`, c.config.Directus.URL)
	slog.Info("Creating bonus drop redemption at URL: %s", "directusURL ", directusURL)

	dataBytes, err := json.Marshal(redemption)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	slog.Info("Successfully created bonus drop redemption")
	return nil
}

func (c *DirectusCMSClient) UpdateBonusDropCountAndUsers(ctx context.Context, bonusCode string, dropCount int, users domain.RedemptionUser) error {
	directusURL := fmt.Sprintf(`%s/bonus_drop`, c.config.Directus.URL)
	slog.Info("Updating bonus drop count and users at URL: %s", "directusURL ", directusURL)

	// Prepare the update data
	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"code": map[string]string{
					"_eq": bonusCode,
				},
			},
		},
		"data": map[string]interface{}{
			"current_redeems": dropCount,
		},
	}

	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	err = c.CreateBonusDropRedemption(ctx, users)
	if err != nil {
		return fmt.Errorf("error creating bonus drop redemption: %w", err)
	}

	slog.Info("Successfully updated bonus drop count and users")
	return nil
}

func (c *DirectusCMSClient) RollbackDropRedemption(ctx context.Context, userId, code string) error {
	directusURL := fmt.Sprintf(`%s/bonusDropRedemption`, c.config.Directus.URL)
	slog.Info("Deleting bonus drop redemption at URL: %s", "directusURL ", directusURL)

	// Prepare the delete data
	deleteData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"user_id": map[string]string{
					"_eq": userId,
				},
				"bonus_drop_code": map[string]string{
					"_eq": code,
				},
			},
		},
	}
	dataBytes, err := json.Marshal(deleteData)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodDelete, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusNoContent {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	slog.Info("Successfully deleted bonus drop redemption")
	return nil
}

func (c *DirectusCMSClient) RollbackBonusDropCountAndRedemption(ctx context.Context, bonusCode, userId string, dropCount int) error {
	directusURL := fmt.Sprintf(`%s/bonus_drop`, c.config.Directus.URL)
	slog.Info("Rolling back bonus drop count at URL: %s", "directusURL ", directusURL)

	// Prepare the rollback data
	rollbackData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"code": map[string]string{
					"_eq": bonusCode,
				},
			},
		},
		"data": map[string]interface{}{
			"current_redeems": dropCount,
			"status":          "active",
		},
	}

	dataBytes, err := json.Marshal(rollbackData)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}
	err = c.RollbackDropRedemption(ctx, userId, bonusCode)
	if err != nil {
		return fmt.Errorf("error rolling back bonus drop redemption: %w", err)
	}

	slog.Info("Successfully rolled back bonus drop count")
	return nil
}

func (c *DirectusCMSClient) UpdateBonusDropStatus(ctx context.Context, bonusCode string, status string) error {
	directusURL := fmt.Sprintf(`%s/bonus_drop`, c.config.Directus.URL)
	slog.Info("Updating bonus drop status at URL: %s", "directusURL", directusURL)

	updateData := map[string]interface{}{
		"query": map[string]interface{}{
			"filter": map[string]interface{}{
				"code": map[string]string{
					"_eq": bonusCode,
				},
			},
		},
		"data": map[string]interface{}{
			"status": status,
		},
	}

	dataBytes, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("error marshalling data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPatch, directusURL, bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.config.Directus.ApiKey)
	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending HTTP request: %w", err)
	}
	defer res.Body.Close()

	respData, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d, body: %s", res.StatusCode, string(respData))
	}

	slog.Info("Successfully updated bonus drop status", "code", bonusCode, "status", status)
	return nil
}
