package rest_client

import (
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/mock"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRailsClient_ValidateWithdrawalRequest(t *testing.T) {
	// Setup
	ctx := context.Background()

	// Create a mock wagering client
	mockWagering := &mockElantilWageringClient{}

	// Create a config for the client
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{ // Changed from RailsClientConfig to RailsClient
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	// Create the client with the mock
	client := &RailsClient{
		config:   config,
		wagering: mockWagering,
	}

	// Test cases
	tests := []struct {
		name           string
		request        domain.CreateWithdrawalRequest
		setupMock      func(mock *mockElantilWageringClient)
		expectedAmount decimal.Decimal
		expectedFee    decimal.Decimal
		expectedErr    string
	}{
		{
			name: "Valid withdrawal request",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMock: func(m *mockElantilWageringClient) {
				m.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)
			},
			expectedAmount: decimal.RequireFromString("0.001"),
			expectedFee:    decimal.RequireFromString("0.00006"),
			expectedErr:    "",
		},
		{
			name: "Unsupported asset",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "UNSUPPORTED",
				Amount:            "10",
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "unsupported asset: UNSUPPORTED",
		},
		{
			name: "Invalid amount format",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "invalid",
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "invalid withdrawal amount: invalid",
		},
		{
			name: "Zero amount",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0",
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "withdrawal amount must be greater than 0",
		},
		{
			name: "Negative amount",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "-0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "withdrawal amount must be greater than 0",
		},
		{
			name: "Amount below minimum",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.0001",
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "withdrawal amount 0.0001 BTC is below minimum of 0.00035 BTC",
		},
		{
			name: "Insufficient balance",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMock: func(m *mockElantilWageringClient) {
				m.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").
					Return(errors.New("insufficient balance"))
			},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "insufficient balance: need 0.001 BTC for withdrawal",
		},
		{
			name: "Withdrawal fee already provided",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
				WithdrawalFee:     decimal.RequireFromString("0.0001"),
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "withdrawal_fee and amount_after_fee are calculated by server",
		},
		{
			name: "Amount after fee already provided",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
				AmountAfterFee:    decimal.RequireFromString("0.0009"),
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "withdrawal_fee and amount_after_fee are calculated by server",
		},
		{
			name: "Whitespace in amount",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "  0.001  ",
				WithdrawalAddress: "******************************************",
			},
			setupMock: func(m *mockElantilWageringClient) {
				m.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)
			},
			expectedAmount: decimal.RequireFromString("0.001"),
			expectedFee:    decimal.RequireFromString("0.00006"),
			expectedErr:    "",
		},
		{
			name: "Invalid withdrawal address",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "invalid-address",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "INVALID_WITHDRAWAL_ADDRESS: Invalid withdrawal address format (address length 15 is invalid for BTC (expected 26-62 characters))",
		},
		{
			name: "Amount too small after fee deduction",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.00006", // Equal to fee, so amount after fee would be 0
				WithdrawalAddress: "******************************************",
			},
			setupMock:      func(m *mockElantilWageringClient) {},
			expectedAmount: decimal.Zero,
			expectedFee:    decimal.Zero,
			expectedErr:    "WITHDRAWAL_VALIDATION_FAILED: Withdrawal validation failed (withdrawal amount 0.00006 BTC is below minimum of 0.00035 BTC)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock for each test case
			mockWagering.ExpectedCalls = nil
			mockWagering.Calls = nil

			// Setup mock expectations
			tt.setupMock(mockWagering)

			// Call the function
			amount, fee, err := client.validateWithdrawalRequest(ctx, tt.request)

			// Check results
			if tt.expectedErr != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
			} else {
				require.NoError(t, err)
				assert.True(t, tt.expectedAmount.Equal(amount),
					"Expected amount %s, got %s", tt.expectedAmount.String(), amount.String())
				assert.True(t, tt.expectedFee.Equal(fee),
					"Expected fee %s, got %s", tt.expectedFee.String(), fee.String())
			}

			// Verify all mock expectations were met
			mockWagering.AssertExpectations(t)
		})
	}
}

// Custom mock implementation for ElantilWageringClient
type mockElantilWageringClient struct {
	mock.Mock
}

func (m *mockElantilWageringClient) CheckUserWalletBalance(ctx context.Context, userExternalID string, currency string, withdrawalAmount string) error {
	args := m.Called(ctx, userExternalID, currency, withdrawalAmount)
	return args.Error(0)
}

// Implement other required methods with empty implementations
func (m *mockElantilWageringClient) ValidateToken(ctx context.Context, token string) (bool, error) {
	return true, nil
}

func (m *mockElantilWageringClient) UpdateUserWallet(ctx context.Context, token string, data domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error) {
	return domain.UserWalletUpdateResponse{}, nil
}

func (m *mockElantilWageringClient) UpsertPlayerActivityTagsByUserExternalID(userExternalID string, categoryKey string, keyToSet string, value string) error {
	return nil
}

func (m *mockElantilWageringClient) GetPlayerActivityTagsByUserExternalID(userExternalID string) (interface{}, error) {
	return nil, nil
}

func (m *mockElantilWageringClient) SendVerificationEmail(userExternalID string) error {
	return nil
}

func (m *mockElantilWageringClient) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName string) (bool, error) {
	return true, nil
}

func (m *mockElantilWageringClient) UpdatePassword(email string) error {
	return nil
}

func (m *mockElantilWageringClient) UpsertPlayerRefferals(userExternalID string, referralId string, username string) error {
	return nil
}

func (m *mockElantilWageringClient) GetBatchWageringSummary(ctx context.Context, externalIDs []string, wageringType *string, duration string, startDate, endDate string) (map[string]domain.BatchWageringData, string, error) {
	return nil, "", nil
}

func (m *mockElantilWageringClient) BatchGetWageringSummary(ctx context.Context, userIDs []string) (map[string]domain.BatchWageringData, error) {
	return nil, nil
}

func (m *mockElantilWageringClient) GetUserInformationFromJWTToken(token string) (string, string, error) {
	return "", "", nil
}

func (m *mockElantilWageringClient) SyncPlayerReferralsFromDB(userExternalID string, dbReferrals []domain.ReferredUser) error {
	return nil
}

func (m *mockElantilWageringClient) GetUserIdsAndBatchWageringSummary(ctx context.Context, wageringType string, duration string, startDate, endDate string) (map[string]domain.BatchWageringData, string, error) {
	return nil, "", nil
}

func (m *mockElantilWageringClient) GetUserTransactions(ctx context.Context, userID string, page, size int64) (domain.TransactionResult, error) {
	return domain.TransactionResult{}, nil
}

func (m *mockElantilWageringClient) AssignBonusTemplate(ctx context.Context, templateKey string, ownerId string, ownerType string, userToken string) error {
	return nil
}

func (m *mockElantilWageringClient) ForfeitBonus(ctx context.Context, bonusId, token string) error {
	return nil
}

func (m *mockElantilWageringClient) GetOwnerBonusesByUserId(ctx context.Context, userId, token string) (*domain.OwnerBonusResponse, error) {
	return nil, nil
}

func (m *mockElantilWageringClient) GetConversionRates(ctx context.Context, fiatCurrency string) (domain.ConversionResponse, error) {
	return domain.ConversionResponse{}, nil
}

func (m *mockElantilWageringClient) GetUserProfile(ctx context.Context, userExternalID string, token string) (domain.GetUserProfileResponse, error) {
	return domain.GetUserProfileResponse{}, nil
}

func (m *mockElantilWageringClient) GetUserWallet(ctx context.Context, userExternalID string, token string) (domain.GetUserWalletResponse, error) {
	return domain.GetUserWalletResponse{}, nil
}

func (m *mockElantilWageringClient) CreateUserLocalWallet(ctx context.Context, userID string, token string) (domain.GetUserWalletResponse, error) {
	return domain.GetUserWalletResponse{}, nil
}

func (m *mockElantilWageringClient) UpdateUserProfile(ctx context.Context, userExternalID string, token string, data domain.UserProfileUpdateRequest) error {
	return nil
}

func TestRailsClient_CreateWithdrawalRequest(t *testing.T) {
	// Setup
	ctx := context.Background()

	// Create a mock transport
	mockTransport := &mockRoundTripper{}

	// Create a real http.Client with the mock transport
	httpClient := &http.Client{
		Transport: mockTransport,
	}

	// Create a mock wagering client
	mockWagering := &mockElantilWageringClient{}

	// Create a config for the client
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	// Create the client with the mocks
	client := &RailsClient{
		httpClient: httpClient,
		config:     config,
		wagering:   mockWagering,
	}

	// Test cases
	tests := []struct {
		name           string
		request        domain.CreateWithdrawalRequest
		setupMocks     func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient)
		expectedErr    string
		expectedStatus int
		responseBody   string
	}{
		{
			name: "Successful withdrawal request",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the validation check
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)

				// Mock the HTTP response
				mockResp := &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`{"status":"success"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    "",
			expectedStatus: http.StatusOK,
			responseBody:   `{"status":"success"}`,
		},
		{
			name: "Validation error",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "UNSUPPORTED",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// No HTTP call should be made
			},
			expectedErr:    "unsupported asset: UNSUPPORTED",
			expectedStatus: 0,
			responseBody:   "",
		},
		{
			name: "HTTP request error - service unavailable",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the validation check
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)

				// Mock HTTP error
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("network error"))
			},
			expectedErr:    "RAILS_SERVICE_UNAVAILABLE: Service temporarily unavailable (Post \"https://example.com/v1/withdrawals\": network error)",
			expectedStatus: 0,
			responseBody:   "",
		},
		{
			name: "API error response",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the validation check
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)

				// Mock error response
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"code":400,"message":"Invalid address"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    "WITHDRAWAL_VALIDATION_FAILED: Withdrawal validation failed (Invalid address)",
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"code":400,"message":"Invalid address"}`,
		},
		{
			name: "Non-JSON error response",
			request: domain.CreateWithdrawalRequest{
				UserID:            "user123",
				AssetUID:          "BTC",
				Amount:            "0.001",
				WithdrawalAddress: "******************************************",
			},
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the validation check
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", "0.001").Return(nil)

				// Mock non-JSON error response
				mockResp := &http.Response{
					StatusCode: http.StatusInternalServerError,
					Body:       io.NopCloser(strings.NewReader("Internal Server Error")),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    "RAILS_INTERNAL_ERROR: Internal service error (request failed with status 500: Internal Server Error)",
			expectedStatus: http.StatusInternalServerError,
			responseBody:   "Internal Server Error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockTransport.ExpectedCalls = nil
			mockTransport.Calls = nil
			mockWagering.ExpectedCalls = nil
			mockWagering.Calls = nil

			// Setup mocks
			tt.setupMocks(mockTransport, mockWagering)

			// Call the function
			err := client.CreateWithdrawalRequest(ctx, tt.request)

			// Check results
			if tt.expectedErr != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr)
			} else {
				require.NoError(t, err)
			}

			// Verify all mock expectations were met
			mockTransport.AssertExpectations(t)
			mockWagering.AssertExpectations(t)
		})
	}
}

// Mock RoundTripper for http.Client
type mockRoundTripper struct {
	mock.Mock
}

func (m *mockRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*http.Response), args.Error(1)
}

func TestRailsClient_AddWithdrawalConfigToAddresses(t *testing.T) {
	// Setup
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	client := &RailsClient{
		config: config,
	}

	// Test cases
	tests := []struct {
		name           string
		inputAddresses []domain.TokenAddress
		expectedCount  int
		expectedTokens []string
	}{
		{
			name: "Filter out tokens without withdrawal config",
			inputAddresses: []domain.TokenAddress{
				{
					Token: "BTC",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "BTC",
							NetworkName:    "Bitcoin",
							TokenTicker:    "BTC",
							NetworkTicker:  "BTC",
							DepositAddress: "******************************************",
						},
					},
				},
				{
					Token: "UNSUPPORTED_TOKEN",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "UNSUPPORTED_TOKEN",
							NetworkName:    "Unsupported",
							TokenTicker:    "UNSUPPORTED",
							NetworkTicker:  "UNSUPPORTED",
							DepositAddress: "invalid-address",
						},
					},
				},
				{
					Token: "ETH",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "ETH",
							NetworkName:    "Ethereum",
							TokenTicker:    "ETH",
							NetworkTicker:  "ETH",
							DepositAddress: "******************************************",
						},
					},
				},
			},
			expectedCount:  2, // Only BTC and ETH should remain
			expectedTokens: []string{"BTC", "ETH"},
		},
		{
			name: "Keep all tokens with valid configs",
			inputAddresses: []domain.TokenAddress{
				{
					Token: "BTC",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "BTC",
							NetworkName:    "Bitcoin",
							TokenTicker:    "BTC",
							NetworkTicker:  "BTC",
							DepositAddress: "******************************************",
						},
					},
				},
				{
					Token: "ETH",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "ETH",
							NetworkName:    "Ethereum",
							TokenTicker:    "ETH",
							NetworkTicker:  "ETH",
							DepositAddress: "******************************************",
						},
					},
				},
			},
			expectedCount:  2,
			expectedTokens: []string{"BTC", "ETH"},
		},
		{
			name: "Filter out all tokens without configs",
			inputAddresses: []domain.TokenAddress{
				{
					Token: "UNSUPPORTED_TOKEN_1",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "UNSUPPORTED_TOKEN_1",
							NetworkName:    "Unsupported1",
							TokenTicker:    "UNSUPPORTED1",
							NetworkTicker:  "UNSUPPORTED1",
							DepositAddress: "invalid-address-1",
						},
					},
				},
				{
					Token: "UNSUPPORTED_TOKEN_2",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "UNSUPPORTED_TOKEN_2",
							NetworkName:    "Unsupported2",
							TokenTicker:    "UNSUPPORTED2",
							NetworkTicker:  "UNSUPPORTED2",
							DepositAddress: "invalid-address-2",
						},
					},
				},
			},
			expectedCount:  0, // All should be filtered out
			expectedTokens: []string{},
		},
		{
			name: "Token with mixed valid and invalid network data",
			inputAddresses: []domain.TokenAddress{
				{
					Token: "MIXED_TOKEN",
					NetworkData: []domain.GetAddressesResponse{
						{
							AssetUID:       "BTC", // Valid config
							NetworkName:    "Bitcoin",
							TokenTicker:    "BTC",
							NetworkTicker:  "BTC",
							DepositAddress: "******************************************",
						},
						{
							AssetUID:       "UNSUPPORTED_TOKEN", // Invalid config
							NetworkName:    "Unsupported",
							TokenTicker:    "UNSUPPORTED",
							NetworkTicker:  "UNSUPPORTED",
							DepositAddress: "invalid-address",
						},
					},
				},
			},
			expectedCount:  1, // Should keep the token but only with valid network data
			expectedTokens: []string{"MIXED_TOKEN"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Call the function
			result, err := client.addWithdrawalConfigToAddresses(tt.inputAddresses)

			// Check that no error occurred
			require.NoError(t, err)

			// Check the count of returned addresses
			assert.Equal(t, tt.expectedCount, len(result))

			// Check that the correct tokens are present
			resultTokens := make([]string, len(result))
			for i, addr := range result {
				resultTokens[i] = addr.Token
			}
			assert.ElementsMatch(t, tt.expectedTokens, resultTokens)

			// Verify that all returned addresses have withdrawal configs set
			for _, addr := range result {
				// Check that DisplayOrder is set at token level
				assert.True(t, addr.DisplayOrder >= 0,
					"DisplayOrder should be set for token %s, got %d", addr.Token, addr.DisplayOrder)
				
				for _, networkData := range addr.NetworkData {
					// Check that withdrawal configs are properly set (not zero)
					assert.False(t, networkData.MinWithdrawal.IsZero(),
						"MinWithdrawal should not be zero for asset %s", networkData.AssetUID)
					assert.False(t, networkData.WithdrawalFee.IsZero(),
						"WithdrawalFee should not be zero for asset %s", networkData.AssetUID)
				}
			}
		})
	}
}

func TestRailsClient_CreateDepositAddressOfUser(t *testing.T) {
	// Setup
	ctx := context.Background()

	// Create a mock transport
	mockTransport := &mockRoundTripper{}

	// Create a real http.Client with the mock transport
	httpClient := &http.Client{
		Transport: mockTransport,
	}

	// Create a config for the client
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	// Create the client with the mocks
	client := &RailsClient{
		httpClient: httpClient,
		config:     config,
	}

	// Test cases
	tests := []struct {
		name           string
		userID         string
		setupMocks     func(mockTransport *mockRoundTripper)
		expectedErr    *domain.RailsClientError
		expectedStatus int
		responseBody   string
	}{
		{
			name:   "Successful deposit address creation",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock the HTTP response
				mockResp := &http.Response{
					StatusCode: http.StatusCreated,
					Body:       io.NopCloser(strings.NewReader(`{"status":"success"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    nil,
			expectedStatus: http.StatusCreated,
			responseBody:   `{"status":"success"}`,
		},
		{
			name:   "HTTP request error - service unavailable",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock HTTP error
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("network error"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, "Post \"https://example.com/v1/users\": network error"),
			expectedStatus: 0,
			responseBody:   "",
		},
		{
			name:   "Rails API error - invalid address",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock Rails error response
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Invalid address","message":"Address validation failed","code":"INVALID_ADDRESS"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrInvalidDepositAddress, "Address validation failed"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Invalid address","message":"Address validation failed","code":"INVALID_ADDRESS"}`,
		},
		{
			name:   "Rails API error - unsupported token",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock Rails error response
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Unsupported token","message":"Token not supported for deposit","code":"UNSUPPORTED_TOKEN"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressUnsupported, "Token not supported for deposit"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Unsupported token","message":"Token not supported for deposit","code":"UNSUPPORTED_TOKEN"}`,
		},
		{
			name:   "Rails API error - unknown code defaults to validation error",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock Rails error response with unknown code
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Unknown error","message":"Some unknown error occurred","code":"UNKNOWN_CODE"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressValidation, "Some unknown error occurred"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Unknown error","message":"Some unknown error occurred","code":"UNKNOWN_CODE"}`,
		},
		{
			name:   "Non-JSON error response",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper) {
				// Mock non-JSON error response
				mockResp := &http.Response{
					StatusCode: http.StatusInternalServerError,
					Body:       io.NopCloser(strings.NewReader("Internal Server Error")),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 500: Internal Server Error"),
			expectedStatus: http.StatusInternalServerError,
			responseBody:   "Internal Server Error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockTransport.ExpectedCalls = nil
			mockTransport.Calls = nil

			// Setup mocks
			tt.setupMocks(mockTransport)

			// Call the function
			err := client.CreateDepositAddressOfUser(ctx, tt.userID)

			// Check results
			if tt.expectedErr != nil {
				require.Error(t, err)
				railsErr, ok := err.(*domain.RailsClientError)
				require.True(t, ok, "Expected RailsClientError, got %T", err)
				assert.Equal(t, tt.expectedErr.Code, railsErr.Code)
				assert.Equal(t, tt.expectedErr.Message, railsErr.Message)
				assert.Equal(t, tt.expectedErr.Details, railsErr.Details)
			} else {
				require.NoError(t, err)
			}

			// Verify all mock expectations were met
			mockTransport.AssertExpectations(t)
		})
	}
}

// TestRailsClient_ErrorTypes tests all the different error types that can be returned by the Rails client
func TestRailsClient_ErrorTypes(t *testing.T) {
	// Setup
	ctx := context.Background()

	// Create a mock transport
	mockTransport := &mockRoundTripper{}

	// Create a real http.Client with the mock transport
	httpClient := &http.Client{
		Transport: mockTransport,
	}

	// Create a mock wagering client
	mockWagering := &mockElantilWageringClient{}

	// Create a config for the client
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	// Create the client with the mocks
	client := &RailsClient{
		httpClient: httpClient,
		config:     config,
		wagering:   mockWagering,
	}

	// Test cases for different error types
	tests := []struct {
		name           string
		setupMocks     func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient)
		expectedErr    *domain.RailsClientError
		expectedStatus int
		responseBody   string
		testFunction   func() error
	}{
		{
			name: "Invalid deposit address error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Invalid address","message":"Address validation failed","code":"INVALID_ADDRESS"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrInvalidDepositAddress, "Address validation failed"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Invalid address","message":"Address validation failed","code":"INVALID_ADDRESS"}`,
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Deposit address validation error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Validation failed","message":"Address format is invalid","code":"DEPOSIT_ADDRESS_VALIDATION_FAILED"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressValidation, "Address format is invalid"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Validation failed","message":"Address format is invalid","code":"DEPOSIT_ADDRESS_VALIDATION_FAILED"}`,
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Deposit address unsupported token error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Unsupported token","message":"Token not supported for deposit","code":"UNSUPPORTED_TOKEN"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressUnsupported, "Token not supported for deposit"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Unsupported token","message":"Token not supported for deposit","code":"UNSUPPORTED_TOKEN"}`,
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Invalid withdrawal address error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// No HTTP call should be made - validation fails early
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrInvalidWithdrawalAddress, "address length 15 is invalid for BTC (expected 26-62 characters)"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				request := domain.CreateWithdrawalRequest{
					UserID:            "user123",
					AssetUID:          "BTC",
					Amount:            "0.001",
					WithdrawalAddress: "invalid-address",
				}
				return client.CreateWithdrawalRequest(ctx, request)
			},
		},
		{
			name: "Withdrawal validation error - amount below minimum",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// No HTTP call should be made - validation fails early
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrWithdrawalValidation, "withdrawal amount 0.0001 BTC is below minimum of 0.00035 BTC"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				request := domain.CreateWithdrawalRequest{
					UserID:            "user123",
					AssetUID:          "BTC",
					Amount:            "0.0001",
					WithdrawalAddress: "******************************************",
				}
				return client.CreateWithdrawalRequest(ctx, request)
			},
		},
		{
			name: "Insufficient balance error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the balance check to fail
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", mock.Anything).Return(errors.New("insufficient balance"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrInsufficientBalance, "insufficient balance: need 1 BTC for withdrawal"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				request := domain.CreateWithdrawalRequest{
					UserID:            "user123",
					AssetUID:          "BTC",
					Amount:            "1.0",
					WithdrawalAddress: "******************************************",
				}
				return client.CreateWithdrawalRequest(ctx, request)
			},
		},
		{
			name: "Withdrawal unsupported token error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// No HTTP call should be made - validation fails early
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrWithdrawalUnsupported, "unsupported asset: UNSUPPORTED"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				request := domain.CreateWithdrawalRequest{
					UserID:            "user123",
					AssetUID:          "UNSUPPORTED",
					Amount:            "1.0",
					WithdrawalAddress: "******************************************",
				}
				return client.CreateWithdrawalRequest(ctx, request)
			},
		},
		{
			name: "Rails service unavailable error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("connection refused"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, "Post \"https://example.com/v1/users\": connection refused"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Rails timeout error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("timeout"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, "Post \"https://example.com/v1/users\": timeout"),
			expectedStatus: 0,
			responseBody:   "",
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Rails internal error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusInternalServerError,
					Body:       io.NopCloser(strings.NewReader("Internal Server Error")),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 500: Internal Server Error"),
			expectedStatus: http.StatusInternalServerError,
			responseBody:   "Internal Server Error",
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Unknown error code defaults to validation error",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Unknown error","message":"Some unknown error occurred","code":"UNKNOWN_ERROR_CODE"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressValidation, "Some unknown error occurred"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Unknown error","message":"Some unknown error occurred","code":"UNKNOWN_ERROR_CODE"}`,
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Malformed JSON response",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"error":"Invalid JSON`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 400: {\"error\":\"Invalid JSON"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"error":"Invalid JSON`,
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Empty response body",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader("")),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 400: "),
			expectedStatus: http.StatusBadRequest,
			responseBody:   "",
			testFunction: func() error {
				return client.CreateDepositAddressOfUser(ctx, "user123")
			},
		},
		{
			name: "Withdrawal API error response",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				// Mock the balance check to succeed
				mockWagering.On("CheckUserWalletBalance", ctx, "user123", "BTC", mock.Anything).Return(nil)

				// Mock the API error response
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"code":400,"message":"Withdrawal limit exceeded"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrWithdrawalValidation, "Withdrawal limit exceeded"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"code":400,"message":"Withdrawal limit exceeded"}`,
			testFunction: func() error {
				request := domain.CreateWithdrawalRequest{
					UserID:            "user123",
					AssetUID:          "BTC",
					Amount:            "0.001",
					WithdrawalAddress: "******************************************",
				}
				return client.CreateWithdrawalRequest(ctx, request)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockTransport.ExpectedCalls = nil
			mockTransport.Calls = nil
			mockWagering.ExpectedCalls = nil
			mockWagering.Calls = nil

			// Setup mocks
			tt.setupMocks(mockTransport, mockWagering)

			// Call the test function
			err := tt.testFunction()

			// Check results
			if tt.expectedErr != nil {
				require.Error(t, err)
				railsErr, ok := err.(*domain.RailsClientError)
				require.True(t, ok, "Expected RailsClientError, got %T", err)
				assert.Equal(t, tt.expectedErr.Code, railsErr.Code)
				assert.Equal(t, tt.expectedErr.Message, railsErr.Message)
				assert.Equal(t, tt.expectedErr.Details, railsErr.Details)
			} else {
				require.NoError(t, err)
			}

			// Verify all mock expectations were met
			mockTransport.AssertExpectations(t)
			mockWagering.AssertExpectations(t)
		})
	}
}

// TestRailsClient_GetUserDepositAddresses_ErrorTypes tests error types for GetUserDepositAddresses
func TestRailsClient_GetUserDepositAddresses_ErrorTypes(t *testing.T) {
	// Setup
	ctx := context.Background()

	// Create a mock transport
	mockTransport := &mockRoundTripper{}

	// Create a real http.Client with the mock transport
	httpClient := &http.Client{
		Transport: mockTransport,
	}

	// Create a mock wagering client
	mockWagering := &mockElantilWageringClient{}

	// Create a config for the client
	config := &utils.RESTServiceConfig{
		RailsClient: utils.RailsClient{
			BaseUrl:   "https://example.com",
			SecretKey: "test-secret-key",
		},
	}

	// Create the client with the mocks
	client := &RailsClient{
		httpClient: httpClient,
		config:     config,
		wagering:   mockWagering,
	}

	// Test cases for GetUserDepositAddresses error types
	tests := []struct {
		name           string
		userID         string
		setupMocks     func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient)
		expectedErr    *domain.RailsClientError
		expectedStatus int
		responseBody   string
	}{
		{
			name:   "Successful get addresses",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader(`[]`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    nil,
			expectedStatus: http.StatusOK,
			responseBody:   `[]`,
		},
		{
			name:   "Service unavailable error",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("connection refused"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, "Get \"https://example.com/v1/addresses/user123\": connection refused"),
			expectedStatus: 0,
			responseBody:   "",
		},
		{
			name:   "Timeout error",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockTransport.On("RoundTrip", mock.Anything).Return(nil, errors.New("timeout"))
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsServiceUnavailable, "Get \"https://example.com/v1/addresses/user123\": timeout"),
			expectedStatus: 0,
			responseBody:   "",
		},
		{
			name:   "Internal server error",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusInternalServerError,
					Body:       io.NopCloser(strings.NewReader("Internal Server Error")),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 500: Internal Server Error"),
			expectedStatus: http.StatusInternalServerError,
			responseBody:   "Internal Server Error",
		},
		{
			name:   "User not found error",
			userID: "nonexistent",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusNotFound,
					Body:       io.NopCloser(strings.NewReader(`{"code":404,"message":"User not found"}`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrDepositAddressValidation, "User not found"),
			expectedStatus: http.StatusNotFound,
			responseBody:   `{"code":404,"message":"User not found"}`,
		},
		{
			name:   "Malformed JSON response",
			userID: "user123",
			setupMocks: func(mockTransport *mockRoundTripper, mockWagering *mockElantilWageringClient) {
				mockResp := &http.Response{
					StatusCode: http.StatusBadRequest,
					Body:       io.NopCloser(strings.NewReader(`{"code":400,"message":"Invalid JSON`)),
				}
				mockTransport.On("RoundTrip", mock.Anything).Return(mockResp, nil)
			},
			expectedErr:    domain.NewRailsClientError(domain.ErrRailsInternalError, "request failed with status 400: {\"code\":400,\"message\":\"Invalid JSON"),
			expectedStatus: http.StatusBadRequest,
			responseBody:   `{"code":400,"message":"Invalid JSON`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockTransport.ExpectedCalls = nil
			mockTransport.Calls = nil
			mockWagering.ExpectedCalls = nil
			mockWagering.Calls = nil

			// Setup mocks
			tt.setupMocks(mockTransport, mockWagering)

			// Call the function
			_, err := client.GetUserDepositAddresses(ctx, tt.userID)

			// Check results
			if tt.expectedErr != nil {
				require.Error(t, err)
				railsErr, ok := err.(*domain.RailsClientError)
				require.True(t, ok, "Expected RailsClientError, got %T", err)
				assert.Equal(t, tt.expectedErr.Code, railsErr.Code)
				assert.Equal(t, tt.expectedErr.Message, railsErr.Message)
				assert.Equal(t, tt.expectedErr.Details, railsErr.Details)
			} else {
				require.NoError(t, err)
			}

			// Verify all mock expectations were met
			mockTransport.AssertExpectations(t)
			mockWagering.AssertExpectations(t)
		})
	}
}
