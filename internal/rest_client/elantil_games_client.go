package rest_client

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// Assert interface implementation.
var _ domain.GameProvider = (*ElantilGamesClient)(nil)

type gamesResponse struct {
	Data []siteGame `json:"data"`
}

type siteGame struct {
	Game        game    `json:"game"`
	ThumbnailID *string `json:"thumbnail"`
}

type game struct {
	ID             int64  `json:"id"`
	Status         string `json:"status"`
	Name           string `json:"name"`
	Provider       int64  `json:"provider"`
	TempEMGameType string `json:"temp_EMGameType"`
	Code           string `json:"code"`
	TempEMGameSlug string `json:"temp_EMGameSlug"`
	TempEMGameCode string `json:"temp_EMGameCode"`
}

type ElantilGamesClient struct {
	httpClient HTTPClient
	baseURL    string
}

// NewGameRepository creates a new instance of GameRepository.
func NewElantilGamesClient(client HTTPClient, baseURL string) *ElantilGamesClient {
	return &ElantilGamesClient{
		httpClient: client,
		baseURL:    baseURL,
	}
}

func (c *ElantilGamesClient) GetAllGames(ctx context.Context) ([]domain.Game, error) {
	url := fmt.Sprintf("%s/items/siteGames?limit=5000&fields=id,thumbnail,game.*,provider.name", c.baseURL)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	body := res.Body
	defer body.Close()

	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("bad status '%v'", res.StatusCode)
	}

	var response gamesResponse
	if err := json.NewDecoder(body).Decode(&response); err != nil {
		return nil, err
	}

	return utils.MapSlice(response.Data, gameToDomainGame), nil
}

func gameToDomainGame(siteGame siteGame) domain.Game {
	game := siteGame.Game
	return domain.Game{
		CMSGameID:    game.ID,
		Name:         utils.PointerOf(game.Name),
		ExternalID:   game.Code,
		Slug:         utils.PointerOf(game.TempEMGameSlug),
		ThumbnailID:  siteGame.ThumbnailID,
		VendorGameID: strconv.FormatInt(game.Provider, 10),
	}
}
