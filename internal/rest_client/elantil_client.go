package rest_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"math"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type ElantilWageringClient struct {
	HTTPClient             HTTPClient
	Config                 *utils.RESTServiceConfig
	Middleware             RestClientMiddleware
	transactionsRepository domain.TransactionRepository
}

func NewElantilWageringClient(client HTTPClient, config *utils.RESTServiceConfig, middleware RestClientMiddleware, transactionsRespository domain.TransactionRepository) *ElantilWageringClient {
	return &ElantilWageringClient{
		HTTPClient:             client,
		Config:                 config,
		Middleware:             middleware,
		transactionsRepository: transactionsRespository,
	}
}

func (c *ElantilWageringClient) ValidateToken(ctx context.Context, token string) (bool, error) {
	_, _, err := c.GetUserInformationFromJWTToken(token)
	if err != nil {
		slog.Error("Token validation failed", "error", err)
		return false, fmt.Errorf("token validation failed: %w", err)
	}
	return true, nil
}

func (c *ElantilWageringClient) UpdateUserWallet(ctx context.Context, token string, data domain.UserWalletUpdateRequest) (domain.UserWalletUpdateResponse, error) {
	bearerToken, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		slog.Info("Error fetching authentication token", "error", err)
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error fetching authentication token: %w", err)
	}
	userExternalID, _, err := c.GetUserInformationFromJWTTokenWithRetry(ctx, token)
	if err != nil {
		slog.Info("Error getting user information", "error", err)
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error getting user information: %w", err)
	}

	slog.Info("User information retrieved from Elantil", "userExternalID", userExternalID)
	apiURL := fmt.Sprintf(`%s/%s/credit`, c.Config.Wallet.BaseURL, userExternalID)
	slog.Info("Sending request to Elantil Wagering API to update user wallet", "url", apiURL)

	body, err := json.Marshal(data)
	if err != nil {
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, strings.NewReader(string(body)))
	if err != nil {
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bearerToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		slog.Info("Unexpected status code in the wallet update API || UpdateUserWallet function", "status code", resp.StatusCode, "body", string(respBody))
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
	}
	var walletUpdateResponse domain.UserWalletUpdateResponse
	if err := json.Unmarshal(respBody, &walletUpdateResponse); err != nil {
		return domain.UserWalletUpdateResponse{}, fmt.Errorf("error unmarshaling response: %w", err)
	}

	slog.Info("User wallet updated successfully", "userId", userExternalID)
	return walletUpdateResponse, nil
}

func (c *ElantilWageringClient) GetUserInformationFromJWTToken(token string) (string, string, error) {
	retrieveUserInfoURL := fmt.Sprintf(`%s/realms/%s/protocol/openid-connect/userinfo`, c.Config.Keycloak.URL, c.Config.Keycloak.TenantID)
	data := url.Values{}
	data.Set("client_id", c.Config.Keycloak.ClientID)
	req, err := http.NewRequest(http.MethodPost, retrieveUserInfoURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", "", fmt.Errorf("error creating request for GetUserInformationFromJWTToken: %w", err)
	}
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("error reading response from GetUserInformationFromJWTToken function: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		slog.Error("Unexpected status code in the user info API || GetUserInformationFromJWTToken", "status code", resp.StatusCode, "body", string(respData))
		return "", "", fmt.Errorf("unexpected status code || GetUserInformationFromJWTToken: %d, body: %s", resp.StatusCode, string(respData))
	}

	var userInfo struct {
		ExternalID string `json:"sub"`
		Username   string `json:"preferred_username"`
	}
	if err := json.Unmarshal(respData, &userInfo); err != nil {
		return "", "", fmt.Errorf("error unmarshaling response: %w", err)
	}
	walletId, err := c.GetUserWalletWithRetry(context.Background(), userInfo.ExternalID, token)
	if err != nil {
		slog.Info("Error getting wallet for the user", "userId", userInfo.ExternalID, "error", err)
		return "", "", fmt.Errorf("error getting user wallet: %w", err)
	}
	return userInfo.ExternalID, walletId, nil
}

func (c *ElantilWageringClient) GetUserUSDWallet(ctx context.Context, userExternalID string, token string) (string, error) {
	apiURL := fmt.Sprintf(`%s/wallets/v1/profiles/%s/wallets`, c.Config.Wallet.ElantilURL, userExternalID)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return "", fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)
	req.Header.Set("Authorization", token)
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making request", "error", err)
		return "", fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		slog.Error("Unexpected status code in getting user wallet id", "status code", resp.StatusCode, "body", string(respBody))
		return "", fmt.Errorf("unexpected status code from retrieving user wallet: %d", resp.StatusCode)
	}

	type WalletResponse struct {
		Wallets map[string]struct {
			ID   string `json:"id"`
			Type string `json:"type"`
		} `json:"wallets"`
	}

	var walletResponse WalletResponse
	if err := json.Unmarshal(respBody, &walletResponse); err != nil {
		slog.Error("Error unmarshaling response", "error", err)
		return "", nil
	}

	for _, wallet := range walletResponse.Wallets {
		if wallet.Type == "main" {
			return wallet.ID, nil
		}
	}

	walletID := walletResponse.Wallets["main"].ID
	slog.Info("User wallet retrieved", "user_id", userExternalID, "wallet_id", walletID)
	return walletID, nil
}

func (c *ElantilWageringClient) GetUserWalletWithRetry(ctx context.Context, userExternalID string, token string) (string, error) {
	maxRetries := 3
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		walletID, err := c.GetUserUSDWallet(ctx, userExternalID, token)
		if err == nil {
			return walletID, nil
		}

		lastErr = err

		// Don't retry on rate limit errors (429)
		if strings.Contains(err.Error(), "429") {
			slog.Info("Rate limit reached, not retrying",
				"user_id", userExternalID,
				"error", err)
			return "", err
		}

		// Only retry on 401 errors
		if !strings.Contains(err.Error(), "401") {
			return "", err
		}

		if attempt == maxRetries {
			break
		}

		// Increased backoff to respect rate limits
		backoffDuration := time.Duration(attempt*attempt) * time.Second // Using 1s base instead of 500ms

		slog.Info("Retrying GetUserUSDWallet due to 401 error",
			"attempt", attempt,
			"user_id", userExternalID,
			"wait_duration_ms", backoffDuration.Milliseconds(),
			"error", err)

		select {
		case <-ctx.Done():
			return "", ctx.Err()
		case <-time.After(backoffDuration):
			// Continue with retry
		}
	}

	return "", fmt.Errorf("failed after %d attempts, last error: %w", maxRetries, lastErr)
}

func (c *ElantilWageringClient) GetUserInformationFromJWTTokenWithRetry(ctx context.Context, token string) (string, string, error) {
	maxRetries := 3
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		userID, walletID, err := c.GetUserInformationFromJWTToken(token)
		if err == nil {
			return userID, walletID, nil
		}

		lastErr = err

		// Don't retry on rate limit errors (429)
		if strings.Contains(err.Error(), "429") {
			slog.Info("Rate limit reached, not retrying",
				"error", err)
			return "", "", err
		}

		// Only retry on 401 errors
		if !strings.Contains(err.Error(), "401") {
			return "", "", err
		}

		if attempt == maxRetries {
			break
		}

		// Increased backoff to respect rate limits
		backoffDuration := time.Duration(attempt*attempt) * time.Second

		slog.Info("Retrying GetUserInformationFromJWTToken due to 401 error",
			"attempt", attempt,
			"wait_duration_ms", backoffDuration.Milliseconds(),
			"error", err)

		select {
		case <-ctx.Done():
			return "", "", ctx.Err()
		case <-time.After(backoffDuration):
			// Continue with retry
		}
	}

	return "", "", fmt.Errorf("failed after %d attempts, last error: %w", maxRetries, lastErr)
}

func (c *ElantilWageringClient) UpsertPlayerActivityTagsByUserExternalID(userExternalID string, categoryKey string, keyToSet string, value string) error {
	if keyToSet == "xp" {
		env := strings.ToLower(os.Getenv("ENV"))
		switch env {
		case "uat":
			return nil
		}
	}

	walletToken, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		slog.Error("Failed to get wallet token", "error", err)
		return err
	}

	apiURLToUpsertTags := fmt.Sprintf(`%s/annotations/v1/profiles/%s/annotate`, c.Config.Wallet.ElantilURL, userExternalID)
	slog.Info("Upserting player activity tags", slog.Any("url", apiURLToUpsertTags))

	requestBody := map[string]interface{}{
		"categoryKey": categoryKey,
		"key":         keyToSet,
		"value":       value,
	}

	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		slog.Info("Failed to marshal request body", "error", err)
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	slog.Info(string(reqBody))

	req, err := http.NewRequest("POST", apiURLToUpsertTags, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", walletToken))
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	bodyBytes, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		slog.Info("Failed to upsert player activity tags", "status code", resp.StatusCode, "body", string(bodyBytes))
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	var responseBody map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &responseBody); err != nil {
		slog.Error("Failed to parse response body", "error", err)
	} else {
		slog.Info("Parsed response body", "body", responseBody)
	}

	return nil
}

func (c *ElantilWageringClient) GetPlayerActivityTagsByUserExternalID(userExternalID string) (interface{}, error) {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return nil, err
	}
	apiURLToGetTags := fmt.Sprintf(`%s/annotations/v1/annotations?filter=and(equals(objectType,'profiles'),equals(objectId,'%s'),equals(category.displayType,'tags'))`, c.Config.Wallet.ElantilURL, userExternalID)
	req, err := http.NewRequest("GET", apiURLToGetTags, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/vnd.api+json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var responseBody struct {
		Data []struct {
			Id         string `json:"id"`
			Attributes struct {
				Key   string `json:"key"`
				Value string `json:"value"`
			} `json:"attributes"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&responseBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode response body: %w", err)
	}
	return responseBody, nil

}

func (c *ElantilWageringClient) SendVerificationEmail(email string) error {
	userExternalID, token, err := c.GetUserInformationFromEmail(email)
	if err != nil {
		slog.Error("Failed to get user information from email", "error", err)
		return fmt.Errorf("failed to get user information from email: %w", err)
	}

	apiUrlToSendEmail := fmt.Sprintf(`%s/admin/realms/%s/users/%s/send-verify-email`, c.Config.Keycloak.URL, c.Config.EmailService.Realm, userExternalID)
	slog.Info("Sending verification email", slog.Any("url", apiUrlToSendEmail))

	// Create the request body
	requestBody := []string{"VERIFY_EMAIL"}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		slog.Error("Failed to marshal request body", "error", err)
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("PUT", apiUrlToSendEmail, bytes.NewBuffer(jsonBody))
	if err != nil {
		slog.Error("Failed to create request", "error", err)
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		slog.Error("Failed to send request", "error", err)
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		bodyBytes, _ := io.ReadAll(resp.Body)
		slog.Error("Unexpected status code", "status code", resp.StatusCode, "body", string(bodyBytes))
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	return nil
}

func (c *ElantilWageringClient) GetUserInformationFromEmail(email string) (string, string, error) {
	token, err := c.Middleware.MiddlewareForRestClient("email")
	if err != nil {
		return "", "", err
	}

	apiUrlToGetUserInfo := fmt.Sprintf(`%s/admin/realms/%s/users?email=%s`, c.Config.Keycloak.URL, c.Config.EmailService.Realm, email)
	slog.Info("Getting user information from email", slog.Any("url", apiUrlToGetUserInfo))

	req, err := http.NewRequest("GET", apiUrlToGetUserInfo, nil)
	if err != nil {
		return "", "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", "", fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	var responseBody []struct {
		ID string `json:"id"`
	}

	err = json.NewDecoder(resp.Body).Decode(&responseBody)
	if err != nil {
		return "", "", fmt.Errorf("failed to decode response body: %w", err)
	}

	if len(responseBody) == 0 {
		return "", "", fmt.Errorf("no user found with email: %s", email)
	}

	return responseBody[0].ID, token, nil
}

func (c *ElantilWageringClient) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(userName string) (bool, error) {
	updateTagsToken, err := c.Middleware.MiddlewareForRestClient("email")
	if err != nil {
		return false, err
	}

	apiURLToCheckUser := fmt.Sprintf(`%s/admin/realms/%s/users?username=%s`, c.Config.Keycloak.URL, c.Config.EmailService.Realm, userName)
	slog.Info("Checking if user exists in Elantil system", slog.Any("url", apiURLToCheckUser))

	req, err := http.NewRequest("GET", apiURLToCheckUser, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", updateTagsToken))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return false, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("failed to read response: %w", err)
	}

	var responseBody []struct {
		ID string `json:"id"`
	}

	err = json.Unmarshal(respData, &responseBody)
	if err != nil {
		return false, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(responseBody) == 0 {
		return false, nil
	}
	// update player tags
	err = c.UpsertPlayerActivityTagsByUserExternalID(responseBody[0].ID, "player_activity", "email_verification", "true")
	if err != nil {
		slog.Error("Failed to update player tags", "error", err)
		return false, fmt.Errorf("failed to update player tags: %w", err)
	}

	return true, nil
}

func (c *ElantilWageringClient) UpdatePassword(email string) error {
	userId, token, err := c.GetUserInformationFromEmail(email)
	if err != nil {
		return fmt.Errorf("error getting user information: %w", err)
	}

	apiURLToUpdatePassword := fmt.Sprintf(`%s/admin/realms/%s/users/%s/execute-actions-email`, c.Config.Keycloak.URL, c.Config.EmailService.Realm, userId)
	slog.Info("Sending request to update password", "url", apiURLToUpdatePassword)

	requestBody := []string{"UPDATE_PASSWORD"}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}
	req, err := http.NewRequest("PUT", apiURLToUpdatePassword, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}
	return nil
}

func (c *ElantilWageringClient) UpsertPlayerRefferals(userExternalID string, referralId string, referralUsername string) error {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return err
	}

	apiURLToUpsertTags := fmt.Sprintf(`%s/annotations/v1/profiles/%s/annotate`, c.Config.Wallet.ElantilURL, userExternalID)
	slog.Info("Upserting player refferals", slog.Any("url", apiURLToUpsertTags))

	// get the player referrals
	playerReferrals, err := c.GetPlayerActivityTagsByUserExternalID(userExternalID)
	if err != nil {
		return err
	}

	var userInfos []domain.UserInfo
	for _, tag := range playerReferrals.(struct {
		Data []struct {
			Id         string `json:"id"`
			Attributes struct {
				Key   string `json:"key"`
				Value string `json:"value"`
			} `json:"attributes"`
		} `json:"data"`
	}).Data {
		if tag.Attributes.Key == "referred_player_ids" {
			if tag.Attributes.Value != "" {
				parts := strings.Split(tag.Attributes.Value, " | ")
				for _, part := range parts {
					userIdStart := strings.Index(part, "userId: ") + 8
					userIdEnd := strings.Index(part[userIdStart:], ",") + userIdStart
					usernameStart := strings.Index(part, "username: ") + 10

					if userIdStart > 7 && userIdEnd > userIdStart && usernameStart > 9 {
						userId := strings.TrimSpace(part[userIdStart:userIdEnd])
						username := strings.TrimSpace(part[usernameStart:])
						userInfos = append(userInfos, domain.UserInfo{
							UserID:   userId,
							Username: username,
						})
					}
				}
			}
		}
	}

	// Check if user already exists
	userExists := false
	for _, user := range userInfos {
		if user.UserID == referralId {
			userExists = true
			break
		}
	}

	// Only append if user doesn't exist
	if !userExists {
		userInfos = append(userInfos, domain.UserInfo{
			UserID:   referralId,
			Username: referralUsername,
		})
	}

	var parts []string
	for _, user := range userInfos {
		part := fmt.Sprintf("userId: %s, username: %s", user.UserID, user.Username)
		parts = append(parts, part)
	}
	formattedValue := strings.Join(parts, " | ")

	requestBody := map[string]interface{}{
		"categoryKey": "referrals",
		"key":         "referred_player_ids",
		"value":       formattedValue,
	}

	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", apiURLToUpsertTags, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

func (c *ElantilWageringClient) SyncPlayerReferralsFromDB(userExternalID string, dbReferrals []domain.ReferredUser) error {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return err
	}

	apiURLToUpsertTags := fmt.Sprintf(`%s/annotations/v1/profiles/%s/annotate`, c.Config.Wallet.ElantilURL, userExternalID)
	slog.Info("Syncing player referrals from DB", slog.Any("url", apiURLToUpsertTags))

	// Get existing player referrals
	existingReferrals, err := c.GetPlayerActivityTagsByUserExternalID(userExternalID)
	if err != nil {
		return err
	}

	var userInfos []domain.UserInfo
	for _, tag := range existingReferrals.(struct {
		Data []struct {
			Id         string `json:"id"`
			Attributes struct {
				Key   string `json:"key"`
				Value string `json:"value"`
			} `json:"attributes"`
		} `json:"data"`
	}).Data {
		if tag.Attributes.Key == "referred_player_ids" {
			if tag.Attributes.Value != "" {
				parts := strings.Split(tag.Attributes.Value, " | ")
				for _, part := range parts {
					userIdStart := strings.Index(part, "userId: ") + 8
					userIdEnd := strings.Index(part[userIdStart:], ",") + userIdStart
					usernameStart := strings.Index(part, "username: ") + 10

					if userIdStart > 7 && userIdEnd > userIdStart && usernameStart > 9 {
						userId := strings.TrimSpace(part[userIdStart:userIdEnd])
						username := strings.TrimSpace(part[usernameStart:])
						userInfos = append(userInfos, domain.UserInfo{
							UserID:   userId,
							Username: username,
						})
					}
				}
			}
		}
	}

	// Append new referrals if they do not exist
	for _, user := range dbReferrals {
		userExists := false
		for _, existingUser := range userInfos {
			if existingUser.UserID == user.UserID {
				userExists = true
				break
			}
		}
		if !userExists {
			userInfos = append(userInfos, domain.UserInfo{
				UserID:   user.UserID,
				Username: user.Username,
			})
		}
	}

	// Format users into required string
	var parts []string
	for _, user := range userInfos {
		part := fmt.Sprintf("userId: %s, username: %s", user.UserID, user.Username)
		parts = append(parts, part)
	}
	formattedValue := strings.Join(parts, " | ")

	requestBody := map[string]interface{}{
		"categoryKey": "referrals",
		"key":         "referred_player_ids",
		"value":       formattedValue,
	}

	reqBody, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", apiURLToUpsertTags, bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	return nil
}

func (c *ElantilWageringClient) GetBatchWageringSummary(ctx context.Context, externalIDs []string, wageringType *string, duration string, startDate, endDate string) (map[string]domain.BatchWageringData, string, error) {
	bearerToken, err := c.Middleware.MiddlewareForRestClient("wagering")
	if err != nil {
		return nil, "", fmt.Errorf("error fetching authentication token: %w", err)
	}

	// Format external IDs with prefix
	formattedIDs := make([]string, len(externalIDs))
	for i, id := range externalIDs {
		formattedIDs[i] = "monkeytilt/" + id
	}

	baseQuery := map[string]interface{}{
		"dimensions": []string{"v_user_daily_kpis.user_id"},
		"measures":   []string{"v_user_daily_kpis.rm_bet_sum", "v_user_daily_kpis.loss_sum", "v_user_daily_kpis.rm_win_sum", "v_user_daily_kpis.expense_sum", "v_user_daily_kpis.ngr_sum", "v_user_daily_kpis.rtp_rate"},
		"segments":   []string{"v_user_daily_kpis.tenant_cy"},
		"timezone":   "UTC",
		"filters": []map[string]interface{}{
			{
				"dimension": "v_user_daily_kpis.user_id",
				"operator":  "equals",
				"values":    formattedIDs,
			},
		},
	}

	if wageringType != nil {
		baseQuery["segments"] = append(baseQuery["segments"].([]string), "v_user_daily_kpis."+*wageringType)
	}

	var timeRange string
	if duration != "lifetime" {
		var queryStartDate, queryEndDate string

		if startDate != "" && endDate != "" {
			queryStartDate = startDate
			queryEndDate = endDate
		} else {
			now := time.Now().UTC()
			switch duration {
			case "day":
				start := now.Truncate(24 * time.Hour)
				end := start.Add(24*time.Hour - time.Second)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "week":
				start := now.AddDate(0, 0, -7).Truncate(24 * time.Hour)
				end := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Second)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "month":
				end := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Second)
				start := now.AddDate(0, 0, -30).Truncate(24 * time.Hour)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "day&hour":
				queryEndDate = now.Format("2006-01-02T15:04:05.999")
				queryStartDate = now.Add(-2 * time.Hour).Format("2006-01-02T15:04:05.000")
				duration = "day" // Adjust granularity for the query
			default:
				return nil, "", fmt.Errorf("invalid duration: %s", duration)
			}
		}

		timeRange = queryStartDate + " - " + queryEndDate

		if duration == "day&hour" {
			duration = "day"
		}

		baseQuery["timeDimensions"] = []map[string]interface{}{
			{
				"dimension": "v_user_daily_kpis.rec_on",
				"dateRange": []string{queryStartDate, queryEndDate},
			},
		}
	}
	queryJSON, err := json.Marshal(baseQuery)
	if err != nil {
		return nil, "", fmt.Errorf("error marshaling query: %w", err)
	}

	apiURL := fmt.Sprintf(`%s?query=%s`, c.Config.Wagering.BaseURL, url.QueryEscape(string(queryJSON)))
	slog.Info("Sending request to Elantil Wagering API for batch wagering summary", "url", apiURL)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return nil, "", fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bearerToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making request", "error", err)
		return nil, "", fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent {
		slog.Error("Unexpected status code", "status code", resp.StatusCode)
		return nil, "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("error reading response: %w", err)
	}

	var wageringResponse domain.WageringDataResponse
	if err := json.Unmarshal(respData, &wageringResponse); err != nil {
		return nil, "", fmt.Errorf("error unmarshaling response: %w", err)
	}

	result := make(map[string]domain.BatchWageringData)
	for _, data := range wageringResponse.Data {
		userID := strings.TrimPrefix(data.UserID, "monkeytilt/")

		result[userID] = domain.BatchWageringData{
			BetSum:     data.BetSum,
			LossSum:    data.LossSum,
			WinSum:     data.WinSum,
			NGRSum:     data.NGRSum,
			ExpenseSum: data.ExpenseSum,
			RtpRate:    data.RtpRate,
		}
	}
	return result, timeRange, nil
}

func (c *ElantilWageringClient) BatchGetWageringSummary(ctx context.Context, userIDs []string) (map[string]domain.BatchWageringData, error) {
	bearerToken, err := c.Middleware.MiddlewareForRestClient("wagering")
	if err != nil {
		return nil, fmt.Errorf("error fetching authentication token: %w", err)
	}

	// Process userIDs in batches of 100
	batchSize := 100
	result := make(map[string]domain.BatchWageringData)

	for i := 0; i < len(userIDs); i += batchSize {
		end := i + batchSize
		if end > len(userIDs) {
			end = len(userIDs)
		}

		// Format user IDs for current batch
		batchIDs := make([]string, len(userIDs[i:end]))
		for j, id := range userIDs[i:end] {
			batchIDs[j] = "monkeytilt/" + id
		}

		// Set time range for last 24 hours
		now := time.Now().UTC()
		endTime := now.Format("2006-01-02T15:04:05")
		startTime := now.Add(-24 * time.Hour).Format("2006-01-02T15:04:05")

		baseQuery := map[string]interface{}{
			"dimensions": []string{"v_user_daily_kpis.user_id"},
			"measures": []string{
				"v_user_daily_kpis.rm_bet_sum",
				"v_user_daily_kpis.loss_sum",
				"v_user_daily_kpis.rm_win_sum",
				"v_user_daily_kpis.expense_sum",
				"v_user_daily_kpis.ngr_sum",
				"v_user_daily_kpis.rtp_rate",
			},
			"segments": []string{"v_user_daily_kpis.tenant_cy"},
			"filters": []map[string]interface{}{
				{
					"dimension": "v_user_daily_kpis.user_id",
					"operator":  "equals",
					"values":    batchIDs,
				},
			},
			"timeDimensions": []map[string]interface{}{
				{
					"dimension":   "v_user_daily_kpis.rec_on",
					"granularity": "day",
					"dateRange":   []string{startTime, endTime},
				},
			},
		}

		queryJSON, err := json.Marshal(baseQuery)
		if err != nil {
			return nil, fmt.Errorf("error marshaling query: %w", err)
		}

		apiURL := fmt.Sprintf(`%s?query=%s`, c.Config.Wagering.BaseURL, url.QueryEscape(string(queryJSON)))
		req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+bearerToken)
		req.Header.Set("Content-Type", "application/json")

		resp, err := c.HTTPClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("error making request: %w", err)
		}

		respData, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return nil, fmt.Errorf("error reading response: %w", err)
		}

		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}

		var wageringResponse domain.WageringDataResponse
		if err := json.Unmarshal(respData, &wageringResponse); err != nil {
			return nil, fmt.Errorf("error unmarshaling response: %w", err)
		}

		// Aggregate data from current batch
		for _, data := range wageringResponse.Data {
			userID := strings.TrimPrefix(data.UserID, "monkeytilt/")
			result[userID] = domain.BatchWageringData{
				BetSum:     data.BetSum,
				LossSum:    data.LossSum,
				WinSum:     data.WinSum,
				NGRSum:     data.NGRSum,
				ExpenseSum: data.ExpenseSum,
				RtpRate:    data.RtpRate,
			}
		}

		// Add small delay between batches
		if end < len(userIDs) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	return result, nil
}

func (c *ElantilWageringClient) GetUserIdsAndBatchWageringSummary(ctx context.Context, wageringType string, duration string, startDate, endDate string) (map[string]domain.BatchWageringData, string, error) {
	bearerToken, err := c.Middleware.MiddlewareForRestClient("wagering")
	if err != nil {
		return nil, "", fmt.Errorf("error fetching authentication token: %w", err)
	}

	baseQuery := map[string]interface{}{
		"dimensions": []string{"v_user_daily_kpis.user_id"},
		"measures":   []string{"v_user_daily_kpis.rm_bet_sum", "v_user_daily_kpis.loss_sum", "v_user_daily_kpis.rm_win_sum", "v_user_daily_kpis.expense_sum", "v_user_daily_kpis.ngr_sum", "v_user_daily_kpis.rtp_rate"},
		"segments":   []string{"v_user_daily_kpis.tenant_cy", "v_user_daily_kpis." + wageringType},
		"timezone":   "UTC",
	}

	var timeRange string
	if duration != "lifetime" {
		var queryStartDate, queryEndDate string

		if startDate != "" && endDate != "" {
			queryStartDate = startDate
			queryEndDate = endDate
		} else {
			now := time.Now().UTC()
			switch duration {
			case "day":
				start := now.AddDate(0, 0, -1).Truncate(24 * time.Hour)
				end := start.Add(24*time.Hour - time.Second)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "week":
				start := now.AddDate(0, 0, -7).Truncate(24 * time.Hour)
				end := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Second)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "month":
				end := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Second)
				start := now.AddDate(0, 0, -30).Truncate(24 * time.Hour)
				queryStartDate = start.Format("2006-01-02T15:04:05.000")
				queryEndDate = end.Format("2006-01-02T15:04:05.999")
			case "day&hour":
				queryEndDate = now.Format("2006-01-02T15:04:05.999")
				queryStartDate = now.Add(-2 * time.Hour).Format("2006-01-02T15:04:05.000")
				duration = "day" // Adjust granularity for the query
			default:
				return nil, "", fmt.Errorf("invalid duration: %s", duration)
			}
		}

		timeRange = queryStartDate + " - " + queryEndDate

		if duration == "day&hour" {
			duration = "day"
		}

		baseQuery["timeDimensions"] = []map[string]interface{}{
			{
				"dimension":   "v_user_daily_kpis.rec_on",
				"granularity": duration,
				"dateRange":   []string{queryStartDate, queryEndDate},
			},
		}
	}
	queryJSON, err := json.Marshal(baseQuery)
	if err != nil {
		return nil, "", fmt.Errorf("error marshaling query: %w", err)
	}

	apiURL := fmt.Sprintf(`%s?query=%s`, c.Config.Wagering.BaseURL, url.QueryEscape(string(queryJSON)))
	slog.Info("Sending request to Elantil Wagering API for batch wagering summary", "url", apiURL)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return nil, "", fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bearerToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making request", "error", err)
		return nil, "", fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent {
		slog.Error("Unexpected status code", "status code", resp.StatusCode)
		return nil, "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("error reading response: %w", err)
	}

	var wageringResponse domain.WageringDataResponse
	if err := json.Unmarshal(respData, &wageringResponse); err != nil {
		return nil, "", fmt.Errorf("error unmarshaling response: %w", err)
	}

	// Convert response to map for easier access
	result := make(map[string]domain.BatchWageringData)
	for _, data := range wageringResponse.Data {
		userID := strings.TrimPrefix(data.UserID, "monkeytilt/")
		result[userID] = domain.BatchWageringData{
			BetSum:     data.BetSum,
			LossSum:    data.LossSum,
			WinSum:     data.WinSum,
			NGRSum:     data.NGRSum,
			ExpenseSum: data.ExpenseSum,
			RtpRate:    data.RtpRate,
		}
	}
	return result, timeRange, nil
}

func (c *ElantilWageringClient) GetUserTransactions(ctx context.Context, userID string, page, size int64) (domain.TransactionResult, error) {
	walletToken, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		slog.Error("Failed to get wallet token", "error", err)
		return domain.TransactionResult{}, err
	}

	var allTransactions domain.TransactionResult
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	originalStartTime := startTime

	// Track earliest and latest transaction times
	var earliestTxTime time.Time
	var latestTxTime time.Time

	// Keep track of last window reduction
	reducingWindow := false

	for {
		// Ensure end time is always after start time by at least 1 hour
		if endTime.Sub(startTime) < time.Hour {
			endTime = startTime.Add(time.Hour)
		}

		apiURL := fmt.Sprintf("https://api.p-prod.monkeytilt.biz/transactions-management/v1/transactions?page[number]=%d&page[size]=%d&sort=-createdOn&filter=and(and(greaterThan(createdOn,'%s'),lessThan(createdOn,'%s')),or(and(equals(productId,'casino'),equals(type,'debit')),and(equals(productId,'sports'),equals(type,'debit'))),equals(status,'completed'),equals(ownerId,'%s'))",
			page, size,
			startTime.Format("2006-01-02T15:04:05.000Z"),
			endTime.Format("2006-01-02T15:04:05.000Z"),
			url.QueryEscape(userID))

		req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
		if err != nil {
			return domain.TransactionResult{}, fmt.Errorf("error creating request: %w", err)
		}

		req.Header.Set("Authorization", "Bearer "+walletToken)
		req.Header.Set("X-Tenant-Id", "monkeytilt")

		resp, err := c.HTTPClient.Do(req)
		if err != nil {
			return domain.TransactionResult{}, fmt.Errorf("error making request: %w", err)
		}

		var batchTransactions domain.TransactionResult
		if err := json.NewDecoder(resp.Body).Decode(&batchTransactions); err != nil {
			resp.Body.Close()
			return domain.TransactionResult{}, fmt.Errorf("error decoding response: %w", err)
		}
		resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return domain.TransactionResult{}, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		}

		currentWindowSize := endTime.Sub(startTime)
		fmt.Printf("Time window: %s to %s (Window size: %s), Records found: %d for user: %s\n",
			startTime.Format("2006-01-02 15:04:05"),
			endTime.Format("2006-01-02 15:04:05"),
			currentWindowSize,
			batchTransactions.Meta.Total,
			userID)

		if batchTransactions.Meta.Total >= 10000 {
			// Too many records, reduce time window and retry
			windowSize := endTime.Sub(startTime)
			newEndTime := startTime.Add(windowSize / 2)

			// Ensure minimum window size of 1 hour
			if newEndTime.Sub(startTime) < time.Hour {
				newEndTime = startTime.Add(time.Hour)
			}

			reducingWindow = true
			endTime = newEndTime
			fmt.Printf("Reducing time window due to high record count. New end time: %s\n",
				endTime.Format("2006-01-02 15:04:05"))
			time.Sleep(100 * time.Millisecond)
			continue
		}

		if batchTransactions.Meta.Total == 0 && !reducingWindow {
			return allTransactions, nil
		}

		// Reset window reduction flag since we got a valid response
		reducingWindow = false

		// Process current batch since it's less than 10000 records and not empty
		if batchTransactions.Meta.Total > 0 {
			for _, tx := range batchTransactions.Transaction {
				txTime := tx.Attributes.CreatedOn

				if earliestTxTime.IsZero() || txTime.Before(earliestTxTime) {
					earliestTxTime = txTime
				}
				if txTime.After(latestTxTime) {
					latestTxTime = txTime
				}
			}

			// Append batch data to final result
			allTransactions.Transaction = append(allTransactions.Transaction, batchTransactions.Transaction...)
			allTransactions.Meta.Total += batchTransactions.Meta.Total
		}

		// If we've reached the beginning of time or moved past original start time, break
		if startTime.Before(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)) || startTime.Before(originalStartTime) {
			break
		}

		// Move to next time window
		// Store the current end time as the next start time
		startTime = endTime
		// Reset end time to current time
		endTime = time.Now()

		// Add a small delay to avoid rate limiting
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Printf("Final results - Total transactions: %d for user: %s\n", allTransactions.Meta.Total, userID)
	if !earliestTxTime.IsZero() {
		fmt.Printf("Transactions time period: from %s to %s\n",
			earliestTxTime.Format("2006-01-02 15:04:05"),
			latestTxTime.Format("2006-01-02 15:04:05"))
	}

	return allTransactions, nil
}
func (c *ElantilWageringClient) AssignBonusTemplate(ctx context.Context, templateKey string, ownerId string, ownerType string, userToken string) error {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return fmt.Errorf("error getting wallet token: %w", err)
	}

	// var ownerBonusToken string
	// if userToken != "" {
	// 	fmt.Printf("using user token: %s\n", userToken)
	// 	ownerBonusToken = userToken
	// } else {
	// 	fmt.Printf("using owner token: %s\n", token)
	// 	ownerBonusToken = token
	// }

	// ownerBonus, err := c.GetOwnerBonuses(ctx)
	// if err != nil {
	// 	fmt.Printf("error getting owner bonuses: %v\n", err)
	// 	return fmt.Errorf("error getting owner bonuses: %w", err)
	// }

	// for _, bonus := range ownerBonus.Data {
	// 	if bonus.Attributes.BonusTemplateKey == templateKey && bonus.Attributes.OwnerID == ownerId {
	// 		return domain.ErrBonusTemplateAssigned
	// 	}
	// }

	firstCreditAmount, err := c.transactionsRepository.GetFirstCreditTransaction(ctx, ownerId)
	if err != nil {
		return domain.ErrCreditTransactionNotFound
	}

	apiURL := fmt.Sprintf(`%s/bonuses/v1/bonus-templates/%s/assign`, c.Config.Wallet.ElantilURL, templateKey)

	requestBody := map[string]string{
		"ownerId":   ownerId,
		"ownerType": ownerType,
		"amount":    strconv.FormatFloat(firstCreditAmount, 'f', 2, 64),
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("error marshaling request body: %v\n", err)
		return fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		fmt.Printf("error sending request: %v\n", err)
		return fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("failed to assign bonus unexpected status code: %d, body: %s\n", resp.StatusCode, string(body))
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

func (c *ElantilWageringClient) ForfeitBonus(ctx context.Context, bonusId, token string) error {
	apiURL := fmt.Sprintf(`%s/bonuses/v1/owner-bonuses/%s/forfeit`, c.Config.Wallet.ElantilURL, bonusId)

	fmt.Printf("token: %s\n", token)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, nil)
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

func (c *ElantilWageringClient) GetOwnerBonuses(ctx context.Context) (*domain.OwnerBonusResponse, error) {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return nil, fmt.Errorf("error getting wallet token: %w", err)
	}

	apiURL := fmt.Sprintf(`%s/bonuses/v1/owner-bonuses`, c.Config.Wallet.ElantilURL)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var bonusResponse domain.OwnerBonusResponse
	if err := json.Unmarshal(body, &bonusResponse); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	return &bonusResponse, nil
}

func (c *ElantilWageringClient) GetOwnerBonusesByUserId(ctx context.Context, userId, token string) (*domain.OwnerBonusResponse, error) {
	apiURL := fmt.Sprintf(`%s/bonuses/v1/owner-bonuses?filter=or(equals(status,'InProgress'),equals(status,'Assigned'),equals(status,'Claimed'))`, c.Config.Wallet.ElantilURL)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var bonusResponse domain.OwnerBonusResponse
	if err := json.Unmarshal(body, &bonusResponse); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	for i := range bonusResponse.Data {
		wageredAmount, _ := strconv.ParseFloat(bonusResponse.Data[i].Attributes.WageredAmount, 64)
		wageringRequirementAmount, _ := strconv.ParseFloat(bonusResponse.Data[i].Attributes.WageringRequirementAmount, 64)
		var progressPercentage float64
		if wageringRequirementAmount > 0 {
			progressPercentage = (wageredAmount / wageringRequirementAmount) * 100
			if progressPercentage > 100 {
				progressPercentage = 100
			}
			progressPercentage = math.Round(progressPercentage*100) / 100
		}
		bonusResponse.Data[i].Attributes.ProgressPercentage = progressPercentage
		if wageredAmount > wageringRequirementAmount {
			bonusResponse.Data[i].Attributes.WageredAmount = strconv.FormatFloat(wageringRequirementAmount, 'f', 2, 64)
		}
	}

	return &bonusResponse, nil
}

func (c *ElantilWageringClient) GetConversionRates(ctx context.Context, fiatCurrency string) (domain.ConversionResponse, error) {
	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return domain.ConversionResponse{}, fmt.Errorf("error getting wallet token: %w", err)
	}

	apiURL := fmt.Sprintf(`%s/currency-rates/v1/active-conversion-rates?page[number]=1&page[size]=10000`, c.Config.Wallet.ElantilURL)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		return domain.ConversionResponse{}, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return domain.ConversionResponse{}, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return domain.ConversionResponse{}, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return domain.ConversionResponse{}, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var conversionRates domain.ElantilConversionResponse
	if err := json.Unmarshal(body, &conversionRates); err != nil {
		return domain.ConversionResponse{}, fmt.Errorf("error unmarshaling response: %w", err)
	}

	conversionResponse := conversionRates.FilterByFiatCurrency(fiatCurrency)

	return conversionResponse, nil
}

func (c *ElantilWageringClient) GetUserProfile(ctx context.Context, userExternalID string, token string) (domain.GetUserProfileResponse, error) {
	apiURL := fmt.Sprintf(`%s/profiles/v1/profiles/%s`, c.Config.Wallet.ElantilURL, userExternalID)
	slog.Info("Sending request to get user profile", "url", apiURL)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return domain.GetUserProfileResponse{}, fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)
	req.Header.Set("Authorization", token)
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making request", "error", err)
		return domain.GetUserProfileResponse{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		slog.Error("Unexpected status code in getting user profile id", "status code", resp.StatusCode, "body", string(respBody))
		return domain.GetUserProfileResponse{}, fmt.Errorf("unexpected status code from retrieving user profile: %d", resp.StatusCode)
	}

	var userProfile domain.GetUserProfileResponse
	if err := json.Unmarshal(respBody, &userProfile); err != nil {
		return domain.GetUserProfileResponse{}, fmt.Errorf("error unmarshaling response: %w", err)
	}
	return userProfile, nil
}

func (c *ElantilWageringClient) CompleteSocialProfile(ctx context.Context, userID, email string, completeProfile domain.SocialProfileCompletionRequest, accessToken string) error {
	apiURL := fmt.Sprintf("%s/identity-management/v1/profiles/%s?intent=complete-social-profile", c.Config.Wallet.ElantilURL, userID)

	body := map[string]interface{}{
		"email":          email,
		"username":       completeProfile.Username,
		"additionalName": completeProfile.Username,
		"consents": map[string]interface{}{
			"isLegalAge": completeProfile.IslegalAge,
		},
	}

	jsonBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPut, apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", accessToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-Id", c.Config.Wagering.ClientID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		bodyBytes, _ := io.ReadAll(resp.Body)
		if resp.StatusCode == http.StatusBadRequest && strings.Contains(string(bodyBytes), "User exists with same username") {
			return domain.ErrUsernameAlreadyExists
		}
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	return nil
}

func (c *ElantilWageringClient) GetUserWallet(ctx context.Context, userExternalID string, token string) (domain.GetUserWalletResponse, error) {
	if token == "" {
		walletToken, err := c.Middleware.MiddlewareForRestClient("wallet")
		if err != nil {
			return domain.GetUserWalletResponse{}, fmt.Errorf("error getting wallet token: %w", err)
		}
		token = "Bearer " + walletToken
	}
	apiURL := fmt.Sprintf(`%s/wallets/v1/profiles/%s/wallets`, c.Config.Wallet.ElantilURL, userExternalID)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, apiURL, nil)
	if err != nil {
		slog.Error("Error creating request", "error", err)
		return domain.GetUserWalletResponse{}, fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)
	req.Header.Set("Authorization", token)
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		slog.Error("Error making request", "error", err)
		return domain.GetUserWalletResponse{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		slog.Error("Unexpected status code in getting user wallet id", "status code", resp.StatusCode, "body", string(respBody))
		return domain.GetUserWalletResponse{}, fmt.Errorf("unexpected status code from retrieving user wallet: %d", resp.StatusCode)
	}

	var userwalletResponse domain.GetUserWalletResponse
	if err := json.Unmarshal(respBody, &userwalletResponse); err != nil {
		slog.Error("Error unmarshaling response", "error", err, "body", string(respBody))
		return domain.GetUserWalletResponse{}, nil
	}

	return userwalletResponse, nil
}

func (c *ElantilWageringClient) CheckUserWalletBalance(ctx context.Context, userExternalID, currency string, withdrawalAmount string) error {
	currency = utils.NormalizeCurrency(currency)
	userWallet, err := c.GetUserWallet(ctx, userExternalID, "")
	if err != nil {
		return fmt.Errorf("error getting user wallet: %w", err)
	}

	// Parse withdrawal amount once outside the loop
	withdrawalAmountDecimal, err := decimal.NewFromString(withdrawalAmount)
	if err != nil {
		return fmt.Errorf("error parsing withdrawal amount: %w", err)
	}
	// todo: ensure that CurrencyCode is unique per token-network pair.
	for _, wallet := range userWallet.Wallets {
		if wallet.CurrencyCode == currency && wallet.Type == "main" {
			balance, err := decimal.NewFromString(wallet.BalanceAmount)
			if err != nil {
				return fmt.Errorf("error parsing balance amount: %w", err)
			}
			if balance.LessThan(withdrawalAmountDecimal) {
				return domain.ErrInsufficientWalletBalance
			}

			return nil
		}
	}

	return domain.ErrWalletNotFound
}

func (c *ElantilWageringClient) CreateWallet(ctx context.Context, userID string, currencyCode string) (domain.GetUserWalletResponse, error) {

	token, err := c.Middleware.MiddlewareForRestClient("wallet")
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error getting wallet token: %w", err)
	}

	apiURL := fmt.Sprintf(`%s/wallets/v1/profiles/%s/wallets`, c.Config.Wallet.ElantilURL, userID)

	requestBody := map[string]interface{}{
		"wallets": []map[string]string{
			{
				"type":             "main",
				"currencyCode":     currencyCode,
				"minBalanceAmount": "0",
			},
		},
		"currencyCode": currencyCode,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		return domain.GetUserWalletResponse{}, fmt.Errorf("unexpected status code in create wallet: %d", resp.StatusCode)
	}

	var userwalletResponse domain.GetUserWalletResponse
	if err := json.Unmarshal(respBody, &userwalletResponse); err != nil {
		slog.Error("Error unmarshaling response", "error", err)
		return domain.GetUserWalletResponse{}, nil
	}

	return userwalletResponse, nil
}

func (c *ElantilWageringClient) CreateUserLocalWallet(ctx context.Context, userID string, token string) (domain.GetUserWalletResponse, error) {
	userWallets, err := c.GetUserWallet(ctx, userID, token)
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error getting user wallets: %w", err)
	}

	for _, wallet := range userWallets.Wallets {
		if wallet.Type == "main" && domain.FiatWallet[wallet.CurrencyCode] {
			return domain.GetUserWalletResponse{}, domain.ErrUserAlreadyHasLocalWallet
		}
	}

	userProfile, err := c.GetUserProfile(ctx, userID, token)
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error getting user profile: %w", err)
	}

	if userProfile.AddressCountryAlpha2Code == "" {
		return domain.GetUserWalletResponse{}, domain.ErrUserCountryNotFound
	}

	currencyCode := domain.CountryToCurrencyCode[userProfile.AddressCountryAlpha2Code]
	if currencyCode == "" {
		return domain.GetUserWalletResponse{}, domain.ErrUserNotEligibleForLocalWallet
	}

	userWalletsResponse, err := c.CreateWallet(ctx, userID, currencyCode)
	if err != nil {
		return domain.GetUserWalletResponse{}, fmt.Errorf("error creating wallet: %w", err)
	}

	return userWalletsResponse, nil
}

func (c *ElantilWageringClient) UpdateUserProfile(ctx context.Context, userExternalID string, token string, data domain.UserProfileUpdateRequest) error {
	apiURL := fmt.Sprintf(`%s/identity-management/v1/profiles/%s`, c.Config.Wallet.ElantilURL, userExternalID)
	slog.Info("Sending request to update user profile", "url", apiURL)

	body, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPut, apiURL, strings.NewReader(string(body)))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("Authorization", token)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-Id", c.Config.Keycloak.TenantID)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)
		slog.Error("Failed to update user profile", "status_code", resp.StatusCode, "body", string(body))
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}
