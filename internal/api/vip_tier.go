package api

import (
	"time"
)

type VIPTiersResponse []VIPTierThreshold

type VIPTierThreshold struct {
	Tier        string `json:"tier"`
	Threshold   int64  `json:"threshold"`
	Description string `json:"description"`
}

type VIPTierResponse struct {
	UserID        string  `json:"userId"`
	NewTier       string  `json:"newTier"`
	WageredAmount float64 `json:"wageredAmount"`
}

type VIPStatusResponse struct {
	OldTier string    `json:"old_tier"`
	NewTier string    `json:"new_tier"`
	Bonus   float64   `json:"bonus"`
	Time    time.Time `json:"time"`
}
