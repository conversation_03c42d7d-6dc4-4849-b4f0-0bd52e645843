package api

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type BonusDropHandler struct {
	bonusDropService domain.BonusDropService
}


func NewBonusDropHandler(bonusDropService domain.BonusDropService) *BonusDropHandler {
	return &BonusDropHandler{
		bonusDropService: bonusDropService,
	}
}

func (h *BonusDropHandler) UpsertBonusDrop(c *fiber.Ctx) error {
	var bonusDrop domain.BonusDrop
	if err := c.BodyParser(&bonusDrop); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request payload")
	}

	if err := h.bonusDropService.UpsertBonusDrop(c.Context(), &bonusDrop); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to upsert bonus drop")
	}

	return c.SendStatus(fiber.StatusOK)
}

func (h *BonusDropHandler) RedeemBonusDrop(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	username, ok := utils.GetUserNameFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	bonusCode := c.Query("bonus_code")
	if bonusCode == "" {
		return fiber.NewError(fiber.StatusBadRequest, "Bonus code is required")
	}

	token := c.Get("Authorization")
	if token == "" {
		return fiber.NewError(fiber.StatusBadRequest, "Token is required")
	}

	resp, err := h.bonusDropService.RedeemBonusDrop(c.Context(), userID, username, bonusCode, token);
	if err != nil {
		return err
	}

	return c.JSON(resp)
}
