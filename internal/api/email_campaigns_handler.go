package api

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type EmailCampaignsHandler struct {
	emailCampaigns domain.EmailCampaignsService
}

func NewEmailCampaignsHandler(emailCampaigns domain.EmailCampaignsService) *EmailCampaignsHandler {
	return &EmailCampaignsHandler{emailCampaigns: emailCampaigns}

}

func (s *EmailCampaignsHandler) CreateEmailCampaigns(c *fiber.Ctx) error {
	var req domain.OptimoveWebhookPayload

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request payload",
		})
	}

	slog.Info("Optimove webhook payload", "payload", req)

	err := s.emailCampaigns.CreateEmailCampaigns(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "email campaigns records saved successfully",
	})
}

func (s *EmailCampaignsHandler) GetEmailCampaigns(c *fiber.Ctx) error {
	email, ok := utils.GetUserEmailFromContext(c)
	if !ok {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Unauthorized",
		})
	}

	result, err := s.emailCampaigns.GetEmailCampaigns(c.Context(), email)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": err.Error(),
		})
	}
	return c.Status(fiber.StatusOK).JSON(result)
}
