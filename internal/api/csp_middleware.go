package api

import (
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type CSPMiddleware struct {
	validator *validator.Validate
}

func NewCSPMiddleware(validator *validator.Validate) *CSPMiddleware {
	return &CSPMiddleware{validator: validator}
}

// The report only middleware is used to test the CSP policy without blocking any content.
func (csp *CSPMiddleware) CSPReportOnlyMiddleware(c *fiber.Ctx) error {
	c.Set("Content-Security-Policy-Report-Only",
		`
        default-src 'self';
        script-src 'self';
        style-src 'self' https://monkeytilt-games.imgix.net;
        img-src 'self' https://monkeytilt-games.imgix.net;
        connect-src 'self' https://community-v2.staging.monkeytilt.codes https://community-v2.prod.monkeytilt.codes https://community-v2.uat.monkeytilt.codes https://api.p-prod.monkeytilt.biz;
        frame-src https://apps.p-prod.monkeytilt.biz;
        object-src 'none';
        upgrade-insecure-requests;
        report-uri https://status-api.monkeytilt.codes/cspViolationReport;
    `)

	return c.Next()
}

// The CSP middleware is used to enforce the CSP policy. Will be used in production later.
func (csp *CSPMiddleware) CSPMiddleware(c *fiber.Ctx) error {
	c.Set("Content-Security-Policy",
		`
        default-src 'self';
        script-src 'self';
        style-src 'self' https://monkeytilt-games.imgix.net;
        img-src 'self' https://monkeytilt-games.imgix.net;
        connect-src 'self' https://community-v2.staging.monkeytilt.codes https://community-v2.prod.monkeytilt.codes https://community-v2.uat.monkeytilt.codes https://api.p-prod.monkeytilt.biz;
        frame-src https://apps.p-prod.monkeytilt.biz;
        object-src 'none';
        upgrade-insecure-requests;
        report-uri https://status-api.monkeytilt.codes/cspViolationReport;
    `)

	return c.Next()
}
