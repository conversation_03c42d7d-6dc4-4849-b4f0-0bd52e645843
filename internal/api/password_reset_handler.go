package api

import (
	"log/slog"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type PasswordResetHandler struct {
	passwordResetService domain.PasswordResetService
	validator            *validator.Validate
}

func NewPasswordResetHandler(
	passwordResetService domain.PasswordResetService,
	validator *validator.Validate,
) *PasswordResetHandler {
	return &PasswordResetHandler{
		passwordResetService: passwordResetService,
		validator:            validator,
	}
}

// InitiatePasswordReset handles POST /auth/reset
// @Summary Initiate password reset
// @Description Validates user exists and sends password reset email. IP address must be provided for rate limiting.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body domain.PasswordResetInitiateRequest true "Password reset request with email, optional baseUrl, and required ipAddress"
// @Success 200 {object} domain.PasswordResetInitiateResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 429 {object} fiber.Error "Rate limited"
// @Failure 500 {object} fiber.Error
// @Router /auth/reset [post]
func (h *PasswordResetHandler) InitiatePasswordReset(c *fiber.Ctx) error {
	body, err := parseBody[domain.PasswordResetInitiateRequest](c, h.validator)
	if err != nil {
		return err
	}

	// Get referrer header for base URL extraction (fallback)
	referrer := c.Get("Referer")
	if referrer == "" {
		referrer = c.Get("Referrer") // alternative spelling
	}

	// Use client IP from request body (required for accurate rate limiting)
	clientIP := body.IPAddress

	slog.Info("Password reset initiation request",
		"email", body.Email,
		"baseUrl", body.BaseUrl,
		"referrer", referrer,
		"userAgent", c.Get("User-Agent"),
		"ip", clientIP)

	err = h.passwordResetService.InitiateReset(c.Context(), body.Email, body.BaseUrl, referrer, clientIP)
	if err != nil {
		// Handle rate limiting error specifically - this should be returned to client
		if err == domain.ErrPasswordResetRateLimited {
			slog.Warn("Password reset rate limited",
				"email", body.Email,
				"ip", clientIP)

			return c.Status(429).JSON(fiber.Map{
				"success": false,
				"message": "Too many password reset attempts. Please try again in 1 hour.",
			})
		}

		slog.Error("Failed to initiate password reset",
			"email", body.Email,
			"ip", clientIP,
			"error", err)

		// Don't expose internal errors for security reasons
		// Always return success to prevent email enumeration attacks
		return c.JSON(domain.PasswordResetInitiateResponse{
			Success: true,
			Message: "Reset email sent successfully",
		})
	}

	return c.JSON(domain.PasswordResetInitiateResponse{
		Success: true,
		Message: "Reset email sent successfully",
	})
}

// ValidatePasswordResetToken handles POST /auth/reset/validate
// @Summary Validate password reset token
// @Description Validates an encrypted password reset token and returns the associated email for form prefilling
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body domain.PasswordResetValidateRequest true "Encrypted token to validate"
// @Success 200 {object} domain.PasswordResetValidateResponse
// @Failure 400 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /auth/reset/validate [post]
func (h *PasswordResetHandler) ValidatePasswordResetToken(c *fiber.Ctx) error {
	body, err := parseBody[domain.PasswordResetValidateRequest](c, h.validator)
	if err != nil {
		return err
	}

	slog.Info("Password reset token validation request",
		"encryptedToken", body.Token[:16]+"...",
		"ip", c.IP())

	valid, email, err := h.passwordResetService.ValidateToken(c.Context(), body.Token)
	if err != nil {
		slog.Error("Failed to validate password reset token",
			"encryptedToken", body.Token[:16]+"...",
			"error", err)

		return c.JSON(domain.PasswordResetValidateResponse{
			Success: true,
			Valid:   false,
			Message: "Token is invalid",
		})
	}

	if !valid {
		slog.Warn("Invalid password reset token",
			"encryptedToken", body.Token[:16]+"...")

		return c.JSON(domain.PasswordResetValidateResponse{
			Success: true,
			Valid:   false,
			Message: "Token is invalid",
		})
	}

	return c.JSON(domain.PasswordResetValidateResponse{
		Success: true,
		Valid:   true,
		Email:   email, // Return email for frontend prefill
		Message: "Token is valid",
	})
}

// CompletePasswordReset handles POST /auth/reset/complete
// @Summary Complete password reset
// @Description Validates encrypted token and updates user password in Keycloak. Password must be base64 encoded.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body domain.PasswordResetCompleteRequest true "Encrypted token and base64 encoded new password"
// @Success 200 {object} domain.PasswordResetCompleteResponse
// @Failure 400 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /auth/reset/complete [post]
func (h *PasswordResetHandler) CompletePasswordReset(c *fiber.Ctx) error {
	body, err := parseBody[domain.PasswordResetCompleteRequest](c, h.validator)
	if err != nil {
		return err
	}

	slog.Info("Password reset completion request",
		"encryptedToken", body.Token[:16]+"...",
		"ip", c.IP())

	err = h.passwordResetService.CompleteReset(c.Context(), body.Token, body.Password)
	if err != nil {
		slog.Error("Failed to complete password reset",
			"encryptedToken", body.Token[:16]+"...",
			"error", err)

		// Map specific errors to user-friendly messages
		switch err {
		case domain.ErrPasswordResetTokenInvalid, domain.ErrPasswordResetTokenNotFound, domain.ErrPasswordResetTokenExpired:
			return c.Status(fiber.StatusBadRequest).JSON(domain.PasswordResetCompleteResponse{
				Success: false,
				Message: "Invalid or expired reset token",
			})
		case domain.ErrUserNotFoundInKeycloak:
			return c.Status(fiber.StatusNotFound).JSON(domain.PasswordResetCompleteResponse{
				Success: false,
				Message: "User not found",
			})
		default:
			// Check if it's a password format error (base64 decoding failed)
			if strings.Contains(err.Error(), "invalid password format") {
				return c.Status(fiber.StatusBadRequest).JSON(domain.PasswordResetCompleteResponse{
					Success: false,
					Message: "Invalid password format. Password must be base64 encoded.",
				})
			}
			return c.Status(fiber.StatusInternalServerError).JSON(domain.PasswordResetCompleteResponse{
				Success: false,
				Message: "Failed to reset password. Please try again.",
			})
		}
	}

	slog.Info("Password reset completed successfully",
		"encryptedToken", body.Token[:16]+"...")

	return c.JSON(domain.PasswordResetCompleteResponse{
		Success: true,
		Message: "Password reset successfully",
	})
}
