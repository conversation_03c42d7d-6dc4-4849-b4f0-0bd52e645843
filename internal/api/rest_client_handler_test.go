package api

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock RailsClientService
type mockRailsClientService struct {
	mock.Mock
}

func (m *mockRailsClientService) CreateDepositAddressOfUser(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *mockRailsClientService) GetUserDepositAddresses(ctx context.Context, userID string) ([]domain.TokenAddress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]domain.TokenAddress), args.Error(1)
}

func (m *mockRailsClientService) CreateWithdrawalRequest(ctx context.Context, request domain.CreateWithdrawalRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

func TestRestClientHandler_CreateDepositAddressOfUser(t *testing.T) {
	// Setup
	app := fiber.New()
	mockService := &mockRailsClientService{}
	handler := NewRestClientHandler(mockService)

	// Add middleware to set user ID in context
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("user_id", "test-user-123")
		return c.Next()
	})

	// Register the correct route
	app.Post("/rails/users/depositaddress", handler.CreateDepositAddressOfUser)

	// Test successful case
	t.Run("Successful deposit address creation", func(t *testing.T) {
		// Setup mock
		mockService.On("CreateDepositAddressOfUser", mock.Anything, "test-user-123").Return(nil)

		// Create request
		req := httptest.NewRequest("POST", "/rails/users/depositaddress", nil)
		resp, err := app.Test(req)
		require.NoError(t, err)

		// Check status code
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Read response body
		bodyBytes, _ := io.ReadAll(resp.Body)
		var body map[string]interface{}
		err = json.Unmarshal(bodyBytes, &body)
		require.NoError(t, err)

		// Check response body
		expectedBody := map[string]interface{}{
			"message": "Deposit address created",
		}
		assert.Equal(t, expectedBody, body)

		// Verify mock expectations
		mockService.AssertExpectations(t)
	})
}

func TestRestClientHandler_CreateWithdrawalRequest(t *testing.T) {
	// Setup
	app := fiber.New()
	mockService := &mockRailsClientService{}
	handler := NewRestClientHandler(mockService)

	// Add middleware to set user ID in context
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("user_id", "test-user-123")
		return c.Next()
	})

	app.Post("/rails/users/withdrawal", handler.CreateWithdrawalRequest)

	// Test successful case
	t.Run("Successful withdrawal request", func(t *testing.T) {
		// Setup mock
		expectedRequest := domain.CreateWithdrawalRequest{
			UserID:            "test-user-123",
			AssetUID:          "BTC",
			Amount:            "0.001",
			WithdrawalAddress: "******************************************",
		}
		mockService.On("CreateWithdrawalRequest", mock.Anything, expectedRequest).Return(nil)

		// Create request body
		requestBody := map[string]interface{}{
			"asset_uid":          "BTC",
			"amount":             "0.001",
			"withdrawal_address": "******************************************",
		}
		requestBodyBytes, err := json.Marshal(requestBody)
		require.NoError(t, err)

		// Create request
		req := httptest.NewRequest("POST", "/rails/users/withdrawal", strings.NewReader(string(requestBodyBytes)))
		req.Header.Set("Content-Type", "application/json")
		resp, err := app.Test(req)
		require.NoError(t, err)

		// Check status code
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Read response body
		bodyBytes, _ := io.ReadAll(resp.Body)
		var body map[string]interface{}
		err = json.Unmarshal(bodyBytes, &body)
		require.NoError(t, err)

		// Check response body
		expectedBody := map[string]interface{}{
			"message": "Withdrawal request created",
		}
		assert.Equal(t, expectedBody, body)

		// Verify mock expectations
		mockService.AssertExpectations(t)
	})
}

func TestRestClientHandler_GetUserDepositAddresses(t *testing.T) {
	// Setup
	app := fiber.New()
	mockService := &mockRailsClientService{}
	handler := NewRestClientHandler(mockService)

	// Add middleware to set user ID in context
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("user_id", "test-user-123")
		return c.Next()
	})

	app.Get("/rails/users/depositaddresses", handler.GetUserDepositAddresses)

	// Test successful case
	t.Run("Successful get addresses", func(t *testing.T) {
		// Setup mock
		addresses := []domain.TokenAddress{
			{
				Token: "BTC",
				NetworkData: []domain.GetAddressesResponse{
					{
						AssetUID:       "BTC",
						NetworkName:    "Bitcoin",
						TokenTicker:    "BTC",
						NetworkTicker:  "BTC",
						DepositAddress: "******************************************",
					},
				},
			},
		}
		mockService.On("GetUserDepositAddresses", mock.Anything, "test-user-123").Return(addresses, nil)

		// Create request
		req := httptest.NewRequest("GET", "/rails/users/depositaddresses", nil)
		resp, err := app.Test(req)
		require.NoError(t, err)

		// Check status code
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Read response body
		bodyBytes, _ := io.ReadAll(resp.Body)
		var body map[string]interface{}
		err = json.Unmarshal(bodyBytes, &body)
		require.NoError(t, err)

		// Check response body
		expectedBody := map[string]interface{}{
			"addresses": []interface{}{
				map[string]interface{}{
					"token": "BTC",
					"network_data": []interface{}{
						map[string]interface{}{
							"asset_uid":       "BTC",
							"network_name":    "Bitcoin",
							"token_ticker":    "BTC",
							"network_ticker":  "BTC",
							"deposit_address": "******************************************",
							"min_withdrawal":  "0",
							"withdrawal_fee":  "0",
						},
					},
				},
			},
		}
		assert.Equal(t, expectedBody, body)

		// Verify mock expectations
		mockService.AssertExpectations(t)
	})
}
