package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type LeaderboardParams struct {
	OrderBy *string `query:"orderBy" validate:"omitempty,oneof=wagered total_bets total_coins"`
}

func queryToLeaderboardOrderParams(query *LeaderboardParams) domain.OrderParams {
	return domain.OrderParams{
		OrderBy: queryToLeaderboardOrderBy(query),
	}
}

func queryToLeaderboardOrderBy(query *LeaderboardParams) string {
	orderBy := "wagered"
	if query.OrderBy != nil {
		orderBy = *query.OrderBy
	}

	return orderBy
}

type leaderboardByUserParams struct {
	LeaderboardParams
	RetrieveAmount int `query:"retrieveAmount" validate:"required,min=1"`
}

type LeaderboardUsersResponse PagedItems[LeaderboardUserResponse]

type LeaderboardUserResponse struct {
	UserResponse
	Rank                        int   `json:"rank"`
	RemainingToNextRank         int64 `json:"remaining_to_next_rank"`
	RemainingToNextRankPosition int64 `json:"remaining_to_next_rank_position,omitempty"`
}

func usersToLeaderboardUsersResponse(users *domain.RankedUsers, leaderboardType string, isAdmin bool) *LeaderboardUsersResponse {
	return &LeaderboardUsersResponse{
		Items: usersToLeaderboardUserResponses(users.Items, leaderboardType, isAdmin),
		Paging: paging{
			TotalCount:  users.Paging.TotalCount,
			CurrentPage: users.Paging.CurrentPage,
			PageSize:    users.Paging.PageSize,
			PageCount:   len(users.Items),
		},
	}
}

func usersToLeaderboardUserResponses(users []domain.RankedUser, leaderboardType string, isAdmin bool) []LeaderboardUserResponse {
	userLeaderboards := make([]LeaderboardUserResponse, 0, len(users))

	var rankBeforeDiffThanZero int64
	var previousUser *domain.RankedUser
	for _, user := range users {
		userLeaderboards = append(userLeaderboards, userToLeaderboardUserResponse(&user, previousUser, leaderboardType, &rankBeforeDiffThanZero, isAdmin))
		previousUser = &user
	}

	return userLeaderboards
}

func userToLeaderboardUserResponse(
	user *domain.RankedUser,
	previousUser *domain.RankedUser,
	leaderboardType string,
	rankBeforeDiffThanZero *int64,
	isAdmin bool,
) LeaderboardUserResponse {
	remainingToNextRankPosition := int64(0)

	if previousUser != nil {
		var diff int64
		switch leaderboardType {
		case totalCoinsField:
			diff = int64(previousUser.TotalCoins - user.TotalCoins)
		case totalBetsField:
			diff = int64(previousUser.TotalBets - user.TotalBets)
		}

		if diff == 0 {
			remainingToNextRankPosition = *rankBeforeDiffThanZero
		} else {
			*rankBeforeDiffThanZero = diff
			remainingToNextRankPosition = diff
		}
	}

	return LeaderboardUserResponse{
		UserResponse:                *userToUserResponseForLeaderboard(&user.User, isAdmin),
		Rank:                        user.Rank,
		RemainingToNextRank:         int64(user.RemainingToNextRank),
		RemainingToNextRankPosition: remainingToNextRankPosition,
	}
}

func rankedUserToRankedUserResponse(user *domain.RankedUser) LeaderboardUserResponse {
	return LeaderboardUserResponse{
		UserResponse:        *userToUserResponse(&user.User, true, false),
		Rank:                user.Rank,
		RemainingToNextRank: int64(user.RemainingToNextRank),
	}
}
