package api

import (
	"log/slog"
	"strconv"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type ReferralHandler struct {
	referralService domain.ReferralService
}

func NewReferralHandler(referralService domain.ReferralService) *ReferralHandler {
	return &ReferralHandler{referralService: referralService}
}

func (h *ReferralHandler) CreateUserReferralCampaign(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	var data struct {
		Username     string `json:"username"`
		CampaignName string `json:"campaignName"`
	}

	token := ctx.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	if err := ctx.BodyParser(&data); err != nil {
		slog.Error("Failed to parse request body", slog.Any("error", err))
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	if err := h.referralService.CreateUserReferralCampaign(ctx.Context(), userID, data.Username, data.CampaignName); err != nil {
		if err == domain.ErrUserAlreadyHasReferralCampaign {
			return fiber.NewError(fiber.StatusBadRequest, "User already has a referral campaign")
		} else if err == domain.ErrResourceNotFound {
			return fiber.NewError(fiber.StatusBadRequest, "User does not exist")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to create user referral campaign")
	}

	return ctx.SendString("User referral campaign created successfully")
}

func (h *ReferralHandler) GetUserReferralCampaign(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	campaignName := ctx.Query("campaign_name")

	campaigns, _, err := h.referralService.GetUserReferralCampaign(ctx.Context(), userID, campaignName)
	if err != nil {
		slog.Error("Failed to get user referral campaign", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get user referral campaign")
	}

	return ctx.JSON(campaigns)
}

func (h *ReferralHandler) AddUserToTheListOfRefferedUsers(ctx *fiber.Ctx) error {
	referralCode := ctx.Params("referralCode")
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	username := ctx.Params("username")
	if err := h.referralService.AddUserToTheListOfRefferedUsers(ctx.Context(), referralCode, userID, username); err != nil {
		if err == domain.ErrUserAlreadyInReferredList {
			return fiber.NewError(fiber.StatusBadRequest, "User already in the list of referred users")
		} else if err == domain.ErrResourceNotFound {
			return fiber.NewError(fiber.StatusInternalServerError, "Failed to add user to the list of referred users")
		} else if err == domain.ErrUserReferringItself {
			return fiber.NewError(fiber.StatusBadRequest, "User cannot refer itself")
		} else if err == domain.ErrRecordNotFound {
			return fiber.NewError(fiber.StatusBadRequest, "Referral code or user not found")
		} else if err == domain.ErrCodeLimitReached {
			return fiber.NewError(fiber.StatusBadRequest, "Code limit reached")
		} else if err == domain.ErrCodeUsed {
			return fiber.NewError(fiber.StatusBadRequest, "Code already used")
		}
	}

	return ctx.JSON(fiber.Map{"message": "User added to the list of referred users successfully"})
}

func (h *ReferralHandler) CheckIfUserAlreadyUsedTheCode(ctx *fiber.Ctx) error {
	referralCode := ctx.Params("referralCode")
	userExternalID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	used, err := h.referralService.CheckIfUserAlreadyUsedTheCode(ctx.Context(), referralCode, userExternalID)
	if err != nil {
		slog.Error("Failed to check if user already used the code", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to check if user already used the code")
	}

	return ctx.JSON(fiber.Map{"used": used})
}

func (h *ReferralHandler) CalculateUserCommissions(ctx *fiber.Ctx) error {
	userID := ctx.Params("id")

	if err := h.referralService.CalculateUserCommissions(ctx.Context(), userID); err != nil {
		slog.Error("Failed to calculate user commissions", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to calculate user commissions")
	}

	return ctx.SendString("User commissions calculated successfully")
}

func (h *ReferralHandler) ClaimCommission(ctx *fiber.Ctx) error {
	userExternalID := ctx.Params("id")
	token := ctx.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	_, err := h.referralService.ClaimReward(ctx.Context(), token, userExternalID)
	if err != nil {
		slog.Error("Failed to claim reward", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to claim reward")
	}

	return ctx.JSON(fiber.Map{"message": "Reward claimed successfully"})
}

func (h *ReferralHandler) GetCommissionHistory(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	offset := ctx.QueryInt("offset") 
	limit := ctx.QueryInt("limit")  

	if offset < 0 {
		offset = 0
	}

	if limit < 1 { 
		limit = -1 
	}

	history, err := h.referralService.GetCommissionHistory(ctx.Context(), userID, offset, limit)
	if err != nil {
		slog.Error("Failed to get commission history", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get commission history")
	}

	return ctx.JSON(history)
}

func (h *ReferralHandler) GetDetailedCommissionInfo(ctx *fiber.Ctx) error {
	parentUserID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	info, err := h.referralService.GetDetailedCommissionInfo(ctx.Context(), parentUserID)
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get detailed commission info")
	}

	return ctx.JSON(info)
}

func (h *ReferralHandler) UpdateUserCommisionPercentage(ctx *fiber.Ctx) error {

	var body []domain.BonusCommissionUpdate

	if err := ctx.BodyParser(&body); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	for _, user := range body {
		defaultCommission, err := strconv.ParseFloat(user.DefaultCommissionPercentage, 64)
		if err != nil {
			return fiber.NewError(fiber.StatusBadRequest, "Invalid default commission percentage")
		}
		err = h.referralService.UpdateUserDefaultCommissionPercentage(user.UserID, strconv.FormatFloat(defaultCommission, 'f', -1, 64))
		if err != nil {
			return fiber.NewError(fiber.StatusInternalServerError, "Failed to update user default commission percentage")
		}

		for _, campaign := range user.Campaigns {
			var commissionStr string
			switch v := campaign.CommissionPercentage.(type) {
			case float64:
				commissionStr = strconv.FormatFloat(v, 'f', -1, 64)
			case string:
				commissionStr = v
			default:
				return fiber.NewError(fiber.StatusBadRequest, "Invalid commission percentage format")
			}
			if err := h.referralService.UpdateUserCommisionPercentage(ctx.Context(), user.UserID, campaign.Name, commissionStr, campaign.ReferredUsers); err != nil {
				return fiber.NewError(fiber.StatusInternalServerError, "Failed to update user commission percentage")
			}
		}
	}
	return ctx.JSON(fiber.Map{"message": "User commission percentage updated successfully"})
}

func (h *ReferralHandler) IsUserInAnyReferralCampaign(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	inCampaign, parentId, err := h.referralService.IsUserInAnyReferralCampaign(ctx.Context(), userID)
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to check if user is in any referral campaign")
	}

	return ctx.JSON(fiber.Map{"inCampaign": inCampaign, "parentId": parentId})
}

func (h *ReferralHandler) CreateDefaultCampaignInDirectus(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	username, ok := utils.GetUserNameFromContext(ctx)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "Username not found in context")
	}

	if err := h.referralService.CreateDefaultCampaignInDirectus(ctx.Context(), userID, username); err != nil {
		if err == domain.ErrDuplicateRecord {
			return fiber.NewError(fiber.StatusOK, "Default campaign already exists")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to create default campaign in Directus")
	}

	return ctx.JSON(fiber.Map{"message": "Default campaign created successfully"})
}

func (h *ReferralHandler) DeleteCampaign(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	campaignName := ctx.Params("campaignName")

	err := h.referralService.DeleteCampaigns(ctx.Context(), userID, campaignName)
	if err != nil {
		if err == domain.ErrDeleleCampaignWithReferredUsers {
			return fiber.NewError(fiber.StatusBadRequest, "Cannot delete campaign with referrals")
		} else if deleteErr, ok := err.(*domain.CampaignDeleteError); ok {
			return fiber.NewError(fiber.StatusBadRequest, deleteErr.Error())
		}

		return fiber.NewError(fiber.StatusInternalServerError, "Failed to delete campaign")
	}

	return ctx.JSON(fiber.Map{"message": "Campaign deleted successfully"})
}

func (h *ReferralHandler) GetCampaignStats(ctx *fiber.Ctx) error {
	userID := ctx.Params("id")
	campaignName := ctx.Params("campaignName")
	stats, err := h.referralService.GetCampaignStats(ctx.Context(), userID, campaignName)
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get campaign stats")
	}

	return ctx.JSON(stats)
}

func (h *ReferralHandler) CalculateAllUsersCommissions(ctx *fiber.Ctx) error {
	if err := h.referralService.CalculateAllUsersCommissions(ctx.Context()); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to calculate all users commissions")
	}

	return ctx.SendString("All users commissions calculated successfully")
}

func (h *ReferralHandler) ManuallyUpdateReferralOfUser(ctx *fiber.Ctx) error {
	var data struct {
		UserId      string `json:"userId"`
		UserName    string `json:"userName"`
		NewParentId string `json:"newParentId"`
	}

	if err := ctx.BodyParser(&data); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	if err :=
		h.referralService.ManuallyUpdateReferralOfUser(data.UserId, data.UserName, data.NewParentId); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to manually update referral of user")
	}

	return ctx.SendString("Referral of user updated successfully")
}

func (h *ReferralHandler) CreateAdminCampaign(ctx *fiber.Ctx) error {
	var data struct {
		ReferralCode   string  `json:"referral_code"`
		RewardAmount   float64 `json:"reward_amount"`
		CodeUsageLimit int     `json:"code_usage_limit"`
	}

	if err := ctx.BodyParser(&data); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	if err := h.referralService.CreateAdminCampaign(ctx.Context(), data.ReferralCode, data.RewardAmount, data.CodeUsageLimit); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to create admin campaign")
	}

	return ctx.JSON(fiber.Map{"message": "Admin campaign created successfully"})
}
