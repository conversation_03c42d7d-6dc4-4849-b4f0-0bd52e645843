package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/gofiber/fiber/v2"
)

// handles bet HTTP requests.
type UserConfigAsssetsHandler struct {
	UserConfigAsssetsHandler domain.UserConfigAssetsService
}

// NewUserConfigAssetsHandler creates new instance of BetHandler.
func NewUserConfigAssetsHandler(userConfigAsssetsHandler domain.UserConfigAssetsService) *UserConfigAsssetsHandler {
	return &UserConfigAsssetsHandler{UserConfigAsssetsHandler: userConfigAsssetsHandler}
}

// Get All avaible Assets for the user Config Profile.
// @Produce application/json
// @Success 200  {object} userConfigAssetResponses
// @Failure 400  {object} fiber.Error
// @Failure 403  {object} fiber.Error
// @Failure 404  {object} fiber.Error
// @Router  /profile/assets [get]
func (h *UserConfigAsssetsHandler) GetAssets(c *fiber.Ctx) error {
	assets, err := h.UserConfigAsssetsHandler.GetAssets(c.Context())
	if err != nil {
		return err
	}

	return c.JSON(userConfigAssetsToUserConfigAssetResponses(assets))
}
