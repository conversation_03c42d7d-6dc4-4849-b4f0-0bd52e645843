package api

import (
	"net/http/httptest"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/apimock"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestWithAccessToken(t *testing.T) {
	validator := validator.New()
	middleware := NewAuthorizationMiddleware(validator)
	app := fiber.New()

	app.Get("/users/:id", middleware.WithAccessToken, func(c *fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	tests := []struct {
		name           string
		idParam        string
		authHeader     string
		expectedStatus int
	}{
		{
			name:           "Valid token and matching ID",
			idParam:        "4e90ba31-b21c-42cc-81c3-e16ec0d58656",
			authHeader:     "Bearer eyJhbGciOiAiUlMyNTYiLCAidHlwZSIgOiAiSldUIiwgImtpZCIgOiAiMnZmWGYiLCJhbGciOiAiUlMyNTYifQ.eyJzdWIiOiAiNGU5MGJhMzEtYjIxYy00MmNjLTgxYzMtZTE2ZWMwZDU4NjU2In0",
			expectedStatus: fiber.StatusOK,
		},
		{
			name:           "Invalid token structure",
			idParam:        "4e90ba31-b21c-42cc-81c3-e16ec0d58656",
			authHeader:     "Bearer invalid.token",
			expectedStatus: fiber.StatusUnauthorized,
		},
		{
			name:           "Missing Authorization header",
			idParam:        "4e90ba31-b21c-42cc-81c3-e16ec0d58656",
			authHeader:     "",
			expectedStatus: fiber.StatusBadRequest,
		},
		{
			name:           "ID does not match sub claim",
			idParam:        "different-id",
			authHeader:     "Bearer eyJhbGciOiAiUlMyNTYiLCAidHlwZSIgOiAiSldUIiwgImtpZCIgOiAiMnZmWGYiLCJhbGciOiAiUlMyNTYifQ.eyJzdWIiOiAiNGU5MGJhMzEtYjIxYy00MmNjLTgxYzMtZTE2ZWMwZDU4NjU2In0",
			expectedStatus: fiber.StatusForbidden,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/users/"+test.idParam, nil)
			if test.authHeader != "" {
				req.Header.Set("Authorization", test.authHeader)
			}
			resp, _ := app.Test(req, -1)
			assert.Equal(t, test.expectedStatus, resp.StatusCode)
		})
	}
}

func TestWithBypass(t *testing.T) {
	validator := validator.New()

	tests := []struct {
		name           string
		bypassAuth     bool
		authHeader     string
		expectedStatus int
		middleware     *apimock.AuthMiddleware
	}{
		{
			name:           "Bypass Authorization",
			bypassAuth:     true,
			authHeader:     "",
			expectedStatus: fiber.StatusOK,
			middleware:     &apimock.AuthMiddleware{},
		},
		{
			name:           "Do not Bypass, Authorization fails",
			bypassAuth:     false,
			authHeader:     "",
			expectedStatus: fiber.StatusUnauthorized,
			middleware: func() *apimock.AuthMiddleware {
				authMiddleware := &apimock.AuthMiddleware{}
				authMiddleware.On("WithAccessToken", mock.Anything).Return(
					fiber.NewError(fiber.StatusUnauthorized, "Authorization failed")).Once()
				return authMiddleware
			}(),
		},
		{
			name:           "Do not Bypass, Authorization succeeds",
			bypassAuth:     false,
			authHeader:     "Bearer eyJhbGciOiAiUlMyNTYiLCAidHlwZSIgOiAiSldUIiwgImtpZCIgOiAiMnZmWGYiLCJhbGciOiAiUlMyNTYifQ.eyJzdWIiOiAiNGU5MGJhMzEtYjIxYy00MmNjLTgxYzMtZTE2ZWMwZDU4NjU2In0",
			expectedStatus: fiber.StatusOK,
			middleware: func() *apimock.AuthMiddleware {
				authMiddleware := &apimock.AuthMiddleware{}
				authMiddleware.On("WithAccessToken", mock.Anything).Return(nil).Once()
				return authMiddleware
			}(),
		},
	}

	for _, test := range tests {
		app := fiber.New()
		app.Get("/test", func(c *fiber.Ctx) error {
			return withBypass(c, validator, test.middleware)
		}, func(c *fiber.Ctx) error {
			return c.SendStatus(fiber.StatusOK)
		})

		t.Run(test.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/test", nil)
			if test.bypassAuth {
				req.Header.Set("X-Bypass-Auth", "true")
			}
			if test.authHeader != "" {
				req.Header.Set("Authorization", test.authHeader)
			}

			resp, _ := app.Test(req, -1)
			assert.Equal(t, test.expectedStatus, resp.StatusCode)
		})
	}
}
