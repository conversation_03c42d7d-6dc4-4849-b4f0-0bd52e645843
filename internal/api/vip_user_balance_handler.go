package api

import (
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// VIPUserBalanceHandler handles VIP user balance HTTP requests.
type VIPUserBalanceHandler struct {
	vipUserBalanceService domain.VIPUserBalanceService
	validator             *validator.Validate
}

// NewVIPUserBalanceHandler creates new instance.
func NewVIPUserBalanceHandler(
	vipUserBalanceService domain.VIPUserBalanceService,
	validator *validator.Validate,
) *VIPUserBalanceHandler {
	return &VIPUserBalanceHandler{
		vipUserBalanceService: vipUserBalanceService,
		validator:             validator,
	}
}

// CreateVIPUserBalance creates a new VIP user balance.
// @Consume application/json
// @Param   body body    vipUserBalanceRequest true "VIP user balance request body."
// @Success 200 {object} nil
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router  /internal/v1/vipUserBalances [POST]
func (h *VIPUserBalanceHandler) CreateVIPUserBalance(c *fiber.Ctx) error {
	body, err := parseBody[vipUserBalanceRequest](c, h.validator)
	if err != nil {
		return err
	}

	vipUser := requestToVIPUserBalance(body)

	if _, err := h.vipUserBalanceService.CreateVIPUserBalance(c.Context(), vipUser); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusCreated)
}

// BumpUserVipStatus Create a new vip user balance and insert the corresponding clamied coins to users table.
// @Consume application/json
// @Param   body body    BumpUserVipStatusRequestBody true "VIP user balance request body."
// @Success 200 {object} nil
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router  /internal/v1/users/:id/vipStatus/bump [POST]
func (h *VIPUserBalanceHandler) BumpUserVipStatus(c *fiber.Ctx) error {
	body, err := parseBody[BumpUserVipStatusRequestBody](c, h.validator)
	if err != nil {
		return err
	}

	externalUserID := c.Params("id")

	err = h.vipUserBalanceService.BumpUser(
		c.Context(),
		domain.BumpUserVIPStatus{
			UserExternalID:  externalUserID,
			VIPStatusToBump: body.VipStatusToBump,
			AddBase:         body.AddBase,
			Source:          body.Source,
		},
	)
	if err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusCreated)
}
