package api

import (
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type UserHandler struct {
	userService             domain.UserService
	userConfigAssetsService domain.UserConfigAssetsService
	validator               *validator.Validate
	authService             domain.AuthenticationService
	redis                   *redis.Client
}

func NewUserHandler(
	userService domain.UserService,
	userConfigAssetsService domain.UserConfigAssetsService,
	validator *validator.Validate,
	authService domain.AuthenticationService,
	redisClient *redis.Client,
) *UserHandler {
	return &UserHandler{
		userService:             userService,
		userConfigAssetsService: userConfigAssetsService,
		validator:               validator,
		authService:             authService,
		redis:                   redisClient,
	}
}

// GetUserByIDOrUserName gets a user by ID or username.
// @Produce application/json
// @Param   idOrUserName path     string       true "User ID or UserName."
// @Success 200          {object} UserResponse
// @Failure 400          {object} fiber.Error
// @Failure 404          {object} fiber.Error
// @Router  /api/v1/users/{id} [get]
func (h *UserHandler) GetUserByIDOrUserName(c *fiber.Ctx) error {
	idOrUsername := c.Params("idOrUserName")
	var user *domain.User
	var isAdmin bool

	token := c.Get("Authorization")
	isAdmin = IsAdmin(c, token, h.authService)

	id, err := uuid.Parse(idOrUsername)
	if err != nil {
		user, err = h.userService.GetUserByUserName(c.Context(), idOrUsername)
		if err != nil {
			// If no real user found, try simulated users
			if h.redis != nil {
				if simUser := h.findSimulatedUser(idOrUsername); simUser != nil {
					return c.JSON(userToUserResponse(simUser, false, isAdmin))
				}
			}

			return err
		}
	} else {
		user, err = h.userService.GetUserByID(c.Context(), id)
		if err != nil {
			// If no real user found, try simulated users
			if h.redis != nil {
				if simUser := h.findSimulatedUser(idOrUsername); simUser != nil {
					return c.JSON(userToUserResponse(simUser, false, isAdmin))
				}
			}
			return err
		}
	}

	// if user.GhostMode {
	// 	return domain.ErrInGhostMode
	// }

	return c.JSON(userToUserResponse(user, false, isAdmin))
}

// GetUserByExternalUserID gets a user by exernal user ID with Rank.
// @Produce application/json
// @Param id            path   string true "Use external ID."
// @Param Authorization header string true "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string true "Refresh token."
// @Success 200 {object} LeaderboardUserResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router /api/v1/auth/users/{id} [get]
func (h *UserHandler) GetUserByExternalUserID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	user, err := h.userService.GetRankedUserByExternalID(c.Context(), userID)
	if err != nil {
		return err
	}

	return c.JSON(rankedUserToRankedUserResponse(user))
}

// UpdateUser updates a user.
// @Accept  application/json
// @Produce application/json
// @Param id            path   string            true "Use external ID."
// @Param body          body   updateUserRequest true "User update request body."
// @Param Authorization header string            true "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string            true "Refresh token."
// @Success 204 {string} string
// @Failure 400 {object} fiber.Error
// @Failure 401 {object} fiber.Error
// @Failure 403 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router /api/v1/users/{id} [patch]
func (h *UserHandler) UpdateUser(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	body, err := parseBody[updateUserRequest](c, h.validator)
	if err != nil {
		return err
	}

	user, err := h.userService.GetUserByExternalID(c.Context(), userID)
	if err != nil {
		return err
	}

	if body.GhostMode != nil {
		user.GhostMode = *body.GhostMode
	}

	if body.HideStats != nil {
		user.HideAllStats = *body.HideStats
	}

	user.FactorSID = body.FactorSID

	if body.Assets != nil {
		assetsTypes, err := h.userConfigAssetsService.GetAssetsTypes(c.Context())
		if err != nil {
			return err
		}

		for _, assetType := range assetsTypes {
			value, ok := body.Assets[assetType]
			if ok {
				asset, err := h.userConfigAssetsService.GetAsset(c.Context(), value)
				if err != nil {
					return err
				}

				if asset.Type != assetType {
					return domain.ErrInvalidAssetType
				}

				user.UserAssets = append(user.UserAssets, domain.UserAsset{
					Type:            assetType,
					UserConfigAsset: *asset,
					User:            *user,
				})
			}

		}
	}

	if err := h.userService.UpdateUser(c.Context(), user); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// RegisterEmail stores the register email.
// @Accept  application/json
// @Produce application/json
// @Param body        body   registerEmailRequest true  "Register email request body."
// @Success 200 {string} string
// @Success 201 {string} string
// @Failure 400 {object} fiber.Error
// @Failure 401 {object} fiber.Error
// @Failure 403 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 409 {object} fiber.Error
// @Router /api/v1/registerEmail [post]
func (h *UserHandler) RegisterEmail(c *fiber.Ctx) error {
	body, err := parseBody[registerEmailRequest](c, h.validator)
	if err != nil {
		return err
	}

	registerEmail, err := h.userService.GetRegisteredEmail(c.Context(), body.Email, body.EmailMarketing)
	if err != nil {
		return err
	}

	if registerEmail != nil && registerEmail.Registered {
		return c.SendStatus(fiber.StatusConflict)
	}

	return c.SendStatus(fiber.StatusCreated)
}

func (h *UserHandler) GetWinnerOfTheMonth(c *fiber.Ctx) error {
	winners, err := h.userService.GetWinnerOfTheMonth(c.Context())
	if err != nil {
		return err
	}

	return c.JSON(winners)
}

func (h *UserHandler) UpdateUserXP(c *fiber.Ctx) error {
	var data []struct {
		XP     string `json:"topup"`
		UserId string `json:"userId"`
	}

	if err := c.BodyParser(&data); err != nil {
		slog.Error("Failed to parse request body", "error", err)
		return c.SendStatus(fiber.StatusBadRequest)
	}

	for _, d := range data {
		xp, err := strconv.ParseFloat(d.XP, 64)
		if err != nil {
			slog.Error("Failed to parse XP", "error", err)
			return c.SendStatus(fiber.StatusBadRequest)
		}
		if _, err := h.userService.UpdateUserXP(c.Context(), d.UserId, xp); err != nil {
			slog.Error("Failed to update user XP", "error", err)
			return err
		}
	}

	return c.SendStatus(fiber.StatusOK)
}

func (h *UserHandler) GetUsersXPAndUpdateInElantil(c *fiber.Ctx) error {
	if err := h.userService.GetUsersXPAndUpdateInElantil(c.Context()); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusOK)
}

func (h *UserHandler) UpdateUserPreferences(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	var data struct {
		GhostMode *bool `json:"ghostMode,omitempty"`
		HideStats *bool `json:"hideStats,omitempty"`
	}
	if err := c.BodyParser(&data); err != nil {
		slog.Error("Failed to parse request body", "error", err)
		return c.SendStatus(fiber.StatusBadRequest)
	}

	if err := h.userService.UpdateUserPreferences(c.Context(), userID, data.GhostMode, data.HideStats); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusOK)
}

func (h *UserHandler) UpdateUserMultiCurrency(c *fiber.Ctx) error {
	var data struct {
		ExternalID    string `json:"external_id"`
		MultiCurrency string `json:"multi_currency"`
	}

	if err := c.BodyParser(&data); err != nil {
		slog.Error("Failed to parse request body", "error", err)
		return c.SendStatus(fiber.StatusBadRequest)
	}

	var multiCurrency bool
	MultiCurrencyLower := strings.ToLower(data.MultiCurrency)
	if MultiCurrencyLower != "true" && MultiCurrencyLower != "false" {
		return c.SendStatus(fiber.StatusBadRequest)
	} else if MultiCurrencyLower == "true" {
		multiCurrency = true
	} else {
		multiCurrency = false
	}

	if err := h.userService.UpdateUserMultiCurrency(c.Context(), data.ExternalID, multiCurrency); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusOK)
}

// findSimulatedUser iterates through recent simulated bets to find matching user by
// UUID or username. Returns nil if no match or if operation takes longer than 5 seconds.
func (h *UserHandler) findSimulatedUser(idOrUsername string) *domain.User {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var (
		targetID uuid.UUID
		isUUID   bool
	)
	if id, err := uuid.Parse(idOrUsername); err == nil {
		targetID, isUUID = id, true
	}

	// Check if context has timed out before Redis operation
	select {
	case <-ctx.Done():
		slog.Warn("findSimulatedUser timed out before Redis operation", "searchTerm", idOrUsername)
		return nil
	default:
	}

	if h.redis == nil {
		return nil
	}

	// Get all slated bets (not just recent ones) to find the user
	vals, err := h.redis.ZRange(ctx, utils.GetSlatedBetsKey(), 0, -1).Result()
	if err != nil {
		if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
			slog.Error("redis fetch slated bets", "error", err)
		}
		return nil
	}

	// newest last, iterate reverse
	for i := len(vals) - 1; i >= 0; i-- {
		// Check if context has timed out during iteration
		select {
		case <-ctx.Done():
			slog.Warn("findSimulatedUser timed out during iteration", "searchTerm", idOrUsername)
			return nil
		default:
		}

		var bet domain.Bet
		if json.Unmarshal([]byte(vals[i]), &bet) != nil {
			continue
		}
		u := bet.User
		if (isUUID && u.ID == targetID) || u.UserName == idOrUsername {
			if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
				slog.Debug("Found slated bet user",
					"searchTerm", idOrUsername,
					"userId", u.ID,
					"userName", u.UserName,
					"vipStatus", u.VIPStatus,
					"isUUID", isUUID)
			}
			return &u
		}
	}
	if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
		slog.Debug("No slated bet user found", "searchTerm", idOrUsername, "isUUID", isUUID)
	}
	return nil
}

func (h *UserHandler) IsUserNameUnique(c *fiber.Ctx) error {
	userName := c.Params("userName")
	if userName == "" {
		return fiber.NewError(fiber.StatusBadRequest, "User name is required")
	}

	isUnique, err := h.userService.IsUserNameUnique(c.Context(), strings.ToLower(userName))
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to check username uniqueness")
	}

	return c.JSON(fiber.Map{
		"is_unique": isUnique,
	})
}
