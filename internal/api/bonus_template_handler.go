package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type BonusTemplateHandler struct {
	bonusService domain.BonusTemplateService
}

func NewBonusTemplateHandler(bonusService domain.BonusTemplateService) *BonusTemplateHandler {
	return &BonusTemplateHandler{bonusService: bonusService}
}

func (h *BonusTemplateHandler) CreateBonusTemplate(ctx *fiber.Ctx) error {
	var req domain.CreateBonusTemplateRequest
	if err := ctx.BodyParser(&req); err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	if err := h.bonusService.CreateBonusTemplate(ctx.Context(), &req); err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.SendStatus(fiber.StatusCreated)
}

func (h *BonusTemplateHandler) GetBonusTemplateByOfferCode(ctx *fiber.Ctx) error {
	username, ok := utils.GetUserNameFromContext(ctx)
	if !ok {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "username not found in context",
		})
	}

	bonusTemplate, err := h.bonusService.GetUserBonusTemplates(ctx.Context(), username)
	if err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.JSON(bonusTemplate)
}

func (h *BonusTemplateHandler) UpdateBonusTemplate(ctx *fiber.Ctx) error {
	username, ok := utils.GetUserNameFromContext(ctx)
	if !ok {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "username not found in context",
		})
	}

	var req domain.UserBonusTemplate
	if err := ctx.BodyParser(&req); err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	req.UserName = username

	if err := h.bonusService.UpdateBonusTemplate(ctx.Context(), &req); err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.SendStatus(fiber.StatusOK)
}

func (h *BonusTemplateHandler) DeleteBonusTemplate(ctx *fiber.Ctx) error {
	username, ok := utils.GetUserNameFromContext(ctx)
	if !ok {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "username not found in context",
		})
	}

	offerCode := ctx.Params("offerCode")
	if offerCode == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "offer_code is required",
		})
	}

	if err := h.bonusService.DeleteBonusTemplate(ctx.Context(), offerCode, username); err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.SendStatus(fiber.StatusNoContent)
}