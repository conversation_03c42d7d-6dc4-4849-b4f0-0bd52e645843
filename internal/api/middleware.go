package api

import (
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type AuthMiddleware interface {
	WithAccessToken(c *fiber.Ctx) error
	WithBypass(c *fiber.Ctx) error
}

func withBypass(c *fiber.Ctx, v *validator.Validate, m AuthMiddleware) error {
	type bypassAuthHeaders struct {
		BypassAuth bool `reqHeader:"X-Bypass-Auth"`
	}

	headers, err := parseHeaders[bypassAuthHeaders](c, v)
	if err != nil {
		return err
	}

	if headers.BypassAuth {
		return c.Next()
	}

	return m.WithAccessToken(c)
}
