package api

import (
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
)

var (
	errTest = errors.New("test error")

	testBetUUID        = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	testUserUUID       = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	testUserExternalID = "1234567"

	testBet = domain.Bet{
		ID:   testUserUUID,
		User: domain.User{UserName: "un user", VIPStatus: "un vip status"},
		Game: domain.Game{Name: nil},
	}

	testBets = &domain.Bets{
		Items: []domain.Bet{testBet},
		Paging: domain.PagingWithoutTotalCount{
			CurrentPage: 1,
			PageSize:    10,
		},
	}
)

func createMockAuthService(t *testing.T) *domainmock.AuthenticationService {
	authService := domainmock.NewAuthenticationService(t)
	authService.On("ValidateAccessToken", mock.Anything, mock.Anything).
		Return(domain.ValidateTokenResponse{
			UserID:   "test-user-id",
			UserName: "test-user",
			Email:    "<EMAIL>",
		}, nil).Maybe()
	return authService
}

func TestBetHandler_GetBets(t *testing.T) {
	testCases := []struct {
		name            string
		inputBetID      string
		betService      *domainmock.BetService
		expectedStatus  int
		route           string
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "Success",
			betService: func() *domainmock.BetService {
				betService := domainmock.NewBetService(t)
				betService.EXPECT().GetBets(
					mock.Anything,
					&domain.GetBetsParams{
						OrderParams: domain.OrderParams{
							OrderBy: "time",
							Order:   domain.OrderDirDesc,
						},
						PagingParams: domain.PagingParams{
							PageNumber: 1,
							PageSize:   10,
						},
						FilterBetsByGame: &[]bool{false}[0],
						BetType:          &[]string{"CASINO"}[0],
					},
				).Return(testBets, nil).Once()
				return betService
			}(),
			expectedStatus:  fiber.StatusOK,
			route:           "/api/v1/bets?pageSize=10&pageNum=1",
			expectedErrFunc: require.NoError,
		},
		{
			name: "EmptyPagingParams",
			betService: func() *domainmock.BetService {
				betService := domainmock.NewBetService(t)
				betService.EXPECT().GetBets(
					mock.Anything,
					&domain.GetBetsParams{
						OrderParams: domain.OrderParams{
							OrderBy: "time",
							Order:   domain.OrderDirDesc,
						},
						PagingParams: domain.PagingParams{
							PageNumber: 1,
							PageSize:   10,
						},
						FilterBetsByGame: &[]bool{false}[0],
						BetType:          &[]string{"CASINO"}[0],
					},
				).Return(testBets, nil).Once()
				return betService
			}(),
			expectedStatus:  fiber.StatusOK,
			route:           "/api/v1/bets",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "PageNumLessThan1",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?pageNum=0",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "InvalidPageNum",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?pageNum=invalid",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "PageSizeLessThan1",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?pageSize=0",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "PageSizeGreaterThan100",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?pageSize=101",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "InvalidPageSize",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?pageSize=invalid",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "InvalidOrder",
			betService:      domainmock.NewBetService(t),
			expectedStatus:  fiber.StatusBadRequest,
			route:           "/api/v1/bets?order=invalid",
			expectedErrFunc: require.NoError,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			app := NewApp()
			mockAuthService := createMockAuthService(t)

			SetUpRoutes(app, &Dependencies{
				BetHandler: NewBetHandler(tc.betService, validator.New(), mockAuthService, nil),
			})

			req := httptest.NewRequest("GET", tc.route, nil)
			resp, err := app.Test(req, -1)
			tc.expectedErrFunc(t, err)
			assert.Equal(t, tc.expectedStatus, resp.StatusCode)
		})
	}
}

func TestBetHandler_GetBetByExternalID(t *testing.T) {
	testBet := &domain.Bet{
		ID:   testBetUUID,
		User: domain.User{UserName: "user", VIPStatus: "vip"},
		Game: domain.Game{Name: nil},
	}
	testCases := []struct {
		name            string
		betServiceMock  func() *domainmock.BetService
		authServiceMock func() *domainmock.AuthenticationService
		route           string
		authHeader      string
		expectedStatus  int
		expectedErrFunc require.ErrorAssertionFunc
	}{
		{
			name: "Valid ExternalID, isAdmin false",
			betServiceMock: func() *domainmock.BetService {
				bs := domainmock.NewBetService(t)
				bs.EXPECT().
					GetBetByExternalID(mock.Anything, testUserExternalID).
					Return(testBet, nil).Once()
				return bs
			},
			authServiceMock: func() *domainmock.AuthenticationService {
				as := domainmock.NewAuthenticationService(t)
				as.On("ValidateAccessToken", mock.Anything, "Bearer testtoken").
					Return(domain.ValidateTokenResponse{
						UserID:   "test-user-id",
						UserName: "test-user",
						Email:    "<EMAIL>",
					}, nil).Once()
				return as
			},
			route:           "/api/v1/bets/" + testUserExternalID,
			authHeader:      "Bearer testtoken",
			expectedStatus:  fiber.StatusOK,
			expectedErrFunc: require.NoError,
		},
		{
			name: "Valid ExternalID, isAdmin true",
			betServiceMock: func() *domainmock.BetService {
				bs := domainmock.NewBetService(t)
				bs.EXPECT().
					GetBetByExternalID(mock.Anything, testUserExternalID).
					Return(testBet, nil).Once()
				return bs
			},
			authServiceMock: func() *domainmock.AuthenticationService {
				as := domainmock.NewAuthenticationService(t)
				as.On("ValidateAccessToken", mock.Anything, "Bearer adminToken").
					Return(domain.ValidateTokenResponse{
						UserID:   "admin-user-id",
						UserName: "admin-user",
						Email:    "<EMAIL>",
					}, nil).Once()
				return as
			},
			route:           "/api/v1/bets/" + testUserExternalID,
			authHeader:      "Bearer adminToken",
			expectedStatus:  fiber.StatusOK,
			expectedErrFunc: require.NoError,
		},
		{
			name: "Invalid ExternalID param",
			betServiceMock: func() *domainmock.BetService {
				bs := domainmock.NewBetService(t)
				bs.EXPECT().
					GetBetByExternalID(mock.Anything, mock.AnythingOfType("string")).
					Return(nil, domain.ErrResourceNotFound).Maybe()
				return bs
			},
			authServiceMock: func() *domainmock.AuthenticationService {
				as := domainmock.NewAuthenticationService(t)
				as.On("ValidateAccessToken", mock.Anything, mock.Anything).
					Return(domain.ValidateTokenResponse{
						UserID:   "test-user-id",
						UserName: "test-user",
						Email:    "<EMAIL>",
					}, nil).Maybe()
				return as
			},
			route:           "/api/v1/bets/!!invalid!!",
			authHeader:      "Bearer testtoken",
			expectedStatus:  fiber.StatusNotFound,
			expectedErrFunc: require.NoError,
		},
		{
			name: "Bet not found",
			betServiceMock: func() *domainmock.BetService {
				bs := domainmock.NewBetService(t)
				bs.EXPECT().
					GetBetByExternalID(mock.Anything, testUserExternalID).
					Return(nil, domain.ErrResourceNotFound).Once()
				return bs
			},
			authServiceMock: func() *domainmock.AuthenticationService {
				as := domainmock.NewAuthenticationService(t)
				as.On("ValidateAccessToken", mock.Anything, "Bearer testtoken").
					Return(domain.ValidateTokenResponse{
						UserID:   "test-user-id",
						UserName: "test-user",
						Email:    "<EMAIL>",
					}, nil).Once()
				return as
			},
			route:           "/api/v1/bets/" + testUserExternalID,
			authHeader:      "Bearer testtoken",
			expectedStatus:  fiber.StatusNotFound,
			expectedErrFunc: require.NoError,
		},
		{
			name: "AuthService returns error, isAdmin false",
			betServiceMock: func() *domainmock.BetService {
				bs := domainmock.NewBetService(t)
				bs.EXPECT().
					GetBetByExternalID(mock.Anything, testUserExternalID).
					Return(testBet, nil).Once()
				return bs
			},
			authServiceMock: func() *domainmock.AuthenticationService {
				as := domainmock.NewAuthenticationService(t)
				as.On("ValidateAccessToken", mock.Anything, "Bearer badtoken").
					Return(domain.ValidateTokenResponse{}, errors.New("auth error")).Once()
				return as
			},
			route:           "/api/v1/bets/" + testUserExternalID,
			authHeader:      "Bearer badtoken",
			expectedStatus:  fiber.StatusOK,
			expectedErrFunc: require.NoError,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			app := NewApp()
			betHandler := NewBetHandler(tc.betServiceMock(), validator.New(), tc.authServiceMock(), nil)
			SetUpRoutes(app, &Dependencies{
				BetHandler:               betHandler,
				AuthenticationMiddleware: middlewareMock,
			})

			req := httptest.NewRequest("GET", tc.route, nil)
			if tc.authHeader != "" {
				req.Header.Set("Authorization", tc.authHeader)
			}
			resp, err := app.Test(req, -1)
			tc.expectedErrFunc(t, err)
			assert.Equal(t, tc.expectedStatus, resp.StatusCode)
		})
	}
}

var middlewareMock = func(c *fiber.Ctx) error {
	return c.Next()
}
