package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type GameResponse struct {
	ID          string  `json:"id"`
	CMSGameID   int64   `json:"cms_game_id"`
	ExternalID  string  `json:"external_id"`
	Name        *string `json:"name"`
	Slug        *string `json:"slug"`
	ThumbnailID *string `json:"thumbnail_id,omitempty"`
}

func gameToGameResponse(g *domain.Game) *GameResponse {
	return &GameResponse{
		ID:          g.ID.String(),
		CMSGameID: g.CMSGameID,
		ExternalID:  g.ExternalID,
		Name:        g.Name,
		Slug:        g.Slug,
		ThumbnailID: g.ThumbnailID,
	}
}
