package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type ToastHandler struct {
	toastRepository *postgres.ToastRepository
}

func NewToastHandler(toastRepository *postgres.ToastRepository) *ToastHandler {
	return &ToastHandler{toastRepository: toastRepository}
}

func (h *ToastHandler) CreateToast(c *fiber.Ctx) error {

	type CreateToastRequest struct {
		Id        int      `json:"id"`
		Message   string   `json:"message"`
		Published bool     `json:"published"`
		Location  []string `json:"location"`
		Type      string   `json:"type"`
		Catagory  string   `json:"catagory"`
		UpdatedAt string   `json:"updated_at"`
		CreatedAt string   `json:"created_at"`
	}

	var req []CreateToastRequest
	if err := c.<PERSON>(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}
	external_id := req[0].Id

	if req[0].Message == "" || len(req[0].Location) == 0 || req[0].Type == "" || int(external_id) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "message, location, and type are required",
		})
	}

	toast, err := h.toastRepository.CreateToast(
		int(external_id),
		req[0].Message,
		req[0].Published,
		req[0].Location,
		req[0].Type,
		req[0].Catagory,
	)

	if err != nil {

		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(toast)
}

func (h *ToastHandler) GetPublishedToasts(c *fiber.Ctx) error {
	toasts, err := h.toastRepository.GetPublishedToasts()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusOK).JSON(toasts)
}

func (h *ToastHandler) UnpublishToast(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "invalid UUID",
		})
	}

	if err := h.toastRepository.UnpublishToast(id); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.SendStatus(fiber.StatusOK)
}
