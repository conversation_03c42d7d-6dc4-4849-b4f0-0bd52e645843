package api

import (
	"encoding/base64"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/limiter"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type AuthenticationMiddleware struct {
	validator             *validator.Validate
	authenticationService domain.AuthenticationService
}

func NewAuthenticationMiddleware(
	validator *validator.Validate,
	authenticationService domain.AuthenticationService,
) *AuthenticationMiddleware {
	return &AuthenticationMiddleware{
		validator:             validator,
		authenticationService: authenticationService,
	}
}

func (m *AuthenticationMiddleware) WithAccessToken(c *fiber.Ctx) error {
	headers, err := parseHeaders[authenticationHeaders](c, m.validator)
	if err != nil {
		return err
	}

	resp, err := m.authenticationService.ValidateAccessToken(c.Context(), headers.Authorization)
	if err != nil {
		slog.Error("Token validation failed", "error", err)
		return fiber.NewError(fiber.StatusUnauthorized, fmt.Sprintf("Token validation failed: %v", err))
	}
	c.Locals(utils.UserIDKey, resp.UserID)
	c.Locals(utils.UserNameKey, resp.UserName)
	c.Locals(utils.UserEmailKey, resp.Email)
	if _, ok := utils.GetUserIDFromContext(c); !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to set user ID in context")
	}

	if _, ok := utils.GetUserNameFromContext(c); !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to set user name in context")
	}

	if _, ok := utils.GetUserEmailFromContext(c); !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to set user email in context")
	}

	return c.Next()
}

func (m *AuthenticationMiddleware) WithBypass(c *fiber.Ctx) error {
	return withBypass(c, m.validator, m)
}

type DirectusAuthentication struct {
	validator *validator.Validate
	config    *utils.DirectusConfig
}

func NewDirectusAuthentication(validator *validator.Validate, config *utils.DirectusConfig) *DirectusAuthentication {
	return &DirectusAuthentication{
		validator: validator,
		config:    config,
	}
}

func (m *DirectusAuthentication) WithAccessToken(c *fiber.Ctx) error {
	authHeaders := c.Get("Authorization")
	if authHeaders == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Authorization header is required",
		})
	}

	token := authHeaders
	if strings.HasPrefix(authHeaders, "Bearer ") {
		token = strings.TrimPrefix(authHeaders, "Bearer ")
	}

	if token != m.config.AuthenticationToken {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Invalid authorization token",
		})
	}

	return c.Next()
}

func RateLimitMiddleware(max int, duration time.Duration) fiber.Handler {
	return limiter.New(limiter.Config{
		Max:        max,
		Expiration: duration,
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.IP() + ":" + c.Get("Authorization")
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(429).JSON(fiber.Map{
				"error": "Too many requests",
			})
		},
	})
}

type SwaggerMiddleware struct {
	config *utils.SwaggerConfig
}

func NewSwaggerMiddleware(config *utils.SwaggerConfig) *SwaggerMiddleware {
	return &SwaggerMiddleware{
		config: config,
	}
}

func (m *SwaggerMiddleware) WithPassword(c *fiber.Ctx) error {
	auth := c.Get("Authorization")
	if auth == "" {
		c.Set("WWW-Authenticate", "Basic realm=\"Swagger Documentation\"")
		return c.SendStatus(fiber.StatusUnauthorized)
	}
	parts := strings.Split(auth, " ")
	if len(parts) != 2 || parts[0] != "Basic" {
		return c.SendStatus(fiber.StatusUnauthorized)
	}
	decoded, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return c.SendStatus(fiber.StatusUnauthorized)
	}
	creds := strings.Split(string(decoded), ":")
	if len(creds) != 2 {
		return c.SendStatus(fiber.StatusUnauthorized)
	}
	if creds[0] != m.config.Username || creds[1] != m.config.Password {
		return c.SendStatus(fiber.StatusUnauthorized)
	}

	return c.Next()
}
