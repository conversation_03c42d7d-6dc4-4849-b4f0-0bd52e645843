package api

import (
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cache"
	"github.com/gofiber/swagger"

	_ "github.com/Monkey-Tilt/monketilt/Golang/community/internal/api/docs"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/gofiber/fiber/v2/middleware/monitor"
)

func NewApp() *fiber.App {
	return fiber.New(fiber.Config{
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  120 * time.Second,
		BodyLimit:    50 * 1024 * 1024,
		ErrorHandler: func(ctx *fiber.Ctx, err error) error {
			slog.Error("Failed to handle request", slog.Any("error", err))

			message := err.Error()

			var code int
			var ferr *fiber.Error

			if errors.As(err, &ferr) {
				code = ferr.Code
			} else {
				switch {
				case errors.Is(err, domain.ErrResourceNotFound):
					code = fiber.StatusNotFound
				case errors.Is(err, domain.ErrInGhostMode):
					code = fiber.StatusNotFound
				case errors.Is(err, domain.ErrInvalidParameter):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrInvalidAssetType):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrResourceGone):
					code = fiber.StatusGone
				case errors.Is(err, domain.ErrInvalidVipUserToBump):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrInvalidVIPUserToBumpTo):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrBonusDropLimitReached):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrBonusDropExpiredOrNotActive):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrBonusDropAlreadyClaimed):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrWageringRequirementsNotMet):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrBonusDropNotFound):
					code = fiber.StatusNotFound
				case errors.Is(err, domain.ErrUserAlreadyHasLocalWallet):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrUserCountryNotFound):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrUserNotEligibleForLocalWallet):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrInsufficientWalletBalance):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrWalletNotFound):
					code = fiber.StatusBadRequest
				case errors.Is(err, domain.ErrUsernameAlreadyExists):
					code = fiber.StatusBadRequest
				default:
					code = fiber.StatusInternalServerError
					// Don't use the error value here since we don't want to leak internal
					// information for these kind of errors.
					message = "internal server error"
				}
			}

			return ctx.Status(code).JSON(fiber.Error{
				Code:    code,
				Message: fmt.Sprintf("Failed to handle request: %v", message),
			})
		},
	})
}

type Dependencies struct {
	BetHandler               *BetHandler
	BoostHandler             *BoostHandler
	GameHandler              *GameHandler
	LeaderboardHandler       *LeaderboardHandler
	TransactionHandler       *TransactionHandler
	UserConfigAsssetsHandler *UserConfigAsssetsHandler
	UserHandler              *UserHandler
	VIPUserBalanceHandler    *VIPUserBalanceHandler
	AuthenticationMiddleware fiber.Handler
	AuthorizationMiddleware  fiber.Handler
	CSPMiddleware            *CSPMiddleware
	BonusHandler             *BonusHandler
	UserBonusHandler         *UserBonusHandler
	ElantilClientHandler     *ElantilClientHandler
	RefferalHandler          *ReferralHandler
	RailsClientHandler       *RestClientHandler
	MaintenanceToastHandler  *ToastHandler
	DirectusMiddleware       fiber.Handler
	SwaggerMiddleware        fiber.Handler
	RecoverXpHandler         *RecoverXpHandler
	SettingsHandler          *SettingsHandler
	BonusTemplateHandler     *BonusTemplateHandler
	CMSTemplateHandler       *CMSTermsAndConditionsHandler
	EmailCampaignsHandler    *EmailCampaignsHandler
	HealthHandler            *HealthHandler
	BonusDropHandler         *BonusDropHandler
	SlatedUsernameHandler    *SlatedUsernameHandler
	PasswordResetHandler     *PasswordResetHandler
}

func SetupRecoveryRoutes(app *fiber.App, recoveryHandler *RecoveryHandler) {
	recovery := app.Group("/api/recovery")
	recovery.Patch("/user/tx", recoveryHandler.RecoverUsersTransactions)
}

// @title       Community API
// @version     1.0
// @description Community API.
func SetUpRoutes(app *fiber.App, dependencies *Dependencies) {
	userConfigAssetsHandler := dependencies.UserConfigAsssetsHandler
	betHandler := dependencies.BetHandler
	gameHandler := dependencies.GameHandler
	leaderboardHandler := dependencies.LeaderboardHandler
	userHandler := dependencies.UserHandler
	boostHandler := dependencies.BoostHandler
	transactionHandler := dependencies.TransactionHandler
	authenticationMiddleware := dependencies.AuthenticationMiddleware
	bonusHandler := dependencies.BonusHandler
	userBonusHandler := dependencies.UserBonusHandler
	elantilClientHandler := dependencies.ElantilClientHandler
	referralHandler := dependencies.RefferalHandler
	railsClientHandler := dependencies.RailsClientHandler
	maintainanceToastHandler := dependencies.MaintenanceToastHandler
	directusMiddleware := dependencies.DirectusMiddleware
	swaggerMiddleware := dependencies.SwaggerMiddleware
	recoverXpHandler := dependencies.RecoverXpHandler
	settingsHandler := dependencies.SettingsHandler
	bonusTemplateHandler := dependencies.BonusTemplateHandler
	cmsTemplateHandler := dependencies.CMSTemplateHandler
	emailCampaignsHandler := dependencies.EmailCampaignsHandler
	healthHandler := dependencies.HealthHandler
	bonusDropHandler := dependencies.BonusDropHandler
	slatedUsernameHandler := dependencies.SlatedUsernameHandler
	passwordResetHandler := dependencies.PasswordResetHandler
	app.Get("/ping", func(c *fiber.Ctx) error {
		return c.SendString("pong")
	})

	app.Get("/test", func(c *fiber.Ctx) error {
		return c.SendString("WAZZUUOUUUP!")
	})

	api := app.Group("/api/v1")

	// Swagger UI
	api.Get("/swagger", swaggerMiddleware, swagger.HandlerDefault)
	api.Get("/swagger/*", swaggerMiddleware, swagger.HandlerDefault)

	cacheMid := cache.New(cache.Config{
		Expiration: 30 * time.Second,
		MaxBytes:   5 * 1024 * 1024 * 1024, // 5 GiB
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.OriginalURL()
		},
	})

	// Bet routes
	api.Get("/bets", cacheMid, betHandler.GetBets)
	api.Get("/users/bets/retrieve", authenticationMiddleware, betHandler.GetBetsByUserID)
	api.Get("/bets/:id", cacheMid, betHandler.GetBetByExternalID)
	api.Get("/users/bets/export", authenticationMiddleware, betHandler.ExportBetsByUserExternalID)
	api.Get("/users/bets/:betId/share", authenticationMiddleware, betHandler.ShareUserBetByBetId)
	api.Get("/users/latestBets", cacheMid, authenticationMiddleware, betHandler.GetLatestBetsByUserId)
	// slack notification
	api.Post("/slack/notification/cms", healthHandler.SendSlackNotificationForCMSAlert)
	// Recover XP routes
	api.Get("/recover/xp", recoverXpHandler.RecoverXP)

	// maintainance toast routes
	api.Post("/toasts/add", directusMiddleware, maintainanceToastHandler.CreateToast)
	api.Get("/toasts/published", maintainanceToastHandler.GetPublishedToasts)
	api.Get("/toasts/unpublish/:id", directusMiddleware, maintainanceToastHandler.UnpublishToast)

	// Transaction routes
	api.Get("/transactions", authenticationMiddleware, transactionHandler.GetTransactions)
	api.Get("/users/transaction/export", authenticationMiddleware, transactionHandler.ExportTranscationsByUserExternalID)
	api.Get("/transactions/:userId", transactionHandler.GetTransactionByType)

	// Register routes
	api.Post("/registerEmail", userHandler.RegisterEmail)

	// User routes
	api.Get("/users/:idOrUserName", userHandler.GetUserByIDOrUserName)
	api.Get("/auth/users", authenticationMiddleware, userHandler.GetUserByExternalUserID)

	// Password Reset routes
	api.Post("/auth/reset", passwordResetHandler.InitiatePasswordReset)
	api.Post("/auth/reset/validate", passwordResetHandler.ValidatePasswordResetToken)
	api.Post("/auth/reset/complete", passwordResetHandler.CompletePasswordReset)

	api.Patch("/users/preferences/update", authenticationMiddleware, userHandler.UpdateUserPreferences)
	api.Patch("/users", authenticationMiddleware, userHandler.UpdateUser)
	api.Get("/users/unique/:username", userHandler.IsUserNameUnique)

	// User asset routes
	api.Get("/profile/assets", cacheMid, userConfigAssetsHandler.GetAssets)

	// Boost routes
	api.Get("/boost/users", authenticationMiddleware, boostHandler.BoostAvailableUser)
	api.Patch("/auth/boost/users", authenticationMiddleware, boostHandler.BoostUser)

	// Leaderboard routes
	api.Get("/leaderboard", cacheMid, leaderboardHandler.GetLeaderboard)
	api.Get("/month/winner", cacheMid, userHandler.GetWinnerOfTheMonth)

	internal := app.Group("/internal/v1")
	api.Post("/games/update", gameHandler.UpdateAllGames)

	// Bonus routes
	api.Get("/list/users/bonuses", authenticationMiddleware, userBonusHandler.GetUserBonusesByExternalID)
	api.Post("/single_bonus/:id/claim", RateLimitMiddleware(1, 5*time.Second), userBonusHandler.SingleClaimUpdate)
	api.Post("/all_bonus/:id/claim", RateLimitMiddleware(1, 5*time.Second), userBonusHandler.ClaimAllBonusAndUpdateWallet)
	api.Post("/tilt_bonus/:id/claim", RateLimitMiddleware(1, 5*time.Second), userBonusHandler.ClaimTiltBonusAndUpdateWallet)
	api.Get("/level-up-bonus", authenticationMiddleware, userBonusHandler.GetLevelUpBonusOfUser)
	api.Delete("/bonus/expired", userBonusHandler.DeleteExpiredBonuses)

	// Directus specific routes
	api.Patch("/users/bonuses/activate", directusMiddleware, userBonusHandler.ActivateUserBonusesByExternalIds)
	api.Post("/users/bonus/reload", directusMiddleware, userBonusHandler.CreateReloadBonuses)
	api.Post("/bonus/assign/:type", directusMiddleware, userBonusHandler.AssignBonusByType)
	api.Post("/users/bonuses/special", directusMiddleware, userBonusHandler.AssignSpecialBonus)
	api.Post("/referral/commission-percentage", directusMiddleware, referralHandler.UpdateUserCommisionPercentage)
	api.Post("/bonus/config", directusMiddleware, bonusHandler.StoreBonusConfig)
	api.Patch("/referral/updateParentId", directusMiddleware, referralHandler.ManuallyUpdateReferralOfUser)
	api.Post("/admin/campaign", directusMiddleware, referralHandler.CreateAdminCampaign)
	api.Patch("/xp/update", directusMiddleware, userHandler.UpdateUserXP)
	api.Patch("/commission/all", directusMiddleware, referralHandler.CalculateAllUsersCommissions)

	// elantil tags routes and send verification email
	api.Post("/users/add/tags", authenticationMiddleware, elantilClientHandler.UpsertPlayerActivityTagsByUserExternalID)
	api.Get("/users/tags/list", authenticationMiddleware, elantilClientHandler.GetPlayerActivityTagsByUserExternalID)
	api.Post("/email/verification/:email", elantilClientHandler.SendVerificationEmail) // TODO:
	api.Get("/users/elantil/exist", authenticationMiddleware, elantilClientHandler.CheckIfUserExistsInElantilSystemAndUpdatePlayerTags)
	api.Post("/validate/token", elantilClientHandler.ValidateToken)
	api.Post("/bonus/template/forfeit/:bonusId", authenticationMiddleware, elantilClientHandler.ForfeitBonus)
	api.Get("/owner/bonuses/", authenticationMiddleware, elantilClientHandler.GetOwnerBonusesByUserId)
	api.Get("/conversion/rates", cacheMid, elantilClientHandler.GetConversionRates)
	api.Get("/user/profile", authenticationMiddleware, elantilClientHandler.GetUserProfile)
	api.Get("/user/wallet", authenticationMiddleware, elantilClientHandler.GetUserWallet)
	api.Post("/user/wallet", authenticationMiddleware, elantilClientHandler.CreateUserLocalWallet)
	api.Put("/user/profile", authenticationMiddleware, elantilClientHandler.UpdateUserProfile)
	api.Put("/user/complete/social", authenticationMiddleware, elantilClientHandler.CompleteSocialProfile)

	// referral routes
	api.Post("/referral", RateLimitMiddleware(5, 2*time.Second), authenticationMiddleware, referralHandler.CreateUserReferralCampaign)
	api.Get("/referral", authenticationMiddleware, referralHandler.GetUserReferralCampaign)
	api.Post("/referral/:referralCode/:username", authenticationMiddleware, referralHandler.AddUserToTheListOfRefferedUsers)
	api.Get("/code/:referralCode/check", authenticationMiddleware, referralHandler.CheckIfUserAlreadyUsedTheCode)

	// claim referral commisions
	api.Post("/referral_comission/claim/:id", RateLimitMiddleware(1, 2*time.Second), referralHandler.ClaimCommission)
	api.Get("/referral_comission/history", authenticationMiddleware, referralHandler.GetCommissionHistory)
	api.Get("/users/commission/info", authenticationMiddleware, referralHandler.GetDetailedCommissionInfo)
	api.Get("/isReferral", authenticationMiddleware, referralHandler.IsUserInAnyReferralCampaign)
	api.Post("/referral/default", authenticationMiddleware, referralHandler.CreateDefaultCampaignInDirectus)
	api.Patch("/updateModalPopupClosed", authenticationMiddleware, userBonusHandler.UpdateModalPopupClosed)

	// user campaign routes
	api.Delete("/campaigns/:campaignName", authenticationMiddleware, referralHandler.DeleteCampaign)

	// settings routes
	api.Post("/settings", authenticationMiddleware, settingsHandler.CreateSettings)
	api.Get("/settings", authenticationMiddleware, settingsHandler.GetSettings)
	// api.Patch("/settings", authenticationMiddleware, settingsHandler.UpdateSettings)

	// user bonus template routes
	api.Post("/user/bonus/template", bonusTemplateHandler.CreateBonusTemplate)
	api.Get("/user/bonus/template", authenticationMiddleware, bonusTemplateHandler.GetBonusTemplateByOfferCode)
	api.Patch("/user/bonus/template", authenticationMiddleware, bonusTemplateHandler.UpdateBonusTemplate)
	api.Delete("/user/bonus/template/:offerCode", authenticationMiddleware, bonusTemplateHandler.DeleteBonusTemplate)

	// cms terms and conditions routes
	api.Post("/terms/conditions", cmsTemplateHandler.CreateandUpdateCMSTermsAndConditions)
	api.Get("/terms/conditions/:category", cmsTemplateHandler.GetCMSTermsAndConditions)
	api.Get("/terms/conditions/individual/sportsbook", cacheMid, cmsTemplateHandler.GetSportsBookCMSTermsAndConditions)
	api.Delete("/terms/conditions/:category", cmsTemplateHandler.DeleteCMSTermsAndCondition)

	//email campaigns routes
	api.Post("/email/campaigns", emailCampaignsHandler.CreateEmailCampaigns)
	api.Get("/email/campaigns/:email", authenticationMiddleware, emailCampaignsHandler.GetEmailCampaigns)

	// rails_client_handler_routes
	api.Post("/rails/users/depositAddress", authenticationMiddleware, railsClientHandler.CreateDepositAddressOfUser)
	api.Get("/rails/users/depositAddresses", authenticationMiddleware, railsClientHandler.GetUserDepositAddresses)
	api.Post("/rails/users/withdrawal", authenticationMiddleware, railsClientHandler.CreateWithdrawalRequest)

	// user currency routes
	api.Patch("/users/multi/currency", userHandler.UpdateUserMultiCurrency)

	// bonus drop routes
	api.Post("/bonus/drop", directusMiddleware, bonusDropHandler.UpsertBonusDrop)
	api.Post("/bonus/drop/redeem", authenticationMiddleware, bonusDropHandler.RedeemBonusDrop)

	api.Get("/register/isReserved/:username", slatedUsernameHandler.IsReserved)

	// internal (testing) routes.
	// Please note these routes are not publicly accessible and are meant for internal use only.
	internal.Post("/users/xp/update", authenticationMiddleware, userHandler.GetUsersXPAndUpdateInElantil)
	internal.Get("/metrics", monitor.New())
	internal.Get("/calculate-commission/:id", authenticationMiddleware, referralHandler.CalculateUserCommissions)
	internal.Delete("/settings/:key", authenticationMiddleware, settingsHandler.DeleteSettings)
	internal.Post("bonus/template/assign", authenticationMiddleware, elantilClientHandler.AssignBonusTemplate)
}
