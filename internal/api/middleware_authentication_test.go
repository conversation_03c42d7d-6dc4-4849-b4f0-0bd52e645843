package api

import (
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestAuthMiddleware_WithAccessToken(t *testing.T) {
	mockValidator := validator.New()
	mockAuthService := domainmock.NewAuthenticationService(t)

	middleware := NewAuthenticationMiddleware(mockValidator, mockAuthService)

	tests := []struct {
		name           string
		setupMock      func()
		accessToken    string
		refreshToken   string
		expectedStatus int
	}{
		{
			name: "Valid token",
			setupMock: func() {
				mockAuthService.On("ValidateAccessToken", mock.Anything, "valid_token").
					Return(domain.ValidateTokenResponse{
						UserID:   "test-user-id",
						UserName: "test-user",
						Email:    "<EMAIL>",
					}, nil).Once()
			},
			accessToken:    "valid_token",
			refreshToken:   "refresh_token",
			expectedStatus: fiber.StatusOK,
		},
		{
			name: "Invalid token",
			setupMock: func() {
				mockAuthService.On("ValidateAccessToken", mock.Anything, "invalid_token").
					Return(domain.ValidateTokenResponse{}, errors.New("invalid token")).Once()
			},
			accessToken:    "invalid_token",
			refreshToken:   "refresh_token",
			expectedStatus: fiber.StatusUnauthorized,
		},
		{
			name: "Missing authorization header",
			setupMock: func() {
			},
			accessToken:    "",
			refreshToken:   "refresh_token",
			expectedStatus: fiber.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMock()

			app := fiber.New()
			app.Use(middleware.WithAccessToken)
			app.Get("/test", func(c *fiber.Ctx) error {
				return c.SendStatus(fiber.StatusOK)
			})

			req := httptest.NewRequest("GET", "/test", nil)
			if tt.accessToken != "" {
				req.Header.Set("Authorization", tt.accessToken)
			}
			if tt.refreshToken != "" {
				req.Header.Set("RefreshToken", tt.refreshToken)
			}

			resp, err := app.Test(req)
			assert.NoError(t, err)

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)
			if tt.name != "Missing authorization header" {
				mockAuthService.AssertExpectations(t)
			}
		})
	}
}