package api

import (
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type idParam struct {
	ID uuid.UUID `param:"id" validate:"required"`
}

type externalIDParam struct {
	ID string `param:"id" validate:"required"`
}

type userExternalIDQuery struct {
	UserExternalID string `query:"userExternalId" validate:"required"`
}

type authenticationHeaders struct {
	Authorization string `reqHeader:"Authorization" validate:"required"`
}

type authorizationHeaders struct {
	Authorization string `reqHeader:"Authorization" validate:"required"`
}

type OrderParams struct {
	Order   *string `query:"order" validate:"omitempty,oneof=desc asc"`
	OrderBy *string `query:"orderBy" validate:"omitempty"`
}

func queryToOrderParams(query *OrderParams, defaultOrderBy string) domain.OrderParams {
	orderBy := defaultOrderBy
	if query.OrderBy != nil {
		orderBy = *query.OrderBy
	}

	order := "desc"
	if query.Order != nil {
		order = *query.Order
	}

	return domain.OrderParams{
		OrderBy: orderBy,
		Order:   domain.OrderDir(order),
	}
}

type PagingParams struct {
	PageNum  *int `query:"pageNum" validate:"omitempty,min=1"`
	PageSize *int `query:"pageSize" validate:"omitempty,min=1,max=50"`
}

func queryToPagingParams(query *PagingParams) domain.PagingParams {
	pageNum := 1
	if query.PageNum != nil {
		pageNum = *query.PageNum
	}

	pageSize := 10
	if query.PageSize != nil {
		pageSize = *query.PageSize
	}

	return domain.PagingParams{
		PageNumber: pageNum,
		PageSize:   pageSize,
	}
}

type ExportParams struct {
	StartDate time.Time `query:"startDate" validate:"required"`
	EndDate   time.Time `query:"endDate" validate:"required"`
}

func queryToExportParams(query *ExportParams) (*domain.ExportParams, error) {
	const maxInterval = 31 * 24 * time.Hour

	endDate := query.EndDate
	startDate := query.StartDate

	if endDate.Sub(startDate) > maxInterval {
		return nil, fiber.NewError(
			fiber.StatusBadRequest,
			fmt.Sprintf("date interval [%v - %v] cannot be bigger than %v", startDate, endDate, maxInterval),
		)
	}

	return &domain.ExportParams{
		StartDate: startDate,
		EndDate:   endDate,
	}, nil
}

func parsePath[T any](ctx *fiber.Ctx, validator *validator.Validate) (*T, error) {
	return parseParamsWithStatus[T](ctx.ParamsParser, validator)
}

func parseQuery[T any](ctx *fiber.Ctx, validator *validator.Validate) (*T, error) {
	return parseParamsWithStatus[T](ctx.QueryParser, validator)
}

func parseHeaders[T any](ctx *fiber.Ctx, validator *validator.Validate) (*T, error) {
	return parseParamsWithStatus[T](ctx.ReqHeaderParser, validator)
}

func parseBody[T any](ctx *fiber.Ctx, validator *validator.Validate) (*T, error) {
	return parseParamsWithStatus[T](ctx.BodyParser, validator)
}

type parseParamsFunc func(out any) error

func parseParamsWithStatus[T any](
	parse parseParamsFunc,
	validator *validator.Validate,
) (*T, error) {
	params, err := parseParams[T](parse, validator)
	if err != nil {
		return nil, fiber.NewError(
			fiber.StatusBadRequest,
			fmt.Sprintf("parse param: %v", err),
		)
	}

	return params, nil
}

func parseParams[T any](
	parseRequest parseParamsFunc,
	validator *validator.Validate,
) (*T, error) {
	var params T
	if err := parseRequest(&params); err != nil {
		return nil, err
	}

	if err := validator.Struct(params); err != nil {
		return nil, err
	}

	return &params, nil
}
