package api

import (
	"log/slog"
	"strconv"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type UserBonusHandler struct {
	userBonusService domain.UserBonusService
	validator        *validator.Validate
}

type BonusData struct {
	ID           int    `json:"id"`
	UserID       string `json:"userId"`
	RewardAmount string `json:"rewardAmount"`
	ExpiresOn    string `json:"expiresOn"`
	Status       string `json:"status"`
	Username     string `json:"username"`
	Category     string `json:"category"`
	Reason       string `json:"reason"`
	Note         string `json:"note"`
}

type ReloadBonusRequest struct {
	Id            string               `json:"id"`
	Status        string               `json:"status"`
	Category      string               `json:"category"`
	UserID        string               `json:"userId"`
	Username      string               `json:"username"`
	RewardAmount  string               `json:"rewardAmount"`
	DateCreated   string               `json:"date_created"`
	UserCreated   string               `json:"user_created"`
	DateUpdated   string               `json:"date_updated"`
	SplitOnDays   string               `json:"splitOnDays"`
	ReloadBonuses []domain.ReloadBonus `json:"reloadBonuses"`
	ExpiresOn     *string              `json:"expiresOn"`
}

func NewUserBonusHandler(userBonusService domain.UserBonusService, validator *validator.Validate) *UserBonusHandler {
	if userBonusService == nil {
		slog.Error("userBonusService is nil in NewUserBonusHandler")
	}
	if validator == nil {
		slog.Error("validator is nil in NewUserBonusHandler")
	}
	return &UserBonusHandler{
		userBonusService: userBonusService,
		validator:        validator,
	}
}

func (h *UserBonusHandler) AssignBonusByType(c *fiber.Ctx) error {
	bonusType := c.Params("type")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	if err := h.userBonusService.AssignBonusByType(c.Context(), bonusType, startDate, endDate); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to activate bonus")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Bonus activated successfully",
	})
}

func (h *UserBonusHandler) GetUserBonusesByExternalID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	bonuses, err := h.userBonusService.GetUserBonusesByExternalID(c.Context(), userID)
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get user bonuses")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"bonuses": bonuses,
	})
}

func (h *UserBonusHandler) UpdateUserBonusStatus(c *fiber.Ctx) error {
	externalID := c.Params("id")
	status := c.Params("status")
	bonusExternalId, err := strconv.ParseInt(c.Params("bonus_external_id"), 10, 64)
	if err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid bonus_external_id")
	}

	if err := h.userBonusService.UpdateUserBonusStatus(c.Context(), int(bonusExternalId), externalID, status); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to update user bonus status")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "User bonus status updated successfully",
	})
}

func (h *UserBonusHandler) DeleteExpiredBonuses(c *fiber.Ctx) error {
	if err := h.userBonusService.DeleteExpiredBonuses(c.Context()); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to delete expired bonuses")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Expired bonuses deleted successfully",
	})
}

func (h *UserBonusHandler) ActivateUserBonusesByExternalIds(c *fiber.Ctx) error {
	var data []BonusData
	if err := c.BodyParser(&data); err != nil {
		slog.Error("Error parsing request body", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "Invalid input data")
	}
	if len(data) == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "body is required")
	}

	for _, item := range data {
		if item.Category == "" {
			return fiber.NewError(fiber.StatusBadRequest, "category is required")
		}
		if item.Status == "" {
			return fiber.NewError(fiber.StatusBadRequest, "status is required")
		}

		rewardAmount, err := strconv.ParseFloat(item.RewardAmount, 64)
		if err != nil {
			return fiber.NewError(fiber.StatusBadRequest, "Invalid reward amount")
		}
		if err := h.userBonusService.ActivateUserBonusesByExternalIds(c.Context(), item.UserID, item.Category, item.Status, rewardAmount, item.ID); err != nil {
			return fiber.NewError(fiber.StatusInternalServerError, "Failed to activate user bonuses")
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "User bonuses activated successfully",
	})
}

func (h *UserBonusHandler) AssignSpecialBonus(c *fiber.Ctx) error {
	var data []BonusData
	if err := c.BodyParser(&data); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid input data")
	}
	if len(data) == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "body is required")
	}

	for _, item := range data {
		rewardAmount, err := strconv.ParseFloat(item.RewardAmount, 64)
		if err != nil {
			return fiber.NewError(fiber.StatusBadRequest, "Invalid reward amount")
		}

		if err := h.userBonusService.AssignSpecialBonus(c.Context(), item.UserID, item.Username, item.Status, rewardAmount, item.ID, item.Reason, item.Note); err != nil {
			return fiber.NewError(fiber.StatusInternalServerError, "Failed to activate user bonuses")
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "User bonuses activated successfully",
	})
}

func (h *UserBonusHandler) CreateReloadBonuses(c *fiber.Ctx) error {
	var data []domain.ReloadBonusRequest
	if err := c.BodyParser(&data); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid input data")
	}
	if len(data) == 0 {
		return fiber.NewError(fiber.StatusBadRequest, "body is required")
	}

	if err := h.userBonusService.CreateReloadBonuses(c.Context(), data); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to create reload bonuses")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Reload bonuses created successfully",
	})

}

func (h *UserBonusHandler) GetLevelUpBonusOfUser(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	bonus, err := h.userBonusService.GetLevelUpBonusOfUser(userID)
	if err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to get level up bonus")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"bonus": bonus,
	})
}

func (h *UserBonusHandler) UpdateModalPopupClosed(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	if err := h.userBonusService.UpdateModalPopupClosed(c.Context(), userID); err != nil {
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to update modal popup closed")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Modal popup closed updated successfully",
	})
}

func (h *UserBonusHandler) ClaimAllBonusAndUpdateWallet(c *fiber.Ctx) error {
	token := c.Get("Authorization")
	id := c.Params("id")
	if token == "" {
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	if err := h.userBonusService.ClaimAllBonusAndUpdateWallet(c.Context(), id, token); err != nil {
		if err == domain.ErrNoBonusToClaim {
			return fiber.NewError(fiber.StatusNotFound, "No bonus to claim")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to claim all bonuses and update wallet")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "All bonuses claimed and wallet updated successfully",
	})
}

func (h *UserBonusHandler) ClaimTiltBonusAndUpdateWallet(c *fiber.Ctx) error {
	token := c.Get("Authorization")
	id := c.Params("id")
	if token == "" {
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	if err := h.userBonusService.ClaimTiltBonusAndUpdateWallet(c.Context(), id, token); err != nil {
		if err == domain.ErrNoBonusToClaim {
			return fiber.NewError(fiber.StatusNotFound, "No bonus to claim")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to claim all bonuses and update wallet")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "All bonuses claimed and wallet updated successfully",
	})
}

func (h *UserBonusHandler) SingleClaimUpdate(c *fiber.Ctx) error {
	userExternalID := c.Params("id")
	var data struct {
		BonusConfigId int `json:"bonusConfigId"`
	}
	if err := c.BodyParser(&data); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "Invalid input data")
	}

	token := c.Get("Authorization")
	if token == "" {
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	err := h.userBonusService.SingleClaimUpdate(c.Context(), token, userExternalID, data.BonusConfigId)
	if err != nil {
		if err == domain.ErrNoBonusToClaim {
			return fiber.NewError(fiber.StatusNotFound, "No bonus to claim")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "Failed to claim bonus")
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Bonus claimed successfully",
	})
}
