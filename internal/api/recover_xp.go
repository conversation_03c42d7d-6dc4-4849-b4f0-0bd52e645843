package api

import (
	"context"
	"strconv"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/service"
	"github.com/gofiber/fiber/v2"
)

type RecoverXpHandler struct {
	userRepository    *postgres.UserRepository
	wageringClient    *rest_client.ElantilWageringClient
	betMessageHandler *service.BetMessageHandler
}

func NewRecoverXpHandler(userRepository *postgres.UserRepository, wageringClient *rest_client.ElantilWageringClient, betMessageHandler *service.BetMessageHandler) *RecoverXpHandler {
	return &RecoverXpHandler{
		userRepository:    userRepository,
		wageringClient:    wageringClient,
		betMessageHandler: betMessageHandler,
	}
}

func (h *RecoverXpHandler) RecoverXP(c *fiber.Ctx) error {

	ElantilStatus := c.Query("status")

	ctx := context.Background()

	ExternalIDs_, err := h.userRepository.GetExternalIDsByVIPStatus(ctx, ElantilStatus)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to retrieve external ids",
		})
	}

	casinoVt := "casino_vt"
	casino_wager, _, err := h.wageringClient.GetBatchWageringSummary(ctx, ExternalIDs_, &casinoVt, "lifetime", "", "")
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to retrieve casino wager summary",
		})
	}

	sportVt := "sport_vt"
	sport_wager, _, err := h.wageringClient.GetBatchWageringSummary(ctx, ExternalIDs_, &sportVt, "lifetime", "", "")
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to retrieve sport wager summary",
		})
	}

	for _, id := range ExternalIDs_ {
		var sportBetSum float64
		var casinoBetSum float64
		sportBetSum, _ = strconv.ParseFloat(sport_wager[id].BetSum, 64)
		casinoBetSum, _ = strconv.ParseFloat(casino_wager[id].BetSum, 64)
		totalBetSum := (sportBetSum * 3) + casinoBetSum
		h.betMessageHandler.HandleUpsertXPUpdate(ctx, id, &totalBetSum, false)
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message":      "XP successfully recovered",
		"external_ids": ExternalIDs_,
	})
}
