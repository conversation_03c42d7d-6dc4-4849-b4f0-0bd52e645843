package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type BonusHandler struct {
	bonusService domain.BonusService
	validator    *validator.Validate
}

func NewBonusHandler(bonusService domain.BonusService, validator *validator.Validate) *BonusHandler {
	return &BonusHandler{
		bonusService: bonusService,
		validator:    validator,
	}
}

func (h *BonusHandler) StoreBonusConfig(c *fiber.Ctx) error {
	err := h.bonusService.CreateBonusConfig(c.Context())
	if err != nil {
		return err
	}
	return c.SendStatus(fiber.StatusCreated)
}

func (h *BonusHandler) UpdateVipTiersConfig(c *fiber.Ctx) error {
	err := h.bonusService.UpdateVipTiersConfig()
	if err != nil {
		return err
	}
	return c.SendStatus(fiber.StatusCreated)
}