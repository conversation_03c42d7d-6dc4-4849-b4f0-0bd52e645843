package api

import (
	"fmt"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/service"
	"github.com/gofiber/fiber/v2"
)

type RecoveryHandler struct {
	recoveryService *service.RecoveryService
}

func NewRecoveryHandler(recoveryService *service.RecoveryService) *RecoveryHandler {
	return &RecoveryHandler{
		recoveryService: recoveryService,
	}
}

func (h *RecoveryHandler) RecoverUsersTransactions(c *fiber.Ctx) error {
	if err := h.recoveryService.RecoverUsersTransactions(c.Context()); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("failed to recover users transactions: %v", err),
		})
	}

	return c.JSON(fiber.Map{
		"message": "Users recovery completed successfully",
	})

}