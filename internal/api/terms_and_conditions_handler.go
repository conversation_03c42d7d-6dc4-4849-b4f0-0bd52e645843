package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/gofiber/fiber/v2"
)

type CMSTermsAndConditionsHandler struct {
	cmsTermandCondition domain.CMSTermsAndConditionsService
}

func NewCMSTermsAndConditionsHandler(cmsTermandCondition domain.CMSTermsAndConditionsService) *CMSTermsAndConditionsHandler {
	return &CMSTermsAndConditionsHandler{cmsTermandCondition: cmsTermandCondition}
}

func (h *CMSTermsAndConditionsHandler) CreateandUpdateCMSTermsAndConditions(c *fiber.Ctx) error {
	var req domain.AddCMSTermsAndConditions

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request payload",
		})
	}

	if req.Category == "" || req.Content == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Category and Content are required",
		})
	}

	err := h.cmsTermandCondition.CreateandUpdateCMSTermsAndConditions(c.Context(), &req)
	if err != nil {
		return err
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Terms and Conditions saved successfully",
	})
}

func (h *CMSTermsAndConditionsHandler) GetCMSTermsAndConditions(c *fiber.Ctx) error {
	category := c.Params("category")

	if category == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Category is required in the path",
		})
	}

	result, err := h.cmsTermandCondition.GetCMSTermsAndConditions(c.Context(), category)
	if err != nil {
		return err
	}
	return c.Status(fiber.StatusOK).JSON(result)
}

func (h *CMSTermsAndConditionsHandler) DeleteCMSTermsAndCondition(c *fiber.Ctx) error {
	category := c.Params("category")

	if category == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Category is required in the path",
		})
	}

	err := h.cmsTermandCondition.DeleteCMSTermsAndConditions(c.Context(), category)
	if err != nil {
		return err
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "terms and conditions deleted successfully",
	})

}

func (h *CMSTermsAndConditionsHandler) GetSportsBookCMSTermsAndConditions(c *fiber.Ctx) error {
	result, err := h.cmsTermandCondition.GetSportsBookCMSTermsAndConditions(c.Context())
	if err != nil {
		return err
	}
	return c.Status(fiber.StatusOK).JSON(result)
}
