package api

import (
	"log/slog"
	"strings"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type ElantilClientHandler struct {
	restClient         domain.ElantilWageringClient
	UserRepository     domain.UserRepository
	ReferralRepository domain.ReferralRepository
}

func NewElantilClientHandler(restClient domain.ElantilWageringClient, userRepository domain.UserRepository, ReferralRespository domain.ReferralRepository) *ElantilClientHandler {
	return &ElantilClientHandler{
		restClient:         restClient,
		UserRepository:     userRepository,
		ReferralRepository: ReferralRespository,
	}
}

func (h *ElantilClientHandler) ValidateToken(c *fiber.Ctx) error {
	token := c.Get("Authorization")
	if token == "" {
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	resp, err := h.restClient.ValidateToken(c.Context(), token)
	if err != nil {
		return c.JSON(fiber.Map{"Valid": resp})
	}
	return c.JSON(fiber.Map{"Valid": resp})
}

func (h *ElantilClientHandler) UpsertPlayerActivityTagsByUserExternalID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	var data struct {
		CategoryKey string `json:"categoryKey"`
		Key         string `json:"key"`
		Value       string `json:"value"`
	}

	if err := c.BodyParser(&data); err != nil {
		return err
	}
	err := h.restClient.UpsertPlayerActivityTagsByUserExternalID(userID, data.CategoryKey, data.Key, data.Value)
	if err != nil {
		return err
	}
	return c.SendStatus(fiber.StatusCreated)
}

func (h *ElantilClientHandler) GetPlayerActivityTagsByUserExternalID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	resp, err := h.restClient.GetPlayerActivityTagsByUserExternalID(userID)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) SendVerificationEmail(c *fiber.Ctx) error {
	userExternalID := c.Params("email")
	err := h.restClient.SendVerificationEmail(userExternalID)
	if err != nil {
		return err
	}
	return c.JSON(fiber.Map{"message": "Verification email sent"})
}

func (h *ElantilClientHandler) CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(c *fiber.Ctx) error {
	username, ok := utils.GetUserNameFromContext(c)
	if !ok {
		slog.Error("Username not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "Username not found in context")
	}

	resp, err := h.restClient.CheckIfUserExistsInElantilSystemAndUpdatePlayerTags(username)
	if err != nil {
		return err
	}

	err = h.UserRepository.UpdateUserEmailVerificationStatus(c.Context(), username, resp)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) UpdatePassword(c *fiber.Ctx) error {
	email := c.Params("email")
	err := h.restClient.UpdatePassword(email)
	if err != nil {
		return err
	}
	return c.JSON(fiber.Map{"message": "Update password email sent"})
}

func (h *ElantilClientHandler) AssignBonusTemplate(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	var body struct {
		OfferCode string `json:"offer_code"`
	}

	if err := c.BodyParser(&body); err != nil {
		return err
	}

	err := h.restClient.AssignBonusTemplate(c.Context(), body.OfferCode, userID, "profiles", token)
	if err != nil {
		if err == domain.ErrBonusTemplateAssigned {
			return fiber.NewError(fiber.StatusConflict, err.Error())
		} else if err == domain.ErrCreditTransactionNotFound {
			return fiber.NewError(fiber.StatusNotFound, err.Error())
		} else {
			return err
		}
	}

	err = h.restClient.UpsertPlayerActivityTagsByUserExternalID(userID, "player_activity", "wb_bonus", "true")
	if err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusCreated)
}

func (h *ElantilClientHandler) ForfeitBonus(c *fiber.Ctx) error {
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	bonusID := c.Params("bonusId")
	err := h.restClient.ForfeitBonus(c.Context(), bonusID, token)
	if err != nil {
		return err
	}
	return c.JSON(fiber.Map{"message": "Bonus forfeited"})
}

func (h *ElantilClientHandler) GetOwnerBonusesByUserId(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	resp, err := h.restClient.GetOwnerBonusesByUserId(c.Context(), userID, token)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) GetConversionRates(c *fiber.Ctx) error {
	fiatCurrency := c.Query("currency")
	fiatCurrency = strings.ToUpper(fiatCurrency)
	if fiatCurrency == "" {
		fiatCurrency = "USD"
	}

	resp, err := h.restClient.GetConversionRates(c.Context(), fiatCurrency)
	if err != nil {
		return err
	}

	return c.JSON(resp)
}

func (h *ElantilClientHandler) GetUserProfile(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	resp, err := h.restClient.GetUserProfile(c.Context(), userID, token)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) CompleteSocialProfile(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	email, ok := utils.GetUserEmailFromContext(c)
	if !ok {
		slog.Error("Email not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "Email not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	var socialProfile domain.SocialProfileCompletionRequest
	if err := c.BodyParser(&socialProfile); err != nil {
		return err
	}

	if socialProfile.ReferralCode != "" {
		isValid, err := h.ReferralRepository.IsReferralCodeValid(c.Context(), socialProfile.ReferralCode)
		if err != nil {
			return err
		}
		if !isValid {
			return fiber.NewError(fiber.StatusBadRequest, "Invalid referral code")
		}
	}

	err := h.restClient.CompleteSocialProfile(c.Context(), userID, email, socialProfile, token)
	if err != nil {
		return err
	}

	if socialProfile.ReferralCode != "" {
		err = h.ReferralRepository.AddUserToTheListOfRefferedUsers(c.Context(), socialProfile.ReferralCode, userID, socialProfile.Username)
		if err != nil {
			slog.Error("Failed to add user to referred users list", "error", err)
			return err
		}
	}

	return c.SendStatus(fiber.StatusNoContent)
}

func (h *ElantilClientHandler) GetUserWallet(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	resp, err := h.restClient.GetUserWallet(c.Context(), userID, token)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) CreateUserLocalWallet(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}
	resp, err := h.restClient.CreateUserLocalWallet(c.Context(), userID, token)
	if err != nil {
		return err
	}
	return c.JSON(resp)
}

func (h *ElantilClientHandler) UpdateUserProfile(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	token := c.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	var UpdateProfile domain.UserProfileUpdateRequest
	if err := c.BodyParser(&UpdateProfile); err != nil {
		return err
	}

	err := h.restClient.UpdateUserProfile(c.Context(), userID, token, UpdateProfile)
	if err != nil {
		return err
	}
	return c.SendStatus(fiber.StatusNoContent)
}
