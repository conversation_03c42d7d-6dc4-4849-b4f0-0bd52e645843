package api

import "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"

type BalanceResponse struct {
	OwnerID  string  `json:"ownerId"`
	Balance  float64 `json:"balance"`
	Currency string  `json:"currency"`
	Type     string  `json:"type"`
}

func BalanceToBalanceResponse(balance *domain.Balance) *BalanceResponse {
	return &BalanceResponse{
		OwnerID:  balance.UserExternalID,
		Balance:  balance.BalanceAmount,
		Currency: balance.Currency,
		Type:     balance.Type,
	}
}
