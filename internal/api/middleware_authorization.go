package api

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type AuthorizationMiddleware struct {
	validator *validator.Validate
}

func NewAuthorizationMiddleware(
	validator *validator.Validate,
) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		validator: validator,
	}
}

func (m *AuthorizationMiddleware) WithAccessToken(c *fiber.Ctx) error {
	headers, err := parseHeaders[authorizationHeaders](c, m.validator)
	if err != nil {
		return err
	}

	id := c.Query("userExternalId")
	if id == "" {
		id = c.Params("id")
	}

	authHeader := headers.Authorization
	if !strings.HasPrefix(authHeader, "Bearer ") {
		return fiber.NewError(fiber.StatusUnauthorized, "Invalid authorization header format")
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")

	claims, err := extractClaims(tokenString)
	if err != nil {
		return fiber.NewError(fiber.StatusUnauthorized, "Invalid token")
	}

	sub, ok := claims["sub"].(string)
	if !ok || sub != id {
		return fiber.NewError(fiber.StatusForbidden, "ID does not match token subject")
	}

	return c.Next()
}

func (m *AuthorizationMiddleware) WithBypass(c *fiber.Ctx) error {
	return withBypass(c, m.validator, m)
}

func extractClaims(tokenString string) (map[string]any, error) {
	parts := strings.Split(tokenString, ".")
	if len(parts) < 2 {
		return nil, errors.New("invalid token structure")
	}

	payload, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}

	var claims map[string]any
	if err := json.Unmarshal(payload, &claims); err != nil {
		return nil, err
	}

	return claims, nil
}
