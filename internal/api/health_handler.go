package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/gofiber/fiber/v2"
)

type HealthHandler struct {
	healthService domain.HealthService
}
type SlackNotificationRequest struct {
	BonusType string `json:"bonus_type"`
	ErrorMsg  string `json:"error_msg"`
}

func NewHealthHandler(healthService domain.HealthService) *HealthHandler {
	return &HealthHandler{
		healthService: healthService,
	}
}
func (h *HealthHandler) SendSlackNotificationForCMSAlert(c *fiber.Ctx) error {
	type Request struct {
		Status     int    `json:"status"`
		StatusText string `json:"statusText"`
		BonusType  string `json:"bonusType"`
		Headers    struct {
			Date                            string `json:"date"`
			ContentType                     string `json:"content-type"`
			ContentLength                   string `json:"content-length"`
			Connection                      string `json:"connection"`
			ContentSecurityPolicyReportOnly string `json:"content-security-policy-report-only"`
		} `json:"headers"`
		Data struct {
			Message string `json:"message,omitempty"`
			Error   string `json:"error,omitempty"`
		} `json:"data"`
	}

	var req []Request
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}
	var msg string
	if req[0].Data.Error != "" {
		msg = req[0].Data.Error
	} else {
		msg = req[0].Data.Message
	}
	if req[0].Status != 200 && req[0].Status != 201 {
		notification := SlackNotificationRequest{
			BonusType: req[0].BonusType,
			ErrorMsg:  msg,
		}
		if err := h.healthService.SendMessageCMSAlert(notification.BonusType, notification.ErrorMsg); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to send the Slack notification",
			})
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Notification sent successfully",
	})
}
