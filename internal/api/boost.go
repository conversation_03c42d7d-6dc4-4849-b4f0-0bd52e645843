package api

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

type createBoostRequest struct {
	// List of user ids to create the boost for
	UsersID []string `json:"users" validate:"required,min=1"`
	// Date in format YYYY-MM-ddThh:mm:ssZ in UTC, should be greater than current time in UTC
	BonusStartsAt time.Time `json:"bonus_starts_at" validate:"required,gt"`
	// Date in format YYYY-MM-ddThh:mm:ssZ in UTC, should be greater than bonus_starts_at
	BonusFinishesAt time.Time `json:"bonus_finishes_at" validate:"required,gtfield=BonusStartsAt"`
	// Duration for the boost bonus in hours. min 1, max 72
	BoostDurationHours int `json:"boost_duration_hours" validate:"required,min=1,max=72"`
	// Multiplier to be used for the coins, possible values: 2, 5, 10
	Multiplier int `json:"multiplier" validate:"required,oneof=2 5 10"`
}

type boostResponse struct {
	RankedUser         LeaderboardUserResponse `json:"user"`
	BonusStartsAt      time.Time               `json:"bonus_starts_at"`
	BonusFinishesAt    time.Time               `json:"bonus_finishes_at"`
	BoostDurationHours int                     `json:"boost_duration_hours"`
	Multiplier         float64                 `json:"multiplier"`
	BoostStartedAt     *time.Time              `json:"boost_started_at"`
}

type boostCreationResponse struct {
	CreatedCount int                        `json:"created_boosts_count"`
	Errors       []createBoostErrorResponse `json:"errors"`
}

type createBoostErrorResponse struct {
	UserID string `json:"user_id"`
	Detail string `json:"detail"`
}

func boostRequestToBoost(request *createBoostRequest) *domain.Boost {
	return &domain.Boost{
		BonusStartsAt:      request.BonusStartsAt.UTC(),
		BonusFinishesAt:    request.BonusFinishesAt.UTC(),
		BoostDurationHours: request.BoostDurationHours,
		Multiplier:         float64(request.Multiplier),
	}
}

func boostToBoostResponse(boost *domain.Boost) *boostResponse {
	return &boostResponse{
		BonusStartsAt:      boost.BonusStartsAt,
		BonusFinishesAt:    boost.BonusFinishesAt,
		BoostDurationHours: boost.BoostDurationHours,
		Multiplier:         boost.Multiplier,
		BoostStartedAt:     boost.BoostStartedAt,
		RankedUser:         userToLeaderboardUserResponse(boost.RankedUser, nil, totalCoinsField, utils.PointerOf(int64(0)), false),
	}
}

func createBoostToResponse(boost *domain.BoostCreationOut) *boostCreationResponse {
	errors := make([]createBoostErrorResponse, len(boost.Errors))
	for i, b := range boost.Errors {
		detail := ""
		switch b.Error {
		case domain.ErrOverlapingDates:
			detail = "Given dates overlap an existent bonus"
		case domain.ErrResourceNotFound:
			detail = "User not found"
		}
		errors[i] = createBoostErrorResponse{
			UserID: b.UserID,
			Detail: detail,
		}
	}

	return &boostCreationResponse{
		CreatedCount: boost.CreatedCount,
		Errors:       errors,
	}
}
