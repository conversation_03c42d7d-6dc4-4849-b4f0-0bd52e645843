package api

import (
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

// GameHandler handles game HTTP requests.
type GameHandler struct {
	gameService domain.GameService
	validator   *validator.Validate
}

// NewGameHandler creates new instance of GameHandler.
func NewGameHandler(gameService domain.GameService, validator *validator.Validate) *GameHandler {
	return &GameHandler{gameService: gameService, validator: validator}
}

// UpdateAllGames updates the games database.
// @Produce application/json
// @Success 204
// @Failure 500 {object} fiber.Error
// @Router  /internal/v1/games/update [post]
func (h *GameHandler) UpdateAllGames(c *fiber.Ctx) error {
	if err := h.gameService.UpsertAllGames(c.Context()); err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusNoContent)
}
