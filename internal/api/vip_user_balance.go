package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type vipUserBalanceRequest struct {
	UserExternalID string   `json:"user_external_id" validate:"required"`
	Handle         *float64 `json:"handle" validate:"required"`
	Coins          *float64 `json:"coins" validate:"required"`
	BetsWon        *int     `json:"bets_won" validate:"required"`
	TotalBets      *int     `json:"total_bets" validate:"required"`
	Source         string   `json:"source" validate:"required"`
}

type BumpUserVipStatusRequestBody struct {
	VipStatusToBump string `json:"vip_status_to_bump" validate:"required"`
	AddBase         bool   `json:"add_base"`
	Source          string `json:"source" validate:"required"`
}

func requestToVIPUserBalance(request *vipUserBalanceRequest) *domain.VIPUserBalance {
	return &domain.VIPUserBalance{
		Handle:    *request.Handle,
		Coins:     *request.Co<PERSON>,
		BetsWon:   *request.BetsWon,
		TotalBets: *request.TotalBets,
		Source:    request.Source,
		User:      domain.User{ExternalID: request.UserExternalID},
	}
}
