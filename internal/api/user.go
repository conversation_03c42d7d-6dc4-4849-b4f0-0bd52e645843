package api

import (
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

var coinsAwardedPerRefer = 200.0

type UserResponse struct {
	ID                  string                   `json:"id"`
	ClaimedCoins        int64                    `json:"claimed_coins"`
	Config              *string                  `json:"config"`
	ExternalID          string                   `json:"external_id"`
	FactorSID           *string                  `json:"factor_sid"`
	GhostMode           bool                     `json:"ghost_mode"`
	HideAllStats        bool                     `json:"hide_all_stats"`
	HideTournamentStats bool                     `json:"hide_tournament_stats"`
	JoinDate            time.Time                `json:"join_date"`
	LastStateApplied    *time.Time               `json:"last_state_applied"`
	ProfileStatus       string                   `json:"profile_status"`
	UnclaimedCoins      int64                    `json:"unclaimed_coins"`
	UserName            string                   `json:"user_name"`
	VIPStatus           string                   `json:"vip_status"`
	Assets              userConfigAssetResponses `json:"assets"`
	Email               string                   `json:"email"`
	Verified            bool                     `json:"verified"`
	EmailChanged        bool                     `json:"email_changed"`
	MultiCurrency       bool                     `json:"multi_currency"`
	// Stats
	TotalBets  int     `json:"total_bets"`
	TotalCoins int64   `json:"total_coins"`
	TotalLoses int     `json:"total_loses"`
	TotalWins  int     `json:"total_wins"`
	Wagered    float64 `json:"wagered"`
	WinRate    float64 `json:"win_rate"`
	XP         float64 `json:"xp"`
}

func userToUserResponse(u *domain.User, isOwner, isAdmin bool) *UserResponse {
	assets := utils.MapSlice(u.UserAssets, func(userAsset domain.UserAsset) domain.UserConfigAsset {
		return userAsset.UserConfigAsset
	})
	userResponse := &UserResponse{
		ID:                  u.ID.String(),
		UnclaimedCoins:      int64(u.TotalCoins) - int64(u.ClaimedCoins),
		Assets:              userConfigAssetsToUserConfigAssetResponses(assets),
		UserName:            u.UserName,
		VIPStatus:           u.VIPStatus,
		GhostMode:           u.GhostMode,
		HideAllStats:        u.HideAllStats,
		JoinDate:            u.JoinDate,
		HideTournamentStats: u.HideTournamentStats,
		TotalBets:           0,
		TotalLoses:          0,
		TotalWins:           0,
		Wagered:             0,
		XP:                  0,
		WinRate:             0,
		MultiCurrency:       u.MultiCurrency,
	}
	if isOwner || isAdmin {
		userResponse.TotalBets = u.TotalBets
		userResponse.TotalLoses = u.NumberOfLosses
		userResponse.TotalWins = u.NumberOfWins
		userResponse.Wagered = u.Wagered
		userResponse.XP = u.XP
		userResponse.WinRate = calculateWinPercentage(u.TotalBets, u.NumberOfWins)
		userResponse.UserName = u.UserName 
		userResponse.Email = u.Email
		userResponse.Verified = u.Verified
	} else {
		if u.GhostMode {
			userResponse.UserName = "Hidden"
		}
		if !u.HideAllStats {
			userResponse.TotalBets = u.TotalBets
			userResponse.TotalLoses = u.NumberOfLosses
			userResponse.TotalWins = u.NumberOfWins
			userResponse.Wagered = u.Wagered
			userResponse.XP = u.XP
			userResponse.WinRate = calculateWinPercentage(u.TotalBets, u.NumberOfWins)
		}
	}
	return userResponse
}

func userToUserResponseForLeaderboard(u *domain.User, isAdmin bool) *UserResponse {
	assets := utils.MapSlice(u.UserAssets, func(userAsset domain.UserAsset) domain.UserConfigAsset {
		return userAsset.UserConfigAsset
	})

	if u.GhostMode && !isAdmin {
		u.UserName = "Hidden"
	}
	userResponse := &UserResponse{
		ID:                  u.ID.String(),
		UnclaimedCoins:      int64(u.TotalCoins) - int64(u.ClaimedCoins),
		Assets:              userConfigAssetsToUserConfigAssetResponses(assets),
		TotalCoins:          int64(u.TotalCoins),
		UserName:            u.UserName,
		VIPStatus:           u.VIPStatus,
		JoinDate:            u.JoinDate,
		HideTournamentStats: u.HideTournamentStats,
		TotalBets:           u.TotalBets,
		TotalLoses:          u.NumberOfLosses,
		TotalWins:           u.NumberOfWins,
		Wagered:             u.Wagered,
		WinRate:             calculateWinPercentage(u.TotalBets, u.NumberOfWins),
		XP:                  u.XP,
	}
	return userResponse
}

func userToUserResponseInternal(u *domain.User) *UserResponse {
	assets := utils.MapSlice(u.UserAssets, func(userAsset domain.UserAsset) domain.UserConfigAsset {
		return userAsset.UserConfigAsset
	})

	userResponse := &UserResponse{
		ID:                  u.ID.String(),
		UnclaimedCoins:      int64(u.TotalCoins) - int64(u.ClaimedCoins),
		Assets:              userConfigAssetsToUserConfigAssetResponses(assets),
		UserName:            u.UserName,
		VIPStatus:           u.VIPStatus,
		JoinDate:            u.JoinDate,
		HideTournamentStats: u.HideTournamentStats,
		TotalBets:           u.TotalBets,
		TotalLoses:          u.NumberOfLosses,
		TotalWins:           u.NumberOfWins,
		Wagered:             u.Wagered,
		WinRate:             calculateWinPercentage(u.TotalBets, u.NumberOfWins),
		XP:                  u.XP,
	}
	return userResponse
}

func calculateWinPercentage(totalBets, totalWins int) float64 {
	if totalBets == 0 {
		return 0.0 // Avoid division by zero
	}

	return utils.RoundFloat(float64(totalWins)/float64(totalBets)*100, 2)
}

type boostParams struct {
	Enabled *bool `query:"enabled" validate:"omitempty"`
}

type updateUserRequest struct {
	HideStats *bool                `json:"hide_stats"`
	GhostMode *bool                `json:"ghost_mode"`
	FactorSID *string              `json:"factor_sid"`
	Assets    map[string]uuid.UUID `json:"assets"`
}

type updateUserCoinsRequest struct {
	Coins       *int   `json:"coins" validate:"required,min=1"`
	Source      string `json:"source" validate:"required"`
	ReferenceID string `json:"reference_id" validate:"required"`
}

type registerEmailRequest struct {
	Email          string `json:"email" validate:"required,email"`
	EmailMarketing bool   `json:"email_marketing"`
}
