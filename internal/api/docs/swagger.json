{"swagger": "2.0", "info": {"description": "Community API.", "title": "Community API", "contact": {}, "version": "1.0"}, "paths": {"/api/v1/auth/boost/users/:id": {"patch": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"type": "string", "description": "User external ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"204": {"description": "No Content", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/auth/users/{id}": {"get": {"produces": ["application/json"], "parameters": [{"type": "string", "description": "Use external ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.LeaderboardUserResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/bets": {"get": {"description": "Get paginated list of bets with optimized pagination. This endpoint uses pagination without total count for improved performance on large datasets.", "produces": ["application/json"], "parameters": [{"type": "integer", "description": "Page number.", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "Page size.", "name": "pageSize", "in": "query"}, {"enum": ["desc asc"], "type": "string", "description": "Order direction.", "name": "order", "in": "query"}, {"type": "string", "description": "Order by field.", "name": "orderBy", "in": "query"}, {"type": "boolean", "description": "Include or not bets that have autogenerated games.", "name": "includeBetsWithoutGame", "in": "query"}, {"type": "string", "description": "Filter bets by given user external ID.", "name": "userExternalId", "in": "query"}, {"type": "number", "description": "Filter bets over a given payout amount.", "name": "payoutOver", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.betsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/bets/{id}": {"get": {"produces": ["application/json"], "parameters": [{"type": "string", "description": "Bet ID.", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.BetResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/boost/users/:id": {"patch": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"type": "string", "description": "User external ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"204": {"description": "No Content", "schema": {"$ref": "#/definitions/internal_api.boostResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/leaderboard": {"get": {"produces": ["application/json"], "parameters": [{"type": "string", "description": "Order by field.", "name": "orderBy", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.LeaderboardUsersResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/leaderboard/{id}/rank": {"get": {"produces": ["application/json"], "parameters": [{"type": "string", "description": "External user ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Order by field.", "name": "orderBy", "in": "query"}, {"type": "integer", "description": "Amount of Users below and above retrieved.", "name": "retrieveAmount", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/internal_api.LeaderboardUserResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/registerEmail": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"description": "Register email request body.", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.registerEmailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "201": {"description": "Created", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/transactions": {"get": {"produces": ["application/json"], "parameters": [{"type": "integer", "description": "Page number.", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "Page size.", "name": "pageSize", "in": "query"}, {"enum": ["desc asc"], "type": "string", "description": "Order direction.", "name": "order", "in": "query"}, {"type": "string", "description": "Order by field.", "name": "orderBy", "in": "query"}, {"type": "string", "description": "Comma-separated list of transaction types.", "name": "type", "in": "query"}, {"type": "string", "description": "User external ID.", "name": "userExternalId", "in": "query"}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.transactionsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/users/{id}": {"get": {"produces": ["application/json"], "parameters": [{"type": "string", "description": "User ID or UserName.", "name": "idOrUserName", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.UserResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}, "patch": {"consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"type": "string", "description": "Use external ID.", "name": "id", "in": "path", "required": true}, {"description": "User update request body.", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.updateUserRequest"}}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"204": {"description": "No Content", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/users/{id}/bets/export": {"get": {"produces": ["text/csv"], "parameters": [{"type": "string", "description": "User external ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Start date filter.", "name": "startDate", "in": "query"}, {"type": "string", "description": "End date filter.", "name": "endDate", "in": "query"}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/internal_api.betExportResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/api/v1/users/{id}/transaction/export": {"get": {"produces": ["text/csv"], "parameters": [{"type": "string", "description": "User external ID.", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Start date filter.", "name": "startDate", "in": "query"}, {"type": "string", "description": "End date filter.", "name": "endDate", "in": "query"}, {"type": "string", "description": "Comma-separated list of transaction types.", "name": "type", "in": "query"}, {"type": "string", "description": "Standard authorization header containing a bearer token.", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "Refresh token.", "name": "RefreshToken", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/internal_api.transactionExportResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/internal/v1/boost": {"post": {"consumes": ["application/json"], "parameters": [{"description": "Create boost body request.", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.createBoostRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.boostCreationResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/internal/v1/games/update": {"post": {"produces": ["application/json"], "responses": {"204": {"description": "No Content"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/internal/v1/users/:id/vipStatus/bump": {"post": {"parameters": [{"description": "VIP user balance request body.", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.BumpUserVipStatusRequestBody"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/internal/v1/vipUserBalances": {"post": {"parameters": [{"description": "VIP user balance request body.", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.vipUserBalanceRequest"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}, "/profile/assets": {"get": {"produces": ["application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.userConfigAssetResponses"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fiber.Error"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fiber.Error"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fiber.Error"}}}}}}, "definitions": {"fiber.Error": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}}}, "internal_api.BetResponse": {"type": "object", "properties": {"bet_amount": {"type": "number"}, "bet_type": {"type": "string"}, "cancel_id": {"type": "string"}, "currency": {"type": "string"}, "external_id": {"type": "string"}, "game": {"$ref": "#/definitions/internal_api.GameResponse"}, "ghost_mode": {"type": "boolean"}, "hidden_boolean": {"type": "boolean"}, "id": {"type": "string"}, "multiplier": {"type": "number"}, "payout": {"type": "number"}, "round_status": {"type": "string"}, "time": {"type": "string"}, "user": {"$ref": "#/definitions/internal_api.UserResponse"}, "win_id": {"type": "string"}}}, "internal_api.BumpUserVipStatusRequestBody": {"type": "object", "required": ["source", "vip_status_to_bump"], "properties": {"add_base": {"type": "boolean"}, "source": {"type": "string"}, "vip_status_to_bump": {"type": "string"}}}, "internal_api.GameResponse": {"type": "object", "properties": {"cms_game_id": {"type": "integer"}, "external_id": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "thumbnail_id": {"type": "string"}}}, "internal_api.LeaderboardUserResponse": {"type": "object", "properties": {"assets": {"$ref": "#/definitions/internal_api.userConfigAssetResponses"}, "claimed_coins": {"type": "integer"}, "config": {"type": "string"}, "external_id": {"type": "string"}, "factor_sid": {"type": "string"}, "ghost_mode": {"type": "boolean"}, "hide_all_stats": {"type": "boolean"}, "hide_tournament_stats": {"type": "boolean"}, "id": {"type": "string"}, "join_date": {"type": "string"}, "last_state_applied": {"type": "string"}, "profile_status": {"type": "string"}, "rank": {"type": "integer"}, "remaining_to_next_rank": {"type": "integer"}, "remaining_to_next_rank_position": {"type": "integer"}, "total_bets": {"description": "Stats", "type": "integer"}, "total_coins": {"type": "integer"}, "total_loses": {"type": "integer"}, "total_wins": {"type": "integer"}, "unclaimed_coins": {"type": "integer"}, "user_name": {"type": "string"}, "vip_status": {"type": "string"}, "wagered": {"type": "number"}, "win_rate": {"type": "number"}}}, "internal_api.LeaderboardUsersResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/internal_api.LeaderboardUserResponse"}}, "paging": {"$ref": "#/definitions/internal_api.paging"}}}, "internal_api.UserResponse": {"type": "object", "properties": {"assets": {"$ref": "#/definitions/internal_api.userConfigAssetResponses"}, "claimed_coins": {"type": "integer"}, "config": {"type": "string"}, "external_id": {"type": "string"}, "factor_sid": {"type": "string"}, "ghost_mode": {"type": "boolean"}, "hide_all_stats": {"type": "boolean"}, "hide_tournament_stats": {"type": "boolean"}, "id": {"type": "string"}, "join_date": {"type": "string"}, "last_state_applied": {"type": "string"}, "profile_status": {"type": "string"}, "total_bets": {"description": "Stats", "type": "integer"}, "total_coins": {"type": "integer"}, "total_loses": {"type": "integer"}, "total_wins": {"type": "integer"}, "unclaimed_coins": {"type": "integer"}, "user_name": {"type": "string"}, "vip_status": {"type": "string"}, "wagered": {"type": "number"}, "win_rate": {"type": "number"}}}, "internal_api.betExportResponse": {"type": "object", "properties": {"betAmount": {"type": "number"}, "betType": {"type": "string"}, "currency": {"type": "string"}, "externalID": {"type": "string"}, "game": {"type": "string"}, "multiplier": {"type": "number"}, "payout": {"type": "number"}, "time": {"type": "string"}}}, "internal_api.betsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/internal_api.BetResponse"}}, "paging": {"$ref": "#/definitions/internal_api.pagingWithoutTotalCount"}}}, "internal_api.boostCreationResponse": {"type": "object", "properties": {"created_boosts_count": {"type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/internal_api.createBoostErrorResponse"}}}}, "internal_api.boostResponse": {"type": "object", "properties": {"bonus_finishes_at": {"type": "string"}, "bonus_starts_at": {"type": "string"}, "boost_duration_hours": {"type": "integer"}, "boost_started_at": {"type": "string"}, "multiplier": {"type": "number"}, "user": {"$ref": "#/definitions/internal_api.LeaderboardUserResponse"}}}, "internal_api.createBoostErrorResponse": {"type": "object", "properties": {"detail": {"type": "string"}, "user_id": {"type": "string"}}}, "internal_api.createBoostRequest": {"type": "object", "required": ["bonus_finishes_at", "bonus_starts_at", "boost_duration_hours", "multiplier", "users"], "properties": {"bonus_finishes_at": {"description": "Date in format YYYY-MM-ddThh:mm:ssZ in UTC, should be greater than bonus_starts_at", "type": "string"}, "bonus_starts_at": {"description": "Date in format YYYY-MM-ddThh:mm:ssZ in UTC, should be greater than current time in UTC", "type": "string"}, "boost_duration_hours": {"description": "Duration for the boost bonus in hours. min 1, max 72", "type": "integer", "maximum": 72, "minimum": 1}, "multiplier": {"description": "Multiplier to be used for the coins, possible values: 2, 5, 10", "type": "integer", "enum": [2, 5, 10]}, "users": {"description": "List of user ids to create the boost for", "type": "array", "minItems": 1, "items": {"type": "string"}}}}, "internal_api.paging": {"type": "object", "properties": {"current_page": {"type": "integer"}, "page_count": {"type": "integer"}, "page_size": {"type": "integer"}, "total_count": {"type": "integer"}}}, "internal_api.pagingWithoutTotalCount": {"type": "object", "description": "Optimized pagination structure without total count field. Eliminates expensive COUNT(*) database operations for better performance on large datasets.", "properties": {"current_page": {"type": "integer"}, "page_count": {"type": "integer"}, "page_size": {"type": "integer"}}}, "internal_api.registerEmailRequest": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string"}, "email_marketing": {"type": "boolean"}}}, "internal_api.transactionExportResponse": {"type": "object", "properties": {"amount": {"type": "number"}, "category": {"type": "string"}, "currency": {"type": "string"}, "externalID": {"type": "string"}, "insertedAt": {"type": "string"}, "status": {"type": "string"}, "type": {"type": "string"}}}, "internal_api.transactionResponse": {"type": "object", "properties": {"amount": {"type": "number"}, "category": {"type": "string"}, "currency": {"type": "string"}, "external_id": {"type": "string"}, "id": {"type": "string"}, "inserted_at": {"type": "string"}, "status": {"type": "string"}, "type": {"type": "string"}}}, "internal_api.transactionsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/internal_api.transactionResponse"}}, "paging": {"$ref": "#/definitions/internal_api.paging"}}}, "internal_api.updateUserRequest": {"type": "object", "properties": {"assets": {"type": "object", "additionalProperties": {"type": "string"}}, "factor_sid": {"type": "string"}, "ghost_mode": {"type": "boolean"}, "hide_stats": {"type": "boolean"}}}, "internal_api.userConfigAssetResponse": {"type": "object", "properties": {"id": {"type": "string"}, "key": {"type": "string"}, "sub_type": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "internal_api.userConfigAssetResponses": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/internal_api.userConfigAssetResponse"}}}, "internal_api.vipUserBalanceRequest": {"type": "object", "required": ["bets_won", "coins", "handle", "source", "total_bets", "user_external_id"], "properties": {"bets_won": {"type": "integer"}, "coins": {"type": "number"}, "handle": {"type": "number"}, "source": {"type": "string"}, "total_bets": {"type": "integer"}, "user_external_id": {"type": "string"}}}}}