package api

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func TestUsersToLeaderboardUsersResponse(t *testing.T) {
	tests := []struct {
		name             string
		users            *domain.RankedUsers
		leaderboardType  string
		expectedResponse *LeaderboardUsersResponse
	}{
		{
			name: "Leaderboard response for total bets",
			users: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User: domain.User{
							ID:                  uuid.MustParse("00000000-0000-0000-0000-000000000001"),
							GhostMode:           false,
							HideAllStats:        true,
							HideTournamentStats: true,
							JoinDate:            time.UnixMilli(666),
							LastUpdatedOn:       utils.PointerOf(time.UnixMilli(666)),
							NumberOfLosses:      10,
							NumberOfWins:        20,
							ProfileStatus:       "profile_status",
							TotalBets:           30,
							UserName:            "user_name2",
							VIPStatus:           "vip_status",
							Wagered:             2000,
							ClaimedCoins:        10.0,
							TotalCoins:          25.7,
						},
						Rank:                1,
						RemainingToNextRank: 2.0,
					},
					{
						User: domain.User{
							ID:                  uuid.MustParse("00000000-0000-0000-0000-000000000002"),
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: false,
							JoinDate:            time.UnixMilli(666),
							LastUpdatedOn:       utils.PointerOf(time.UnixMilli(666)),
							NumberOfLosses:      2,
							NumberOfWins:        3,
							ProfileStatus:       "profile_status",
							TotalBets:           5,
							UserName:            "user_name1",
							VIPStatus:           "vip_status",
							Wagered:             1000,
							ClaimedCoins:        5.5,
							TotalCoins:          10.2,
						},
						Rank:                2,
						RemainingToNextRank: 1.0,
					},
				},
				Paging: domain.Paging{
					TotalCount:  100,
					CurrentPage: 1,
					PageSize:    100,
				},
			},
			leaderboardType: totalBetsField,
			expectedResponse: &LeaderboardUsersResponse{
				Items: []LeaderboardUserResponse{
					{
						UserResponse: UserResponse{
							ID:                  "00000000-0000-0000-0000-000000000001",
							ClaimedCoins:        0,
							Config:              (*string)(nil),
							ExternalID:          "",
							FactorSID:           (*string)(nil),
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: true, 
							JoinDate:            time.UnixMilli(666), 
							LastStateApplied:    (*time.Time)(nil), 
							ProfileStatus:       "",
							UnclaimedCoins:      15, 
							UserName:            "user_name2",
							VIPStatus:           "vip_status",
							Assets:              userConfigAssetResponses{},
							Email:               "",
							Verified:            false,
							EmailChanged:        false,
							MultiCurrency:       false,
							TotalBets:           30,
							TotalCoins:          25, 
							TotalLoses:          10, 
							TotalWins:           20,
							Wagered:             2000,
							WinRate:             66.67, 
							XP:                  0,
						},
						Rank:                        1,
						RemainingToNextRank:         2,
						RemainingToNextRankPosition: 0,
					},
					{
						UserResponse: UserResponse{
							ID:                  "00000000-0000-0000-0000-000000000002",
							ClaimedCoins:        0,
							Config:              (*string)(nil),
							ExternalID:          "",
							FactorSID:           (*string)(nil),
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: false,
							JoinDate:            time.UnixMilli(666),
							LastStateApplied:    (*time.Time)(nil), 
							ProfileStatus:       "", 
							UnclaimedCoins:      5,  
							UserName:            "user_name1",
							VIPStatus:           "vip_status",
							Assets:              userConfigAssetResponses{},
							Email:               "",
							Verified:            false,
							EmailChanged:        false,
							MultiCurrency:       false,
							TotalBets:           5,
							TotalCoins:          10, 
							TotalLoses:          2,
							TotalWins:           3,
							Wagered:             1000,
							WinRate:             60.0,
							XP:                  0,
						},
						Rank:                        2,
						RemainingToNextRank:         1,
						RemainingToNextRankPosition: 25, 
					},
				},
				Paging: paging{
					TotalCount:  100,
					CurrentPage: 1,
					PageSize:    100,
					PageCount:   2,
				},
			},
		},
		{
			name: "Leaderboard response for total coins",
			users: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User: domain.User{
							ID:                  uuid.MustParse("00000000-0000-0000-0000-000000000001"),
							ExternalID:          "external_id",
							GhostMode:           false,
							HideAllStats:        true,
							HideTournamentStats: true,
							JoinDate:            time.UnixMilli(666),
							LastUpdatedOn:       utils.PointerOf(time.UnixMilli(666)),
							NumberOfLosses:      10,
							NumberOfWins:        20,
							ProfileStatus:       "profile_status",
							TotalBets:           30,
							UserName:            "user_name2",
							VIPStatus:           "vip_status",
							Wagered:             2000,
							ClaimedCoins:        10.0,
							TotalCoins:          25.7,
						},
						Rank:                1,
						RemainingToNextRank: 2.0,
					},
					{
						User: domain.User{
							ID:                  uuid.MustParse("00000000-0000-0000-0000-000000000002"),
							ExternalID:          "external_id",
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: false,
							JoinDate:            time.UnixMilli(666),
							LastUpdatedOn:       utils.PointerOf(time.UnixMilli(666)),
							NumberOfLosses:      2,
							NumberOfWins:        3,
							ProfileStatus:       "profile_status",
							TotalBets:           5,
							UserName:            "user_name1",
							VIPStatus:           "vip_status",
							Wagered:             1000,
							ClaimedCoins:        5.5,
							TotalCoins:          10.2,
						},
						Rank:                2,
						RemainingToNextRank: 1.0,
					},
				},
				Paging: domain.Paging{
					TotalCount:  100,
					CurrentPage: 1,
					PageSize:    100,
				},
			},
			leaderboardType: totalCoinsField,
			expectedResponse: &LeaderboardUsersResponse{
				Items: []LeaderboardUserResponse{
					{
						UserResponse: UserResponse{
							ID:                  "00000000-0000-0000-0000-000000000001",
							ClaimedCoins:        0,
							Config:              (*string)(nil),
							ExternalID:          "", 
							FactorSID:           (*string)(nil),
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: true,
							JoinDate:            time.UnixMilli(666),
							LastStateApplied:    (*time.Time)(nil),
							ProfileStatus:       "",
							UnclaimedCoins:      15,
							UserName:            "user_name2",
							VIPStatus:           "vip_status",
							Assets:              userConfigAssetResponses{},
							Email:               "",
							Verified:            false,
							EmailChanged:        false,
							MultiCurrency:       false,
							TotalBets:           30,
							TotalCoins:          25,
							TotalLoses:          10,
							TotalWins:           20,
							Wagered:             2000,
							WinRate:             66.67,
							XP:                  0,
						},
						Rank:                1,
						RemainingToNextRank: 2,
					},
					{
						UserResponse: UserResponse{
							ID:                  "00000000-0000-0000-0000-000000000002",
							ClaimedCoins:        0,
							Config:              (*string)(nil),
							ExternalID:          "", 
							FactorSID:           (*string)(nil),
							GhostMode:           false,
							HideAllStats:        false,
							HideTournamentStats: false,
							JoinDate:            time.UnixMilli(666),
							LastStateApplied:    (*time.Time)(nil),
							ProfileStatus:       "", 
							UnclaimedCoins:      5,
							UserName:            "user_name1",
							VIPStatus:           "vip_status",
							Assets:              userConfigAssetResponses{},
							Email:               "",
							Verified:            false,
							EmailChanged:        false,
							MultiCurrency:       false,
							TotalBets:           5,
							TotalCoins:          10,
							TotalLoses:          2,
							TotalWins:           3,
							Wagered:             1000,
							WinRate:             60.0,
							XP:                  0,
						},
						Rank:                        2,
						RemainingToNextRank:         1,
						RemainingToNextRankPosition: 15, 
					},
				},
				Paging: paging{
					TotalCount:  100,
					CurrentPage: 1,
					PageSize:    100,
					PageCount:   2,
				},
			},
		},
		{
			name: "Multiple users with the same rank",
			users: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
							TotalBets:  30,
							TotalCoins: 100.0,
						},
						Rank:                1,
						RemainingToNextRank: 0.0,
					},
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000002"),
							TotalBets:  30,
							TotalCoins: 100.0,
						},
						Rank:                1,
						RemainingToNextRank: 0.0,
					},
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000003"),
							TotalBets:  20,
							TotalCoins: 80.0,
						},
						Rank:                2,
						RemainingToNextRank: 20.0,
					},
				},
				Paging: domain.Paging{
					TotalCount:  3,
					CurrentPage: 1,
					PageSize:    100,
				},
			},
			leaderboardType: totalBetsField,
			expectedResponse: &LeaderboardUsersResponse{
				Items: []LeaderboardUserResponse{
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000001",
							TotalBets:      30,
							UnclaimedCoins: 100,
							TotalCoins:     100,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        1,
						RemainingToNextRank:         0,
						RemainingToNextRankPosition: 0,
					},
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000002",
							TotalBets:      30,
							UnclaimedCoins: 100,
							TotalCoins:     100,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        1,
						RemainingToNextRank:         0,
						RemainingToNextRankPosition: 0,
					},
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000003",
							TotalBets:      20,
							UnclaimedCoins: 80,
							TotalCoins:     80,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        2,
						RemainingToNextRank:         20,
						RemainingToNextRankPosition: 10,
					},
				},
				Paging: paging{
					TotalCount:  3,
					CurrentPage: 1,
					PageSize:    100,
					PageCount:   3,
				},
			},
		},
		{
			name: "Users with the same value but different ranks",
			users: &domain.RankedUsers{
				Items: []domain.RankedUser{
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000001"),
							TotalBets:  100,
							TotalCoins: 1000.0,
						},
						Rank:                1,
						RemainingToNextRank: 0.0,
					},
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000002"),
							TotalBets:  90,
							TotalCoins: 900.0,
						},
						Rank:                2,
						RemainingToNextRank: 10.0,
					},
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000003"),
							TotalBets:  90,
							TotalCoins: 900.0,
						},
						Rank:                3,
						RemainingToNextRank: 10.0,
					},
					{
						User: domain.User{
							ID:         uuid.MustParse("00000000-0000-0000-0000-000000000004"),
							TotalBets:  80,
							TotalCoins: 800.0,
						},
						Rank:                4,
						RemainingToNextRank: 20.0,
					},
				},
				Paging: domain.Paging{
					TotalCount:  4,
					CurrentPage: 1,
					PageSize:    100,
				},
			},
			leaderboardType: totalBetsField,
			expectedResponse: &LeaderboardUsersResponse{
				Items: []LeaderboardUserResponse{
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000001",
							TotalBets:      100,
							UnclaimedCoins: 1000,
							TotalCoins:     1000,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        1,
						RemainingToNextRank:         0,
						RemainingToNextRankPosition: 0,
					},
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000002",
							TotalBets:      90,
							UnclaimedCoins: 900,
							TotalCoins:     900,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        2,
						RemainingToNextRank:         10,
						RemainingToNextRankPosition: 10,
					},
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000003",
							TotalBets:      90,
							UnclaimedCoins: 900,
							TotalCoins:     900,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        3,
						RemainingToNextRank:         10,
						RemainingToNextRankPosition: 10,
					},
					{
						UserResponse: UserResponse{
							ID:             "00000000-0000-0000-0000-000000000004",
							TotalBets:      80,
							UnclaimedCoins: 800,
							TotalCoins:     800,
							Assets:         userConfigAssetResponses{},
						},
						Rank:                        4,
						RemainingToNextRank:         20,
						RemainingToNextRankPosition: 10,
					},
				},
				Paging: paging{
					TotalCount:  4,
					CurrentPage: 1,
					PageSize:    100,
					PageCount:   4,
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			response := usersToLeaderboardUsersResponse(test.users, test.leaderboardType, false)
			require.Equal(t, test.expectedResponse, response)
		})
	}
}

func BenchmarkUsersToLeaderboardUsersResponse(b *testing.B) {
	users := &domain.RankedUsers{
		Items: make([]domain.RankedUser, 10000),
		Paging: domain.Paging{
			TotalCount:  10000,
			CurrentPage: 1,
			PageSize:    10000,
		},
	}
	for i := range users.Items {
		users.Items[i] = domain.RankedUser{
			User: domain.User{
				ID:         uuid.New(),
				TotalBets:  i,
				TotalCoins: float64(i),
			},
			Rank:                i + 1,
			RemainingToNextRank: float64(len(users.Items) - i),
		}
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		usersToLeaderboardUsersResponse(users, totalBetsField, false)
	}
}