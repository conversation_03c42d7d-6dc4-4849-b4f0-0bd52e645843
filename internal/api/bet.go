package api

import (
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

type betParams struct {
	OrderParams
	PagingParams
	UserExternalID         *string  `query:"userExternalId"`
	IncludeBetsWithoutGame *bool    `query:"includeBetsWithoutGame"`
	PayoutOver             *float64 `query:"payoutOver"`
	FilterBetsByGame       *bool    `query:"filterBetsByGame"`
	BetType                *string  `query:"betType"`
}

type betByUserIDParams struct {
	OrderParams
	PagingParams
	UserID                 uuid.UUID `query:"userId"`
	IncludeBetsWithoutGame bool      `query:"includeBetsWithoutGame"`
	FilterBetsByGame       bool      `query:"filterBetsByGame"`
	From                   *string   `query:"from"`
	To                     *string   `query:"to"`
	Result                 *string   `query:"result"`
	Type                   *string   `query:"type"`
	Currency               *string   `query:"currency"`
	Status                 *string   `query:"status"`
}

func queryToGetBetsParams(query *betParams) *domain.GetBetsParams {
	includeBetsWithoutGame := false
	if query.IncludeBetsWithoutGame != nil {
		includeBetsWithoutGame = *query.IncludeBetsWithoutGame
	}

	filterBetsByGame := false
	if query.FilterBetsByGame != nil {
		filterBetsByGame = *query.FilterBetsByGame
	}

	betType := "CASINO"
	if query.BetType != nil {
		betType = strings.ToUpper(*query.BetType)
	}

	return &domain.GetBetsParams{
		UserExternalID:         query.UserExternalID,
		IncludeBetsWithoutGame: includeBetsWithoutGame,
		PayoutOver:             query.PayoutOver,
		OrderParams:            queryToOrderParams(&query.OrderParams, "time"),
		PagingParams:           queryToPagingParams(&query.PagingParams),
		FilterBetsByGame:       &filterBetsByGame,
		BetType:                &betType,
	}
}

func queryToGetBetsByUserIDParams(query *betByUserIDParams) *domain.GetBetsByUserIDParams {
	includeBetsWithoutGame := false
	if query.IncludeBetsWithoutGame {
		includeBetsWithoutGame = true
	}

	filterBetsByGame := false
	if query.FilterBetsByGame {
		filterBetsByGame = true
	}

	betType := "CASINO"
	if query.Type != nil {
		betType = strings.ToUpper(*query.Type)
	}

	return &domain.GetBetsByUserIDParams{
		UserID:                 query.UserID,
		OrderParams:            queryToOrderParams(&query.OrderParams, "time"),
		PagingParams:           queryToPagingParams(&query.PagingParams),
		IncludeBetsWithoutGame: includeBetsWithoutGame,
		FilterBetsByGame:       filterBetsByGame,
		From:                   parseTransactionFrom(query.From),
		To:                     parseTransactionTo(query.To),
		Result:                 normalizeStringPointer(query.Result),
		Type:                   &betType,
		Currency:               parseTransCurrency(query.Currency),
		Status:                 normalizeStringPointer(query.Status),
	}
}

func normalizeStringPointer(s *string) *string {
	if s == nil {
		return nil
	}
	trimmed := strings.TrimSpace(*s)
	if trimmed == "" {
		return nil
	}
	lowered := strings.ToLower(trimmed)
	return &lowered
}

type betExportParams struct {
	ExportParams
}

func queryToBetExportParams(
	query *betExportParams,
	path *externalIDParam,
) (*domain.ExportBetsParams, error) {
	exprtParams, err := queryToExportParams(&query.ExportParams)
	if err != nil {
		return nil, err
	}

	return &domain.ExportBetsParams{
		ExportParams:   *exprtParams,
		UserExternalID: path.ID,
	}, nil
}

type BetResponse struct {
	ID              string       `json:"id,omitempty"`
	BetAmount       *float64     `json:"bet_amount,omitempty"`
	ActualBetAmount *float64     `json:"actual_bet_amount,omitempty"`
	BetType         string       `json:"bet_type,omitempty"`
	CancelID        *string      `json:"cancel_id,omitempty"`
	Currency        string       `json:"currency,omitempty"`
	ExternalID      string       `json:"external_id,omitempty"`
	GhostMode       bool         `json:"ghost_mode,omitempty"`
	HiddenBoolean   bool         `json:"hidden_boolean,omitempty"`
	Multiplier      float64      `json:"multiplier,omitempty"`
	Payout          *float64     `json:"payout,omitempty"`
	ActualWinAmount *float64     `json:"actual_win_amount,omitempty"`
	RoundStatus     string       `json:"round_status,omitempty"`
	Time            time.Time    `json:"time,omitempty"`
	WinID           *string      `json:"win_id,omitempty"`
	Event           *string      `json:"event,omitempty"`
	Odds            *float64     `json:"odds,omitempty"`
	Game            GameResponse `json:"game,omitempty"`
	User            UserResponse `json:"user,omitempty"`
}

type betsResponse PagedItemsWithoutTotalCount[BetResponse]

type userBetsResponse UserPagedItems[BetResponse]

func betsToBetsResponse(bets *domain.Bets, isOwner bool, isAdmin bool) *betsResponse {
	return &betsResponse{
		Items: utils.MapSlice(bets.Items, func(bet domain.Bet) BetResponse {
			return BetToBetResponse(bet, isOwner, isAdmin)
		}),
		Paging: pagingWithoutTotalCount{
			CurrentPage: bets.Paging.CurrentPage,
			PageSize:    bets.Paging.PageSize,
			PageCount:   len(bets.Items),
		},
	}
}

func betsToUserBetsResponse(bets *domain.UserBets, isOwner bool, isAdmin bool) *userBetsResponse {
	return &userBetsResponse{
		Items: utils.MapSlice(bets.Items, func(bet domain.Bet) BetResponse {
			return BetToBetResponse(bet, isOwner, isAdmin)
		}),
		Paging: paging{
			TotalCount:  bets.Paging.TotalCount,
			CurrentPage: bets.Paging.CurrentPage,
			PageSize:    bets.Paging.PageSize,
			PageCount:   len(bets.Items),
		},
		Details: Details(bets.Details),
	}
}

func BetToBetResponse(b domain.Bet, isOwner bool, isAdmin bool) BetResponse {
	game := gameToGameResponse(&b.Game)
	user := userToUserResponse(&b.User, isOwner, isAdmin)
	return BetResponse{
		ID:              b.ID.String(),
		BetAmount:       b.BetAmount,
		ActualBetAmount: b.ActualBetAmount,
		BetType:         string(b.BetType),
		Currency:        b.Currency,
		ExternalID:      b.ExternalID,
		GhostMode:       b.GhostMode,
		HiddenBoolean:   b.HiddenBoolean,
		Multiplier:      utils.RoundFloat(b.Multiplier, 2),
		Payout:          b.Payout,
		ActualWinAmount: b.ActualWinAmount,
		RoundStatus:     string(b.RoundStatus),
		Event:           b.Event,
		Odds:            b.Odds,
		Time:            b.Time,
		Game:            *game,
		User:            *user,
	}
}

type betsExportResponse []betExportResponse

type betExportResponse struct {
	BetAmount  *float64  `csv:"bet_amount"`
	BetType    string    `csv:"bet_type"`
	Currency   string    `csv:"currency"`
	ExternalID string    `csv:"external_id"`
	Multiplier float64   `csv:"multiplier"`
	Payout     *float64  `csv:"payout"`
	Time       time.Time `csv:"time"`
	Game       *string   `csv:"game"`
}

func betsToBetsExportResponse(bets []domain.Bet) betsExportResponse {
	return utils.MapSlice(bets, betToBetExportResponse)
}

func betToBetExportResponse(bet domain.Bet) betExportResponse {
	return betExportResponse{
		BetAmount:  bet.BetAmount,
		BetType:    string(bet.BetType),
		Currency:   bet.Currency,
		ExternalID: bet.ExternalID,
		Multiplier: utils.RoundFloat(bet.Multiplier, 2),
		Payout:     bet.Payout,
		Time:       bet.Time,
		Game:       bet.Game.Name,
	}
}
