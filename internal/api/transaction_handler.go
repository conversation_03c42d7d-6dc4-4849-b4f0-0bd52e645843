package api

import (
	"log/slog"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gocarina/gocsv"
	"github.com/gofiber/fiber/v2"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// BoostHandler handles bet HTTP requests.
type TransactionHandler struct {
	transactionService domain.TransactionService
	validator          *validator.Validate
}

// TransactionHandler creates new instance of BoosttHandler.
func NewTransactionHandler(transactionService domain.TransactionService, validator *validator.Validate) *TransactionHandler {
	return &TransactionHandler{transactionService: transactionService, validator: validator}
}

// BetTransactions gets all transactions.
// @Produce application/json
// @Param pageNum        query  int    false "Page number."
// @Param pageSize       query  int    false "Page size."
// @Param order          query  string false "Order direction." Enums(desc asc)
// @Param orderBy        query  string false "Order by field."
// @Param type           query  string false "Comma-separated list of transaction types."
// @Param userExternalId query  string false "User external ID."
// @Param Authorization  header string true  "Standard authorization header containing a bearer token."
// @Param RefreshToken   header string true  "Refresh token."
// @Success 200 {object} transactionsResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/transactions [get]
func (h *TransactionHandler) GetTransactions(c *fiber.Ctx) error {
	query, err := parseQuery[transactionParams](c, h.validator)
	if err != nil {
		return err
	}

	userId, ok := utils.GetUserIDFromContext(c)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	query.UserExternalID = userId
	params := queryToGetTransactionsParams(query)
	transactions, err := h.transactionService.GetTransactions(c.Context(), params)
	if err != nil {
		return err
	}

	return c.JSON(transactionsToTransactionsResponse(transactions))
}

// ExportTranscationsByUserExternalID exports all transactions.
// @Produce text/csv
// @Param id            path   string true  "User external ID."
// @Param startDate     query  string false "Start date filter."
// @Param endDate       query  string false "End date filter."
// @Param type          query  string false "Comma-separated list of transaction types."
// @Param Authorization header string true  "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string true  "Refresh token."
// @Success 200 {object} transactionsExportResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/users/{id}/transaction/export [get]
func (t *TransactionHandler) ExportTranscationsByUserExternalID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	query, err := parseQuery[transactionExportParams](c, t.validator)
	if err != nil {
		return err
	}

	externalID := &externalIDParam{ID: userID}
	params, err := queryToTransactionExportParams(query, externalID)
	if err != nil {
		return err
	}

	transactions, err := t.transactionService.ExportTransactions(c.Context(), params)
	if err != nil {
		return err
	}

	return gocsv.Marshal(transactionsToTransactionsExportResponse(transactions), c)
}

func (h *TransactionHandler) GetTransactionByType(c *fiber.Ctx) error {
	userId := c.Params("userId")
	transType := c.Query("transType")
	category := c.Query("category")
	currency := c.Query("currency")
	offset := c.Query("offset")
	limit := c.Query("limit")

	offsetInt, err := strconv.Atoi(offset)
	if err != nil {
		slog.Error("failed to convert offset to int: %v", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "failed to convert offset to int")
	}

	limitInt, err := strconv.Atoi(limit)
	if err != nil {
		slog.Error("failed to convert limit to int: %v", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "failed to convert limit to int")
	}

	transactions, err := h.transactionService.GetTransactionByType(c.Context(), transType, category, currency, userId, offsetInt, limitInt)
	if err != nil {
		slog.Error("failed to get transactions: %v", "error", err)
		return fiber.NewError(fiber.StatusInternalServerError, "failed to get transactions")
	}

	return c.JSON(transactions)
}

// GetTransactionExplorer gets transaction explorer links for a specific user
func (h *TransactionHandler) GetTransactionExplorer(c *fiber.Ctx) error {
	userID := c.Params("user_id")
	if userID == "" {
		return fiber.NewError(fiber.StatusBadRequest, "user_id is required")
	}

	// Call the service to get explorer links
	explorerLinks, err := h.transactionService.GetTransactionExplorerLinks(c.Context(), userID)
	if err != nil {
		slog.Error("failed to get transaction explorer links", "user_id", userID, "error", err)
		return fiber.NewError(fiber.StatusInternalServerError, "failed to get transaction explorer links")
	}

	return c.JSON(explorerLinks)
}
