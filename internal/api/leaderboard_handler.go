package api

import (
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"golang.org/x/sync/singleflight"
)

const (
	wageredField           = "wagered"
	totalBetsField         = "total_bets"
	totalCoinsField        = "total_coins"
	retrieveAmountParamKey = "retrieveAmount"
)

type LeaderboardHandler struct {
	g           singleflight.Group
	userService domain.UserService
	validator   *validator.Validate
	authService domain.AuthenticationService
}

// NewGameHandler creates new instance of GameHandler.
func NewLeaderboardHandler(userService domain.UserService, validator *validator.Validate, authService domain.AuthenticationService) *LeaderboardHandler {
	return &LeaderboardHandler{userService: userService, validator: validator, authService: authService}
}

// GetLeaderboard gets user leaderboard.
// @Produce application/json
// @Param orderBy query string false "Order by field."
// @Success 200 {object} LeaderboardUsersResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/leaderboard [get]
func (h *LeaderboardHandler) GetLeaderboard(c *fiber.Ctx) error {

	query, err := parseQuery[LeaderboardParams](c, h.validator)
	if err != nil {
		return err
	}

	params := queryToLeaderboardOrderParams(query)

	users, err, _ := h.g.Do(c.OriginalURL(), func() (any, error) {
		users, err := h.userService.GetRankedUsers(c.Context(), &domain.GetUserParams{
			OrderParams: params,
			PagingParams: domain.PagingParams{
				PageNumber: 1,
				PageSize:   20,
			},
		})
		if err != nil {
			return "", err
		}

		return users, nil
	})

	if err != nil {
		return err
	}

	rankedUsers := users.(*domain.RankedUsers)
	token := c.Get("Authorization")
	isAdmin := IsAdmin(c, token, h.authService)
	var response *LeaderboardUsersResponse
	switch params.OrderBy {
	case wageredField:
		response = usersToLeaderboardUsersResponse(rankedUsers, wageredField, isAdmin)
	case totalBetsField:
		response = usersToLeaderboardUsersResponse(rankedUsers, totalBetsField, isAdmin)
	case totalCoinsField:
		response = usersToLeaderboardUsersResponse(rankedUsers, totalCoinsField, isAdmin)
	}

	return c.JSON(response)
}

// GetLeaderboardByUserExternalID gets user leaderboard wagered filtered by user External ID.
// @Produce application/json
// @Param   id             path  string true  "External user ID."
// @Param   orderBy        query string false "Order by field."
// @Param   retrieveAmount query int    false "Amount of Users below and above retrieved."
// @Success 200 {array}  LeaderboardUserResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/leaderboard/{id}/rank [get]
func (h *LeaderboardHandler) GetLeaderboardByUserExternalID(c *fiber.Ctx) error {
	externalUserID := c.Params("id")

	query, err := parseQuery[leaderboardByUserParams](c, h.validator)
	if err != nil {
		return err
	}

	orderBy := queryToLeaderboardOrderBy(&query.LeaderboardParams)
	retrieveAmount := query.RetrieveAmount
	if retrieveAmount > 100 {
		retrieveAmount = 5
	}

	users, err := h.userService.GetRankedUsersByExternalID(
		c.Context(),
		externalUserID,
		retrieveAmount,
		orderBy,
	)
	if err != nil {
		return err
	}

	token := c.Get("Authorization")
	isAdmin := IsAdmin(c, token, h.authService)

	var response []LeaderboardUserResponse
	switch orderBy {
	case wageredField:
		response = usersToLeaderboardUserResponses(users, wageredField, isAdmin)
	case totalBetsField:
		response = usersToLeaderboardUserResponses(users, totalBetsField, isAdmin)
	case totalCoinsField:
		response = usersToLeaderboardUserResponses(users, totalCoinsField, isAdmin)
	}

	return c.JSON(response)
}
