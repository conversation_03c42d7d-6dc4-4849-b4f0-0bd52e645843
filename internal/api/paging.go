package api

// PagedItems represents the paginated result items.
type PagedItems[T any] struct {
	Items  []T    `json:"items"`
	Paging paging `json:"paging"`
}

type TransactionPagedItems[T any] struct {
	Items   []T     `json:"items"`
	Paging  paging  `json:"paging"`
	Details Details `json:"details"`
}

type PagedItemsWithoutTotalCount[T any] struct {
	Items  []T                     `json:"items"`
	Paging pagingWithoutTotalCount `json:"paging"`
}

type UserPagedItems[T any] struct {
	Items   []T                     `json:"items"`
	Paging  paging `json:"paging"`
	Details Details                 `json:"details"`
}

// paging represents the paginated results information.
type paging struct {
	TotalCount  int64 `json:"total_count"`
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	PageCount   int   `json:"page_count"`
}

type pagingWithoutTotalCount struct {
	CurrentPage int `json:"current_page"`
	PageSize    int `json:"page_size"`
	PageCount   int `json:"page_count"`
}

type Details struct {
	TotalCredit float64 `json:"total_credit"`
	TotalDebit  float64 `json:"total_debit"`
}
