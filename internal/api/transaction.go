package api

import (
	"slices"
	"strings"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/google/uuid"
)

var (
	defaultTransactionTypes      = []string{"credit", "debit"}
	defaultTransactionCategories = []string{"payments"}
	defaultTransactionStatus     = "completed"
)

type transactionParams struct {
	OrderParams
	PagingParams
	UserExternalID        string  `query:"userExternalId"`
	TransactionTypes      *string `query:"type"`
	TransactionStatus     *string `query:"status"`
	TransactionCategories *string `query:"categories"`
	TransactionFrom       *string `query:"from"`
	TransactionTo         *string `query:"to"`
	Currency              *string `query:"currency"`
}

func queryToGetTransactionsParams(query *transactionParams) *domain.GetTransactionParams {
	return &domain.GetTransactionParams{
		OrderParams:    queryToOrderParams(&query.OrderParams, "inserted_at"),
		PagingParams:   queryToPagingParams(&query.PagingParams),
		UserExternalID: query.UserExternalID,
		Types:          parseTransTypes(query.TransactionTypes),
		Status:         parseTransStatus(query.TransactionStatus),
		Categories:     parseTransCategories(query.TransactionCategories),
		From:           parseTransactionFrom(query.TransactionFrom),
		To:             parseTransactionTo(query.TransactionTo),
		Currency:       parseTransCurrency(query.Currency),
	}
}

type transactionsResponse TransactionPagedItems[transactionResponse]

type transactionResponse struct {
	ID                      uuid.UUID `json:"id"`
	Amount                  float64   `json:"amount"`
	CryptoFiatAmount        float64   `json:"crypto_fiat_amount"`
	Category                string    `json:"category"`
	Currency                string    `json:"currency"`
	ExternalID              string    `json:"external_id"`
	InsertedAt              time.Time `json:"inserted_at"`
	Status                  string    `json:"status"`
	Type                    string    `json:"type"`
	TransactionExplorerLink *string   `json:"transaction_explorer_link,omitempty"`
}

func transactionToTransactionResponse(transaction domain.Transaction) transactionResponse {
	if transaction.CryptoFiatAmount == 0 {
		transaction.CryptoFiatAmount = transaction.Amount
	}
	return transactionResponse{
		ID:                      transaction.ID,
		Amount:                  transaction.Amount,
		CryptoFiatAmount:        transaction.CryptoFiatAmount,
		Category:                transaction.Category,
		ExternalID:              transaction.ExternalID,
		Currency:                transaction.Currency,
		InsertedAt:              transaction.InsertedAt,
		Status:                  transaction.Status,
		Type:                    transaction.Type,
		TransactionExplorerLink: transaction.TransactionExplorerLink,
	}
}

func transactionsToTransactionsResponse(transactions *domain.Transactions) *transactionsResponse {
	return &transactionsResponse{
		Items: utils.MapSlice(transactions.Items, transactionToTransactionResponse),
		Paging: paging{
			TotalCount:  transactions.Paging.TotalCount,
			CurrentPage: transactions.Paging.CurrentPage,
			PageSize:    transactions.Paging.PageSize,
			PageCount:   len(transactions.Items),
		},
		Details: Details(transactions.Details),
	}
}

type transactionExportParams struct {
	ExportParams
	TransactionTypes *string `query:"type"`
}

func queryToTransactionExportParams(
	query *transactionExportParams,
	path *externalIDParam,
) (*domain.ExportTransactionsParams, error) {
	exportParams, err := queryToExportParams(&query.ExportParams)
	if err != nil {
		return nil, err
	}

	return &domain.ExportTransactionsParams{
		ExportParams:   *exportParams,
		UserExternalID: path.ID,
		Types:          parseTransTypes(query.TransactionTypes),
		Categories:     defaultTransactionCategories,
	}, nil
}

func parseTransTypes(typesParam *string) []string {
	if typesParam == nil {
		return defaultTransactionTypes
	}

	var transTypes []string
	types := strings.Split(*typesParam, ",")

	if slices.Contains(types, "withdraw") {
		transTypes = append(transTypes, "debit")
	}

	if slices.Contains(types, "deposit") {
		transTypes = append(transTypes, "credit")
	}

	for _, t := range types {
		trimmed := strings.TrimSpace(t)
		if trimmed != "withdraw" && trimmed != "deposit" && trimmed != "" {
			transTypes = append(transTypes, trimmed)
		}
	}

	return transTypes
}

func parseTransCategories(categoriesParam *string) []string {
	if categoriesParam == nil {
		return defaultTransactionCategories
	}

	categories := strings.Split(*categoriesParam, ",")

	for i, category := range categories {
		categories[i] = strings.TrimSpace(category)
	}

	if slices.Contains(categories, "bonus") {
		var filtered []string
		for _, category := range categories {
			if category != "bonus" {
				filtered = append(filtered, category)
			}
		}
		categories = append(filtered, "promotions")
	}

	return categories
}

func parseTransStatus(statusParam *string) []string {
	if statusParam == nil {
		return nil
	}

	statuses := strings.Split(*statusParam, ",")
	for i, status := range statuses {
		statuses[i] = strings.ToLower(strings.TrimSpace(status))
	}

	return statuses
}

func parseTransCurrency(currencyParam *string) []string {
	if currencyParam == nil {
		return nil
	}

	currencies := strings.Split(*currencyParam, ",")
	for i, currency := range currencies {
		currencies[i] = strings.ToUpper(strings.TrimSpace(currency))
	}

	return currencies
}

func parseTransactionFrom(fromParam *string) *time.Time {
	if fromParam == nil {
		return nil
	}

	startDate, _ := time.Parse(time.RFC3339, *fromParam)

	return &startDate
}

func parseTransactionTo(toParam *string) *time.Time {
	if toParam == nil {
		return nil
	}

	endDate, _ := time.Parse(time.RFC3339, *toParam)

	return &endDate
}

type transactionsExportResponse []transactionExportResponse

type transactionExportResponse struct {
	Amount     float64   `csv:"amount"`
	Category   string    `csv:"category"`
	Currency   string    `csv:"currency"`
	ExternalID string    `csv:"external_id"`
	InsertedAt time.Time `csv:"inserted_at"`
	Status     string    `csv:"status"`
	Type       string    `csv:"type"`
}

func transactionsToTransactionsExportResponse(transaction []domain.Transaction) transactionsExportResponse {
	return utils.MapSlice(transaction, transactionToTransactionExportResponse)
}

func transactionToTransactionExportResponse(transaction domain.Transaction) transactionExportResponse {
	return transactionExportResponse{
		Amount:     transaction.Amount,
		Category:   transaction.Category,
		Currency:   transaction.Currency,
		ExternalID: transaction.ExternalID,
		InsertedAt: transaction.InsertedAt,
		Status:     transaction.Status,
		Type:       transaction.Type,
	}
}
