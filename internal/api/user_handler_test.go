package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/mocks/domainmock"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

func MockTokenValidation(c *fiber.Ctx) error {
	token := c.Get("Authorization")
	if token != "Bearer valid_token" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid token"})
	}
	c.Locals(utils.UserIDKey, "1234567")
	c.Locals(utils.User<PERSON>ame<PERSON><PERSON>, "test-user")
	c.Locals(utils.UserEmail<PERSON>ey, "<EMAIL>")
	return c.Next()
}

func createMockAuthServiceForUserHandler(t *testing.T) *domainmock.AuthenticationService {
	authService := domainmock.NewAuthenticationService(t)
	authService.On("ValidateAccessToken", mock.Anything, mock.Anything).
		Return(domain.ValidateTokenResponse{
			UserID:   "test-user-id",
			UserName: "test-user",
			Email:    "<EMAIL>", // Non-admin email
		}, nil).Maybe() // Use Maybe() so it doesn't fail if not called
	return authService
}

func TestUserHandler_GetUserByIDOrUserName(t *testing.T) {
	userUUID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	testCases := []struct {
		name              string
		inputBetID        string
		userService       *domainmock.UserService
		userAssetsService *domainmock.UserConfigAssetsService
		expectedStatus    int
		expectedBody      string
		route             string
		token             string
		expectedErrFunc   require.ErrorAssertionFunc
	}{
		{
			name: "Get User by ID",
			userService: func(id *uuid.UUID, user *domain.User, err error) *domainmock.UserService {
				userService := domainmock.NewUserService(t)
				userService.EXPECT().GetUserByID(mock.Anything, *id).Return(user, err).Once()
				return userService
			}(
				&userUUID,
				&domain.User{ID: userUUID, ExternalID: ""},
				nil),
			expectedStatus:  fiber.StatusOK,
			expectedBody:    `{"id":"00000000-0000-0000-0000-000000000001","external_id":"","claimed_coins":0,"config":null,"factor_sid":null,"ghost_mode":false,"hide_all_stats":false,"hide_tournament_stats":false,"join_date":"0001-01-01T00:00:00Z","last_state_applied":null,"profile_status":"","unclaimed_coins":0,"user_name":"","vip_status":"","assets":{},"email":"","verified":false,"email_changed":false,"multi_currency":false,"total_bets":0,"total_coins":0,"total_loses":0,"total_wins":0,"wagered":0,"win_rate":0,"xp":0}`,
			route:           "/api/v1/users/00000000-0000-0000-0000-000000000001",
			token:           "Bearer valid_token",
			expectedErrFunc: require.NoError,
		},
		{
			name: "Not Found",
			userService: func(username string, user *domain.User, err error) *domainmock.UserService {
				userService := domainmock.NewUserService(t)
				userService.EXPECT().GetUserByUserName(mock.Anything, username).Return(user, err).Once()
				return userService
			}("sarasa", nil, domain.ErrResourceNotFound),
			expectedStatus:  fiber.StatusNotFound,
			expectedBody:    `{"error":"resource not found"}`,
			route:           "/api/v1/users/sarasa",
			token:           "Bearer valid_token",
			expectedErrFunc: require.NoError,
		},
		{
			name: "Ghost Mode",
			userService: func(id uuid.UUID, user *domain.User, err error) *domainmock.UserService {
				userService := domainmock.NewUserService(t)
				userService.EXPECT().GetUserByID(mock.Anything, id).Return(&domain.User{ID: id, GhostMode: true}, nil).Once()
				return userService
			}(userUUID, nil, nil),
			expectedStatus:  fiber.StatusOK,
			expectedBody:    `{"id":"00000000-0000-0000-0000-000000000001","external_id":"","claimed_coins":0,"config":null,"factor_sid":null,"ghost_mode":true,"hide_all_stats":false,"hide_tournament_stats":false,"join_date":"0001-01-01T00:00:00Z","last_state_applied":null,"profile_status":"","unclaimed_coins":0,"user_name":"Hidden","vip_status":"","assets":{},"email":"","verified":false,"email_changed":false,"multi_currency":false,"total_bets":0,"total_coins":0,"total_loses":0,"total_wins":0,"wagered":0,"win_rate":0,"xp":0}`,
			route:           "/api/v1/users/00000000-0000-0000-0000-000000000001",
			token:           "Bearer valid_token",
			expectedErrFunc: require.NoError,
		},
		{
			name:            "Invalid Token",
			userService:     domainmock.NewUserService(t),
			expectedStatus:  fiber.StatusUnauthorized,
			expectedBody:    `{"error":"Invalid token"}`,
			route:           "/api/v1/users/00000000-0000-0000-0000-000000000001",
			token:           "Bearer invalid_token",
			expectedErrFunc: require.NoError,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			app := fiber.New(fiber.Config{
				ErrorHandler: func(c *fiber.Ctx, err error) error {
					if errors.Is(err, domain.ErrResourceNotFound) {
						return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": err.Error()})
					}
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
				},
			})

			app.Use(MockTokenValidation)
			mockAuthService := createMockAuthServiceForUserHandler(t)
			userHandler := NewUserHandler(tc.userService, tc.userAssetsService, validator.New(), mockAuthService, nil)
			api := app.Group("/api/v1")
			api.Get("/users/:idOrUserName", userHandler.GetUserByIDOrUserName)

			req := httptest.NewRequest("GET", tc.route, nil)
			req.Header.Set("Authorization", tc.token)

			resp, err := app.Test(req, -1)
			tc.expectedErrFunc(t, err)
			assert.Equal(t, tc.expectedStatus, resp.StatusCode)

			body, _ := io.ReadAll(resp.Body)
			var actualResponse map[string]interface{}
			err = json.Unmarshal(body, &actualResponse)
			require.NoError(t, err)

			var expectedResponse map[string]interface{}
			err = json.Unmarshal([]byte(tc.expectedBody), &expectedResponse)
			require.NoError(t, err)

			assert.Equal(t, expectedResponse, actualResponse)
		})
	}
}

func TestUserHandler_GetUserByExternalUserID(t *testing.T) {
	testUserExternalID := "1234567"
	userUUID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	testCases := []struct {
		name           string
		mockSetup      func(*domainmock.UserService)
		token          string
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "NotFound",
			mockSetup: func(us *domainmock.UserService) {
				us.On("GetRankedUserByExternalID", mock.Anything, testUserExternalID).Return(nil, domain.ErrResourceNotFound)
			},
			token:          "Bearer valid_token",
			expectedStatus: fiber.StatusNotFound,
			expectedBody:   `{"error":"resource not found"}`,
		},
		{
			name: "InternalError",
			mockSetup: func(us *domainmock.UserService) {
				us.On("GetRankedUserByExternalID", mock.Anything, testUserExternalID).Return(nil, errors.New("internal error"))
			},
			token:          "Bearer valid_token",
			expectedStatus: fiber.StatusInternalServerError,
			expectedBody:   `{"error":"internal error"}`,
		},
		{
			name: "Success",
			mockSetup: func(us *domainmock.UserService) {
				rankedUser := &domain.RankedUser{
					User: domain.User{ID: userUUID, ExternalID: testUserExternalID},
				}
				us.On("GetRankedUserByExternalID", mock.Anything, testUserExternalID).Return(rankedUser, nil)
			},
			token:          "Bearer valid_token",
			expectedStatus: fiber.StatusOK,
			expectedBody:   `{"id":"00000000-0000-0000-0000-000000000001","external_id":"","claimed_coins":0,"config":null,"factor_sid":null,"ghost_mode":false,"hide_all_stats":false,"hide_tournament_stats":false,"join_date":"0001-01-01T00:00:00Z","last_state_applied":null,"profile_status":"","unclaimed_coins":0,"user_name":"","vip_status":"","assets":{},"email":"","verified":false,"email_changed":false,"multi_currency":false,"total_bets":0,"total_coins":0,"total_loses":0,"total_wins":0,"wagered":0,"win_rate":0,"xp":0,"rank":0,"remaining_to_next_rank":0}`,
		},
		{
			name:           "Invalid Token",
			mockSetup:      func(us *domainmock.UserService) {},
			token:          "Bearer invalid_token",
			expectedStatus: fiber.StatusUnauthorized,
			expectedBody:   `{"error":"Invalid token"}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			app := fiber.New(fiber.Config{
				ErrorHandler: func(c *fiber.Ctx, err error) error {
					if errors.Is(err, domain.ErrResourceNotFound) {
						return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": err.Error()})
					}
					return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": err.Error()})
				},
			})
			mockUserService := new(domainmock.UserService)
			tc.mockSetup(mockUserService)
			mockAuthService := createMockAuthServiceForUserHandler(t)
			userHandler := NewUserHandler(mockUserService, nil, validator.New(), mockAuthService, nil)
			app.Use(MockTokenValidation)
			app.Get("/api/v1/auth/users/:id", userHandler.GetUserByExternalUserID)

			req := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/auth/users/%s", testUserExternalID), nil)
			req.Header.Set("Authorization", tc.token)
			resp, err := app.Test(req)
			require.NoError(t, err)

			assert.Equal(t, tc.expectedStatus, resp.StatusCode)

			body, _ := io.ReadAll(resp.Body)
			t.Logf("Response body: %s", string(body))

			var actualResponse map[string]interface{}
			err = json.Unmarshal(body, &actualResponse)
			require.NoError(t, err)

			var expectedResponse map[string]interface{}
			err = json.Unmarshal([]byte(tc.expectedBody), &expectedResponse)
			require.NoError(t, err)

			assert.Equal(t, expectedResponse, actualResponse)

			mockUserService.AssertExpectations(t)
		})
	}
}
