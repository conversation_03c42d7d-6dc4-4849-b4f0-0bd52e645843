package api

import "github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"

type userConfigAssetResponses map[string][]userConfigAssetResponse

type userConfigAssetResponse struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	SubType string `json:"sub_type"`
	Key     string `json:"key"`
	Value   string `json:"value"`
}

func userConfigAssetsToUserConfigAssetResponses(assets []domain.UserConfigAsset) userConfigAssetResponses {
	resp := userConfigAssetResponses{}
	for _, asset := range assets {
		assetType := asset.Type
		resp[assetType] = append(resp[assetType], userConfigAssetToUserConfigAssetResponse(asset))
	}

	return resp
}

func userConfigAssetToUserConfigAssetResponse(asset domain.UserConfigAsset) userConfigAssetResponse {
	return userConfigAssetResponse{
		ID:      asset.ID.String(),
		Type:    string(asset.Type),
		SubType: string(asset.SubType),
		Key:     asset.Key,
		Value:   asset.Value,
	}
}
