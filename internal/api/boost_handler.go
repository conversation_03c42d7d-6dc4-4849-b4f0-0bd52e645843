package api

import (
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// <PERSON>ost<PERSON>andler handles bet HTTP requests.
type BoostHandler struct {
	boostService domain.BoostService
	userService  domain.UserService
	validator    *validator.Validate
}

// NewBoostHandler creates new instance of BoosttHandler.
func NewBoostHandler(boostService domain.BoostService, userService domain.UserService, validator *validator.Validate) *BoostHandler {
	return &BoostHandler{boostService: boostService, userService: userService, validator: validator}
}

// CreateBoost create boost that will be available for a user to be used.
// @Accept  application/json
// @Param   body body     createBoostRequest true "Create boost body request."
// @Success 200  {object} boostCreationResponse
// @Failure 400  {object} fiber.Error
// @Failure 401  {object} fiber.Error
// @Failure 403  {object} fiber.Error
// @Failure 404  {object} fiber.Error
// @Router /internal/v1/boost [post]
func (h *BoostHandler) CreateBoost(c *fiber.Ctx) error {
	body, err := parseBody[createBoostRequest](c, h.validator)
	if err != nil {
		return err
	}

	createBoostResult, err := h.boostService.CreateBoost(c.Context(), boostRequestToBoost(body), body.UsersID)
	if err != nil {
		return err
	}

	return c.JSON(createBoostToResponse(createBoostResult))
}

// Activate Booster.
// @Accept  application/json
// @Produce application/json
// @Param id            path   string true "User external ID."
// @Param Authorization header string true "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string true "Refresh token."
// @Success 204 {string} string
// @Failure 400 {object} fiber.Error
// @Failure 403 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router /api/v1/auth/boost/users/:id [patch]
func (h *BoostHandler) BoostUser(c *fiber.Ctx) error {
	path, err := parsePath[externalIDParam](c, h.validator)
	if err != nil {
		return err
	}

	user, err := h.userService.GetUserByExternalID(c.Context(), path.ID)
	if err != nil {
		return err
	}

	boost, err := h.boostService.GetAvailableBoostByUserID(c.Context(), user.ExternalID, time.Now().UTC(), false)
	if err != nil {
		return err
	}

	boost.BoostStartedAt = utils.PointerOf(time.Now())

	err = h.boostService.UpdateBoosts(c.Context(), *boost)

	if err != nil {
		return err
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// Available Booster fro an user.
// @Accept  application/json
// @Produce application/json
// @Param id            path   string true "User external ID."
// @Param Authorization header string true "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string true "Refresh token."
// @Success 204 {object} boostResponse
// @Failure 400 {object} fiber.Error
// @Failure 403 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router  /api/v1/boost/users/:id [patch]
func (h *BoostHandler) BoostAvailableUser(c *fiber.Ctx) error {
	path, err := parsePath[externalIDParam](c, h.validator)
	if err != nil {
		return err
	}

	user, err := h.userService.GetUserByExternalID(c.Context(), path.ID)
	if err != nil {
		return err
	}

	boost, err := h.boostService.GetAvailableBoostByUserID(c.Context(), user.ExternalID, time.Now().UTC(), true)
	if err != nil {
		return err
	}

	return c.JSON(boostToBoostResponse(boost))
}
