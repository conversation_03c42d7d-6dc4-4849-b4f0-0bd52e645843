package api

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type RestClientHandler struct {
	RailsService domain.RailsClientService
}

func NewRestClientHandler(railsService domain.RailsClientService) *RestClientHandler {
	return &RestClientHandler{
		RailsService: railsService,
	}
}

// handleRailsError converts RailsClientError to appropriate HTTP response
func (r *RestClientHandler) handleRailsError(c *fiber.Ctx, err error) error {
	if railsErr, ok := err.(*domain.RailsClientError); ok {
		// Map error codes to HTTP status codes
		var statusCode int
		switch railsErr.Code {
		case domain.ErrInvalidDepositAddress, domain.ErrInvalidWithdrawalAddress:
			statusCode = fiber.StatusBadRequest
		case domain.ErrDepositAddressValidation, domain.ErrWithdrawalValidation:
			statusCode = fiber.StatusBadRequest
		case domain.ErrInsufficientBalance:
			statusCode = fiber.StatusBadRequest
		case domain.ErrWithdrawalLimitExceeded:
			statusCode = fiber.StatusBadRequest
		case domain.ErrDepositAddressUnsupported, domain.ErrWithdrawalUnsupported:
			statusCode = fiber.StatusBadRequest
		case domain.ErrRailsServiceUnavailable:
			statusCode = fiber.StatusServiceUnavailable
		case domain.ErrRailsTimeout:
			statusCode = fiber.StatusRequestTimeout
		default:
			statusCode = fiber.StatusInternalServerError
		}

		return c.Status(statusCode).JSON(fiber.Map{
			"error": fiber.Map{
				"code":    railsErr.Code,
				"message": railsErr.Message,
				"details": railsErr.Details,
			},
		})
	}

	// For non-Rails errors, return generic internal server error
	slog.Error("Unexpected error in Rails client handler", "error", err)
	return fiber.NewError(fiber.StatusInternalServerError, "Internal server error")
}

func (r *RestClientHandler) CreateDepositAddressOfUser(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	err := r.RailsService.CreateDepositAddressOfUser(c.Context(), userID)
	if err != nil {
		return r.handleRailsError(c, err)
	}
	return c.JSON(fiber.Map{"message": "Deposit address created"})
}

func (r *RestClientHandler) GetUserDepositAddresses(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}
	addresses, err := r.RailsService.GetUserDepositAddresses(c.Context(), userID)
	if err != nil {
		return r.handleRailsError(c, err)
	}
	return c.JSON(fiber.Map{"addresses": addresses})
}

func (r *RestClientHandler) CreateWithdrawalRequest(c *fiber.Ctx) error {
	var req domain.CreateWithdrawalRequest

	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	if err := c.BodyParser(&req); err != nil {
		slog.Error("Failed to parse withdrawal request", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
	}

	req.UserID = userID

	if err := r.RailsService.CreateWithdrawalRequest(c.Context(), req); err != nil {
		return r.handleRailsError(c, err)
	}
	return c.JSON(fiber.Map{"message": "Withdrawal request created"})
}
