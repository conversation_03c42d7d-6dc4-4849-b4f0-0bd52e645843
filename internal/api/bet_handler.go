package api

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/gocarina/gocsv"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/sync/singleflight"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// BetHandler handles bet HTTP requests.
type BetHandler struct {
	g           singleflight.Group
	betService  domain.BetService
	validator   *validator.Validate
	authService domain.AuthenticationService
	redis       *redis.Client
}

// NewBetHandler creates new instance of BetHandler.
func NewBetHandler(
	betService domain.BetService,
	validator *validator.Validate,
	authService domain.AuthenticationService,
	redisClient *redis.Client,
) *BetHandler {
	return &BetHandler{
		betService:  betService,
		validator:   validator,
		authService: authService,
		redis:       redisClient,
	}
}

// GetBetByExternalID gets a bet by ExternalID.
// @Produce application/json
// @Param   id   path    string      true "Bet ID."
// @Success 200 {object} BetResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router  /api/v1/bets/{id} [get]
func (h *BetHandler) GetBetByExternalID(c *fiber.Ctx) error {
	path, err := parsePath[externalIDParam](c, h.validator)
	if err != nil {
		return err
	}

	token := c.Get("Authorization")
	isAdmin := IsAdmin(c, token, h.authService)

	if h.redis != nil {
		now := time.Now()
		minScore := fmt.Sprintf("%d", now.Add(-1*time.Hour).UnixMilli())
		vals, err := h.redis.ZRangeByScore(context.Background(), utils.GetSlatedBetsKey(), &redis.ZRangeBy{Min: minScore, Max: "+inf"}).Result()
		if err == nil {
			for _, v := range vals {
				var simBet domain.Bet
				if json.Unmarshal([]byte(v), &simBet) == nil {
					if simBet.ExternalID == path.ID {
						return c.JSON(BetToBetResponse(simBet, false, isAdmin))
					}
				}
			}
		}
		if utils.ENABLE_SLATED_BET_DEBUG_LOGS {
			slog.Info("No slated bet found for external ID", "externalId", path.ID)
		}
	}

	bet, err := h.betService.GetBetByExternalID(c.Context(), path.ID)
	if err != nil {
		slog.Error("Failed to get bet by ID", "error", err, "betId", path.ID)
		return err
	}

	return c.JSON(BetToBetResponse(*bet, false, isAdmin))
}

// GetBets gets all bets.
// @Produce application/json
// @Param pageNum                query  int     false "Page number."
// @Param pageSize               query  int     false "Page size."
// @Param order                  query  string  false "Order direction." Enums(desc asc)
// @Param orderBy                query  string  false "Order by field."
// @Param includeBetsWithoutGame query  bool    false "Include or not bets that have autogenerated games."
// @Param userExternalId         query  string  false "Filter bets by given user external ID."
// @Param payoutOver             query  float64 false "Filter bets over a given payout amount."
// @Success 200 {object} betsResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/bets [get]
func (h *BetHandler) GetBets(c *fiber.Ctx) error {
	query, err := parseQuery[betParams](c, h.validator)
	if err != nil {
		return err
	}
	if query.UserExternalID != nil {
		return fiber.NewError(fiber.StatusForbidden, "unathorized to get bets by user external ID")
	}
	betParams := queryToGetBetsParams(query)

	bets, err, _ := h.g.Do(c.OriginalURL(), func() (any, error) {
		return h.betService.GetBets(c.Context(), betParams)
	})
	if err != nil {
		return err
	}

	token := c.Get("Authorization")
	isAdmin := IsAdmin(c, token, h.authService)

	betsPtr, ok := bets.(*domain.Bets)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "unexpected bets data type")
	}
	return c.JSON(betsToBetsResponse(betsPtr, false, isAdmin))
}

// ExportBetsByUserExternalID exports all bets.
// @Produce text/csv
// @Param id            path   string true  "User external ID."
// @Param startDate     query  string false "Start date filter."
// @Param endDate       query  string false "End date filter."
// @Param Authorization header string true  "Standard authorization header containing a bearer token."
// @Param RefreshToken  header string true  "Refresh token."
// @Success 200 {object} betsExportResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Failure 500 {object} fiber.Error
// @Router /api/v1/users/{id}/bets/export [get]
func (h *BetHandler) ExportBetsByUserExternalID(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	query, err := parseQuery[betExportParams](c, h.validator)
	if err != nil {
		return err
	}

	externalID := &externalIDParam{ID: userID}
	params, err := queryToBetExportParams(query, externalID)
	if err != nil {
		return err
	}

	bets, err := h.betService.ExportBets(c.Context(), params)
	if err != nil {
		return err
	}

	return gocsv.Marshal(betsToBetsExportResponse(bets), c)
}

// GetLatestBetsByUserId gets latest bets by user ID.
// @Produce application/json
// @Param   id   path    string      true "User ID."
// @Success 200 {array} BetResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router  /api/v1/users/{id}/bets/latest [get]
func (h *BetHandler) GetLatestBetsByUserId(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	bets, err := h.betService.GetLatestBetsByUserId(c.Context(), userID)
	if err != nil {
		slog.Error("error retrieving latest bets by user ID", "error", err)
		return err
	}

	return c.JSON(fiber.Map{"bets": bets, "count": len(bets)})
}

// ShareUserBetByBetId shares user bet by bet ID.
// @Produce application/json
// @Param   id   path    string      true "User ID."
// @Param   betId   path    string      true "Bet ID."
// @Success 200 {object} BetResponse
// @Failure 400 {object} fiber.Error
// @Failure 404 {object} fiber.Error
// @Router  /api/v1/users/{id}/bets/{betId}/share [post]
func (h *BetHandler) ShareUserBetByBetId(c *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("user ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "user ID not found in context")
	}
	betId := c.Params("betId")

	betUUID, err := uuid.Parse(betId)
	if err != nil {
		slog.Error("invalid bet ID", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "invalid bet ID")
	}

	bet, mt_original, err := h.betService.ShareUserBetByBetId(c.Context(), userID, betUUID)
	if err != nil {
		slog.Error("error sharing user bet by bet ID", "error", err)
		if err == domain.ErrBetNotAssociatedWithUser {
			return fiber.NewError(fiber.StatusBadRequest, "bet not associated with user")
		}
		return fiber.NewError(fiber.StatusInternalServerError, "failed to share bet")
	}

	return c.JSON(fiber.Map{
		"bet":         bet,
		"mt_original": mt_original,
	})
}

func (h *BetHandler) GetBetsByUserID(c *fiber.Ctx) error {
	query, err := parseQuery[betByUserIDParams](c, h.validator)
	if err != nil {
		return err
	}
	userID, ok := utils.GetUserIDFromContext(c)
	if !ok {
		slog.Error("user ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "user ID not found in context")
	}
	var isAdmin bool
	email, ok := utils.GetUserEmailFromContext(c)
	if !ok {
		slog.Error("email not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "email not found in context")
	}

	if strings.Contains(email, "@monkeytilt.co") {
		isAdmin = true
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		slog.Error("invalid user ID", "error", err)
		return fiber.NewError(fiber.StatusBadRequest, "invalid user ID")
	}
	query.UserID = userUUID
	betParams := queryToGetBetsByUserIDParams(query)
	bets, err, _ := h.g.Do(c.OriginalURL(), func() (any, error) {
		bets, err := h.betService.GetBetsByUserID(c.Context(), betParams)
		if err != nil {
			return "", err
		}

		return bets, nil
	})
	if err != nil {
		return err
	}

	betsPtr, ok := bets.(*domain.UserBets)
	if !ok {
		return fiber.NewError(fiber.StatusInternalServerError, "unexpected bets data type")
	}
	return c.JSON(betsToUserBetsResponse(betsPtr, false, isAdmin))
}

func IsAdmin(c *fiber.Ctx, token string, authService domain.AuthenticationService) bool {
	if token == "" {
		return false
	}
	resp, err := authService.ValidateAccessToken(c.Context(), token)
	if err != nil {
		slog.Error("error validating access token", "error", err)
		return false
	}
	email := resp.Email

	if email == "" {
		slog.Error("email not found in token")
		return false
	}

	return strings.Contains(email, "@monkeytilt.co")
}
