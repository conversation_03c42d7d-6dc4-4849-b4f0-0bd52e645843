package api

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

type SettingsHandler struct {
	settingsService domain.SettingsService
}

func NewSettingsHandler(settingsService domain.SettingsService) *SettingsHandler {
	return &SettingsHandler{settingsService: settingsService}
}

func (h *SettingsHandler) CreateSettings(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	token := ctx.Get("Authorization")
	if token == "" {
		slog.Error("Token is required")
		return fiber.NewError(fiber.StatusBadRequest, "token is required")
	}

	var req domain.UserSettingsPayload
	if err := ctx.BodyParser(&req); err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	resp, err := h.settingsService.CreateSettings(ctx.Context(), userID, req)
	if err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.JSON(resp)
}

func (h *SettingsHandler) GetSettings(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	settings, err := h.settingsService.GetSettings(userID)
	if err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.JSON(settings)
}

func (h *SettingsHandler) UpdateSettings(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	var req domain.UserSettings
	if err := ctx.BodyParser(&req); err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	if err := h.settingsService.UpdateSettings(userID, req); err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.SendStatus(fiber.StatusOK)
}

func (h *SettingsHandler) DeleteSettings(ctx *fiber.Ctx) error {
	userID, ok := utils.GetUserIDFromContext(ctx)
	if !ok {
		slog.Error("User ID not found in context")
		return fiber.NewError(fiber.StatusInternalServerError, "User ID not found in context")
	}

	key := ctx.Params("key")
	if key == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "key is required",
		})
	}

	if err := h.settingsService.DeleteSettings(userID, key); err != nil {
		return ctx.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return ctx.SendStatus(fiber.StatusOK)
}
