services:
  zookeeper:
    container_name: zookeeper
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["C<PERSON>", "nc", "-z", "localhost", "2181"]
      interval: 10s
      retries: 5
      start_period: 20s
      timeout: 5s

  kafka:
    container_name: kafka
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
    # if you want to create a directory for the volume to have more control over files and configs:
    #     mkdir ./kafka-data
    #     sudo chown -R 1000:1000 ./kafka-data
    # then replace following line for:
    # - ./kafka-data:/var/lib/kafka/data
    #  and run the docker-compose up.
      - kafka-data:/var/lib/kafka/data
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 10s
      retries: 20
      start_period: 20s
      timeout: 5s

  kafka_ui:
    container_name: kafka_ui
    image: provectuslabs/kafka-ui:latest
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "9090:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181

  kafka_cli:
    container_name: kafka_cli
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      kafka:
        condition: service_healthy
    tty: true
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    # TODO: Put this into a proper shellscript file.
    entrypoint:
      - sh
      - -c
      - |
        # Wait until Kafka is ready
        while ! kafka-topics --bootstrap-server kafka:9092 --list >/dev/null 2>&1; do
          echo "Waiting for Kafka to be ready..."
          sleep 5
        done

        # Check and create topic if it doesn't exist
        create_topic() {
          kafka-topics --bootstrap-server kafka:9092 --list | grep -q "$${1}" || \
            kafka-topics --create --topic "$${1}" --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1
        }

        echo "Kafka is ready. Creating topics..."

        create_topic game-configs-changed
        create_topic owner-bonuses-changed
        create_topic owner-sessions-changed
        create_topic owner-wallets-changed
        create_topic session-changed
        create_topic transaction-changed

        echo "Topics created successfully."

        # Keep the CLI container running
        tail -f /dev/null

  redis_local:
    container_name: redis_local
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: ["redis-server", "--appendonly", "yes", "--save", "60", "1"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  postgres:
    container_name: postgres
    image: postgres:13
    environment:
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 1s
      timeout: 2s
      retries: 5

  localstack:
    container_name: localstack
    image: localstack/localstack:latest
    environment:
      - SERVICES=secretsmanager
      - AWS_DEFAULT_REGION=us-east-1
    ports:
      - "4566:4566"
    volumes:
      - ./scripts/localstack-setup.sh:/etc/localstack/init/ready.d/init-aws.sh
      - localstack-data:/var/lib/localstack
    healthcheck:
      test: >-
        curl -sf localhost:4566/_localstack/init/ready | grep -q '"completed": true,'
      interval: 5s
      timeout: 5s
      start_period: 1m
      retries: 5

  community_events:
    container_name: community_events
    build:
        context: ./
        dockerfile: events.Dockerfile
    environment:
      - ENV=local
      - DATABASE_MIGRATION=true
    ports:
        - "8080:8080"
    volumes:
      - ./config/local/events_service.yaml:/config/events_service.yaml
    depends_on:
      kafka_cli:
        condition: service_started
      postgres:
        condition: service_healthy
      localstack:
        condition: service_healthy
      redis_local:
        condition: service_healthy
    healthcheck:
      test: >-
        curl -sf localhost:8080/ping
      interval: 1s
      timeout: 2s
      retries: 5

  community_rest:
    container_name: community_rest
    build:
        context: ./
        dockerfile: rest.Dockerfile
    environment:
      - ENV=local
      - DATABASE_MIGRATION=false
    ports:
        - "8081:8080"
    volumes:
      - ./config/local/rest_service.yaml:/config/rest_service.yaml
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_healthy
      community_events:
        condition: service_healthy
      redis_local:
        condition: service_healthy

volumes:
  kafka-data:
  postgres-data:
  localstack-data:
  redis-data:
