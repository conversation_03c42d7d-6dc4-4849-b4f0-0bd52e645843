#!/bin/sh
set -eu

create_secret() {
    local secret_name="local/${1:?}"
    local secret_value="${2:?}"
    echo "Creating secret: $secret_name"
    if awslocal secretsmanager describe-secret --secret-id "$secret_name" >/dev/null 2>&1; then
        echo "Secret $secret_name already exists, updating..."
        awslocal secretsmanager update-secret --secret-id "$secret_name" --secret-string "$secret_value"
    else
        echo "Creating new secret: $secret_name"
        awslocal secretsmanager create-secret --name "$secret_name" --secret-string "$secret_value"
    fi
}
echo "Waiting for LocalStack services to be ready..."
sleep 5
create_secret community-db-password   "password"
create_secret chainkafka              "dummy-kafka-cert-chain"
create_secret keykafka                "dummy-kafka-ssl-key"
create_secret sslKeyPassword          "dummy-ssl-password"
create_secret cakafka                 "dummy-kafka-ca"
create_secret grecoProducerPassword   "dummy-greco-password"
create_secret grecoProducerHMAC       "dummy-greco-hmac-key"
create_secret keycloack_client_secret "dummy-keycloak-client-secret"
create_secret directus_api_key                "dummy-directus-api-key"
create_secret directus_authentication_token   "dummy-directus-auth-token"
create_secret wagering_client_secret          "dummy-wagering-secret"
create_secret wallet_client_secret            "dummy-wallet-secret"
create_secret email_service_client_secret     "dummy-email-service-secret"
create_secret sendgrid_api_key        "dummy-sendgrid-api-key"
create_secret rails_client_secret     "dummy-rails-client-secret"
create_secret swagger_username        "admin"
create_secret swagger_password        "password"
create_secret slack_token             "xoxb-dummy-slack-token"
create_secret slack_channel_id        "C08EJ3JHNBY"
create_secret aws_access_key          "dummy-access-key"
create_secret aws_secret_access_key   "dummy-secret-access-key"

echo "All secrets created successfully!"
echo "Verifying secrets..."
awslocal secretsmanager list-secrets --query 'SecretList[].Name' --output table

echo "LocalStack secrets initialization completed!"