#!/bin/sh
set -eu

if ! command -v curl > /dev/null 2>&1; then
    echo 'Please install "curl"'
    exit 1
fi

internal_ip_address=${1:?}

user_external_id=${2:?}
handle=${3:?}
bets_won=${4:?}
total_bets=${5:?}
source=${6:?}

{ body=$(cat) ; } << EOF
{
  "user_external_id": "${user_external_id}",
  "handle": ${handle},
  "bets_won": ${bets_won},
  "total_bets": ${total_bets},
  "coins": $(echo "scale=2; ${handle}/10" | bc),
  "source": "${source}"
}
EOF

curl \
    --data "${body}" \
    --header 'Content-Type: application/json' \
    --location \
    "${internal_ip_address}:6969/internal/v1/vipUserBalances"
