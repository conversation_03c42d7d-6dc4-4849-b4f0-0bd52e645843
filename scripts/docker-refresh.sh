#!/bin/bash

# Docker Refresh Script (Project-Specific)
# This script stops containers, removes images/volumes, and rebuilds everything
# ONLY for the current project (safe for other projects)

set -e

echo "🛑 Stopping and removing containers..."
docker-compose down -v

echo "🧹 Removing project-specific images..."
# Remove images for this project (based on docker-compose.yaml)
docker-compose config --services | while read service; do
    image_name="${PWD##*/}_${service}"
    echo "Removing image: $image_name"
    docker rmi -f "$image_name" 2>/dev/null || true
done

echo "🔨 Rebuilding images without cache..."
docker-compose build --no-cache

echo "🚀 Starting containers..."
docker-compose up -d

echo "✅ Docker environment refreshed successfully!"
echo "📊 Current container status:"
docker-compose ps 